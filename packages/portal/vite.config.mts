import { fileURLToPath, URL } from "node:url";

import { resolve } from "path";
import { loadEnv } from "vite";
import { getPluginsList } from "./build/plugins";
import type { ConfigEnv, UserConfigExport } from "vite";

const root: string = process.cwd();

const pathResolve = (dir: string): string => {
  return resolve(__dirname, ".", dir);
};

// https://vitejs.dev/config/
export default ({ mode }: ConfigEnv): UserConfigExport => {
  console.log(loadEnv(mode, root));
  return {
    server: {
      host: true,
      proxy: {
        "^/(ifinance|ftc|workflow|app|api).*/.*": {
          target: "http://*************:9900",
          changeOrigin: true
        }
      },
      hmr: true
    },
    plugins: getPluginsList(),
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url))
      }
    },
    build: {
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: pathResolve("index.html")
        },
        // 静态资源分类打包
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    }
  };
};
