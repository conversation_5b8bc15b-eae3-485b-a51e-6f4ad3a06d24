﻿{
  "server": {
    "gateway": "/",
    "grantType": "ifinance-finance-client",
    "framePath": "",
    "smPublicKey": "04b323b4b709428d4b45f947e322500436f6863fea9617c3ebcf9cfe964139e71fec1a6757698220f07b19623876449bfed99064d910ad2dcdc82bc79dfc342f23",
    "sse": "/{messageCenter}/api/v1/finance/operate/frontend-support/sse/connect/"
  },
  "context": {
    "loan": "http://*************:3000/mock/172",
    "investment": "http://*************:3000/mock/175",
    "finance-blank-voucher": "http://*************:3000/mock/176",
    "mock172": "http://*************:3000/mock/172",
    "mock": "http://*************:3000/mock/62",
    "treasury-monitor-mock": "http://*************:3000/mock/102",
    "bank-plat-mock": "http://*************:3000/mock/107",
    "mock166": "http://*************:3000/mock/166",
    "mock77": "http://*************:3000/mock/77",
    "resolution": "http://*************:3000/mock/170",
    "mock72": "http://*************:3000/mock/72",
    "fileHost": "http://*************:9702/ftc-file-service/api/v1/file/download",
    "kkfileHost": "http://*************:9858/iss-file-view/onlinePreviewAuth",
    "file": "ftc-file-service",
    "engine": "workflow-engine",
    "modeler": "workflow-modeler-finance",
    "bankplat": "ifinance-bankplat-application",

    "risk": "ifinance-risk-restful",

    "bankportal": "ifinance-bankplat-application",
    "appJob": "ifinance-bankplat-job-application",
    "receipt-eves": "ifinance-bankplat-ereceipt-application",
    "receipt-parent": "ifinance-bankplat-receipt-application",
    "accounting-query": "ifinance-transaction-center-application-query",
    "accounting-operate": "ifinance-transaction-center-application-operate",
    "accounting-batch": "ifinance-transaction-center-application-batch",
    "antimoneylaundering-query": "ifinance-transaction-center-application-query",
    "antimoneylaundering-operate": "ifinance-transaction-center-application-operate",
    "antimoneylaundering-batch": "ifinance-transaction-center-application-batch",
    "basic": "ifinance-support-application",
    "cashmanage-query": "ifinance-settlement-counter-application-query",
    "cashmanage-operate": "ifinance-settlement-counter-application-operate",
    "cashmanage-batch": "ifinance-settlement-counter-application-batch",
    "charge": "ifinance-transaction-center-application-operate",
    "clientmanage": "ifinance-support-application",
    "collateral-query": "ifinance-loan-application-query",
    "collateral-operate": "ifinance-loan-application-operate",
    "collateral-batch": "ifinance-loan-application-batch",
    "notice-query": "ifinance-settlement-counter-application-query",
    "notice-operate": "ifinance-settlement-counter-application-operate",
    "notice-batch": "ifinance-settlement-counter-application-batch",
    "credit-certification-query": "ifinance-transaction-center-application-query",
    "credit-certification-operate": "ifinance-transaction-center-application-operate",
    "credit-certification-batch": "ifinance-transaction-center-application-batch",
    "credit-manage-query": "ifinance-loan-application-query",
    "credit-manage-operate": "ifinance-loan-application-operate",
    "credit-manage-batch": "ifinance-loan-application-batch",
    "credit-rating-query": "ifinance-loan-application-query",
    "credit-rating-operate": "ifinance-loan-application-operate",
    "credit-rating-batch": "ifinance-loan-application-batch",
    "dayend": "ifinance-dayend-application",
    "deposit-reserve-query": "ifinance-interbank-application-query",
    "deposit-reserve-operate": "ifinance-interbank-application-operate",
    "deposit-reserve-batch": "ifinance-interbank-application-batch",
    "deposit-reserve-counter-query": "ifinance-interbank-application-query",
    "deposit-reserve-counter-operate": "ifinance-interbank-application-operate",
    "deposit-reserve-counter-batch": "ifinance-interbank-application-batch",
    "draft-redis-count-query": "ifinance-interbank-application-query",
    "draft-redis-count-operate": "ifinance-interbank-application-operate",
    "draft-redis-count-batch": "ifinance-interbank-application-batch",
    "fcre-query": "ifinance-transaction-center-application-query",
    "fcre-operate": "ifinance-transaction-center-application-operate",
    "fcre-batch": "ifinance-transaction-center-application-batch",
    "forex-interface-query": "ifinance-fx-application-query",
    "forex-interface-operate": "ifinance-fx-application-operate",
    "forex-interface-batch": "ifinance-fx-application-batch",
    "experience": "ifinance-portal-application",
    "inner-account-query": "ifinance-transaction-center-application-query",
    "inner-account-operate": "ifinance-transaction-center-application-operate",
    "inner-account-batch": "ifinance-transaction-center-application-batch",
    "interbank-credit-query": "ifinance-interbank-application-query",
    "interbank-credit-operate": "ifinance-interbank-application-operate",
    "interbank-credit-batch": "ifinance-interbank-application-batch",
    "interbank-deposit-query": "ifinance-interbank-application-query",
    "interbank-deposit-operate": "ifinance-interbank-application-operate",
    "interbank-deposit-batch": "ifinance-interbank-application-batch",
    "interbank-lending-counter-query": "ifinance-interbank-application-query",
    "interbank-lending-counter-operate": "ifinance-interbank-application-operate",
    "interbank-lending-counter-batch": "ifinance-interbank-application-batch",
    "interbank-lending-query": "ifinance-interbank-application-query",
    "interbank-lending-operate": "ifinance-interbank-application-operate",
    "interbank-lending-batch": "ifinance-interbank-application-batch",
    "letter-guarantee-query": "ifinance-loan-application-query",
    "letter-guarantee-operate": "ifinance-loan-application-operate",
    "letter-guarantee-batch": "ifinance-loan-application-batch",
    "letter-guarantee-counter-query": "ifinance-loan-application-query",
    "letter-guarantee-counter-operate": "ifinance-loan-application-operate",
    "letter-guarantee-counter-batch": "ifinance-loan-application-batch",
    "loan-counter-query": "ifinance-loan-application-query",
    "loan-counter-operate": "ifinance-loan-application-operate",
    "loan-counter-batch": "ifinance-loan-application-batch",
    "loan-core-query": "ifinance-loan-application-query",
    "loan-core-operate": "ifinance-loan-application-operate",
    "loan-core-batch": "ifinance-loan-application-batch",
    "master-data": "ifinance-support-application",
    "messageCenter": "ifinance-support-application",
    "monitor-query": "ifinance-transaction-center-application-query",
    "monitor-operate": "ifinance-transaction-center-application-operate",
    "monitor-batch": "ifinance-transaction-center-application-batch",
    "ncdmanage-query": "ifinance-interbank-application-query",
    "ncdmanage-operate": "ifinance-interbank-application-operate",
    "ncdmanage-batch": "ifinance-interbank-application-batch",
    "portal": "ifinance-portal-application",
    "price": "ifinance-support-application",
    "printCenter": "ifinance-print-center-application",
    "pdf": "ifinance-support-application",
    "reconciliation-query": "ifinance-transaction-center-application-query",
    "reconciliation-operate": "ifinance-transaction-center-application-operate",
    "reconciliation-batch": "ifinance-transaction-center-application-batch",
    "ureport": "ifinance-analyse-application",
    "search": "ifinance-search-application",
    "settlement-counter-query": "ifinance-settlement-counter-application-query",
    "settlement-counter-operate": "ifinance-settlement-counter-application-operate",
    "settlement-counter-batch": "ifinance-settlement-counter-application-batch",
    "system": "ifinance-support-application",
    "systemmanage": "ifinance-support-application",
    "system-manage": "ifinance-support-application",
    "transaction-query": "ifinance-transaction-center-application-query",
    "transaction-operate": "ifinance-transaction-center-application-operate",
    "transaction-batch": "ifinance-transaction-center-application-batch",
    "transfer-credit-assets-query": "ifinance-loan-application-query",
    "transfer-credit-assets-operate": "ifinance-loan-application-operate",
    "transfer-credit-assets-batch": "ifinance-loan-application-batch",
    "workbench": "ifinance-portal-application",
    "workflow": "ifinance-workdesk-service",
    "interbank-price-query": "ifinance-interbank-application-query",
    "interbank-price-operate": "ifinance-interbank-application-operate",
    "interbank-price-batch": "ifinance-interbank-application-batch",
    "encoding": "ifinance-support-application",
    "pre-loan-operate": "ifinance-loan-application-operate",
    "pre-loan-query": "ifinance-loan-application-query",
    "pre-loan-batch": "ifinance-loan-application-batch",
    "loan-after-operate": "ifinance-loan-application-operate",
    "loan-after-query": "ifinance-loan-application-query",
    "loan-after-batch": "ifinance-loan-application-batch",
    "forex-manage-query": "ifinance-fx-application-query",
    "forex-manage-operate": "ifinance-fx-application-operate",
    "forex-manage-batch": "ifinance-fx-application-batch",
    "credit-reporting-operate": "ifinance-credit-reporting-application",
    "impairment-batch": "ifinance-transaction-center-application-batch",
    "impairment-operate": "ifinance-transaction-center-application-operate",
    "impairment-query": "ifinance-transaction-center-application-query"
  },
  "system": {
    "systemCode": "ifinance7",
    "dashboardRefreshInterval": 1800000
  }
}
