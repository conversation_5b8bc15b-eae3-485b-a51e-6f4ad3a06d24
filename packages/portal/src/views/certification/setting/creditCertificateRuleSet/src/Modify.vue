<template>
  <f-blank-scene :title="t('certification.setting.creditCertificateRuleSet.editTitle')" :isPoint="false">
    <f-multi-form-panel
      :column="3"
      ref="creditCertificateRuleSetForm"
      :model="creditCertificateRuleSetDto"
      :rules="formRules"
    >
      <f-panel :title="t('certification.setting.creditCertificateRuleSet.panelTitleBasic')">
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelCreditCertificateType')"
          prop="creditCertificateType"
          required
        >
          <f-select
            v-model="creditCertificateRuleSetDto.creditCertificateType"
            :url="getAuth"
            :placeholder="t('certification.setting.creditCertificateRuleSet.placeholderCreditCertificateType')"
            :extra-data="{
              menuResourceId: '*********',
              parentPropertyKey: 'CREDIT_CERTIFICATE'
            }"
            value-key="propertyKey"
            label="propertyName"
            disabled
          />
        </f-form-item>
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelCollectRuleName')"
          prop="collectRuleId"
          required
        >
          <f-magnifier-single
            v-model="creditCertificateRuleSetDto.collectRuleCode"
            :url="collectRule"
            :placeholder="t('certification.setting.creditCertificateRuleSet.placeholderCollectRuleName')"
            method="post"
            row-key="ruleCode"
            row-label="ruleName"
            input-key="ruleCodeOrName"
            :params="{
              chargeType: 'CREDIT_VERIFICATION'
            }"
            @change="collectRuleChange"
            @clear="clearCollectRule"
            auto-init
          >
            <f-magnifier-column
              prop="ruleType"
              :label="t('certification.setting.creditCertificateRuleSet.columnTitleCollectRuleType')"
              :formatter="formatterRuleType"
              :filterInput="false"
            />
            <f-magnifier-column
              prop="ruleName"
              :label="t('certification.setting.creditCertificateRuleSet.columnTitleCollectRuleName')"
            />
            <f-magnifier-column
              prop="chargeAmount"
              :label="t('certification.setting.creditCertificateRuleSet.columnTitleChargeAmount')"
            />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelCommissionAmount')"
          prop="commissionAmount"
          required
        >
          <f-amount v-model="creditCertificateRuleSetDto.commissionAmount" />
        </f-form-item>
        <!--超时未办结提醒-->
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelTimeoutRemind')"
          prop="timeoutRemind"
          required
        >
          <f-switch
            v-model="creditCertificateRuleSetDto.timeoutRemind"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
          />
        </f-form-item>
        <!--办结时限-->
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelDeadlineCompletion')"
          prop="deadlineCompletion"
          :required="creditCertificateRuleSetDto.timeoutRemind === yesOrNo.YES"
          v-show="creditCertificateRuleSetDto.timeoutRemind === yesOrNo.YES"
        >
          <f-number
            v-model="creditCertificateRuleSetDto.deadlineCompletion"
            :placeholder="t('certification.setting.creditCertificateRuleSet.placeholderDeadlineCompletion')"
            :whole-number="true"
            max="999"
            min="0"
          />
        </f-form-item>
        <!--时限是否含节假日-->
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelIncludeHoliday')"
          prop="includeHoliday"
          v-show="creditCertificateRuleSetDto.timeoutRemind === yesOrNo.YES"
          :required="creditCertificateRuleSetDto.timeoutRemind === yesOrNo.YES"
        >
          <f-switch
            v-model="creditCertificateRuleSetDto.includeHoliday"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
          />
        </f-form-item>
        <!--允许自动受理-->
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelAutoAcceptance')"
          prop="autoAcceptance"
          required
        >
          <f-switch
            v-model="creditCertificateRuleSetDto.autoAcceptance"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
          />
        </f-form-item>
        <!--自动受理需要审批-->
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelApprovalRequired')"
          prop="approvalRequired"
          :required="creditCertificateRuleSetDto.autoAcceptance === yesOrNo.YES"
          v-show="creditCertificateRuleSetDto.autoAcceptance === yesOrNo.YES"
        >
          <f-switch
            v-model="creditCertificateRuleSetDto.approvalRequired"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
          />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('certification.setting.creditCertificateRuleSet.panelTitleBizInfo')">
        <f-form-item
          :label="t('certification.setting.creditCertificateRuleSet.labelFinanceInstruction')"
          prop="financeInstruction"
          :employ="3"
          required
        >
          <f-textarea
            v-model="creditCertificateRuleSetDto.financeInstruction"
            :placeholder="t('certification.setting.creditCertificateRuleSet.placeholderFinanceInstruction')"
            :min-rows="3"
            :max-rows="5"
          />
          <!--          <Editor v-model="creditCertificateRuleSetDto.financeInstruction" ref="editor" />-->
        </f-form-item>
      </f-panel>
      <f-panel :title="t('certification.setting.creditCertificateRuleSet.panelTitleDataScope')">
        <f-form-item
          :label="
            creditCertificateRuleSetDto.creditCertificateType !== creditCertificateTypeEnum.LOAN_CREDIT_CERTIFICATE
              ? t('certification.setting.creditCertificateRuleSet.labelAccountGroup')
              : t('certification.setting.creditCertificateRuleSet.labelLoanAccountGroup')
          "
          prop="dataScope"
          :employ="3"
          required
        >
          <f-checkbox-group v-model="creditCertificateRuleSetDto.dataScope">
            <f-checkbox v-for="itemGroup in accountGroupConst" :key="itemGroup" :label="itemGroup.propertyKey">
              {{ itemGroup.propertyName }}
            </f-checkbox>
          </f-checkbox-group>
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>

    <!-- 底部按钮 -->
    <template #footer>
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="save"
        operate="save"
        @close="handleSaveSuccess"
        :before-trigger="handleFormValidator"
      />
      <f-submit-state
        :gather-params="gatherRemoveInfo"
        type="danger"
        :url="remove"
        operate="remove"
        @close="handleRemoveSuccess"
      />
      <f-button type="primary" @click="linkToList"
        >{{ t("certification.setting.creditCertificateRuleSet.btnLink") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { useForm } from "../hooks/useForm";
import { usePage } from "../hooks/usePage";
import { save, remove, collectRule, getAuth } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { onMounted } from "vue";
import httpTool from "@/utils/http.ts";

const { t } = useI18n();
//资信类型
const creditCertificateTypeEnum = useConst("certification.CreditCertificateType");
//是否
const yesOrNo = useConst("basic.YesOrNo");

const {
  creditCertificateRuleSetDto,
  formRules,
  linkToList,
  gatherSaveInfo,
  handleSaveSuccess,
  handleFormValidator,
  creditCertificateRuleSetForm,
  loadCreditCertificateRuleSetDto,
  handleRemoveSuccess,
  gatherRemoveInfo,
  collectRuleChange,
  clearCollectRule,
  formatterRuleType,
  accountGroupConst
} = useForm();

const { pageParams } = usePage();
// 页面初始化
onMounted(async () => {
  const res = await loadCreditCertificateRuleSetDto(pageParams?.id);
  if (res) {
    const res = await httpTool.post(getAuth, {
      menuResourceId: "*********",
      parentPropertyKey: creditCertificateRuleSetDto.creditCertificateType
    });
    if (res.success && res.data) {
      accountGroupConst.value = res.data;
    }
  }
});
</script>
