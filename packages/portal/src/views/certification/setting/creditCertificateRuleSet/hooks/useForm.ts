import type { CreditCertificateRuleSetDto } from "../types";
import { reactive, shallowRef, ref } from "vue";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { getAuth, getOne, openDateUrl } from "../url";
import { goPage } from "./usePage";

export const useForm = () => {
  const yesOrNo = useConst("certification.YesOrNo");
  //规则分类
  const ruleType = useConst("charge.ChargeRuleType");

  const creditCertificateRuleSetForm = shallowRef();

  // 业务权限设置数据对象
  const creditCertificateRuleSetDto = reactive<CreditCertificateRuleSetDto>({
    id: null,
    creditCertificateType: null,
    collectRuleId: null,
    collectRuleCode: "",
    collectRuleName: "",
    commissionAmount: null,
    timeoutRemind: yesOrNo.NO,
    deadlineCompletion: null,
    includeHoliday: yesOrNo.NO,
    autoAcceptance: yesOrNo.NO,
    approvalRequired: yesOrNo.NO,
    financeInstruction:
      "1、 本证明书不得转让，不得作为担保、融资的依据或凭证；\n" +
      "2、 本资信证明仅证明委托人在我司的上述业务情况，我司不承担因使用本证明书所产生的任何法律责任；\n" +
      "3、 本资信证明书的任何内容的修改、涂改均使资信证明书无效；\n" +
      "4、 本证明如涉及委托人财务数据，均摘录于委托人提供的财务报表；\n" +
      "5、 本证明有效期为3个月。",
    dataScope: [],
    dataStatus: "",
    inputUserName: "",
    modifyUserName: "",
    inputUserId: "",
    modifyUserId: "",
    inputTime: "",
    modifyTime: "",
    version: ""
  });

  // 获取资信证明类型枚举常量
  const creditCertificateTypeEnum = useConst("certification.CreditCertificateType");

  const linkToList = () => {
    goPage("list");
  };

  const gatherSaveInfo = () => {
    if (creditCertificateRuleSetDto.timeoutRemind === yesOrNo.NO) {
      creditCertificateRuleSetDto.deadlineCompletion = null;
      creditCertificateRuleSetDto.includeHoliday = null;
    }
    if (creditCertificateRuleSetDto.autoAcceptance === yesOrNo.NO) {
      creditCertificateRuleSetDto.approvalRequired = null;
    }
    return creditCertificateRuleSetDto;
  };

  const handleSaveSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  const handleFormValidator = async () => {
    const result = await creditCertificateRuleSetForm.value.form.validate((valid: any) => valid);
    if (!result) {
      return false;
    }
    return result;
  };

  // 获取明细信息
  const loadCreditCertificateRuleSetDto = async (id: number) => {
    const res = await httpTool.post(getOne, { id: id });
    if (res.success) {
      Object.assign(creditCertificateRuleSetDto, res.data);
      if (creditCertificateRuleSetDto.includeHoliday === null) {
        creditCertificateRuleSetDto.includeHoliday = yesOrNo.NO;
      }
      if (creditCertificateRuleSetDto.approvalRequired === null) {
        creditCertificateRuleSetDto.approvalRequired = yesOrNo.NO;
      }
    }
    return res.data;
  };

  const gatherRemoveInfo = () => {
    return creditCertificateRuleSetDto;
  };

  const handleRemoveSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  const collectRuleChange = (row: any) => {
    if (row) {
      creditCertificateRuleSetDto.collectRuleId = row.id;
      creditCertificateRuleSetDto.collectRuleCode = row.ruleCode;
      creditCertificateRuleSetDto.collectRuleName = row.ruleName;
      creditCertificateRuleSetDto.commissionAmount = row.chargeAmount;
    }
  };

  //计费规则clear
  const clearCollectRule = () => {
    creditCertificateRuleSetDto.collectRuleId = null;
    creditCertificateRuleSetDto.collectRuleCode = "";
    creditCertificateRuleSetDto.collectRuleName = "";
    creditCertificateRuleSetDto.commissionAmount = null;
  };

  //格式化放大镜
  const formatterRuleType = (row: any) => {
    return ruleType.valueToLabel(row.ruleType);
  };

  const accountGroupConst = ref([]);

  const creditCertificateTypeChange = async (value: string) => {
    const res = await httpTool.post(getAuth, {
      menuResourceId: "*********",
      parentPropertyKey: value
    });
    if (res.success && res.data) {
      creditCertificateRuleSetDto.dataScope = [];
      accountGroupConst.value = res.data.filter(item => {
        return item.permissionType !== "None";
      });
    }
  };

  //获取开机日
  const openDate = ref();
  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(openDateUrl).then((res: any) => {
        resolve([res.data.onlineDate, res.data.onlineDate]);
        openDate.value = res.data.onlineDate;
      });
    });
  };

  return {
    creditCertificateRuleSetForm,
    creditCertificateRuleSetDto,
    creditCertificateTypeEnum,
    linkToList,
    gatherSaveInfo,
    handleSaveSuccess,
    handleFormValidator,
    loadCreditCertificateRuleSetDto,
    handleRemoveSuccess,
    gatherRemoveInfo,
    collectRuleChange,
    clearCollectRule,
    formatterRuleType,
    creditCertificateTypeChange,
    getOpenDate,
    accountGroupConst
  };
};

export default useForm;
