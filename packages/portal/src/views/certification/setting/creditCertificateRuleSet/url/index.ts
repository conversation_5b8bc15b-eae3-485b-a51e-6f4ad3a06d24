// 资信证明受理规则设置
// {credit-certification-operate}

// 保存
export const save = "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/save";
// 删除
export const remove = "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/remove";
// 批量删除
export const batchRemove =
  "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/batch-remove";
// 列表查询
export const listPage =
  "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/list-page";
// 详情
export const getOne = "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/detail";
// 设置查询
export const querySetting =
  "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/query-setting";
// 列表导出
export const listExport =
  "{credit-certification-operate}/api/v1/finance/operate/credit/credit-certificate-rule/list/export";
// 计费规则
export const collectRule =
  "{charge}/api/v1/finance/charge/charge-set/charge-rule-set/remote/server/get-active-rule-by-charge-type";
//获取当前开机日
export const openDateUrl = "{dayend}/api/v1/finance/dayend/remote/server/open-date";
export const getAuth = "{system-manage}/api/v1/finance/system/biz-prop-permission/remote/server/list";
