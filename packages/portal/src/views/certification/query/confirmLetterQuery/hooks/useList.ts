import httpTool from "@/utils/http";
import { useI18n } from "vue-i18n";
import { reactive, shallowRef, ref, computed, nextTick, onBeforeMount } from "vue";
import { useConst } from "@ifs/support";
import { printDom } from "@/utils/print";
import { goPage } from "./usePage";
import { useTableColumns } from "./useTableColumns";
import type { WhiteListReturnDto } from "../types";
import {
  exportUrl,
  queryOpenDate,
  list,
  list1,
  list2,
  list3,
  list4,
  list5,
  list6,
  list7,
  list8,
  list9,
  list10
} from "../url";
import { storeToRefs } from "pinia";
import { useUserStoreHook } from "@/stores/modules/user";

export const useList = (childRefList: any[]) => {
  const { t } = useI18n();
  const rowId = ref<number>();
  const detail = shallowRef();
  const { defaultOfficeId } = storeToRefs(useUserStoreHook());
  const {
    tableColumns1,
    tableColumns2,
    tableColumns3,
    tableColumns4,
    tableColumns5,
    tableColumns6,
    tableColumns7,
    tableColumns8,
    tableColumns9,
    tableColumns10,
    tableColumns11,
    tableColumns12,
    tableColumns13,
    tableColumns14,
    originColumns1,
    originColumns2,
    originColumns3,
    originColumns4,
    originColumns5,
    originColumns6,
    originColumns7,
    originColumns8,
    originColumns9,
    originColumns10
  } = useTableColumns();

  const accType = useConst("certification.AccountGroup");
  // 表格配置
  const config = [
    {
      tableColumns: originColumns1,
      prop: "bankDepositList",
      url: list1,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-001",
      tableCompId: "certification-query-confirmLetterQuery-table-001",
      title: t("certification.query.confirmLetterQuery.panelTitle1")
    },
    {
      tableColumns: originColumns2,
      prop: "bankLoansList",
      url: list2,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-002",
      tableCompId: "certification-query-confirmLetterQuery-table-002",
      title: t("certification.query.confirmLetterQuery.panelTitle2")
    },
    {
      tableColumns: originColumns3,
      prop: "logoutAccountList",
      url: list3,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-003",
      tableCompId: "certification-query-confirmLetterQuery-table-003",
      title: t("certification.query.confirmLetterQuery.panelTitle3")
    },
    {
      tableColumns: originColumns4,
      prop: "lenderEntrustLoanInfoList",
      url: list4,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-004",
      tableCompId: "certification-query-confirmLetterQuery-table-004",
      title: t("certification.query.confirmLetterQuery.panelTitle4")
    },
    {
      tableColumns: originColumns5,
      prop: "borrowerEntrustLoanInfoList",
      url: list5,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-005",
      tableCompId: "certification-query-confirmLetterQuery-table-005",
      title: t("certification.query.confirmLetterQuery.panelTitle5")
    },
    {
      tableColumns: originColumns6,
      prop: "companyGuaranteeInfoList",
      url: list6,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-006",
      tableCompId: "certification-query-confirmLetterQuery-table-006",
      title: t("certification.query.confirmLetterQuery.panelTitle6")
    },
    {
      tableColumns: originColumns7,
      prop: "guaranteeConfirmationList",
      url: list7,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-007",
      tableCompId: "certification-query-confirmLetterQuery-table-007",
      title: t("certification.query.confirmLetterQuery.panelTitle7")
    },
    {
      tableColumns: originColumns8,
      prop: "ebankAcceptBillInfoList",
      url: list8,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-008",
      tableCompId: "certification-query-confirmLetterQuery-table-008",
      title: t("certification.query.confirmLetterQuery.panelTitle8")
    },
    {
      tableColumns: originColumns9,
      prop: "discountBillInfoList",
      url: list9,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-009",
      tableCompId: "certification-query-confirmLetterQuery-table-009",
      title: t("certification.query.confirmLetterQuery.panelTitle9")
    },
    {
      tableColumns: originColumns10,
      prop: "bearBillInfoList",
      // url: list10,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-010",
      tableCompId: "certification-query-confirmLetterQuery-table-010",
      title: t("certification.query.confirmLetterQuery.panelTitle10")
    },
    {
      tableColumns: tableColumns11,
      prop: "nullList1",
      // url: accountInfoUrl,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-011",
      tableCompId: "certification-query-confirmLetterQuery-table-011",
      title: t("certification.query.confirmLetterQuery.panelTitle11")
    },
    {
      tableColumns: tableColumns12,
      prop: "nullList2",
      // url: accountInfoUrl,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-012",
      tableCompId: "certification-query-confirmLetterQuery-table-012",
      title: t("certification.query.confirmLetterQuery.panelTitle12")
    },
    {
      tableColumns: tableColumns13,
      prop: "nullList3",
      // url: accountInfoUrl,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-013",
      tableCompId: "certification-query-confirmLetterQuery-table-013",
      title: t("certification.query.confirmLetterQuery.panelTitle13")
    },
    {
      tableColumns: tableColumns14,
      prop: "nullList4",
      // url: accountInfoUrl,
      exportUrl: exportUrl,
      queryCompId: "certification-query-confirmLetterQuery-query-014",
      tableCompId: "certification-query-confirmLetterQuery-table-014",
      title: t("certification.query.confirmLetterQuery.panelTitle14")
    }
  ];
  const printConfig = [
    {
      tableColumns: tableColumns1,
      prop: "bankDepositList",
      title: t("certification.query.confirmLetterQuery.panelTitle1")
    },
    {
      tableColumns: tableColumns2,
      prop: "bankLoansList",
      title: t("certification.query.confirmLetterQuery.panelTitle2")
    },
    {
      tableColumns: tableColumns3,
      prop: "logoutAccountList",
      title: t("certification.query.confirmLetterQuery.panelTitle3")
    },
    {
      tableColumns: tableColumns4,
      prop: "lenderEntrustLoanInfoList",
      title: t("certification.query.confirmLetterQuery.panelTitle4")
    },
    {
      tableColumns: tableColumns5,
      prop: "borrowerEntrustLoanInfoList",
      title: t("certification.query.confirmLetterQuery.panelTitle5")
    },
    {
      tableColumns: tableColumns6,
      prop: "companyGuaranteeInfoList",
      title: t("certification.query.confirmLetterQuery.panelTitle6")
    },
    {
      tableColumns: tableColumns7,
      prop: "guaranteeConfirmationList",
      title: t("certification.query.confirmLetterQuery.panelTitle7")
    },
    {
      tableColumns: tableColumns8,
      prop: "ebankAcceptBillInfoList",
      title: t("certification.query.confirmLetterQuery.panelTitle8")
    },
    {
      tableColumns: tableColumns9,
      prop: "discountBillInfoList",
      title: t("certification.query.confirmLetterQuery.panelTitle9")
    },
    {
      tableColumns: tableColumns10,
      prop: "bearBillInfoList",
      title: t("certification.query.confirmLetterQuery.panelTitle10")
    },
    {
      tableColumns: tableColumns11,
      prop: "nullList1",
      title: t("certification.query.confirmLetterQuery.panelTitle11")
    },
    {
      tableColumns: tableColumns12,
      prop: "nullList2",
      title: t("certification.query.confirmLetterQuery.panelTitle12")
    },
    {
      tableColumns: tableColumns13,
      prop: "nullList3",
      title: t("certification.query.confirmLetterQuery.panelTitle13")
    },
    {
      tableColumns: tableColumns14,
      prop: "nullList4",
      title: t("certification.query.confirmLetterQuery.panelTitle14")
    }
  ];
  // 表格查询对象
  const queryFrom = reactive({
    officeId: defaultOfficeId.value,
    clientCode: "",
    clientId: null,
    baseDate: "",
    queryDate: "",
    confirmationQueryDate: "",
    loanClientId: null,
    consignClientId: null,
    guaranteeClientId: null,
    drawerClientId: null,
    discountClientId: null,
    closeDateEnd: "",
    closeAccountFlag: "NO",
    accountStatusList: ["CANCELLATION"]
  });

  const postParamsBackend = (tableType: string) => {
    return function (formData: Record<string, any>) {
      switch (tableType) {
        case "bankLoansList":
          return {
            officeId: formData.officeId,
            loanClientId: formData.loanClientId,
            queryDate: formData.queryDate
          };
        case "lenderEntrustLoanInfoList":
          return {
            officeId: formData.officeId,
            consignClientId: formData.consignClientId,
            queryDate: formData.queryDate
          };
        case "borrowerEntrustLoanInfoList":
          return {
            officeId: formData.officeId,
            loanClientId: formData.loanClientId,
            queryDate: formData.queryDate
          };
        case "companyGuaranteeInfoList":
          return {
            officeId: formData.officeId,
            guaranteeClientId: formData.guaranteeClientId,
            queryDate: formData.queryDate
          };
        case "ebankAcceptBillInfoList":
          return {
            officeId: formData.officeId,
            drawerClientId: formData.drawerClientId,
            queryDate: formData.queryDate
          };
        case "discountBillInfoList":
          return {
            officeId: formData.officeId,
            discountClientId: formData.discountClientId,
            queryDate: formData.queryDate
          };
        default:
          return formData;
      }
    };
  };

  const dto = reactive({
    content: t("certification.query.confirmLetterQuery.nohave")
  });
  // 表格模板
  const queryTable = shallowRef();
  // 已选列表
  const checkedList = ref<WhiteListReturnDto[]>([]);
  // 抽屉模板
  const drawerRef = shallowRef();
  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length > 0);
  // 控制全选checkbox
  const selectableAll = (rows: any[]) => {
    return !rows;
  };
  // 打开抽屉
  const handleOpen = (row: WhiteListReturnDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  // 点击修改
  const changeRow = (row: WhiteListReturnDto) => {
    goPage("change", { id: row.id });
  };
  // 点击
  const modifyRow = (row: WhiteListReturnDto) => {
    goPage("modify", { id: row.id });
  };
  // 新增跳转
  const add = () => {
    goPage("add");
  };
  //
  const changeList = () => {
    goPage("changeList");
  };

  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
  };
  const tableListDto = reactive({
    bankDepositList: [],
    bankLoansList: [],
    bearBillInfoList: [],
    borrowerEntrustLoanInfoList: [],
    lenderEntrustLoanInfoList: [],
    companyGuaranteeInfoList: [],
    discountBillInfoList: [],
    ebankAcceptBillInfoList: [],
    guaranteeConfirmationList: [],
    logoutAccountList: [],
    nullList1: [
      {
        lcNo: t("certification.query.confirmLetterQuery.nohave"),
        beneficiary: "",
        currencyName: "",
        creditAmount: "",
        dueDate: "",
        unuseAmount: ""
      }
    ],
    nullList2: [
      {
        category: t("certification.query.confirmLetterQuery.nohave"),
        contractCode: t("certification.query.confirmLetterQuery.nohave"),
        lendCurrency: t("certification.query.confirmLetterQuery.nohave"),
        buyCurrency: t("certification.query.confirmLetterQuery.nohave"),
        unfulfillAmount: t("certification.query.confirmLetterQuery.nohave"),
        exchangeRate: t("certification.query.confirmLetterQuery.nohave"),
        settlementDate: t("certification.query.confirmLetterQuery.nohave")
      }
    ],
    nullList3: [
      {
        securityName: t("certification.query.confirmLetterQuery.nohave"),
        securityNo: t("certification.query.confirmLetterQuery.nohave"),
        count: t("certification.query.confirmLetterQuery.nohave"),
        currencyName: t("certification.query.confirmLetterQuery.nohave"),
        money: t("certification.query.confirmLetterQuery.nohave")
      }
    ],
    nullList4: [
      {
        productName: t("certification.query.confirmLetterQuery.nohave"),
        productType: t("certification.query.confirmLetterQuery.nohave"),
        currencyName: "",
        holdShares: "",
        netProdAmount: "",
        purchaseDate: "",
        dueDate: "",
        useGuarantee: ""
      }
    ]
  });
  const handleQuery = async () => {
    if (childRefList) {
      for (const key in childRefList) {
        childRefList[key].handleSearch();
      }
    }
    const commonList = await httpTool.post(list, queryFrom);
    if (commonList.success) {
      Object.assign(tableListDto, commonList.data);
    }
    const list10Res = await httpTool.post(list10, queryFrom);
    if (list10Res.success) {
      tableListDto.bearBillInfoList = list10Res.data;
    }
  };
  const onlineDate = ref("");
  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(queryOpenDate).then((res: any) => {
        onlineDate.value = res.data.onlineDate;
        resolve([]);
      });
    });
  };

  const endDisabledDate = (date: any) => {
    if (onlineDate.value) {
      return date.getTime() >= new Date(onlineDate.value).getTime() - 3600 * 1000 * 24;
    }
  };
  const printNode = ref();
  const handlePrint = () => {
    printDom(
      printNode.value,
      `
        @media print { body { margin: 0; padding: 0mm 10mm; } }
        @page { size: A4 landscape; margin: 0; marks: none; }
        .is-always-shadow { box-shadow: none !important; margin-bottom: 0 !important }
      `
    );
  };

  const clientConfirm = row => {
    if (row) {
      queryFrom.clientId = row.clientId;
      queryFrom.loanClientId = row.clientId;
      queryFrom.consignClientId = row.clientId;
      queryFrom.guaranteeClientId = row.clientId;
      queryFrom.drawerClientId = row.clientId;
      queryFrom.discountClientId = row.clientId;
    }
  };
  const clientClear = () => {
    queryFrom.clientId = null;
    queryFrom.loanClientId = null;
    queryFrom.consignClientId = null;
    queryFrom.guaranteeClientId = null;
    queryFrom.drawerClientId = null;
    queryFrom.discountClientId = null;
  };
  const dateChange = (val: string) => {
    if (val) {
      queryFrom.queryDate = val;
      queryFrom.closeDateEnd = val;
      queryFrom.confirmationQueryDate = val;
    } else {
      queryFrom.queryDate = "";
      queryFrom.closeDateEnd = "";
      queryFrom.confirmationQueryDate = "";
    }
  };

  onBeforeMount(() => {
    getOpenDate();
  });
  return {
    queryFrom,
    queryTable,
    drawerRef,
    isChecked,
    handleOpen,
    changeRow,
    add,
    changeList,
    handleSearch,
    selectableAll,
    rowId,
    detail,
    modifyRow,
    dto,
    handleQuery,
    config,
    getOpenDate,
    endDisabledDate,
    printNode,
    handlePrint,
    clientConfirm,
    clientClear,
    tableListDto,
    accType,
    dateChange,
    printConfig,
    postParamsBackend
  };
};

export default useList;
