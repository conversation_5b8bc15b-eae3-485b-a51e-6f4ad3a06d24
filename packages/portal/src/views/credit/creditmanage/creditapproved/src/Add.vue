<template>
  <f-blank-scene :title="t('credit.creditmanage.creditapproved.addTitle')">
    <f-multi-form-panel ref="form1" :rules="rules" :model="basicInfo" :column="3">
      <!-- 基础信息-->
      <f-panel :title="t('credit.creditmanage.creditapproved.basicInfo')" id="form10" position="1">
        <!--授信编号放大镜-->
        <f-form-item :label="t('credit.creditmanage.creditapproved.creditNo')" :required="true" prop="creditNo">
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapproved.creditNoMagnifier')"
            :url="getcreditNoUrl"
            method="post"
            v-model="basicInfo.originApplyId"
            row-key="originApplyId"
            row-label="creditNo"
            input-key="creditNo"
            auto-init
            @change="creditNoChange"
            @clear="clearCreditNo"
          >
            <f-magnifier-column prop="creditNo" :label="t('credit.creditmanage.creditapproved.creditNo')" />
            <f-magnifier-column
              prop="clientClass"
              :filter-input="false"
              :formatter="{ name: 'const', const: 'credit.ClientClass' }"
              :label="t('credit.creditmanage.creditapproved.clientClass')"
            />
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapproved.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapproved.clientName')" />
            <f-magnifier-column
              prop="targetCurrencyName"
              :label="t('credit.creditmanage.creditapproved.targetCurrencyId')"
            />
            <f-magnifier-column
              prop="creditQuota"
              formatter="amount"
              :label="t('credit.creditmanage.creditapproved.creditQuota')"
            />
            <f-magnifier-column prop="effectiveDate" :label="t('credit.creditmanage.creditapproved.startDate')" />
            <f-magnifier-column prop="endDate" :label="t('credit.creditmanage.creditapproved.endDate')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.targetCurrencyId')"
          prop="targetCurrencyId"
          :required="true"
        >
          <f-select
            ref="currencyRef"
            v-model="basicInfo.targetCurrencyId"
            value-key="currencyId"
            label="currencyName"
            :url="getCurrencyInfo"
            method="post"
            @change="currencyChange"
            auto-select
            disabled
          />
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapproved.clientClass')" prop="clientClass" :required="true">
          <f-select
            v-model="basicInfo.clientClass"
            :data="clientClass.pickConst([clientClass.INTER_CLIENT, clientClass.OUT_OF_CLIENT])"
            @change="clientClassChange"
            disabled
          />
        </f-form-item>
        <!--客户相关信息-->
        <f-form-item :label="t('credit.creditmanage.creditapproved.clientCode')" prop="clientId" :required="true">
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapproved.clientCodeMagnifier')"
            :url="getClientInfo"
            method="post"
            v-model="basicInfo.clientId"
            row-key="clientId"
            row-label="clientCode"
            input-key="codeOrName"
            auto-init
            :params="{
              clientClass: paramsClientType,
              isBlack: 1
              // officeId: basicInfo.officeId
            }"
            @change="clientCodeChange"
            @clear="clearClientChange"
            disabled
          >
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapproved.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapproved.clientName')" />
            <f-magnifier-column
              prop="clientClass"
              :filter-input="false"
              :label="t('credit.creditmanage.creditapproved.clientClass')"
            >
              <template #default="{ row }">
                {{
                  row.clientClass === clientTypeClass.MEMBER
                    ? t("credit.creditmanage.creditapproved.innerClient")
                    : t("credit.creditmanage.creditapproved.outerClient")
                }}</template
              ></f-magnifier-column
            >
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapproved.clientName')" prop="clientName">
          <f-input v-model="basicInfo.clientName" disabled
        /></f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapproved.enterpriseScale')" prop="enterpriseScale">
          <f-select v-model="basicInfo.enterpriseScale" :data="enterpriseScale" disabled />
        </f-form-item>
        <!-- 是否项目授信 -->
        <f-form-item :label="t('credit.creditmanage.creditapproved.isProjectCredit')">
          <f-switch
            v-model="basicInfo.projectCredit"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
            disabled
          />
        </f-form-item>
        <!-- 项目授信放大镜 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.projectName')"
          :required="basicInfo.projectCredit === yesOrNo.YES"
          prop="projectName"
        >
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapproved.projectInfoMagnifier')"
            :url="projectMagnifier"
            method="post"
            v-model="basicInfo.projectId"
            row-key="id"
            row-label="projectName"
            input-key="projectName"
            auto-init
            :params="{
              clientId: basicInfo.clientId
            }"
            @change="projectChange"
            @clear="clearProject"
            disabled
          >
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapproved.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapproved.clientName')" />
            <f-magnifier-column prop="projectName" :label="t('credit.creditmanage.creditapproved.projectName')" />
          </f-magnifier-single>
        </f-form-item>
        <!--信用评级相关信息-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditRatingCode')"
          :required="state.businessRule.genCreditRatingFlag === yesOrNo.YES && basicInfo.originApplyId <= 0"
          prop="businessCode"
        >
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapproved.creditRatingCodeMagnifier')"
            :url="getCreditRatingCode"
            method="post"
            v-model="basicInfo.creditRatingCode"
            row-key="businessCode"
            row-label="businessCode"
            input-key="businessCode"
            auto-init
            disabled
          >
            <f-magnifier-column
              prop="businessCode"
              :label="t('credit.creditmanage.creditapproved.creditRatingCodeColumn')"
            />
            <f-magnifier-column
              prop="clientType"
              :filter-input="false"
              :formatter="{ name: 'const', const: 'credit.ClientClass' }"
              :label="t('credit.creditmanage.creditapproved.clientClass')"
            />
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapproved.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapproved.clientName')" />
            <f-magnifier-column prop="approvedLevel" :label="t('credit.creditmanage.creditapproved.creditGradeName')" />
            <f-magnifier-column
              prop="initialScore"
              :label="t('credit.creditmanage.creditapproved.creditRatingScoreMod')"
            />
            <f-magnifier-column
              prop="effectiveDateRange"
              :filter-input="false"
              :label="t('credit.creditmanage.creditapproved.effectiveDateRange')"
            >
              <template #default="{ row }"> {{ row.ratingEffectiveDate + "-" + row.ratingExpirationDate }}</template>
            </f-magnifier-column>
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapproved.industry')" prop="industryCategory">
          <f-input v-model="basicInfo.industryCategory" disabled />
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapproved.creditGradeName')" prop="creditGradeName">
          <f-input v-model="basicInfo.creditGradeName" disabled
        /></f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapproved.creditRatingScore')" prop="creditRatingScore">
          <f-input v-model="basicInfo.creditRatingScore" disabled
        /></f-form-item>
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditRatingEffectiveDate')"
          prop="creditRatingEffectiveDate"
        >
          <f-input v-model="basicInfo.creditRatingEffectiveDate" disabled
        /></f-form-item>
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditRatingInvalidDate')"
          prop="creditRatingInvalidDate"
        >
          <f-input v-model="basicInfo.creditRatingInvalidDate" disabled
        /></f-form-item>
        <!--组织架构信息-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditOrgStructureCode')"
          prop="creditOrgStructureCode"
          v-show="basicInfo.creditStyle === creditStyleType.GROUP_CREDIT"
          :required="basicInfo.creditStyle === creditStyleType.GROUP_CREDIT"
        >
          <div style="display: flex; width: 100%">
            <f-magnifier-single
              :title="t('credit.creditmanage.creditapproved.creditOrgStructureCodeMagnifier')"
              :url="getcreditOrgStructureInfo"
              method="post"
              v-model="basicInfo.creditOrgStructureId"
              row-key="id"
              row-label="orgStructureName"
              input-key="codeOrName"
              auto-init
              :params="{
                clientType: basicInfo.clientClass
                // officeId: basicInfo.officeId
              }"
              @change="creditOrgStructureChange"
              @clear="clearCreditOrgStructure"
              class="magnifierStyle"
              :disabled="basicInfo.originApplyId > 0"
            >
              <f-magnifier-column
                prop="orgStructureCode"
                :label="t('credit.creditmanage.creditapproved.orgStructureCode')"
              />
              <f-magnifier-column
                prop="orgStructureName"
                :label="t('credit.creditmanage.creditapproved.orgStructureName')"
              />
            </f-magnifier-single>
            <div class="buttonStyle">
              <f-button type="primary" :disabled="basicInfo.creditOrgStructureId <= 0" @click="openClientDialog">{{
                t("credit.creditmanage.creditapproved.clientInfoDetail")
              }}</f-button>
            </div>
          </div>
        </f-form-item>
        <!--综合授信品种额度控制方式-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.composeVarietyControlMethod')"
          prop="composeVarietyControlMethod"
          :required="true"
        >
          <f-select
            v-model="basicInfo.composeVarietyControlMethod"
            :data="composeVarietyControlMethodType"
            @change="composeVarietyControlMethodChange"
            :disabled="basicInfo.originApplyId > 0"
          />
        </f-form-item>
        <!--集团授信单位额度控制方式-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.groupClientControlMethod')"
          prop="groupClientControlMethod"
          v-show="
            basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
            basicInfo.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE
          "
          :required="
            basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
            basicInfo.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE
          "
        >
          <f-select
            v-model="basicInfo.groupClientControlMethod"
            :data="groupClientControlMethodType"
            @change="groupClientControlMethodChange"
            :disabled="basicInfo.originApplyId > 0"
          />
        </f-form-item>
      </f-panel>
      <!-- 授信测算 -->
      <f-panel :title="t('credit.creditmanage.creditapproved.creditCalculation')">
        <!-- 信用等级 -->
        <f-form-item :label="t('credit.creditmanage.creditapproved.creditGradeName')" prop="creditGradeName">
          <f-input v-model="basicInfo.creditGradeName" disabled />
        </f-form-item>
        <!-- 信用调节系数(V) -->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditAdjustmentFactor')"
          prop="creditAdjustmentFactor"
        >
          <f-input v-model="basicInfo.creditAdjustmentFactor" disabled />
        </f-form-item>
        <!-- 净资产（E) -->
        <f-form-item :label="t('credit.creditmanage.creditapproved.netAssets')" prop="netAssets">
          <f-amount v-model="basicInfo.netAssets" :symbol="currencySymbol" disabled tooltip />
        </f-form-item>
        <!-- 所属行业 -->
        <f-form-item :label="t('credit.creditmanage.creditapproved.industry')" prop="industryCategory">
          <f-input v-model="basicInfo.industryCategory" disabled />
        </f-form-item>
        <!-- 行业信贷比例（D） -->
        <f-form-item :label="t('credit.creditmanage.creditapproved.industryCreditRatio')" prop="industryCreditRatio">
          <f-number v-model="basicInfo.industryCreditRatio" :precision="4" :min="0.0" :max="100" is-rate disabled />
        </f-form-item>
        <!-- 目标杠杆比率（K） -->
        <f-form-item :label="t('credit.creditmanage.creditapproved.targetLeverageRatio')" prop="targetLeverageRatio">
          <f-number v-model="basicInfo.targetLeverageRatio" :precision="4" :min="0.0" :max="100" is-rate disabled />
        </f-form-item>
        <!-- 参考值计算公式 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.referenceValueFormula')"
          prop="referenceValueFormula"
        >
          <f-input v-model="basicInfo.referenceValueFormula" disabled />
        </f-form-item>
        <!-- 授信参考值结果 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditReferenceValueResult')"
          prop="creditReferenceValueResult"
        >
          <f-input v-model="basicInfo.creditReferenceValueResult" disabled />
        </f-form-item>
        <!-- 申请额度 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.applyQuota')"
          prop="applyQuota"
          v-if="basicInfo.creditDataSource === creditDataSource.FUND_MANAGEMENT_PLATFORM"
        >
          <f-amount v-model="basicInfo.applyQuota" :symbol="currencySymbol" disabled tooltip />
        </f-form-item>
        <!--申请期限-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.applyTerm')"
          prop="creditTermMonth"
          :required="true"
          v-if="basicInfo.creditDataSource === creditDataSource.FUND_MANAGEMENT_PLATFORM"
        >
          <f-number v-model="basicInfo.creditTermMonth" disabled />
        </f-form-item>
      </f-panel>
      <!-- 授信明细 -->
      <f-panel :title="t('credit.creditmanage.creditapproved.creditDetail')" id="form3" position="4">
        <f-table-edit
          ref="creditDetailGrid"
          row-key="_randomId"
          :data="creditDetailList"
          style="width: 100%"
          border
          @add-row="addCreditDetailRow"
          :before-close-edit="beforeCreditDetailClose"
          :before-delete="beforeCreditDetailDelete"
        >
          <template #top-operate>
            <f-button
              type="info"
              @click.prevent="openCarryover"
              :disabled="creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory"
              >{{ t("credit.creditmanage.creditapproved.carryover") }}</f-button
            >
          </template>
          <f-table-column
            :formatter="item.formatter"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :align="item.align"
            v-for="(item, key) in detailState.columns"
          >
            <!-- 准入控制方式 -->
            <template #edit="scope">
              <template v-if="item.prop === 'creditControlType'">
                <f-select v-model="scope.row[item.prop]" :data="creditControlType" />
              </template>
              <template v-else-if="item.prop === 'creditType'">
                <f-select
                  v-model="scope.row[item.prop]"
                  :data="creditType"
                  @change="
                    () => {
                      scope.row.creditVarietyCodes = '';
                      scope.row.creditVarietyList = '';
                      scope.row.creditVarietyCode = '';
                    }
                  "
                  :disabled="basicInfo.originApplyId > 0 && creditTypeDisable"
                />
              </template>
              <template v-else-if="item.prop === 'creditVarietyNames'">
                <f-select
                  v-model="scope.row.creditVarietyCode"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  :url="getCreditVarietyInfo"
                  method="post"
                  v-if="scope.row.creditType === creditType.SPECIAL_CREDIT"
                  @change="
                    (data, row) => {
                      scope.row.creditVarietyCodes = [row.creditVarietyCode];
                      scope.row.creditVarietyList = [row];
                      scope.row.creditVarietyCode = row.creditVarietyCode;
                      scope.row.creditVarietyNames = row.creditVarietyName;
                    }
                  "
                />
                <f-select
                  v-model="scope.row.creditVarietyCodes"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  :url="getCreditVarietyInfo"
                  method="post"
                  v-if="scope.row.creditType !== creditType.SPECIAL_CREDIT"
                  @change="
                    (codes, rows) => {
                      scope.row.creditVarietyCodes = codes;
                      scope.row.creditVarietyList = rows;
                      scope.row.creditVarietyCode = codes.join(',');
                      scope.row.creditVarietyNames = rows.map(x => x.creditVarietyName).join(',');
                    }
                  "
                  filterable
                  multiple
                  collapse-tags
                  select-all
                  init-if-blank
                  auto-select
                />
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                <f-select v-model="scope.row[item.prop]" :data="creditUseType" />
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <f-amount v-model="scope.row[item.prop]" :negative="false" />
              </template>
              <template v-else-if="item.prop === 'guaranteeRatio'">
                <f-number v-model="scope.row.guaranteeRatio" :precision="2" max="100" min="0" />
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
            <template #default="scope">
              <template v-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditLimitType'">
                {{ creditLimitType.valueToLabel(scope.row.creditLimitType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <span class="span-amount">
                  {{ format(scope.row[item.prop]) }}
                </span>
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </f-table-column>
        </f-table-edit>
      </f-panel>
      <!-- 授信品种明细 -->
      <f-panel
        :title="t('credit.creditmanage.creditapproved.creditVarietyDetail')"
        id="form9"
        v-if="
          basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL &&
          creditDetailmagnifierData.length > 0
        "
        position="5"
      >
        <f-table-edit
          ref="creditVarietyDetailGrid"
          row-key="_randomId"
          :data="creditVarietyDetailList"
          style="width: 100%"
          border
          @add-row="addCreditVarietyDetailRow"
          :before-open-edit="beforeCreditVarietyDetailOpen"
          :before-close-edit="beforeCreditVarietyDetailClose"
          :before-delete="beforeCreditVarietyDetailDelete"
        >
          <f-table-column
            v-for="(item, key) in detailState.varietyDetailcolumns"
            :formatter="item.formatter"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :align="item.align"
          >
            <!-- 准入控制方式 -->
            <template #edit="scope">
              <template v-if="item.prop === 'refDetailSeqNo'">
                <f-magnifier-single
                  ref="refDetailSeqNoMagnifier"
                  :title="t('credit.creditmanage.creditapproved.refDetailSeqNoMagnifier')"
                  :table-data="{ data: creditDetailmagnifierData, total: creditDetailmagnifierData.length }"
                  v-model="scope.row.refDetailSeqNo"
                  row-key="seqNo"
                  row-label="seqNo"
                  :pagination="false"
                  @change="
                    info => {
                      scope.row.refDetailSeqNo = info.seqNo;
                      // scope.row.creditVarietyCodes = info.creditVarietyCodes;
                      scope.row.creditVarietyList = info.creditVarietyList;
                      scope.row.creditVarietyCode = info.creditVarietyCode;
                      // scope.row.creditVarietyNames = info.creditVarietyNames;
                      scope.row.creditType = info.creditType;
                      scope.row.creditControlType = info.creditControlType;
                      scope.row.creditUseType = info.creditUseType;
                      scope.row.guaranteeRatio = info.guaranteeRatio;
                      grtCreditVarietyList(info.seqNo);
                    }
                  "
                  @clear="
                    () => {
                      scope.row.refDetailSeqNo = '';
                      scope.row.creditVarietyCodes = '';
                      scope.row.creditVarietyList = '';
                      scope.row.creditVarietyCode = '';
                      scope.row.creditType = '';
                      scope.row.creditControlType = '';
                      scope.row.creditUseType = '';
                      scope.row.guaranteeRatio = '';
                    }
                  "
                  v-if="scope.row.addDetailFlag === yesOrNo.YES"
                >
                  <f-magnifier-column
                    prop="seqNo"
                    :filter-input="false"
                    :label="t('credit.creditmanage.creditapproved.seqNo')"
                  />
                </f-magnifier-single>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
              <template v-else-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template v-else-if="item.prop === 'creditVarietyNames'">
                <f-select
                  v-model="scope.row.creditVarietyCodes"
                  :data="selectVarietyData"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  blank-option
                  filterable
                  multiple
                  collapse-tags
                  init-if-blank
                  @change="
                    (codes, rows) => {
                      scope.row.creditVarietyCodes = codes;
                      scope.row.creditVarietyList = rows;
                      scope.row.creditVarietyCode = codes.join(',');
                      scope.row.creditVarietyNames = rows.map(x => x.creditVarietyName).join(',');
                    }
                  "
                />
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <f-amount v-model="scope.row[item.prop]" :negative="false" />
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
            <template #default="scope">
              <template v-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <span class="span-amount">
                  {{ format(scope.row[item.prop]) }}
                </span>
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </f-table-column>
        </f-table-edit>
      </f-panel>
      <!-- 授信明细 -->
      <f-panel :title="t('credit.creditmanage.creditapproved.creditInfo')" id="form2" position="3">
        <!--授信额度-->
        <f-form-item :label="t('credit.creditmanage.creditapproved.creditQuota')" prop="creditQuota">
          <f-amount v-model="basicInfo.creditQuota" disabled
        /></f-form-item>
        <!--结转金额-->
        <f-form-item :label="t('credit.creditmanage.creditapproved.carryoverAmount')" prop="carryoverAmount">
          <f-amount v-model="basicInfo.carryoverAmount" disabled
        /></f-form-item>
        <!--授信生效日-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.startDate')"
          prop="effectiveDate"
          :required="effectiveDateActiveRuleType.CUSTOM === state.businessRule.addEffectiveDateCreateType"
        >
          <f-date-picker
            v-model="basicInfo.effectiveDate"
            type="date"
            @change="getCreditTerm"
            :disabled="effectiveDateActiveRuleType.CUSTOM !== state.businessRule.addEffectiveDateCreateType"
          />
        </f-form-item>
        <!--授信结束日-->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.endDate')"
          prop="endDate"
          :required="effectiveDateActiveRuleType.CUSTOM === state.businessRule.addEffectiveDateCreateType"
        >
          <f-date-picker
            v-model="basicInfo.endDate"
            type="date"
            @change="getCreditTerm"
            :disabled="effectiveDateActiveRuleType.CUSTOM !== state.businessRule.addEffectiveDateCreateType"
          />
        </f-form-item>
        <!-- 授信期限(天) -->
        <f-form-item
          :label="t('credit.creditmanage.creditapproved.creditTermDay')"
          prop="creditTermDay"
          :required="true"
        >
          <f-number v-model="basicInfo.creditTermDay" disabled />
        </f-form-item>
        <!--备注-->
        <f-form-item :label="t('credit.creditmanage.creditapproved.remarks')" :employ="2" prop="remarks">
          <f-textarea v-model="basicInfo.remarks" :max-rows="4" :min-rows="1" :maxlength="200" />
        </f-form-item>
      </f-panel>
      <!-- 下级客户授信明细 -->
      <f-panel
        :title="t('credit.creditmanage.creditapproved.subClientCreditDetail')"
        id="form5"
        v-if="
          basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
          basicInfo.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
          basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL &&
          basicInfo.creditOrgStructureId > 0
        "
        position="6"
      >
        <f-table-edit
          ref="subCreditClientGrid"
          row-key="_randomId"
          :data="subCreditClientList"
          style="width: 100%"
          border
          @add-row="addSubCreditRow"
          :before-close-edit="beforeSubCreditClose"
          :before-open-edit="beforeSubCreditOpen"
          @select="handleSubCreditSelect"
          @select-all="handleSubCreditSelect"
          @clear-selection="clearSubCreditSelection"
        >
          <template #top-operate>
            <f-form-item :label="t('credit.creditmanage.creditapproved.groupCreditNo')">
              <f-magnifier-single
                ref="refDetailSeqNoMagnifier"
                :title="t('credit.creditmanage.creditapproved.refSubClientDetailSeqNoMagnifier')"
                :table-data="{ data: creditClientmagnifierData, total: creditClientmagnifierData.length }"
                v-model="state.subClientDetail.refDetailSeqNo"
                row-key="seqNo"
                row-label="seqNo"
                :pagination="false"
                @change="
                  info => {
                    state.subClientDetail.refDetailSeqNo = info.seqNo;
                    state.subClientDetail.creditVarietyCodes = info.creditVarietyCodes;
                    state.subClientDetail.creditVarietyList = info.creditVarietyList;
                    state.subClientDetail.creditVarietyCode = info.creditVarietyCode;
                    state.subClientDetail.creditVarietyNames = info.creditVarietyNames;
                    state.subClientDetail.creditType = info.creditType;
                    state.subClientDetail.creditControlType = info.creditControlType;
                    state.subClientDetail.creditUseType = info.creditUseType;
                  }
                "
                @clear="
                  () => {
                    scope.row.refDetailSeqNo = '';
                    scope.row.creditVarietyCodes = '';
                    scope.row.creditVarietyList = '';
                    scope.row.creditVarietyCode = '';
                    scope.row.creditType = '';
                    scope.row.creditControlType = '';
                    scope.row.creditUseType = '';
                    scope.row.guaranteeRatio = '';
                  }
                "
              >
                <f-magnifier-column
                  prop="seqNo"
                  :filter-input="false"
                  :label="t('credit.creditmanage.creditapproved.seqNo')"
                />
              </f-magnifier-single>
            </f-form-item>
            <f-form-item
              :label="t('credit.creditmanage.creditapproved.amount')"
              v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.TOTAL_LIMIT"
            >
              <f-amount v-model="state.subClientDetail.quota" tooltip :negative="false" />
            </f-form-item>
            <f-form-item
              :label="t('credit.creditmanage.creditapproved.creditQuotaTd')"
              v-if="
                basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD ||
                basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT
              "
            >
              <f-amount v-model="state.subClientDetail.creditQuota" tooltip :negative="false" />
            </f-form-item>
            <f-form-item
              :label="t('credit.creditmanage.creditapproved.assureQuota')"
              v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD"
            >
              <f-amount v-model="state.subClientDetail.assureQuota" tooltip :negative="false" />
            </f-form-item>
            <f-form-item
              :label="t('credit.creditmanage.creditapproved.pledgeQuota')"
              v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD"
            >
              <f-amount v-model="state.subClientDetail.pledgeQuota" tooltip :negative="false" />
            </f-form-item>
            <f-form-item
              :label="t('credit.creditmanage.creditapproved.impawnQuota')"
              v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD"
            >
              <f-amount v-model="state.subClientDetail.impawnQuota" tooltip :negative="false" />
            </f-form-item>
            <f-form-item
              :label="t('credit.creditmanage.creditapproved.noCreditQuota')"
              v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT"
            >
              <f-amount v-model="state.subClientDetail.noCreditQuota" tooltip :negative="false" />
            </f-form-item>
            <f-button
              type="info"
              @click.prevent="sharedQuota"
              :disabled="state.subClientDetail.refDetailSeqNo === ''"
              >{{ t("credit.creditmanage.creditapproved.sharedQuota") }}</f-button
            >
            <f-button
              type="info"
              @click.prevent="openSubClientCarryover"
              :disabled="creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory"
              >{{ t("credit.creditmanage.creditapproved.carryover") }}</f-button
            >
          </template>
          <f-table-column type="selection" width="55" />
          <f-table-column
            v-for="(item, key) in detailState.subClientColumns"
            :formatter="item.formatter"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :align="item.align"
          >
            <!-- 准入控制方式 -->
            <template #edit="scope">
              <template v-if="item.prop === 'refDetailSeqNo'">
                <f-magnifier-single
                  ref="refDetailSeqNoMagnifier"
                  :title="t('credit.creditmanage.creditapproved.refSubClientDetailSeqNoMagnifier')"
                  :table-data="{ data: creditClientmagnifierData, total: creditClientmagnifierData.length }"
                  v-model="scope.row.refDetailSeqNo"
                  row-key="seqNo"
                  row-label="seqNo"
                  :pagination="false"
                  @change="
                    info => {
                      scope.row.refDetailSeqNo = info.seqNo;
                      scope.row.creditVarietyCodes = info.creditVarietyCodes;
                      scope.row.creditVarietyList = info.creditVarietyList;
                      scope.row.creditVarietyCode = info.creditVarietyCode;
                      scope.row.creditVarietyNames = info.creditVarietyNames;
                      scope.row.creditType = info.creditType;
                      scope.row.creditControlType = info.creditControlType;
                      scope.row.creditUseType = info.creditUseType;
                      scope.row.guaranteeRatio = info.guaranteeRatio;
                      grtCreditClientList(info.seqNo);
                    }
                  "
                  @clear="
                    () => {
                      scope.row.refDetailSeqNo = '';
                      scope.row.creditVarietyCodes = '';
                      scope.row.creditVarietyList = '';
                      scope.row.creditVarietyCode = '';
                      scope.row.creditType = '';
                      scope.row.creditControlType = '';
                      scope.row.creditUseType = '';
                      scope.row.guaranteeRatio = '';
                    }
                  "
                  v-if="scope.row.addDetailFlag === yesOrNo.YES"
                >
                  <f-magnifier-column
                    prop="seqNo"
                    :filter-input="false"
                    :label="t('credit.creditmanage.creditapproved.seqNo')"
                  />
                </f-magnifier-single>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
              <template v-else-if="item.prop === 'clientCode'">
                <f-magnifier-single
                  ref="clientCodeMagnifier"
                  :title="t('credit.creditmanage.creditapproved.clientCodeMagnifier')"
                  :url="getStructureClientInfo"
                  method="post"
                  v-model="scope.row.clientId"
                  row-key="clientId"
                  row-label="clientCode"
                  input-key="codeOrName"
                  auto-init
                  :params="{
                    clientType: basicInfo.clientClass,
                    orgStructureId: basicInfo.creditOrgStructureId,
                    isBlack: 1
                  }"
                  @change="
                    info => {
                      scope.row.clientId = info.clientId;
                      scope.row.clientCode = info.clientCode;
                      scope.row.clientName = info.clientName;
                    }
                  "
                  @clear="
                    () => {
                      scope.row.clientId = '';
                      scope.row.clientCode = '';
                      scope.row.clientName = '';
                    }
                  "
                  v-if="scope.row.addDetailFlag === yesOrNo.YES"
                >
                  <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapproved.clientCode')" />
                  <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapproved.clientName')" />
                  <f-magnifier-column
                    prop="clientClass"
                    :filter-input="false"
                    :formatter="{ name: 'const', const: 'credit.ClientClass' }"
                    :label="t('credit.creditmanage.creditapproved.clientClass')"
                  />
                </f-magnifier-single>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
              <template v-else-if="item.prop === 'creditVarietyNames'">
                <template
                  v-if="
                    scope.row.creditType !== creditType.COMPOSITE_CREDIT ||
                    basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL
                  "
                >
                  {{ scope.row[item.prop] }}
                </template>
                <f-select
                  v-model="scope.row.creditVarietyCodes"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  :data="selectClientData"
                  @change="
                    (codes, rows) => {
                      scope.row.creditVarietyCodes = codes;
                      scope.row.creditVarietyList = rows;
                      scope.row.creditVarietyCode = codes.join(',');
                      scope.row.creditVarietyNames = rows.map(x => x.creditVarietyName).join(',');
                    }
                  "
                  filterable
                  multiple
                  collapse-tags
                  select-all
                  init-if-blank
                  v-else
                />
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <f-amount v-model="scope.row[item.prop]" />
              </template>
              <template v-else-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
            <template #default="scope">
              <template v-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <span class="span-amount">
                  {{ format(scope.row[item.prop]) }}
                </span>
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </f-table-column>
          <template #operate="scope">
            <OperateButton
              :options="generalSubClientButtonOption(scope)"
              @on-edit="beforeSubCreditOpen(scope)"
              @on-clone="subClientClone(scope)"
              @on-close="beforeSubCreditClose(scope)"
              @on-delete="subClientDelete(scope)"
            />
          </template>
        </f-table-edit>
      </f-panel>
      <f-panel :title="t('credit.creditmanage.creditapproved.fileinfo')" id="form6" position="8">
        <f-form-item :employ="3">
          <f-attm-upload
            ref="upload"
            :beforeDelete="fileBeforeDelete"
            :beforeBatchDelete="beforeBatchDelete"
            drag
            v-model="basicInfo.fileIdArr"
            multiple
          />
        </f-form-item>
      </f-panel>
      <f-dialog
        v-model="state.carryoverInfo.showDialog"
        :before-close="
          () => {
            state.carryoverInfo.showDialog = false;
          }
        "
        :title="t('credit.creditmanage.creditapproved.carryoverDetail')"
        style="width: 80%"
      >
        <f-panel :title="t('credit.creditmanage.creditapproved.carryoverDetailInfo')" id="form6" :asAnchor="false">
          <f-table-edit
            ref="carryoverGrid"
            row-key="_randomId"
            :data="carryoverList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.carryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            />
            <template #operate="scope">
              <OperateButton :options="generalButtonOption(scope)" @on-remove="deleteCarryoverButton(scope)" />
            </template>
          </f-table-edit>
        </f-panel>
        <f-panel :title="t('credit.creditmanage.creditapproved.notCarryoverDetailInfo')" id="form7" :asAnchor="false">
          <f-table-edit
            ref="notCarryoverGrid"
            row-key="_randomId"
            :data="notCarryoverList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.notCarryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            >
              <template #edit="scope">
                <template v-if="item.prop === 'targetDetailSeqNo'">
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapproved.refDetailSeqNoMagnifier')"
                    :table-data="{ data: creditDetailList, total: creditDetailList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.TOTAL_LIMIT"
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapproved.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapproved.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapproved.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="quota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.quota')"
                    />
                    <f-magnifier-column
                      prop="guaranteeRatio"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.guaranteeRatio')"
                    />
                  </f-magnifier-single>
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapproved.refDetailSeqNoMagnifier')"
                    :table-data="{ data: creditDetailList, total: creditDetailList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-else-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT"
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapproved.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapproved.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapproved.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="creditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.creditQuota')"
                    />
                    <f-magnifier-column
                      prop="noCreditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.noCreditQuota')"
                    />
                  </f-magnifier-single>
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapproved.refDetailSeqNoMagnifier')"
                    :table-data="{ data: creditDetailList, total: creditDetailList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-else
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapproved.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapproved.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapproved.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="creditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.creditQuota')"
                    />
                    <f-magnifier-column
                      prop="assureQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.assureQuota')"
                    />
                    <f-magnifier-column
                      prop="pledgeQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.pledgeQuota')"
                    />
                    <f-magnifier-column
                      prop="impawnQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapproved.impawnQuota')"
                    />
                  </f-magnifier-single>
                </template>
                <template v-else-if="item.prop === 'originCreditStyle'">
                  {{ creditStyleType.valueToLabel(scope.row.originCreditStyle) }}
                </template>
                <template v-else-if="item.prop === 'originCreditType'">
                  {{ creditType.valueToLabel(scope.row.originCreditType) }}
                </template>
                <template v-else-if="item.prop === 'originCreditTerm'">
                  {{ monthType.valueToLabel(scope.row.originCreditTerm) }}
                </template>
                <template v-else-if="['originCarryoverAmount', 'targetCarryoverAmount'].includes(item.prop)">
                  <span class="span-amount">
                    {{ format(scope.row[item.prop]) }}
                  </span>
                </template>
                <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                  {{ format(scope.row[item.prop]) }}
                </template>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
            </f-table-column>
            <template #operate="scope">
              <OperateButton
                :options="generalNotCurryoverButtonOption(scope)"
                @on-edit="openCarryoverEditButton(scope)"
                @on-carryover="carryoverButton(scope)"
                @on-close="closeCarryoverEditButton(scope)"
              />
            </template>
          </f-table-edit>
        </f-panel>
        <template #footer>
          <f-button type="info" plain @click="state.carryoverInfo.showDialog = false">{{
            t("credit.creditmanage.creditapproved.close")
          }}</f-button>
        </template>
      </f-dialog>
      <f-dialog
        v-model="state.carryoverInfo.showSubClientDialog"
        :before-close="
          () => {
            state.carryoverInfo.showSubClientDialog = false;
          }
        "
        :title="t('credit.creditmanage.creditapproved.carryoverDetail')"
        style="width: 80%"
      >
        <f-panel :title="t('credit.creditmanage.creditapproved.carryoverDetailInfo')" id="form6" :asAnchor="false">
          <f-table-edit
            ref="carryoverSubClientDetailGrid"
            row-key="_randomId"
            :data="carryoverSubClientDetailDtoList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.carryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            />
            <template #operate="scope">
              <OperateButton
                :options="generalCarryoverSubClientButtonOption(scope)"
                @on-remove="deleteSubClientCarryoverButton(scope)"
              />
            </template>
          </f-table-edit>
        </f-panel>
        <f-panel :title="t('credit.creditmanage.creditapproved.notCarryoverDetailInfo')" id="form7" :asAnchor="false">
          <f-table-edit
            ref="unCarryoverSubClientDetailGrid"
            row-key="_randomId"
            :data="unCarryoverSubClientDetailDtoList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.notCarryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            >
              <template #edit="scope">
                <template v-if="item.prop === 'targetDetailSeqNo'">
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapproved.refDetailSeqNoMagnifier')"
                    :table-data="{ data: subCreditClientList, total: subCreditClientList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapproved.seqNo')"
                    />
                  </f-magnifier-single>
                </template>
                <template v-else-if="item.prop === 'originCreditStyle'">
                  {{ creditStyleType.valueToLabel(scope.row.originCreditStyle) }}
                </template>
                <template v-else-if="item.prop === 'originCreditType'">
                  {{ creditType.valueToLabel(scope.row.originCreditType) }}
                </template>
                <template v-else-if="item.prop === 'originCreditTerm'">
                  {{ monthType.valueToLabel(scope.row.originCreditTerm) }}
                </template>
                <template v-else-if="['originCarryoverAmount', 'targetCarryoverAmount'].includes(item.prop)">
                  <span class="span-amount">
                    {{ format(scope.row[item.prop]) }}
                  </span>
                </template>
                <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                  {{ format(scope.row[item.prop]) }}
                </template>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
            </f-table-column>
            <template #operate="scope">
              <OperateButton
                :options="generalUnCarryoverSubClientButtonOption(scope)"
                @on-edit="openSubClientCarryoverEditButton(scope)"
                @on-carryover="carryoveSubClientButton(scope)"
                @on-close="closeSubClientCarryoverEditButton(scope)"
              />
            </template>
          </f-table-edit>
        </f-panel>
        <template #footer>
          <f-button type="info" plain @click="state.carryoverInfo.showSubClientDialog = false">{{
            t("credit.creditmanage.creditapproved.close")
          }}</f-button>
        </template>
      </f-dialog>
      <OrgStructureDialog ref="orgStructureDialog" :id="basicInfo.creditOrgStructureId" />
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="getCreditInvestigateReportInfoUrl"
        :before-trigger="formValidator"
        :operate-name="t('credit.creditmanage.creditapproved.creditInvestigateReport')"
        @submit-success="reportDataSuccess"
        :showConfirm="false"
        :result-confirm="t('credit.creditmanage.creditapproved.reportDataSuccess')"
        :result-title="t('credit.creditmanage.creditapproved.creditInvestigateReport')"
        :show-result="false"
      />
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="temporaryUrl"
        type="info"
        :before-trigger="formTemporaryValidator"
        :confirm-text="t('credit.creditmanage.creditapproved.temporaryConfirm')"
        :batch-confirm-map="{
          success: t('credit.creditmanage.creditapproved.temporarySuccess'),
          fail: t('credit.creditmanage.creditapproved.temporaryFail')
        }"
        :operate-name="t('credit.creditmanage.creditapproved.temporaryTitle')"
        :result-confirm="t('credit.creditmanage.creditapproved.temporarySuccess')"
        :result-title="t('credit.creditmanage.creditapproved.temporaryTitle')"
        @close="saveDataSuccess"
        v-if="basicInfo.checkStatus === statusEnum.DRAFT || basicInfo.checkStatus === ''"
      />
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="save"
        operate="save"
        :confirm-text="saveMessage"
        :before-trigger="formValidator"
        @close="saveDataSuccess"
      />
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="submitUrl"
        operate="submit"
        :confirm-text="submitMessage"
        :before-trigger="formValidator"
        @close="submitDataSuccess"
      />
      <f-button type="info" @click.prevent="goBack">{{ t("credit.creditmanage.creditapproved.linkquery") }}</f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useCreditAppovedRules } from "../hooks/useCreditAppovedRules";
import { useCreditAppoved } from "../hooks/useCreditAppoved";
import useAdd from "../hooks/useAdd";
import OperateButton from "@/components/operate-button/operate-button";
import { useConst } from "@ifs/support";
import { onMounted, ref } from "vue";
import { format } from "@/utils/currency";
import {
  getCurrencyInfo,
  save,
  submitUrl,
  getCreditRatingCode,
  getcreditOrgStructureInfo,
  getCreditVarietyInfo,
  getClientInfo,
  temporaryUrl,
  getStructureClientInfo,
  projectMagnifier,
  getCreditInvestigateReportInfoUrl,
  getcreditNoUrl
} from "../url";
import { useCurrency } from "@/hooks";

import { usePage } from "../hooks/usePage";
const { pageParams } = usePage();
const { t } = useI18n();
//授信方式
const creditStyleType = useConst("credit.CreditStyleType");
//企业规模
const enterpriseScale = useConst("credit.EnterpriseScale");
//客户类别
const clientClass = useConst("credit.ClientClass");
//客户类别
const clientTypeClass = useConst("clientmanage.ClientClass");
//授信分类
const creditClassification = useConst("credit.CreditClassification");
//授信额度设置方式
const creditType = useConst("credit.CreditType");
//授信使用类型
const creditUseType = useConst("credit.CreditUseType");
//额度类型
const creditLimitType = useConst("credit.CreditLimitType");
//期限类型
const monthType = useConst("credit.MonthType");
//准入控制方式
const creditControlType = useConst("credit.CreditControlType");
//授信生效日规则
const effectiveDateActiveRuleType = useConst("credit.EffectiveDateActiveRuleType");
// //集团授信单位额度控制方式
const groupClientControlMethodType = useConst("credit.GroupClientControlMethodType");
//是否枚举
const yesOrNo = useConst("counter.YesOrNo");
//授信额度设置方式
const creditLimitSetMethodType = useConst("credit.CreditLimitSetMethodType");
// //综合授信品种额度控制方式
const composeVarietyControlMethodType = useConst("credit.ComposeVarietyControlMethodType");
//下级单位额度分配权限归属
const groupQuotaAssignTargetType = useConst("credit.GroupQuotaAssignTargetType");
//数据来源
const creditDataSource = useConst("credit.CreditDataSource");
//状态
const statusEnum = useConst("cashmanage.CheckStatus");
const {
  basicInfo,
  formValidator,
  form1,
  generalSaveInfo,
  upload,
  //下级客户可编辑表格
  subCreditClientGrid,
  subCreditClientList,
  addSubCreditRow,
  beforeSubCreditClose,
  handleSubCreditSelect,
  clearSubCreditSelection,
  generalSubClientButtonOption,
  subClientClone,
  subClientDelete,
  //授信明细可编辑表格
  beforeCreditDetailClose,
  addCreditDetailRow,
  creditDetailList,
  creditDetailGrid,
  //授信品种明细
  creditVarietyDetailGrid,
  creditVarietyDetailList,
  addCreditVarietyDetailRow,
  beforeCreditVarietyDetailClose,
  //变更触发事件
  openCarryover,
  //组织架构事件
  creditOrgStructureChange,
  clearCreditOrgStructure,
  //客户变更事件,
  clientCodeChange,
  clearClientChange,
  //客户类型变更事件
  clientClassChange,
  paramsClientType,
  //公共处理信息
  state,
  detailState,
  //共享额度
  sharedQuota,
  //结转处理
  //结转处理
  carryoverGrid,
  generalButtonOption,
  openCarryoverEditButton,
  closeCarryoverEditButton,
  deleteCarryoverButton,
  carryoverButton,
  carryoverList,
  notCarryoverGrid,
  generalNotCurryoverButtonOption,
  notCarryoverList,
  //初始化加载
  getOpenDate,
  getBusinessRuleSet,
  creditDetailmagnifierData,
  creditClientmagnifierData,
  beforeCreditVarietyDetailOpen,
  beforeSubCreditOpen,
  selectVarietyData,
  grtCreditVarietyList,
  beforeCreditVarietyDetailDelete,
  beforeCreditDetailDelete,
  selectClientData,
  grtCreditClientList,
  submitMessage,
  saveMessage,
  groupClientControlMethodChange,
  composeVarietyControlMethodChange,
  //查看客户信息
  openClientDialog,
  fileBeforeDelete,
  beforeBatchDelete,
  formTemporaryValidator,
  currencyChange,
  //下级客户操作
  openSubClientCarryover,
  //下级客户处理
  deleteSubClientCarryoverButton,
  carryoverSubClientDetailGrid,
  generalCarryoverSubClientButtonOption,
  carryoverSubClientDetailDtoList,
  //下级客户未结转
  unCarryoverSubClientDetailGrid,
  unCarryoverSubClientDetailDtoList,
  generalUnCarryoverSubClientButtonOption,
  openSubClientCarryoverEditButton,
  closeSubClientCarryoverEditButton,
  carryoveSubClientButton,
  orgStructureDialog,
  getCreditTerm,
  projectChange,
  clearProject,
  setBasicInfo,
  creditNoChange,
  clearCreditNo,
  creditTypeDisable
} = useCreditAppoved();
const { currencySymbol } = useCurrency(basicInfo);
const { goBack, goToReport } = useAdd(basicInfo, creditDetailList);
onMounted(async () => {
  getOpenDate();
  getBusinessRuleSet(false);
  getHeight();
  setBasicInfo(pageParams);
});
const contentHeight = ref<number | string>(0);
const getHeight = () => {
  // 内容区域高度
  contentHeight.value = `${document.body.clientHeight - 590}px`;
};
const submitDataSuccess = (res: any) => {
  if (res?.success) {
    //返回列表页面
    goBack();
  }
};
const reportDataSuccess = (res: any) => {
  if (res) {
    basicInfo.creditInvestigateReportInfo = res;
    //返回列表页面
    goToReport();
  }
};

//必填项校验
const { rules } = useCreditAppovedRules(basicInfo);
const saveDataSuccess = (res: any) => {
  if (res.success) {
    basicInfo.id = res.data.id;
    basicInfo.creditNo = res.data.creditNo;
    basicInfo.applyNo = res.data.applyNo;
    basicInfo.version = res.data.version;
    basicInfo.checkStatus = res.data.checkStatus;
    basicInfo.inputUserId = res.data.inputUserId;
    basicInfo.inputUserName = res.data.inputUserName;
    basicInfo.inputTime = res.data.inputTime;
  }
};
</script>
<style lang="scss" scoped>
.magnifierStyle {
  width: 70%;
}
.buttonStyle {
  width: 30%;
}
.span-amount {
  color: #ff9e00;
  text-align: right;
}
</style>
