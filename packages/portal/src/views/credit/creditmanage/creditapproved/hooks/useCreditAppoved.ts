import type { CreditApplyDto } from "../types";
import { reactive, ref, h, shallowRef, computed } from "vue";
import { useConst } from "@ifs/support";
import { random } from "@/utils/uuid";
import {
  getBusinessRuleSetUrl,
  openDateUrl,
  calculateEstimateQuotaUrl,
  getStructureClientInfo,
  calculateExposeCreditLimitUrl,
  getInfo,
  getCollateralTypeUrl
} from "../url";
import { useI18n } from "vue-i18n";
import { useCreditAppovedColumns } from "./useCreditAppovedColumns";
import httpTool from "@/utils/http";
import { formatDate } from "@/utils/date";
import { useCarryover, useCarryoverColumns } from "../../common/hooks/useCarryover";
import { FMessageBox } from "@dtg/frontend-plus";
import { DtgConfirm } from "@dtg/frontend-plus-icons";
import { FIcon } from "@dtg/frontend-plus";
import { useConversion } from "@/hooks/conversion";

export const useCreditAppoved = () => {
  const { t } = useI18n();
  //授信方式
  const creditStyleType = useConst("credit.CreditStyleType");
  //客户类别
  const clientClass = useConst("credit.ClientClass");
  //授信分类
  const creditClassification = useConst("credit.CreditClassification");
  //授信额度设置方式
  const creditType = useConst("credit.CreditType");
  //授信风险类型
  const creditRiskType = useConst("credit.CreditRiskType");
  //授信使用类型
  const creditUseType = useConst("credit.CreditUseType");
  //币种类型
  const currencyType = useConst("credit.CurrencyType");
  //期限类型
  const monthType = useConst("credit.MonthType");
  //是否枚举
  const yesOrNo = useConst("counter.YesOrNo");
  //风险类型
  const ratingType = useConst("creditrating.RatingType");
  //准入控制方式
  const creditControlType = useConst("credit.CreditControlType");
  //授信生效日规则
  const effectiveDateActiveRuleType = useConst("credit.EffectiveDateActiveRuleType");
  //集团授信单位额度控制方式
  const groupClientControlMethodType = useConst("credit.GroupClientControlMethodType");
  //授信额度设置方式
  const creditLimitSetMethodType = useConst("credit.CreditLimitSetMethodType");
  //综合授信品种额度控制方式
  const composeVarietyControlMethodType = useConst("credit.ComposeVarietyControlMethodType");
  //控制方式
  const genMaxLimitControlType = useConst("credit.GenMaxLimitControlType");
  //下级单位额度分配权限归属
  const groupQuotaAssignTargetType = useConst("credit.GroupQuotaAssignTargetType");
  // 授信期限
  const creditDataSource = useConst("credit.CreditDataSource");
  const {
    creditDetailColumns2,
    creditDetailColumns1,
    creditDetailColumns3,
    creditDetailColumns4,
    creditDetailColumns5,
    creditDetailColumns6,
    creditDetailColumns7,
    creditDetailColumns8,
    creditDetailColumns9
  } = useCreditAppovedColumns();
  const { convert } = useConversion([""]);
  const { notCarryoverDetailColumns, carryoverDetailColumns } = useCarryoverColumns();
  const basicInfo = reactive<CreditApplyDto>({
    id: -1, //主键
    businessType: "", //业务类型
    applyNo: "", //单据号
    creditNo: "", //授信编号
    creditVersion: "", //授信版本
    globalSerial: "", //全局流水号
    changeType: "", //变更类型;1：增加授信额度；2：延长授信期；3：增加授信品种
    originApplyId: "", //来源单据id
    originApplyNo: "", //来源单据号
    executeDate: "", //执行日
    checkStatus: "", //审批状态
    creditStyle: creditStyleType.SINGLE_LEGAL_CREDIT, //授信方式;1:单一法人授信；2：集团授信
    currencyType: currencyType.ONE_CURRENCY, //币种类型;1：单一币种；2：多币种
    targetCurrencyId: "", //总额度币种Id
    targetCurrencyCode: "CNY", //总额度币种编码
    targetCurrencyName: t("credit.creditmanage.creditapproved.currencyDefaultName"), //总额度币种名称
    clientClass: clientClass.INTER_CLIENT, //客户类型;1：内部客户；2：外部客户
    clientId: "", //客户id
    clientHisId: -1, //客户历史id
    clientCode: "", //客户编码
    clientName: "", //客户名称
    industryCategoryId: "", //行业类别id
    industryCategoryName: "", //行业类别名称
    industryCategoryCode: "", //行业类别编码
    enterpriseScale: "", //企业规模
    creditRatingId: "", //信用等级评定Id
    creditRatingCode: "", //信用等级评定code
    creditGradeId: "", //信用等级Id
    creditGradeName: "", //信用等级名称
    creditGradeCode: "", //信用等级编码
    creditRatingScore: "", //信用等级评估分值
    creditRatingRiskType: "", //信用等级评估风险类型;1:高风险；2：低风险
    creditRatingEffectiveDate: "", //信用等级评估生效日期
    creditRatingInvalidDate: "", //信用等级评估失效日期
    creditOrgStructureId: "", //授信组织架构Id
    creditOrgStructureCode: "", //授信组织架构编码
    creditOrgStructureName: "", //授信组织架构名称
    creditLimitSetMethod: "", //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    composeVarietyControlMethod: "", //综合授信品种额度控制方式;0:不控制；1：按总额控制；2：按品种控制
    groupClientControlMethod: composeVarietyControlMethodType.NOT_CONTROL, //集团授信单位额度控制方式;0:不控制；1：按总额控制；2：按单位控制
    creditCategory: creditClassification.CENTRALIZED_CREDIT, //授信分类;1:集中授信；2:临时授信
    creditRiskType: creditRiskType.HIGH_RISK, //授信风险类型;1:高风险；2：低风险
    creditEstimateQuota: "0.00", //授信测算风险限额
    creditQuota: "0.00", //授信额度
    carryoverAmount: "0.00", //结转金额
    exposeCreditLimit: "0.00", //敞口授信额度
    effectiveDateActiveRule: "", //授信生效日规则;1:授信核定审批通过日期;2:授信激活日期;3:自定义日期
    effectiveDate: "", //授信生效日
    endDate: "", //授信结束日
    creditTermMonth: monthType.TWELVE, //授信期限个月
    needResolutionFlag: "", //是否需要决议
    resolutionId: "", //决议ID
    resolutionCode: "", //决议编号
    resolutionDate: "", //决议日期
    resolutionMeetingDesc: "", //会议期次
    resolutionStatus: "", //决议状态;0：待决议；1：全票通过；2：非全票通过；11：不通过：
    resolutionResultDesc: "", //决议结果
    activeMethod: "", //激活方式;1:人工；2：自动
    activeDate: "", //激活日期
    stopReason: "", //终止原因
    alreadyUsed: "", //是否已被使用;用于过滤已经被使用的前置单据
    remarks: "", //备注
    previousDetailSeqNo: null,
    failedReason: "",
    fileRefId: "",
    version: 0, //乐观锁
    inputUserId: -1, //创建人ID
    inputUserName: "", //创建人名称
    inputTime: "", //创建时间
    modifyUserId: -1, //更新人ID
    modifyUserName: "", //更新人名称
    modifyTime: "", //更新时间
    transactionStatus: "", //分布式事务状态
    dataStatus: "", //数据状态
    tenantId: "", //租户标识
    subCreditClientList: [],
    guaranteeDetailList: [],
    creditDetailList: [],
    creditVarietyDetailList: [],
    fileIdArr: [],
    addApplyMandatoryFlag: "", //申请是否必须
    addEffectiveDateCreateType: "", //授信生效日规则
    changeApplyMandatoryFlag: "", //变更规则_授信变更申请是否必须
    genCreditAutoActivate: "", //授信是否自动激活
    genCreditLimitMethod: "", //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    genCreditRatingFlag: "", //通用规则-信用评级是否必须
    genMaxLimitControlType: "", //最高限额校验控制类型
    genMaxLimitValidPeriod: "", //最高额度有效期限类型
    groupQuotaAssignTarget: "", //下级单位额度分配权限归属
    projectCredit: "", // 是否项目
    projectId: null, // 项目ID
    projectCode: "", // 项目编号
    projectName: "", // 项目名称
    creditAdjustmentFactor: null, // 信用调节系数(V)
    netAssets: null, // 净资产(E)
    industryCategory: "", // 所属行业
    industryCreditRatio: null, // 行业信贷比例(D)
    targetLeverageRatio: null, // 目标杠杆比例(K)
    referenceValueFormula: "", // 参考值计算公式
    creditReferenceValueResult: null, // 授信参考值结果
    applyQuota: null, // 申请额度
    creditTermDay: null, // 授信期限(天)
    creditDataSource: creditDataSource.COUNTER, // 授信期限
    instructCode: "", // 指令号
    cirId: "", //授信调查报告ID
    creditInvestigateReportInfo: null // 授信调查报告信息
  });

  const currencyRef = shallowRef();
  // 币种查询入参
  const currencyParams = reactive({
    officeId: basicInfo.officeId
  });

  // 授信调查报告关闭页面后原页面值回显
  const setBasicInfo = (info: any) => {
    if (info !== undefined) {
      Object.assign(basicInfo, convert(info));
      Object.assign(creditDetailList, convert(basicInfo.creditDetailList));
      Object.assign(creditVarietyDetailList, convert(basicInfo.creditVarietyDetailList));
      if (basicInfo.fileIdArr === null || basicInfo.fileIdArr.length === 0) {
        basicInfo.fileIdArr = basicInfo.fileRefId.split(",");
      }
      initModifierData();
    }
  };
  // 机构下拉框
  const officeChange = (value: any, info: any) => {
    basicInfo.officeCode = info.officeCode;
    basicInfo.officeName = info.officeName;
    currencyParams.officeId = info.officeId;
    currencyRef.value.initRemoteData();
    basicInfo.targetCurrencyId = "";
    basicInfo.targetCurrencyCode = "";
    basicInfo.targetCurrencyName = "";
  };
  // 币种下拉框
  const currencyChange = (value: any, info: any) => {
    basicInfo.targetCurrencyId = info.currencyId;
    basicInfo.targetCurrencyCode = info.currencyCode;
    basicInfo.targetCurrencyName = info.currencyName;
  };

  const form1 = shallowRef();
  const submitMessage = ref("");
  const saveMessage = ref("");
  // 点击提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    submitMessage.value = t("credit.creditmanage.creditapproved.submitMessage");
    saveMessage.value = t("credit.creditmanage.creditapproved.saveConfirm");
    let result = true;
    await form1.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    if (result) {
      if (editCheckEditing()) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.editCheckEditingTip"));
        return false;
      }
      if (creditDetailList.length <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditDetailListTip"));
        return false;
      }
      if (
        basicInfo.creditRatingRiskType === creditRiskType.LOW_RISK &&
        basicInfo.creditRiskType === creditRiskType.HIGH_RISK
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditRatingRiskTypeTip"));
        return false;
      }
      if (
        basicInfo.creditStyle !== creditStyleType.SINGLE_LEGAL_CREDIT &&
        creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditCategoryTip"));
        return false;
      }
      if (basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL) {
        //校验未拆分明细
        if (checkDetailInfo()) {
          return false;
        }
      }

      if (subClientCheck()) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.subCreditClientListCodeTip"));
        return false;
      }
      let message = false;
      //控制限额
      if (Number(basicInfo.creditEstimateQuota) < Number(basicInfo.creditQuota)) {
        if (state.businessRule.genMaxLimitControlType === genMaxLimitControlType.STRONG) {
          FMessageBox.report(t("credit.creditmanage.creditapproved.genMaxLimitControlTypeTip1"));
          return false;
        } else if (state.businessRule.genMaxLimitControlType === genMaxLimitControlType.SOFT) {
          submitMessage.value = t("credit.creditmanage.creditapproved.genMaxLimitControlTypeTip2");
          saveMessage.value = t("credit.creditmanage.creditapproved.genMaxLimitControlTypeTip3");
          message = true;
        }
      }
      if (message) {
        submitMessage.value = t("credit.creditmanage.creditapproved.genMaxLimitControlTypeTip") + submitMessage.value;
        saveMessage.value = t("credit.creditmanage.creditapproved.genMaxLimitControlTypeTip") + saveMessage.value;
      }
    }

    return result;
  };
  const subClientCheck = () => {
    if (
      basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
      basicInfo.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
      basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL
    ) {
      for (let index = 0; index < subCreditClientList.length; index++) {
        if (subCreditClientList[index].refDetailSeqNo === "") {
          return true;
        }
      }
    }
    return false;
  };
  //附件
  const upload = ref();
  //上传附件返回的数组信息
  const fileMainInfos = ref<any[]>([]);
  //页面保存逻辑
  const generalSaveInfo = () => {
    if (upload.value.fileData) {
      fileMainInfos.value.splice(0);
      fileMainInfos.value.push(...upload.value.fileData);
      if (fileMainInfos.value.length > 0) {
        basicInfo.fileRefId = fileMainInfos.value.map((item: any) => item.id).join(",");
      }
    }
    if (
      basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
      basicInfo.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
      basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL
    ) {
      basicInfo.subCreditClientList = subCreditClientList;
    } else {
      basicInfo.subCreditClientList = [];
    }
    basicInfo.guaranteeDetailList = guaranteeDetailList;
    basicInfo.creditDetailList = creditDetailList;
    basicInfo.creditVarietyDetailList = creditVarietyDetailList;
    basicInfo.carryoverDetailDtoList = carryoverList;
    basicInfo.carryoverSubClientDetailDtoList = carryoverSubClientDetailDtoList;

    return basicInfo;
  };
  const clientCodeDisabled = ref(true);
  //查询详情信息
  const getDetailInfo = (info: CreditApplyDto, row: any) => {
    Object.assign(info, row.data);
    if (basicInfo.clientClass === clientClass.INTER_CLIENT) {
      paramsClientType.value = 1;
    } else {
      paramsClientType.value = 2;
    }
    closeEdit();
    if (row.data.subCreditClientList !== null && row.data.subCreditClientList.length > 0) {
      row.data.subCreditClientList.forEach(element => {
        const elementData = {
          seqNo: element.seqNo,
          clientCode: element.clientCode,
          clientName: element.clientName,
          clientId: element.clientId,
          clientHisId: element.clientHisId,
          creditType: element.creditType,
          refDetailSeqNo: element.refDetailSeqNo,
          creditVarietyCodes: element.creditVarietyCodes,
          creditVarietyList: element.creditVarietyList,
          creditVarietyNames: element.creditVarietyNames,
          creditVarietyCode: element.creditVarietyCode,
          creditControlType: element.creditControlType,
          creditUseType: element.creditUseType,
          quota: element.quota,
          guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
          creditQuota: element.creditQuota,
          assureQuota: element.assureQuota,
          pledgeQuota: element.pledgeQuota,
          impawnQuota: element.impawnQuota,
          noCreditQuota: element.noCreditQuota,
          nextVarietySeqNo: element.nextVarietySeqNo,
          nextClientSeqNo: element.nextClientSeqNo,
          addDetailFlag: element.addDetailFlag,
          _randomId: random()
        };
        subCreditClientList.push(elementData);
        editSubClientState[elementData._randomId] = false;
      });
    }
    if (row.data.guaranteeDetailList !== null && row.data.guaranteeDetailList.length > 0) {
      row.data.guaranteeDetailList.forEach(element => {
        const elementData = {
          currencyName: element.currencyName,
          currencyId: element.currencyId,
          currencyCode: element.currencyCode,
          clientId: element.clientId,
          clientCode: element.clientCode,
          clientName: element.clientName,
          collateralUseType: element.collateralUseType,
          collateralTypeId: element.collateralTypeId,
          collateralTypeCode: element.collateralTypeCode,
          collateralTypeName: element.collateralTypeName,
          guaranteeDesc: element.guaranteeDesc,
          _randomId: random()
        };
        guaranteeDetailList.push(elementData);
      });
    }
    if (row.data.creditDetailList !== null && row.data.creditDetailList.length > 0) {
      row.data.creditDetailList.forEach(element => {
        const elementData = {
          seqNo: element.seqNo,
          clientCode: element.clientCode,
          clientName: element.clientName,
          clientId: element.clientId,
          clientHisId: element.clientHisId,
          creditType: element.creditType,
          refDetailSeqNo: element.refDetailSeqNo,
          creditVarietyCodes: element.creditVarietyCodes,
          creditVarietyList: element.creditVarietyList,
          creditVarietyNames: element.creditVarietyNames,
          creditVarietyCode: element.creditVarietyCode,
          creditControlType: element.creditControlType,
          creditUseType: element.creditUseType,
          quota: element.quota,
          guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
          creditQuota: element.creditQuota,
          assureQuota: element.assureQuota,
          pledgeQuota: element.pledgeQuota,
          impawnQuota: element.impawnQuota,
          noCreditQuota: element.noCreditQuota,
          nextVarietySeqNo: element.nextVarietySeqNo,
          nextClientSeqNo: element.nextClientSeqNo,
          addDetailFlag: element.addDetailFlag,
          _randomId: random()
        };
        creditDetailList.push(elementData);
      });
    }
    if (row.data.creditVarietyDetailList !== null && row.data.creditVarietyDetailList.length > 0) {
      row.data.creditVarietyDetailList.forEach(element => {
        const elementData = {
          seqNo: element.seqNo,
          clientCode: element.clientCode,
          clientName: element.clientName,
          clientId: element.clientId,
          clientHisId: element.clientHisId,
          creditType: element.creditType,
          refDetailSeqNo: element.refDetailSeqNo,
          creditVarietyCodes: element.creditVarietyCodes,
          creditVarietyList: element.creditVarietyList,
          creditVarietyNames: element.creditVarietyNames,
          creditVarietyCode: element.creditVarietyCode,
          creditControlType: element.creditControlType,
          creditUseType: element.creditUseType,
          quota: element.quota,
          guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
          creditQuota: element.creditQuota,
          assureQuota: element.assureQuota,
          pledgeQuota: element.pledgeQuota,
          impawnQuota: element.impawnQuota,
          noCreditQuota: element.noCreditQuota,
          nextVarietySeqNo: element.nextVarietySeqNo,
          nextClientSeqNo: element.nextClientSeqNo,
          addDetailFlag: element.addDetailFlag,
          _randomId: random()
        };
        creditVarietyDetailList.push(elementData);
      });
    }
    carryoverList.splice(0);
    //结转信息处理
    if (row.data.carryoverDetailDtoList !== null && row.data.carryoverDetailDtoList.length > 0) {
      row.data.carryoverDetailDtoList.forEach(element => {
        const elementData = {
          clientId: element.clientId,
          clientHisId: element.clientHisId,
          clientCode: element.clientCode,
          clientName: element.clientName,
          originCreditNo: element.originCreditNo,
          originCreditStyle: element.originCreditStyle,
          originCreditVersion: element.originCreditVersion,
          originCreditType: element.originCreditType,
          originCreditVarietyId: element.originCreditVarietyId,
          originCreditVarietyCode: element.originCreditVarietyCode,
          originCreditVarietyName: element.originCreditVarietyName,
          originGuaranteeRatio: element.originGuaranteeRatio,
          originEffectiveDate: element.originEffectiveDate,
          originEndDate: element.originEndDate,
          originDetailSeqNo: element.originDetailSeqNo,
          originCreditTerm: element.originCreditTerm,
          originCarryoverAmount: element.originCarryoverAmount,
          carryoverExchangeRate: element.carryoverExchangeRate,
          targetCarryoverAmount: element.targetCarryoverAmount,
          targetCreditNo: element.targetCreditNo,
          targetCreditVersion: element.targetCreditVersion,
          targetDetailSeqNo: element.targetDetailSeqNo,
          addDetailFlag: element.addDetailFlag,
          _randomId: random()
        };
        carryoverList.push(elementData);
      });
    }
    carryoverSubClientDetailDtoList.splice(0);
    if (row.data.carryoverSubClientDetailDtoList !== null && row.data.carryoverSubClientDetailDtoList.length > 0) {
      row.data.carryoverSubClientDetailDtoList.forEach(element => {
        const elementData = {
          clientId: element.clientId,
          clientHisId: element.clientHisId,
          clientCode: element.clientCode,
          clientName: element.clientName,
          originCreditNo: element.originCreditNo,
          originCreditStyle: element.originCreditStyle,
          originCreditVersion: element.originCreditVersion,
          originCreditType: element.originCreditType,
          originCreditVarietyId: element.originCreditVarietyId,
          originCreditVarietyCode: element.originCreditVarietyCode,
          originCreditVarietyName: element.originCreditVarietyName,
          originGuaranteeRatio: element.originGuaranteeRatio,
          originEffectiveDate: element.originEffectiveDate,
          originEndDate: element.originEndDate,
          originDetailSeqNo: element.originDetailSeqNo,
          originCreditTerm: element.originCreditTerm,
          originCarryoverAmount: element.originCarryoverAmount,
          carryoverExchangeRate: element.carryoverExchangeRate,
          targetCarryoverAmount: element.targetCarryoverAmount,
          targetCreditNo: element.targetCreditNo,
          targetCreditVersion: element.targetCreditVersion,
          targetDetailSeqNo: element.targetDetailSeqNo,
          _randomId: random()
        };
        carryoverSubClientDetailDtoList.push(elementData);
      });
    }
    if (basicInfo.clientId > 0) {
      clientCodeDisabled.value = false;
    }
    /*state.businessRule.addApplyMandatoryFlag = basicInfo.addApplyMandatoryFlag; //申请是否必须
    state.businessRule.addEffectiveDateCreateType = basicInfo.addEffectiveDateCreateType; //授信生效日规则
    state.businessRule.genCreditLimitMethod = basicInfo.genCreditLimitMethod; //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    state.businessRule.groupQuotaAssignTarget = basicInfo.groupQuotaAssignTarget; //下级单位额度分配权限归属*/
    if (info.fileRefId) {
      info.fileIdArr = info.fileRefId.split(","); // 如果后端返回的文件id字符串是用逗号拼接的
    } else {
      info.fileIdArr = [];
    }
    if (upload.value) {
      upload.value.init(info.fileIdArr);
    }
    if (basicInfo.originApplyId > 0) {
      httpTool.post(getInfo, basicInfo).then((res: any) => {
        if (res.data.fileRefId) {
          state.fileIdArr = res.data.fileRefId.split(","); // 如果后端返回的文件id字符串是用逗号拼接的
        } else {
          state.fileIdArr = [];
        }
      });
    }
    creditLimitSetMethodTypeChange();
    initModifierData();
  };
  //操作列按钮逻辑
  const editSubClientState = reactive<Record<string, boolean>>({});
  const subClientClone = (scope: any) => {
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (editSubClientState[subCreditClientList[index]._randomId]) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.subCreditClientListCloneTip"));
        return false;
      }
    }
    subCreditClientList.push({
      seqNo: "",
      clientId: scope.row.clientId,
      clientHisId: scope.row.clientHisId,
      clientCode: scope.row.clientCode,
      clientName: scope.row.clientName,
      creditType: scope.row.creditType,
      refDetailSeqNo: scope.row.refDetailSeqNo,
      creditVarietyCodes: scope.row.creditVarietyCodes,
      creditVarietyList: scope.row.creditVarietyList,
      creditVarietyNames: scope.row.creditVarietyNames,
      creditVarietyCode: scope.row.creditVarietyCode,
      creditControlType: scope.row.creditControlType,
      creditUseType: scope.row.creditUseType,
      quota: scope.row.quota,
      guaranteeRatio: scope.row.guaranteeRatio,
      creditQuota: scope.row.creditQuota,
      assureQuota: scope.row.assureQuota,
      pledgeQuota: scope.row.pledgeQuota,
      impawnQuota: scope.row.impawnQuota,
      noCreditQuota: scope.row.noCreditQuota,
      addDetailFlag: yesOrNo.YES,
      _randomId: random()
    });
    editSubClientState[subCreditClientList[subCreditClientList.length - 1]._randomId] = true;
    subCreditClientGrid.value.openEdit(subCreditClientList[subCreditClientList.length - 1]._randomId);
  };
  const subClientDelete = (scope: any) => {
    if (!validateDeleteDeatil(scope.row)) {
      return false;
    }
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (scope.row._randomId === subCreditClientList[index]._randomId) {
        subCreditClientGrid.value.closeEdit(scope.row._randomId);
        subCreditClientList.splice(index, 1);
        break;
      }
    }
    editSubClientState[scope.row._randomId] = false;
  };
  const generalSubClientButtonOption = (scope: any) => {
    return [
      {
        type: "edit",
        buttonText: t("credit.creditmanage.creditapproved.edit"),
        isShow: !editSubClientState[scope.row._randomId],
        emitName: "on-edit",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "clone",
        buttonText: t("credit.creditmanage.creditapproved.clone"),
        isShow: !editSubClientState[scope.row._randomId],
        emitName: "on-clone",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "close",
        buttonText: t("credit.creditmanage.creditapproved.save"),
        isShow: editSubClientState[scope.row._randomId],
        emitName: "on-close",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "delete",
        buttonText: t("credit.creditmanage.creditapproved.delete"),
        emitName: "on-delete",
        originalProps: {
          type: "danger",
          icon: null
        }
      }
    ];
  };
  //下级客户授信可编辑表格
  const subCreditClientGrid = shallowRef();
  const subCreditClientList = reactive<any[]>([]);
  const addSubCreditRow = () => {
    subCreditClientList.push({
      seqNo: "",
      clientCode: "",
      clientName: "",
      creditType: creditType.COMPOSITE_CREDIT,
      refDetailSeqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyNames: "",
      creditVarietyCode: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0,
      addDetailFlag: yesOrNo.YES,
      _randomId: random()
    });
    editSubClientState[subCreditClientList[subCreditClientList.length - 1]._randomId] = true;
    subCreditClientGrid.value.openEdit(subCreditClientList[subCreditClientList.length - 1]._randomId);
  };
  const beforeSubCreditOpen = (scope: any) => {
    grtCreditClientList(scope.row.refDetailSeqNo);
    editSubClientState[scope.row._randomId] = true;
    subCreditClientGrid.value.openEdit(subCreditClientList[scope.$index]._randomId);
  };
  const beforeSubCreditClose = (scope: any) => {
    if (scope.row.refDetailSeqNo === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.refSubClientDetailSeqNoTip"));
      return false;
    }
    if (scope.row.clientCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.clientCodeSeqNoTip"));
      return false;
    }
    getNextNo(scope.row.refDetailSeqNo, true);
    if (
      scope.row.creditType !== creditType.SPECIAL_CREDIT &&
      creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
    ) {
      FMessageBox.report(t("credit.creditmanage.creditapproved.creditCategoryDetailTip"));
      return false;
    }
    if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (scope.row.creditQuota <= 0 && scope.row.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditTip"));
        return false;
      }
      if (
        scope.row.noCreditQuota > state.refSeqNoElement.noCreditQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeSubGtTip"));
        return false;
      }
    } else if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        scope.row.assureQuota <= 0 &&
        scope.row.creditQuota <= 0 &&
        scope.row.pledgeQuota <= 0 &&
        scope.row.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeTip"));
        return false;
      }
      if (
        scope.row.assureQuota > state.refSeqNoElement.assureQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota ||
        scope.row.pledgeQuota > state.refSeqNoElement.pledgeQuota ||
        scope.row.impawnQuota > state.refSeqNoElement.impawnQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeSubGtTip"));
        return false;
      }
    } else {
      if (scope.row.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.quotaTip"));
        return false;
      }
      if (scope.row.quota > state.refSeqNoElement.quota) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeSubGtTip"));
        return false;
      }
    }
    if (scope.row.creditVarietyCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.creditVarietyCodeTip"));
      return false;
    }
    if (checkClientData(subCreditClientList, scope.row)) {
      return false;
    }
    if (scope.row.seqNo === "") {
      getNextNo(scope.row.refDetailSeqNo, true);
      const nextClientSeqNo = state.nextSeqNo + 1;
      scope.row.seqNo =
        scope.row.refDetailSeqNo + (nextClientSeqNo > 10 ? "-0" + nextClientSeqNo : "-00" + nextClientSeqNo);
      updateNextNo(nextClientSeqNo, scope.row.refDetailSeqNo, true);
    }
    editSubClientState[scope.row._randomId] = false;
    subCreditClientGrid.value.closeEdit(subCreditClientList[scope.$index]._randomId);
  };
  const updateNextNo = (nextSeqNo: string, refDetailSeqNo: string, isClient: boolean) => {
    creditDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        if (isClient) {
          element.nextClientSeqNo = nextSeqNo;
        } else {
          element.nextVarietySeqNo = nextSeqNo;
        }
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        element.nextClientSeqNo = nextSeqNo;
      }
    });
  };

  const getNextNo = (refDetailSeqNo: string, isClient: boolean) => {
    creditDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        state.refSeqNoElement = element;
        if (isClient) {
          state.nextSeqNo = element.nextClientSeqNo;
        } else {
          state.nextSeqNo = element.nextVarietySeqNo;
        }
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        state.nextSeqNo = element.nextClientSeqNo;
        state.refSeqNoElement = element;
      }
    });
  };

  //担保信息可编辑表格
  const guaranteeDetailGrid = shallowRef();
  const guaranteeDetailList = reactive<any[]>([]);
  const addGuaranteeDetailRow = () => {
    guaranteeDetailList.push({
      currencyName: "",
      currencyId: "",
      currencyCode: "",
      clientId: "",
      clientCode: "",
      clientName: "",
      collateralUseType: "",
      collateralTypeId: "",
      collateralTypeCode: "",
      collateralTypeName: "",
      _randomId: random()
    });
    guaranteeDetailGrid.value.openEdit(guaranteeDetailList[guaranteeDetailList.length - 1]._randomId);
  };
  const beforeGuaranteeDetailClose = (scope: any) => {
    if (scope.row.currencyName === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.currencyNameTip"));
      return false;
    }
    if (scope.row.clientName === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.clientNameTip"));
      return false;
    }
    if (scope.row.collateralUseType === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.collateralUseTypeTip"));
      return false;
    }
    if (scope.row.collateralTypeName === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.collateralTypeNameTip"));
      return false;
    }
    guaranteeDetailGrid.value.closeEdit(guaranteeDetailList[scope.$index]._randomId);
  };
  const creditTypeDisable = ref<boolean>(true);
  //授信明细可编辑表格
  const creditDetailGrid = shallowRef();
  const creditDetailList = reactive<any[]>([]);
  const addCreditDetailRow = () => {
    creditDetailList.push({
      creditType: creditType.SPECIAL_CREDIT,
      seqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyCode: "",
      creditVarietyNames: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0,
      nextVarietySeqNo: 0,
      nextClientSeqNo: 0,
      addDetailFlag: yesOrNo.YES,
      _randomId: random()
    });
    creditDetailGrid.value.openEdit(creditDetailList[creditDetailList.length - 1]._randomId);
    creditTypeDisable.value = false;
  };

  const beforeCreditDetailDelete = (scope: any) => {
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (
        subCreditClientList[index].refDetailSeqNo === scope.row.seqNo &&
        subCreditClientList[index].refDetailSeqNo !== "" &&
        scope.row.seqNo !== ""
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditDetailDeleteTip"));
        return false;
      }
    }
    for (let index = 0; index < creditVarietyDetailList.length; index++) {
      if (
        creditVarietyDetailList[index].refDetailSeqNo === scope.row.seqNo &&
        creditVarietyDetailList[index].refDetailSeqNo !== "" &&
        scope.row.seqNo !== ""
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditDetailDeleteTip"));
        return false;
      }
    }
    if (!validateDeleteDeatil(scope.row)) {
      return false;
    }
    return true;
  };
  const beforeCreditDetailClose = (scope: any) => {
    if (
      scope.row.creditType !== creditType.SPECIAL_CREDIT &&
      creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
    ) {
      FMessageBox.report(t("credit.creditmanage.creditapproved.creditCategoryDetailTip"));
      return false;
    }
    if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (scope.row.creditQuota <= 0 && scope.row.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditTip"));
        return false;
      }
    } else if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        scope.row.assureQuota <= 0 &&
        scope.row.creditQuota <= 0 &&
        scope.row.pledgeQuota <= 0 &&
        scope.row.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeTip"));
        return false;
      }
    } else {
      if (scope.row.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.quotaTip"));
        return false;
      }
    }
    if (scope.row.creditVarietyCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.creditVarietyCodeTip"));
      return false;
    }
    if (checkVerData(creditDetailList, scope.row)) {
      return false;
    }
    if (scope.row.seqNo === undefined || scope.row.seqNo === "") {
      const previousDetailSeqNo =
        basicInfo.previousDetailSeqNo === null ? 1 : Number(basicInfo.previousDetailSeqNo) + 1;
      basicInfo.previousDetailSeqNo = previousDetailSeqNo;
      creditDetailList[scope.$index].seqNo =
        previousDetailSeqNo < 10 ? "0" + previousDetailSeqNo : String(previousDetailSeqNo);
    }
    updateSubInfoData();
    creditDetailGrid.value.closeEdit(creditDetailList[scope.$index]._randomId);
    initModifierData();
    calculateQuota();
    creditTypeDisable.value = true;
  };

  const updateSubInfoData = () => {
    //重新处理放大镜数据
    creditDetailList.forEach(element => {
      creditVarietyDetailList.forEach(subEle => {
        if (element.seqNo === subEle.refDetailSeqNo) {
          subEle.creditUseType = element.creditUseType;
          subEle.creditControlType = element.creditControlType;
          subEle.guaranteeRatio = element.guaranteeRatio;
        }
      });
      subCreditClientList.forEach(subEle => {
        if (element.seqNo === subEle.refDetailSeqNo) {
          subEle.creditUseType = element.creditUseType;
          subEle.creditControlType = element.creditControlType;
          subEle.guaranteeRatio = element.guaranteeRatio;
        }
      });
    });
    creditVarietyDetailList.forEach(element => {
      subCreditClientList.forEach(subEle => {
        if (element.seqNo === subEle.refDetailSeqNo) {
          subEle.creditUseType = element.creditUseType;
          subEle.creditControlType = element.creditControlType;
          subEle.guaranteeRatio = element.guaranteeRatio;
        }
      });
    });
  };

  const checkClientData = (dataList: any[], data) => {
    const compareData = data.clientCode + data.refDetailSeqNo;
    const checkedList = [];
    dataList.forEach(element => {
      checkedList.push(element.clientCode + element.refDetailSeqNo);
    });
    let count = 0;
    for (let index = 0; index < checkedList.length; index++) {
      if (checkedList[index] === compareData) {
        count += 1;
        if (count > 1) {
          FMessageBox.report(t("credit.creditmanage.creditapproved.checkClientDataTip"));
          return true;
        }
      }
    }

    return false;
  };

  const checkVerData = (dataList: any[], data: any) => {
    const checkList = [];
    const checkedList = [];
    dataList.forEach(element => {
      element.creditVarietyList.forEach(e => {
        checkList.push(e.creditVarietyCode + element.guaranteeRatio);
        checkedList.push(element.creditType + e.creditVarietyCode);
      });
    });
    for (let index = 0; index < data.creditVarietyList.length; index++) {
      let count = 0;
      const compore = data.creditType + data.creditVarietyList[index].creditVarietyCode;
      for (let j = 0; j < checkedList.length; j++) {
        if (checkedList[j] === compore) {
          count += 1;
          if (count > 1) {
            FMessageBox.report(t("credit.creditmanage.creditapproved.checkVerDataTip"));
            return true;
          }
        }
      }
    }
    return false;
  };

  const checkVerCodeData = (dataList: any[], data) => {
    const checkList = [];
    dataList.forEach(element => {
      element.creditVarietyList.forEach(e => {
        checkList.push(element.refDetailSeqNo + e.creditVarietyCode);
      });
    });
    for (let i = 0; i < data.creditVarietyList.length; i++) {
      const compareData = data.refDetailSeqNo + data.creditVarietyList[i].creditVarietyCode;
      let count = 0;
      for (let index = 0; index < checkList.length; index++) {
        if (checkList[index] === compareData) {
          count += 1;
          if (count > 1) {
            FMessageBox.report(t("credit.creditmanage.creditapproved.checkVerCodeDataTip"));
            return true;
          }
        }
      }
    }
    return false;
  };

  const checkDetailInfo = () => {
    const checkList = [];
    const checkVarietyList = [];
    const unCheckList = [];
    creditDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        element.creditVarietyList.forEach(e => {
          checkList.push(element.seqNo + e.creditVarietyCode);
        });
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        element.creditVarietyList.forEach(e => {
          checkVarietyList.push(element.refDetailSeqNo + e.creditVarietyCode);
        });
      }
    });
    for (let index = 0; index < checkList.length; index++) {
      if (!checkVarietyList.includes(checkList[index])) {
        unCheckList.push(checkList[index]);
        FMessageBox.report(t("credit.creditmanage.creditapproved.checkDetailInfoTip"));
        return true;
      }
    }
    if (
      basicInfo.composeVarietyControlMethod === composeVarietyControlMethodType.VARIETY_CONTROL &&
      creditVarietyDetailList.length === 0
    ) {
      for (let index = 0; index < creditDetailList.length; index++) {
        if (creditType.COMPOSITE_CREDIT === creditDetailList[index].creditType) {
          FMessageBox.report(t("credit.creditmanage.creditapproved.creditVarietyDetailListLengthTip"));
          return true;
        }
      }
    }
    return false;
  };

  const formTemporaryValidator = () => {
    if (editCheckEditing()) {
      FMessageBox.report(t("credit.creditmanage.creditapproved.editCheckEditingTip"));
      return false;
    }
    return true;
  };

  //可编辑表格校验
  const editCheckEditing = () => {
    if (creditDetailGrid.value.isEditing()) {
      return true;
    }
    /*if (guaranteeDetailGrid.value.isEditing()) {
      return true;
    }*/
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (editSubClientState[subCreditClientList[index]._randomId]) {
        return true;
      }
    }
    if (creditVarietyDetailGrid.value && creditVarietyDetailGrid.value.isEditing()) {
      return true;
    }
    return false;
  };

  const creditDetailmagnifierData = reactive<any[]>([]);
  const creditClientmagnifierData = reactive<any[]>([]);

  const initModifierData = () => {
    //重新处理放大镜数据
    creditDetailmagnifierData.splice(0);
    creditDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        creditDetailmagnifierData.push(element);
      }
    });

    //记录已经拆分了的授信明细序号
    const checkedList = [];
    creditVarietyDetailList.forEach(element => {
      checkedList.push(element.refDetailSeqNo);
    });
    creditClientmagnifierData.splice(0);
    creditDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        if (basicInfo.composeVarietyControlMethod === composeVarietyControlMethodType.NOT_CONTROL) {
          creditClientmagnifierData.push(element);
        } else if (!checkedList.includes(element.seqNo)) {
          creditClientmagnifierData.push(element);
        }
      } else {
        creditClientmagnifierData.push(element);
      }
    });
    if (basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL) {
      creditVarietyDetailList.forEach(element => {
        if (creditType.COMPOSITE_CREDIT === element.creditType) {
          creditClientmagnifierData.push(element);
        }
      });
    }
  };

  //授信品种明细可编辑表格
  const creditVarietyDetailGrid = shallowRef();
  const creditVarietyDetailList = reactive<any[]>([]);
  const addCreditVarietyDetailRow = () => {
    creditVarietyDetailList.push({
      seqNo: "",
      creditType: creditType.COMPOSITE_CREDIT,
      refDetailSeqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyCode: "",
      creditVarietyNames: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0,
      nextClientSeqNo: 0,
      addDetailFlag: yesOrNo.YES,
      _randomId: random()
    });

    creditVarietyDetailGrid.value.openEdit(creditVarietyDetailList[creditVarietyDetailList.length - 1]._randomId);
  };
  const selectVarietyData = reactive([]);
  const grtCreditVarietyList = (seqNo: string) => {
    selectVarietyData.splice(0);
    creditDetailList.forEach(element => {
      if (seqNo === element.seqNo) {
        selectVarietyData.push(...element.creditVarietyList);
      }
    });
  };
  const selectClientData = reactive([]);
  const grtCreditClientList = (seqNo: string) => {
    selectClientData.splice(0);
    creditDetailList.forEach(element => {
      if (seqNo === element.seqNo) {
        selectClientData.push(...element.creditVarietyList);
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (seqNo === element.seqNo) {
        selectClientData.push(...element.creditVarietyList);
      }
    });
  };

  //授信品种明细校验
  const beforeCreditVarietyDetailDelete = (scope: any) => {
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (
        subCreditClientList[index].refDetailSeqNo === scope.row.seqNo &&
        subCreditClientList[index].refDetailSeqNo !== "" &&
        scope.row.seqNo !== ""
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.varietyDetailDeleteTip"));
        return false;
      }
    }
    initModifierData();
    return true;
  };
  const beforeCreditVarietyDetailClose = (scope: any) => {
    if (scope.row.refDetailSeqNo === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.refDetailSeqNoTip"));
      return false;
    }
    getNextNo(scope.row.refDetailSeqNo, false);
    if (
      scope.row.creditType !== creditType.SPECIAL_CREDIT &&
      creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
    ) {
      FMessageBox.report(t("credit.creditmanage.creditapproved.creditCategoryDetailTip"));
      return false;
    }
    if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (scope.row.creditQuota <= 0 && scope.row.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditTip"));
        return false;
      }
      if (
        scope.row.noCreditQuota > state.refSeqNoElement.noCreditQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeGtTip"));
        return false;
      }
    } else if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        scope.row.assureQuota <= 0 &&
        scope.row.creditQuota <= 0 &&
        scope.row.pledgeQuota <= 0 &&
        scope.row.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeTip"));
        return false;
      }
      if (
        scope.row.assureQuota > state.refSeqNoElement.assureQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota ||
        scope.row.pledgeQuota > state.refSeqNoElement.pledgeQuota ||
        scope.row.impawnQuota > state.refSeqNoElement.impawnQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeGtTip"));
        return false;
      }
    } else {
      if (scope.row.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.quotaTip"));
        return false;
      }
      if (scope.row.quota > state.refSeqNoElement.quota) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeGtTip"));
        return false;
      }
    }
    if (scope.row.creditVarietyCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.creditVarietyCodeTip"));
      return false;
    }
    if (checkVerCodeData(creditVarietyDetailList, scope.row)) {
      return false;
    }
    //下级客户引用授信明细校验
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (subCreditClientList[index].refDetailSeqNo === scope.row.refDetailSeqNo) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.subClientDetailShowTip"));
        return false;
      }
    }
    if (scope.row.seqNo === "") {
      getNextNo(scope.row.refDetailSeqNo, false);
      const nextVarietySeqNo = state.nextSeqNo + 1;
      scope.row.seqNo =
        scope.row.refDetailSeqNo + (nextVarietySeqNo >= 10 ? "-0" + nextVarietySeqNo : "-00" + nextVarietySeqNo);
      updateNextNo(nextVarietySeqNo, scope.row.refDetailSeqNo, false);
    }
    creditVarietyDetailGrid.value.closeEdit(creditVarietyDetailList[scope.$index]._randomId);
    initModifierData();
  };

  const beforeCreditVarietyDetailOpen = (scope: any) => {
    grtCreditVarietyList(scope.row.refDetailSeqNo);
    creditVarietyDetailGrid.value.openEdit(creditVarietyDetailList[scope.$index]._randomId);
  };

  // 已选列表
  const checkedSubCreditList = reactive([]);
  // 勾选checkbox
  const handleSubCreditSelect = (row: any[]) => {
    if (row.length > 0) {
      checkedSubCreditList.splice(0);
      checkedSubCreditList.push(...row);
    }
  };
  //清除选项
  const clearSubCreditSelection = () => {
    checkedSubCreditList.splice(0);
  };
  //共享额度
  const sharedQuota = () => {
    if (state.subClientDetail.refDetailSeqNo === "") {
      FMessageBox.report(t("credit.creditmanage.creditapproved.refDetailSeqNoGtTip"));
      return false;
    }
    if (checkedSubCreditList.length === 0) {
      FMessageBox.report(t("credit.creditmanage.creditapproved.checkedSubCreditListTip"));
      return false;
    }
    getNextNo(state.subClientDetail.refDetailSeqNo, true);
    if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (state.subClientDetail.creditQuota <= 0 && state.refSeqNoElement.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.creditTip"));
        return false;
      }
      if (
        state.subClientDetail.noCreditQuota > state.refSeqNoElement.noCreditQuota &&
        state.subClientDetail.creditQuota > state.refSeqNoElement.creditQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeSubGtTip"));
        return false;
      }
    } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        state.subClientDetail.assureQuota <= 0 &&
        state.subClientDetail.creditQuota <= 0 &&
        state.subClientDetail.pledgeQuota <= 0 &&
        state.subClientDetail.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeTip"));
        return false;
      }
      if (
        state.subClientDetail.assureQuota > state.refSeqNoElement.assureQuota ||
        state.subClientDetail.creditQuota > state.refSeqNoElement.creditQuota ||
        state.subClientDetail.pledgeQuota > state.refSeqNoElement.pledgeQuota ||
        state.subClientDetail.impawnQuota > state.refSeqNoElement.impawnQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeSubGtTip"));
        return false;
      }
    } else {
      if (state.subClientDetail.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.quotaTip"));
        return false;
      }
      if (state.subClientDetail.quota > state.refSeqNoElement.quota) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.guaranteeSubGtTip"));
        return false;
      }
    }
    FMessageBox.confirm(t("credit.creditmanage.creditapproved.sharedQuotaConfirm"), "Warning", {
      confirmButtonText: t("credit.creditmanage.creditapproved.doconfirm"),
      cancelButtonText: t("credit.creditmanage.creditapproved.docancel"),
      title: t("credit.creditmanage.creditapproved.sharedQuota"),
      customClass: "tree-card-message",
      buttonSize: "default",
      message: h("div", { class: "el-tree-card__messagebox" }, [
        h(FIcon, { size: 50 }, [h(DtgConfirm)]),
        h("span", null, t("credit.creditmanage.creditapproved.sharedQuotaConfirm"))
      ])
    }).then(() => {
      let nextClientSeqNo = state.nextSeqNo;
      checkedSubCreditList.forEach(element => {
        element.refDetailSeqNo = state.refSeqNoElement.seqNo;
        element.creditVarietyCodes = state.refSeqNoElement.creditVarietyCodes;
        element.creditVarietyList = state.refSeqNoElement.creditVarietyList;
        element.creditType = state.refSeqNoElement.creditType;
        element.creditVarietyCode = state.refSeqNoElement.creditVarietyCode;
        element.creditVarietyNames = state.refSeqNoElement.creditVarietyNames;
        element.creditControlType = state.refSeqNoElement.creditControlType;
        element.creditUseType = state.refSeqNoElement.creditUseType;
        if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
          element.creditQuota = state.subClientDetail.creditQuota;
          element.noCreditQuota = state.subClientDetail.noCreditQuota;
        } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
          element.creditQuota = state.subClientDetail.creditQuota;
          element.assureQuota = state.subClientDetail.assureQuota;
          element.pledgeQuota = state.subClientDetail.pledgeQuota;
          element.impawnQuota = state.subClientDetail.impawnQuota;
        } else {
          element.quota = state.subClientDetail.quota;
          element.guaranteeRatio = state.refSeqNoElement.guaranteeRatio;
        }
        nextClientSeqNo += 1;
        if (element.seqNo === "") {
          element.seqNo =
            state.subClientDetail.refDetailSeqNo +
            (nextClientSeqNo > 10 ? "-0" + nextClientSeqNo : "-00" + nextClientSeqNo);
        }
      });
      updateNextNo(nextClientSeqNo, state.refSeqNoElement.seqNo, true);
      FMessageBox.report({
        type: "success",
        message: t("credit.creditmanage.creditapproved.sharedQuotaSuccess")
      });
      checkedSubCreditList.splice(0);
      state.subClientDetail.creditQuota = 0;
      state.subClientDetail.assureQuota = 0;
      state.subClientDetail.pledgeQuota = 0;
      state.subClientDetail.impawnQuota = 0;
      state.subClientDetail.quota = 0;
      state.subClientDetail.noCreditQuota = 0;
      subCreditClientGrid.value.table.clearSelection();
    });
  };
  //基础信息change
  const creditLimitSetMethodTypeChange = () => {
    if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      detailState.columns = creditDetailColumns3;
      detailState.varietyDetailcolumns = creditDetailColumns6;
      detailState.subClientColumns = creditDetailColumns9;
    } else if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      detailState.columns = creditDetailColumns2;
      detailState.varietyDetailcolumns = creditDetailColumns5;
      detailState.subClientColumns = creditDetailColumns8;
    } else {
      detailState.columns = creditDetailColumns1;
      detailState.varietyDetailcolumns = creditDetailColumns4;
      detailState.subClientColumns = creditDetailColumns7;
    }
    if (state.businessRule.genCreditLimitMethod !== creditLimitSetMethodType.TOTAL_LIMIT) {
      for (let index = 1; index < state.carryoverInfo.notCarryoverDetailColumns.length; index++) {
        if (state.carryoverInfo.notCarryoverDetailColumns[index].prop === "originGuaranteeRatio") {
          state.carryoverInfo.notCarryoverDetailColumns.splice(index, 1);
        }
      }
      for (let index = 1; index < state.carryoverInfo.carryoverDetailColumns.length; index++) {
        if (state.carryoverInfo.carryoverDetailColumns[index].prop === "originGuaranteeRatio") {
          state.carryoverInfo.carryoverDetailColumns.splice(index, 1);
        }
      }
    }
  };
  //组织架构事件
  const creditOrgStructureChange = (row: any) => {
    basicInfo.creditOrgStructureId = row.id; //授信组织架构Id
    basicInfo.creditOrgStructureCode = row.orgStructureCode; //授信组织架构编码
    basicInfo.creditOrgStructureName = row.orgStructureName; //授信组织架构名称
    subCreditClientList.splice(0);
    getSubClientList();
  };
  const creditStyleTypeChange = () => {
    if (basicInfo.creditStyle === creditStyleType.SINGLE_LEGAL_CREDIT) {
      creditVarietyDetailList.splice(0);
      subCreditClientList.splice(0);
    } else {
      getSubClientList();
    }
    initModifierData();
  };
  const clearCreditOrgStructure = () => {
    basicInfo.creditOrgStructureId = ""; //授信组织架构Id
    basicInfo.creditOrgStructureCode = ""; //授信组织架构编码
    basicInfo.creditOrgStructureName = ""; //授信组织架构名称
    subCreditClientList.splice(0);
  };

  //信用评级事件
  const creditRatingCodeChange = (row: any) => {
    basicInfo.creditRatingId = row.id; //信用等级评定Id
    basicInfo.creditRatingCode = row.businessCode; //信用等级评定code
    basicInfo.creditGradeId = ""; //信用等级Id
    basicInfo.creditGradeName = row.approvedLevel; //信用等级名称
    basicInfo.creditGradeCode = row.approvedLevelCode; //信用等级编码
    basicInfo.creditRatingScore = row.initialScore; //信用等级评估分值
    basicInfo.creditRatingRiskType =
      row.ratingType === ratingType.LOW ? creditRiskType.LOW_RISK : creditRiskType.HIGH_RISK; //信用等级评估风险类型;1:高风险；2：低风险
    basicInfo.creditRatingEffectiveDate = row.ratingEffectiveDate; //信用等级评估生效日期
    basicInfo.creditRatingInvalidDate = row.ratingExpirationDate; //信用等级评估失效日期
  };
  const clearCreditRatingCode = () => {
    basicInfo.creditRatingId = ""; //信用等级评定Id
    basicInfo.creditRatingCode = ""; //信用等级评定code
    basicInfo.creditGradeId = ""; //信用等级Id
    basicInfo.creditGradeName = ""; //信用等级名称
    basicInfo.creditGradeCode = ""; //信用等级编码
    basicInfo.creditRatingScore = ""; //信用等级评估分值
    basicInfo.creditRatingRiskType = ""; //信用等级评估风险类型;1:高风险；2：低风险
    basicInfo.creditRatingEffectiveDate = ""; //信用等级评估生效日期
    basicInfo.creditRatingInvalidDate = ""; //信用等级评估失效日期
  };

  //客户变更事件
  const clientCodeChange = (row: any) => {
    basicInfo.clientId = row.clientId;
    basicInfo.clientHisId = row.clientHisId;
    basicInfo.clientCode = row.clientCode;
    basicInfo.clientName = row.clientName;
    basicInfo.industryCategoryId = row.industryCategoryId; //行业类别id
    basicInfo.industryCategoryName = row.industryCategoryName; //行业类别名称
    basicInfo.industryCategoryCode = row.industryCategoryCode; //行业类别编码
    basicInfo.enterpriseScale = row.enterpriseScale; //企业规模
    clearCreditOrgStructure();
    calculateEstimateQuota();
    calculateExposeCreditLimit();
    carryoverSubClientDetailDtoList.splice(0);
    carryoverList.splice(0);
  };
  const clearClientChange = () => {
    basicInfo.clientId = "";
    basicInfo.clientHisId = "";
    basicInfo.clientCode = "";
    basicInfo.clientName = "";
    basicInfo.industryCategoryId = ""; //行业类别id
    basicInfo.industryCategoryName = ""; //行业类别名称
    basicInfo.industryCategoryCode = ""; //行业类别编码
    basicInfo.enterpriseScale = ""; //企业规模
    carryoverSubClientDetailDtoList.splice(0);
    carryoverList.splice(0);
  };
  const composeVarietyControlMethodChange = () => {
    if (basicInfo.composeVarietyControlMethod === composeVarietyControlMethodType.NOT_CONTROL) {
      subCreditClientList.splice(0);
      creditVarietyDetailList.splice(0);
      initModifierData();
    }
  };
  //币种类型变更事件
  const currencyTypeChange = () => {
    if (basicInfo.currencyType === currencyType.MANY_CURRENCY) {
      basicInfo.targetCurrencyId = 1;
      basicInfo.targetCurrencyCode = "CNY";
      basicInfo.targetCurrencyName = t("credit.creditmanage.creditapproved.currencyDefaultName");
    }
  };

  const paramsClientType = ref(1); //中客户类型参数
  //客户类型变更事件
  const clientClassChange = () => {
    if (basicInfo.clientClass === clientClass.INTER_CLIENT) {
      paramsClientType.value = 1;
    } else {
      paramsClientType.value = 2;
    }
    clearClientChange();
  };

  //公共处理信息
  const state = reactive({
    subClientDetail: {
      seqNo: "",
      creditType: creditType.COMPOSITE_CREDIT,
      refDetailSeqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyNames: "",
      creditVarietyCode: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0
    },
    //查看客户信息
    clientInfoDetailDialog: false,
    clientInfo: [],
    currentNodeKey: -1,
    clientDto: { clientCode: "", clientName: "" },
    //结转数据
    carryoverInfo: {
      showDialog: false,
      showSubClientDialog: false,
      notCarryoverDetailColumns: notCarryoverDetailColumns,
      carryoverDetailColumns: carryoverDetailColumns
    },
    fileIdArr: [],
    exposeCreditLimit: 0,
    refSeqNoElement: null,
    nextSeqNo: 0,
    //公共校验规则
    businessRule: {
      addApplyMandatoryFlag: "", //申请是否必须
      addApplyNeedResolutionType: "", //授信申请是否需要决议
      addEffectiveDateCreateType: "", //授信生效日规则
      changeApplyMandatoryFlag: "", //变更规则_授信变更申请是否必须
      changeApplyNeedResolutionRule: "", //变更规则_授信变更申请决议规则
      changeApplyNeedResolutionType: "", //变更规则_授信变更申请是否需要决议
      genCreditAutoActivate: "", //授信是否自动激活
      genCreditLimitMethod: "", //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
      genCreditRatingFlag: "", //通用规则-信用评级是否必须
      genMaxLimitControlType: "", //最高限额校验控制类型
      genMaxLimitValidPeriod: "", //最高额度有效期限类型
      groupQuotaAcceptFlag: "", //下级单位额度分配是否需财务公司受理
      groupQuotaAssignTarget: "" //下级单位额度分配权限归属
    }
  });

  //授信明细动态列表
  const detailState = reactive({
    columns: creditDetailColumns1,
    varietyDetailcolumns: creditDetailColumns4,
    subClientColumns: creditDetailColumns7
  });

  const openDate = ref();
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      openDate.value = res.data.onlineDate;
      basicInfo.executeDate = res.data.onlineDate;
      setStartDate();
    });
  };
  const setStartDate = () => {
    if (effectiveDateActiveRuleType.CUSTOM === basicInfo.addEffectiveDateCreateType) {
      basicInfo.effectiveDate = openDate.value;
      getEndDate();
    } else {
      basicInfo.effectiveDate = "";
      basicInfo.endDate = "";
    }
  };

  const getEndDate = () => {
    if (basicInfo.effectiveDate !== "") {
      //根据期限计算结束日期
      const monthTime = new Date(basicInfo.effectiveDate);
      const newTime = monthTime.setMonth(monthTime.getMonth() + basicInfo.creditTermMonth);
      basicInfo.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
    }
  };
  //获取业务规则设置
  const getBusinessRuleSet = (isModify: boolean) => {
    httpTool.post(getBusinessRuleSetUrl).then((res: any) => {
      if (res.success) {
        if (!isModify) {
          Object.assign(state.businessRule, res.data);
          basicInfo.addApplyMandatoryFlag = res.data.addApplyMandatoryFlag; //申请是否必须
          basicInfo.changeApplyMandatoryFlag = res.data.changeApplyMandatoryFlag; //变更规则_授信变更申请是否必须
          basicInfo.genCreditAutoActivate = res.data.genCreditAutoActivate; //授信是否自动激活
          basicInfo.genCreditLimitMethod = res.data.genCreditLimitMethod; //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
          basicInfo.groupQuotaAssignTarget = res.data.groupQuotaAssignTarget; //下级单位额度分配权限归属
        }
        state.businessRule.genCreditRatingFlag = res.data.genCreditRatingFlag;
        basicInfo.genCreditRatingFlag = res.data.genCreditRatingFlag; //通用规则-信用评级是否必须
        state.businessRule.addEffectiveDateCreateType = res.data.addEffectiveDateCreateType; //授信生效日规则
        state.businessRule.genMaxLimitControlType = res.data.genMaxLimitControlType; //最高限额校验控制类型
        state.businessRule.genMaxLimitValidPeriod = res.data.genMaxLimitValidPeriod; //最高额度有效期限类型
        state.businessRule.groupQuotaAssignTarget = res.data.groupQuotaAssignTarget; //下级单位额度分配权限归属

        basicInfo.addEffectiveDateCreateType = res.data.addEffectiveDateCreateType; //授信生效日规则
        basicInfo.genMaxLimitControlType = res.data.genMaxLimitControlType; //最高限额校验控制类型
        basicInfo.genMaxLimitValidPeriod = res.data.genMaxLimitValidPeriod; //最高额度有效期限类型
        creditLimitSetMethodTypeChange();
        setStartDate();
      }
    });
  };
  const calculateQuota = () => {
    let quota = 0;
    creditDetailList.forEach(element => {
      if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
        quota = quota + element.noCreditQuota + element.creditQuota;
      } else if (basicInfo.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
        quota = quota + element.assureQuota + element.creditQuota + element.pledgeQuota + element.impawnQuota;
      } else {
        quota += element.quota;
      }
    });
    basicInfo.creditQuota = quota;
    basicInfo.exposeCreditLimit = quota - state.exposeCreditLimit;
  };
  //计算风险限额
  const calculateEstimateQuota = () => {
    httpTool.post(calculateEstimateQuotaUrl, basicInfo).then((res: any) => {
      if (res.success) {
        basicInfo.creditEstimateQuota = res.data;
      } else {
        basicInfo.creditEstimateQuota = 0;
      }
    });
  };

  //计算敞口限额
  const calculateExposeCreditLimit = () => {
    httpTool.post(calculateExposeCreditLimitUrl, basicInfo).then((res: any) => {
      if (res.success) {
        state.exposeCreditLimit = res.data;
      } else {
        state.exposeCreditLimit = 0;
      }
    });
  };
  const groupClientControlMethodChange = () => {
    getSubClientList();
  };
  const getSubClientList = () => {
    if (
      basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
      basicInfo.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
      basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL &&
      basicInfo.creditOrgStructureId > 0
    ) {
      httpTool
        .post(getStructureClientInfo, {
          clientClass: basicInfo.clientClass,
          orgStructureId: basicInfo.creditOrgStructureId
        })
        .then((res: any) => {
          if (res.success) {
            subCreditClientList.splice(0);
            res.data.forEach(element => {
              const _randomId = random();
              subCreditClientList.push({
                seqNo: "",
                clientId: element.clientId,
                clientHisId: element.clientHisId,
                clientCode: element.clientCode,
                clientName: element.clientName,
                creditType: "",
                refDetailSeqNo: "",
                creditVarietyCodes: [],
                creditVarietyList: [],
                creditVarietyNames: "",
                creditVarietyCode: "",
                creditControlType: "",
                creditUseType: "",
                quota: 0,
                guaranteeRatio: 0,
                creditQuota: 0,
                assureQuota: 0,
                pledgeQuota: 0,
                impawnQuota: 0,
                noCreditQuota: 0,
                addDetailFlag: yesOrNo.YES,
                _randomId: _randomId
              });
              editSubClientState[_randomId] = false;
            });
          }
        });
    } else {
      subCreditClientList.splice(0);
    }
  };

  //授信编号change
  const creditNoChange = (row: any) => {
    Object.assign(basicInfo, row);
    basicInfo.applyNo = "";
    basicInfo.originApplyId = row.id;
    basicInfo.originApplyNo = row.applyNo;
    basicInfo.creditNo = row.creditNo;
    basicInfo.id = -1;
    basicInfo.version = 0;
    basicInfo.checkStatus = "";
    basicInfo.projectCredit = row.projectCredit;
    closeEdit();
    row.subCreditClientList.forEach(element => {
      const elementData = {
        seqNo: element.seqNo,
        clientCode: element.clientCode,
        clientName: element.clientName,
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        creditType: element.creditType,
        refDetailSeqNo: element.refDetailSeqNo,
        creditVarietyCodes: element.creditVarietyCodes,
        creditVarietyList: element.creditVarietyList,
        creditVarietyNames: element.creditVarietyNames,
        creditVarietyCode: element.creditVarietyCode,
        creditControlType: element.creditControlType,
        creditUseType: element.creditUseType,
        quota: element.quota,
        guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
        creditQuota: element.creditQuota,
        assureQuota: element.assureQuota,
        pledgeQuota: element.pledgeQuota,
        impawnQuota: element.impawnQuota,
        noCreditQuota: element.noCreditQuota,
        nextVarietySeqNo: element.nextVarietySeqNo,
        nextClientSeqNo: element.nextClientSeqNo,
        addDetailFlag: yesOrNo.NO,
        _randomId: random()
      };
      subCreditClientList.push(elementData);
      editSubClientState[elementData._randomId] = false;
    });
    row.guaranteeDetailList.forEach(element => {
      const elementData = {
        currencyName: element.currencyName,
        currencyId: element.currencyId,
        currencyCode: element.currencyCode,
        clientId: element.clientId,
        clientCode: element.clientCode,
        clientName: element.clientName,
        collateralUseType: element.collateralUseType,
        collateralTypeId: element.collateralTypeId,
        collateralTypeCode: element.collateralTypeCode,
        collateralTypeName: element.collateralTypeName,
        guaranteeDesc: element.guaranteeDesc,
        _randomId: random()
      };
      guaranteeDetailList.push(elementData);
    });
    row.creditDetailList.forEach(element => {
      const elementData = {
        seqNo: element.seqNo,
        clientCode: element.clientCode,
        clientName: element.clientName,
        creditType: element.creditType,
        refDetailSeqNo: element.refDetailSeqNo,
        creditVarietyCodes: element.creditVarietyCodes,
        creditVarietyList: element.creditVarietyList,
        creditVarietyNames: element.creditVarietyNames,
        creditVarietyCode: element.creditVarietyCode,
        creditControlType: element.creditControlType,
        creditUseType: element.creditUseType,
        quota: element.quota,
        guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
        creditQuota: element.creditQuota,
        assureQuota: element.assureQuota,
        pledgeQuota: element.pledgeQuota,
        impawnQuota: element.impawnQuota,
        noCreditQuota: element.noCreditQuota,
        nextVarietySeqNo: element.nextVarietySeqNo,
        nextClientSeqNo: element.nextClientSeqNo,
        addDetailFlag: yesOrNo.NO,
        _randomId: random()
      };
      creditDetailList.push(elementData);
    });
    row.creditVarietyDetailList.forEach(element => {
      const elementData = {
        seqNo: element.seqNo,
        clientCode: element.clientCode,
        clientName: element.clientName,
        creditType: element.creditType,
        refDetailSeqNo: element.refDetailSeqNo,
        creditVarietyCodes: element.creditVarietyCodes,
        creditVarietyList: element.creditVarietyList,
        creditVarietyNames: element.creditVarietyNames,
        creditVarietyCode: element.creditVarietyCode,
        creditControlType: element.creditControlType,
        creditUseType: element.creditUseType,
        quota: element.quota,
        guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
        creditQuota: element.creditQuota,
        assureQuota: element.assureQuota,
        pledgeQuota: element.pledgeQuota,
        impawnQuota: element.impawnQuota,
        noCreditQuota: element.noCreditQuota,
        nextVarietySeqNo: element.nextVarietySeqNo,
        nextClientSeqNo: element.nextClientSeqNo,
        addDetailFlag: yesOrNo.NO,
        _randomId: random()
      };
      creditVarietyDetailList.push(elementData);
    });
    carryoverList.splice(0);
    //结转信息处理
    row.carryoverDetailDtoList.forEach(element => {
      const elementData = {
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        clientCode: element.clientCode,
        clientName: element.clientName,
        originCreditNo: element.originCreditNo,
        originCreditStyle: element.originCreditStyle,
        originCreditVersion: element.originCreditVersion,
        originCreditType: element.originCreditType,
        originCreditVarietyId: element.originCreditVarietyId,
        originCreditVarietyCode: element.originCreditVarietyCode,
        originCreditVarietyName: element.originCreditVarietyName,
        originGuaranteeRatio: element.originGuaranteeRatio,
        originEffectiveDate: element.originEffectiveDate,
        originEndDate: element.originEndDate,
        originDetailSeqNo: element.originDetailSeqNo,
        originCreditTerm: element.originCreditTerm,
        originCarryoverAmount: element.originCarryoverAmount,
        carryoverExchangeRate: element.carryoverExchangeRate,
        targetCarryoverAmount: element.targetCarryoverAmount,
        targetCreditNo: element.targetCreditNo,
        targetCreditVersion: element.targetCreditVersion,
        targetDetailSeqNo: element.targetDetailSeqNo,
        _randomId: random()
      };
      carryoverList.push(elementData);
    });
    carryoverSubClientDetailDtoList.splice(0);
    row.carryoverSubClientDetailDtoList.forEach(element => {
      const elementData = {
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        clientCode: element.clientCode,
        clientName: element.clientName,
        originCreditNo: element.originCreditNo,
        originCreditStyle: element.originCreditStyle,
        originCreditVersion: element.originCreditVersion,
        originCreditType: element.originCreditType,
        originCreditVarietyId: element.originCreditVarietyId,
        originCreditVarietyCode: element.originCreditVarietyCode,
        originCreditVarietyName: element.originCreditVarietyName,
        originGuaranteeRatio: element.originGuaranteeRatio,
        originEffectiveDate: element.originEffectiveDate,
        originEndDate: element.originEndDate,
        originDetailSeqNo: element.originDetailSeqNo,
        originCreditTerm: element.originCreditTerm,
        originCarryoverAmount: element.originCarryoverAmount,
        carryoverExchangeRate: element.carryoverExchangeRate,
        targetCarryoverAmount: element.targetCarryoverAmount,
        targetCreditNo: element.targetCreditNo,
        targetCreditVersion: element.targetCreditVersion,
        targetDetailSeqNo: element.targetDetailSeqNo,
        _randomId: random()
      };
      carryoverSubClientDetailDtoList.push(elementData);
    });
    basicInfo.addEffectiveDateCreateType = row.addEffectiveDateCreateType; //授信生效日规则
    basicInfo.genCreditLimitMethod = row.genCreditLimitMethod; //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    basicInfo.groupQuotaAssignTarget = row.groupQuotaAssignTarget; //下级单位额度分配权限归属
    if (basicInfo.fileRefId) {
      basicInfo.fileIdArr = basicInfo.fileRefId.split(","); // 如果后端返回的文件id字符串是用逗号拼接的
    } else {
      basicInfo.fileIdArr = [];
    }
    state.fileIdArr = basicInfo.fileIdArr;
    if (upload.value) {
      upload.value.init(basicInfo.fileIdArr);
    }
    creditLimitSetMethodTypeChange();
    initModifierData();
  };
  const clearCreditNo = () => {
    closeEdit();
    basicInfo.creditStyle = creditStyleType.SINGLE_LEGAL_CREDIT; //授信方式;1:单一法人授信；2：集团授信
    basicInfo.currencyType = currencyType.ONE_CURRENCY; //币种类型;1：单一币种；2：多币种
    basicInfo.targetCurrencyId = 1; //总额度币种Id
    basicInfo.targetCurrencyCode = "CNY"; //总额度币种编码
    basicInfo.targetCurrencyName = t("credit.creditmanage.creditapproved.currencyDefaultName"); //总额度币种名称
    basicInfo.clientClass = clientClass.INTER_CLIENT; //客户类型;1：内部客户；2：外部客户
    basicInfo.clientId = ""; //客户id
    basicInfo.clientHisId = -1; //客户历史id
    basicInfo.clientCode = ""; //客户编码
    basicInfo.clientName = ""; //客户名称
    basicInfo.industryCategoryId = ""; //行业类别id
    basicInfo.industryCategoryName = ""; //行业类别名称
    basicInfo.industryCategoryCode = ""; //行业类别编码
    basicInfo.enterpriseScale = ""; //企业规模
    basicInfo.originApplyId = -1;
    basicInfo.originApplyNo = "";
    basicInfo.creditNo = "";
    basicInfo.creditRatingId = ""; //信用等级评定Id
    basicInfo.creditRatingCode = ""; //信用等级评定code
    basicInfo.creditGradeId = ""; //信用等级Id
    basicInfo.creditGradeName = ""; //信用等级名称
    basicInfo.creditGradeCode = ""; //信用等级编码
    basicInfo.creditRatingScore = ""; //信用等级评估分值
    basicInfo.creditRatingRiskType = ""; //信用等级评估风险类型;1=高风险；2：低风险
    basicInfo.creditRatingEffectiveDate = ""; //信用等级评估生效日期
    basicInfo.creditRatingInvalidDate = ""; //信用等级评估失效日期
    basicInfo.creditOrgStructureId = ""; //授信组织架构Id
    basicInfo.creditOrgStructureCode = ""; //授信组织架构编码
    basicInfo.creditOrgStructureName = ""; //授信组织架构名称
    basicInfo.creditLimitSetMethod = ""; //授信额度设置方式;1=按总额度设置; 2=按担保方式设置 3=按信用非信用设置
    basicInfo.composeVarietyControlMethod = groupClientControlMethodType.NOT_CONTROL; //综合授信品种额度控制方式;0=不控制；1：按总额控制；2：按品种控制
    basicInfo.groupClientControlMethod = composeVarietyControlMethodType.NOT_CONTROL; //集团授信单位额度控制方式;0=不控制；1：按总额控制；2：按单位控制
    basicInfo.creditCategory = creditClassification.CENTRALIZED_CREDIT; //授信分类;1=集中授信；2=临时授信
    basicInfo.creditRiskType = creditRiskType.HIGH_RISK; //授信风险类型;1=高风险；2：低风险
    basicInfo.creditEstimateQuota = "0.00"; //授信测算风险限额
    basicInfo.creditQuota = "0.00"; //授信额度
    basicInfo.carryoverAmount = "0.00"; //结转金额
    basicInfo.exposeCreditLimit = "0.00"; //敞口授信额度
    basicInfo.effectiveDateActiveRule = ""; //授信生效日规则;1=授信核定审批通过日期;2=授信激活日期;3=自定义日期
    basicInfo.effectiveDate = ""; //授信生效日
    basicInfo.endDate = ""; //授信结束日
    basicInfo.creditTermMonth = monthType.TWELVE; //授信期限个月
    basicInfo.genCreditLimitMethod = state.businessRule.genCreditLimitMethod;
    basicInfo.groupQuotaAssignTarget = state.businessRule.groupQuotaAssignTarget;
    basicInfo.addEffectiveDateCreateType = state.businessRule.addEffectiveDateCreateType;
    initModifierData();
    setStartDate();
  };
  //决议编号放大镜
  const resolutionCodeChange = (row: any) => {
    basicInfo.resolutionId = row.resolutionId;
    basicInfo.resolutionCode = row.resolutionCode;
    basicInfo.resolutionDate = row.resolutionDate;
    basicInfo.resolutionMeetingDesc = row.resolutionMeetingDesc;
    basicInfo.resolutionResultDesc = row.resolutionResultDesc;
  };
  const clearResolutionCode = () => {
    basicInfo.resolutionId = -1;
    basicInfo.resolutionCode = "";
    basicInfo.resolutionDate = "";
    basicInfo.resolutionMeetingDesc = "";
    basicInfo.resolutionResultDesc = "";
  };

  const orgStructureDialog = shallowRef();
  const openClientDialog = () => {
    orgStructureDialog.value.openStructureDialog();
  };
  const fileBeforeDelete = (data: any) => {
    if (state.fileIdArr.includes(data.uid)) {
      FMessageBox.report(t("credit.creditmanage.creditapproved.fileIdArrTip"));
      return false;
    }
    return true;
  };
  const beforeBatchDelete = (data: any[]) => {
    for (let i = 0; i < data.length; i++) {
      if (state.fileIdArr.includes(data[i].uid)) {
        FMessageBox.report(t("credit.creditmanage.creditapproved.fileIdArrTip"));
        return false;
      }
      return true;
    }
  };
  const closeEdit = () => {
    if (subCreditClientGrid.value) {
      subCreditClientList.forEach(element => {
        subCreditClientGrid.value.closeEdit(element._randomId);
      });
    }
    if (guaranteeDetailGrid.value) {
      guaranteeDetailList.forEach(element => {
        guaranteeDetailGrid.value.closeEdit(element._randomId);
      });
    }
    if (creditDetailGrid.value) {
      creditDetailList.forEach(element => {
        creditDetailGrid.value.closeEdit(element._randomId);
      });
    }
    if (creditVarietyDetailGrid.value) {
      creditVarietyDetailList.forEach(element => {
        creditVarietyDetailGrid.value.closeEdit(element._randomId);
      });
    }
    subCreditClientList.splice(0);
    guaranteeDetailList.splice(0);
    creditDetailList.splice(0);
    creditVarietyDetailList.splice(0);
  };
  const collateralTypeList = reactive([]);
  const initCollateralTypeList = (row: any) => {
    httpTool.post(getCollateralTypeUrl, { collateralUseType: row.collateralUseType }).then((res: any) => {
      if (res.success && res.data) {
        collateralTypeList.splice(0);
        collateralTypeList.push(...res.data);
      }
    });
  };
  const beforeGuaranteeDetailopen = (scope: any) => {
    initCollateralTypeList(scope.row);
    return true;
  };

  // 开始日期和结束日期change事件,计算授信期限,计算规则：结束日-开始日+1
  const getCreditTerm = () => {
    if (basicInfo.effectiveDate !== "" && basicInfo.endDate !== ""! && isInstruct) {
      // 将日期字符串转换为 Date 对象
      const startDate = new Date(basicInfo.effectiveDate);
      const endDate = new Date(basicInfo.endDate);
      // 计算两个日期之间的毫秒差
      const timeDifference = endDate.getTime() - startDate.getTime();
      // 将毫秒差转换为天数
      const dayDifference = timeDifference / (1000 * 3600 * 24);
      // 返回天数差的绝对值
      basicInfo.creditTermDay = Math.abs(Math.round(dayDifference)) + 1;
    }
  };

  //项目放大镜变更事件
  const projectChange = (row: any) => {
    basicInfo.projectName = row.name;
    basicInfo.projectCode = row.code;
    basicInfo.projectId = row.id;
  };
  const clearProject = () => {
    basicInfo.projectId = null;
    basicInfo.projectCode = "";
    basicInfo.projectName = "";
  };

  // 是否指令来源
  const isInstruct = computed(() => basicInfo.creditDataSource === creditDataSource.FUND_MANAGEMENT_PLATFORM);
  const {
    //明细结转列表
    carryoverGrid,
    carryoverList,
    generalButtonOption,
    deleteCarryoverButton,
    //授信明细未结转
    notCarryoverGrid,
    notCarryoverList,
    generalNotCurryoverButtonOption,
    openCarryoverEditButton,
    carryoverButton,
    closeCarryoverEditButton,
    //明细操作
    openCarryover,
    //下级客户操作
    openSubClientCarryover,
    //下级客户处理
    deleteSubClientCarryoverButton,
    carryoverSubClientDetailGrid,
    generalCarryoverSubClientButtonOption,
    carryoverSubClientDetailDtoList,
    //下级客户未结转
    unCarryoverSubClientDetailGrid,
    unCarryoverSubClientDetailDtoList,
    generalUnCarryoverSubClientButtonOption,
    openSubClientCarryoverEditButton,
    closeSubClientCarryoverEditButton,
    carryoveSubClientButton,
    //结转校验
    validateDeleteDeatil
  } = useCarryover(basicInfo, state, creditDetailList, subCreditClientList);
  return {
    basicInfo,
    getDetailInfo: (row: any) => {
      getDetailInfo(basicInfo, row);
    },
    formValidator,
    form1,
    upload,
    generalSaveInfo,
    officeChange,
    currencyChange,
    //下级客户可编辑表格
    subCreditClientGrid,
    subCreditClientList,
    addSubCreditRow,
    beforeSubCreditClose,
    handleSubCreditSelect,
    clearSubCreditSelection,
    generalSubClientButtonOption,
    subClientClone,
    subClientDelete,
    //担保明细可编辑表格
    guaranteeDetailGrid,
    guaranteeDetailList,
    addGuaranteeDetailRow,
    beforeGuaranteeDetailClose,
    beforeGuaranteeDetailopen,
    //授信明细可编辑表格
    beforeCreditDetailClose,
    addCreditDetailRow,
    creditDetailList,
    creditDetailGrid,
    //授信品种明细
    creditVarietyDetailGrid,
    creditVarietyDetailList,
    addCreditVarietyDetailRow,
    beforeCreditVarietyDetailClose,
    //变更触发事件
    openCarryover,
    //组织架构事件
    creditOrgStructureChange,
    clearCreditOrgStructure,
    creditStyleTypeChange,
    //信用评级事件
    creditRatingCodeChange,
    clearCreditRatingCode,
    //客户变更事件,
    clientCodeChange,
    clearClientChange,
    //币种类型变更事件
    currencyTypeChange,
    //客户类型变更事件
    clientClassChange,
    paramsClientType,
    //公共处理信息
    state,
    detailState,
    //共享额度
    sharedQuota,
    //结转处理
    carryoverGrid,
    generalButtonOption,
    openCarryoverEditButton,
    closeCarryoverEditButton,
    deleteCarryoverButton,
    carryoverButton,
    carryoverList,
    notCarryoverGrid,
    generalNotCurryoverButtonOption,
    notCarryoverList,
    getOpenDate,
    getBusinessRuleSet,
    //获取结束日期
    getEndDate,
    creditDetailmagnifierData,
    creditClientmagnifierData,
    beforeCreditVarietyDetailOpen,
    beforeSubCreditOpen,
    selectVarietyData,
    grtCreditVarietyList,
    selectClientData,
    grtCreditClientList,
    beforeCreditVarietyDetailDelete,
    beforeCreditDetailDelete,
    submitMessage,
    saveMessage,
    calculateExposeCreditLimit,
    groupClientControlMethodChange,
    composeVarietyControlMethodChange,
    //授信编号change
    creditNoChange,
    clearCreditNo,
    //决议编号放大镜
    resolutionCodeChange,
    clearResolutionCode,
    //查看客户信息
    openClientDialog,
    orgStructureDialog,
    fileBeforeDelete,
    beforeBatchDelete,
    formTemporaryValidator,
    clientCodeDisabled,
    //下级客户操作
    openSubClientCarryover,
    //下级客户处理
    deleteSubClientCarryoverButton,
    carryoverSubClientDetailGrid,
    generalCarryoverSubClientButtonOption,
    carryoverSubClientDetailDtoList,
    //下级客户未结转
    unCarryoverSubClientDetailGrid,
    unCarryoverSubClientDetailDtoList,
    generalUnCarryoverSubClientButtonOption,
    openSubClientCarryoverEditButton,
    closeSubClientCarryoverEditButton,
    carryoveSubClientButton,
    //结转校验
    validateDeleteDeatil,
    //押品
    initCollateralTypeList,
    collateralTypeList,
    getCreditTerm,
    projectChange,
    clearProject,
    isInstruct,
    setBasicInfo,
    creditTypeDisable
  };
};
