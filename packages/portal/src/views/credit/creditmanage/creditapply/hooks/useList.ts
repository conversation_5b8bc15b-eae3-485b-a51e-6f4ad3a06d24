import { reactive, ref, computed, shallowRef, nextTick } from "vue";
import {
  deleteOneUrl,
  getCreditInvestigateReportInfoUrl,
  openDateUrl,
  submitListUrl,
  queryRatingDetailUrl,
  submitCreditRatingUrl,
  batchSubmitCreditRatingUrl
} from "../url";
import { goPage } from "./usePage";
import type { CreditApplyDto } from "../types";
import httpTool from "@/utils/http";
import { useI18n } from "vue-i18n";
import { useModelRange } from "@/hooks/conversion";
import { useConst } from "@ifs/support";
import { FMessageBox } from "@dtg/frontend-plus";

// 跳转新增页
const add = () => {
  goPage("add");
};

// 跳转修改页
const handleOpen = (row: CreditApplyDto) => {
  goPage("modify", { id: row.id });
};

export const useList = (basicInfo: CreditApplyDto) => {
  const { t } = useI18n();
  // 表格模板
  const queryTable = shallowRef();
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  //状态
  const statusEnum = useConst("cashmanage.CheckStatus");
  //业务状态
  const businessStatus = useConst("common.BusinessStatus");

  //返回结果提示信息
  const deleteResultConfim = {
    success: t("credit.creditmanage.creditapply.deleteSuccess"),
    fail: t("credit.creditmanage.creditapply.deleteFail")
  };

  const submitResultConfim = {
    success: t("credit.creditmanage.creditapply.submitSuccess"),
    fail: t("credit.creditmanage.creditapply.submitFail")
  };

  const queryForm = reactive({
    targetCurrencyIds: [],
    clientId: [],
    checkStatus: [],
    creditAmount: [],
    inputTime: [],
    creditNo: "",
    projectCredit: "",
    projectName: "",
    creditDataSource: []
  });

  // 已选列表
  const checkedList = ref<CreditApplyDto[]>([]);
  // 是否选中checkbox
  const isCheckedBatchSubmit = computed(
    () =>
      checkedList.value.length > 0 &&
      checkedList.value.every((row: CreditApplyDto) => [statusEnum.SAVE, statusEnum.REFUSE].includes(row.checkStatus))
  );

  const isChecked = computed(() => checkedList.value.length > 0);
  // 控制全选checkbox
  const selectableAll = () => {
    return true;
  };
  // 勾选checkbox
  const handleSelect = (row: CreditApplyDto[]) => {
    checkedList.value = row;
  };
  const clearSelection = () => {
    checkedList.value.splice(0);
  };
  // 批量删除的参数
  const gatherBatchParams = () => {
    return { list: checkedList.value };
  };

  const submitMessage = ref("");

  const beforrDeleteTrigger = () => {
    submitMessage.value = t("credit.creditmanage.creditapply.chooseDeleteTip", [checkedList.value.length]);
    return true;
  };

  const beforrSubmitTrigger = async () => {
    let result = true;
    let message = "";
    const creditRatingIds: { id: number }[] = [];
    for (const element of checkedList.value) {
      if (element.checkStatus === statusEnum.DRAFT) {
        result = false;
        break;
      }
      const res = await httpTool.post(queryRatingDetailUrl, { id: element.creditRatingId });
      if (res.data.businessStatus === businessStatus.SAVE || res.data.businessStatus === businessStatus.REFUSE) {
        message = message + element.creditNo + ",";
      }
      creditRatingIds.push({ id: element.creditRatingId });
    }
    if (message !== "" && message.length > 0) {
      await FMessageBox.confirm(message + t("credit.creditmanage.creditapply.submitCreditRatingTip")).then(async () => {
        await httpTool.post(batchSubmitCreditRatingUrl, { list: creditRatingIds }).then((res: any) => {
          if (res && res.error) {
            FMessageBox.alert(t("credit.creditmanage.creditapply.submitCreditRatingTip2") + res.message.description);
            result = false;
            return result;
          }
        });
      });
    }
    if (result) {
      submitMessage.value = t("credit.creditmanage.creditapply.chooseSubmitTip", [checkedList.value.length]);
    }

    return result;
  };

  const rowId = ref<number>();
  const detail = shallowRef();
  // 打开抽屉
  const handleOpenDetail = (row: CreditApplyDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  const generalButtonOption = (row: any) => {
    return reactive([
      {
        type: "modify",
        isShow: [statusEnum.SAVE, statusEnum.REFUSE, statusEnum.DRAFT].includes(row.checkStatus)
      },
      {
        type: "submit",
        isShow: [statusEnum.SAVE, statusEnum.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: submitListUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        },
        beforeTrigger: () => {
          return creditRatingValidate(row);
        }
      },
      {
        type: "remove",
        isShow: [statusEnum.SAVE, statusEnum.REFUSE, statusEnum.DRAFT].includes(row.checkStatus),
        submitComOpt: {
          url: deleteOneUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ]);
  };
  const creditRatingValidate = async (param: any) => {
    let result = true;
    const res = await httpTool.post(queryRatingDetailUrl, { id: param.creditRatingId });
    if (res.data.businessStatus === businessStatus.SAVE || res.data.businessStatus === businessStatus.REFUSE) {
      await FMessageBox.confirm(t("credit.creditmanage.creditapply.submitCreditRatingTip")).then(async () => {
        await httpTool
          .post(submitCreditRatingUrl, {
            id: basicInfo.creditRatingId
          })
          .then((res: any) => {
            if (res && res.error) {
              FMessageBox.alert(t("credit.creditmanage.creditapply.submitCreditRatingTip2") + res.message.description);
              result = false;
            }
          });
      });
      return result;
    }
  };
  // 批量删除成功回调
  const handleSuccess = (row: any) => {
    if (row.success) {
      handleSearch();
    }
  };

  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(openDateUrl).then((res: any) => {
        resolve([res.data.onlineDate, res.data.onlineDate]);
        nextTick(() => {
          queryTable.value.renderTableData();
        });
      });
    });
  };
  const { postParams } = useModelRange(["creditAmount", "inputTime"]);
  const confirmForm = ref();

  const beforeConfirm = async () => {
    let result = true;
    await confirmForm.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });

    return result;
  };
  const CreditInvestigateReport = (row: any) => {
    httpTool.post(getCreditInvestigateReportInfoUrl, { id: row.id }).then((res: any) => {
      if (res.success) {
        basicInfo.creditInvestigateReportInfo = res.data;
        basicInfo.linkFlag = true;
        basicInfo.checkStatus = row.checkStatus;
        goPage("report", basicInfo);
      }
    });
  };
  return {
    queryForm,
    add,
    isChecked,
    selectableAll,
    handleOpen,
    rowId,
    detail,
    queryTable,
    handleOpenDetail,
    handleSelect,
    generalButtonOption,
    gatherBatchParams,
    handleSearch,
    handleSuccess,
    clearSelection,
    getOpenDate,
    submitMessage,
    beforrDeleteTrigger,
    postParams,
    deleteResultConfim,
    beforrSubmitTrigger,
    submitResultConfim,
    confirmForm,
    beforeConfirm,
    CreditInvestigateReport,
    isCheckedBatchSubmit
  };
};

export default useList;
