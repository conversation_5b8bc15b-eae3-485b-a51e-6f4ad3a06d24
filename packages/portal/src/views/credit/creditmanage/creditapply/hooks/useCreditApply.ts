import type { CreditApplyDto } from "../types";
import { reactive, ref, h, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import { random } from "@/utils/uuid";
import { divide, multiply, subtract } from "@/utils/currency";
import {
  getBusinessRuleSetUrl,
  openDateUrl,
  calculateEstimateQuotaUrl,
  getStructureClientInfo,
  calculateExposeCreditLimitUrl,
  checkAssessRuleUrl,
  getCollateralTypeUrl,
  getRatingModel,
  getCreditLeverageSet,
  getNetAssets,
  submitCreditRatingUrl,
  queryRatingDetailUrl,
  checkSameCredit
} from "../url";
import { useI18n } from "vue-i18n";
import { useCreditApplyColumns } from "./useCreditApplyColumns";
import { useCarryover, useCarryoverColumns } from "../../common/hooks/useCarryover";
import httpTool from "@/utils/http";
import { formatDate } from "@/utils/date";
import { FMessageBox } from "@dtg/frontend-plus";
import { DtgConfirm } from "@dtg/frontend-plus-icons";
import { FIcon } from "@dtg/frontend-plus";
import { useConversion } from "@/hooks/conversion";
export const useCreditApply = () => {
  const { t } = useI18n();
  //授信方式
  const creditStyleType = useConst("credit.CreditStyleType");
  //客户类别
  const clientClass = useConst("credit.ClientClass");
  //授信分类
  const creditClassification = useConst("credit.CreditClassification");
  //授信额度设置方式
  const creditType = useConst("credit.CreditType");
  //授信风险类型
  const creditRiskType = useConst("credit.CreditRiskType");
  //授信使用类型
  const creditUseType = useConst("credit.CreditUseType");
  //币种类型
  const currencyType = useConst("credit.CurrencyType");
  //期限类型
  const monthType = useConst("credit.MonthType");
  //准入控制方式
  const creditControlType = useConst("credit.CreditControlType");
  //授信生效日规则
  const effectiveDateActiveRuleType = useConst("credit.EffectiveDateActiveRuleType");
  //集团授信单位额度控制方式
  const groupClientControlMethodType = useConst("credit.GroupClientControlMethodType");
  //授信额度设置方式
  const creditLimitSetMethodType = useConst("credit.CreditLimitSetMethodType");
  //综合授信品种额度控制方式
  const composeVarietyControlMethodType = useConst("credit.ComposeVarietyControlMethodType");
  //控制方式
  const genMaxLimitControlType = useConst("credit.GenMaxLimitControlType");
  //下级单位额度分配权限归属
  const groupQuotaAssignTargetType = useConst("credit.GroupQuotaAssignTargetType");
  //是否枚举
  const yesOrNo = useConst("counter.YesOrNo");
  //业务状态
  const businessStatus = useConst("common.BusinessStatus");
  //信用评级状态
  const creditRatingStatus = useConst("creditrating.CreditRatingStatus");
  const {
    creditDetailColumns2,
    creditDetailColumns1,
    creditDetailColumns3,
    creditDetailColumns4,
    creditDetailColumns5,
    creditDetailColumns6,
    creditDetailColumns7,
    creditDetailColumns8,
    creditDetailColumns9
  } = useCreditApplyColumns();
  const { convert } = useConversion([""]);
  const { notCarryoverDetailColumns, carryoverDetailColumns } = useCarryoverColumns();

  const basicInfo = reactive<CreditApplyDto>({
    id: -1, //主键
    businessType: "", //业务类型
    applyNo: "", //单据号
    creditNo: "", //授信编号
    creditVersion: "", //授信版本
    globalSerial: "", //全局流水号
    changeType: "", //变更类型;1：增加授信额度；2：延长授信期；3：增加授信品种
    originApplyId: "", //来源单据id
    originApplyNo: "", //来源单据号
    executeDate: "", //执行日
    checkStatus: "", //审批状态
    creditStyle: creditStyleType.SINGLE_LEGAL_CREDIT, //授信方式;1:单一法人授信；2：集团授信
    currencyType: currencyType.ONE_CURRENCY, //币种类型;1：单一币种；2：多币种
    targetCurrencyId: 1, //总额度币种Id
    targetCurrencyCode: "CNY", //总额度币种编码
    targetCurrencyName: t("credit.creditmanage.creditapply.currencyDefaultName"), //总额度币种名称
    clientClass: clientClass.INTER_CLIENT, //客户类型;1：内部客户；2：外部客户
    clientId: "", //客户id
    clientHisId: -1, //客户历史id
    clientCode: "", //客户编码
    clientName: "", //客户名称
    projectCredit: "", //是否项目授信
    projectId: "", //项目id
    projectCode: "", //项目编号
    projectName: "", //项目名称
    industryCategoryId: "", //行业类别id
    industryCategoryName: "", //行业类别名称
    industryCategoryCode: "", //行业类别编码
    enterpriseScale: "", //企业规模
    creditRatingId: "", //信用等级评定Id
    creditRatingCode: "", //信用等级评定code
    creditGradeId: "", //信用等级Id
    creditGradeName: "", //信用等级名称
    creditGradeCode: "", //信用等级编码
    creditRatingScore: "", //信用等级评估分值
    creditRatingRiskType: "", //信用等级评估风险类型;1:高风险；2：低风险
    creditRatingEffectiveDate: "", //信用等级评估生效日期
    creditRatingInvalidDate: "", //信用等级评估失效日期
    industryCategory: "", //所属行业
    creditAdjustmentFactor: "", // 信用调节系数(V)
    netAssets: "", // 净资产(E)
    industryCreditRatio: "", // 行业信贷比例(D)
    targetLeverageRatio: "", // 目标杠杆比例(K)
    referenceValueFormula: "CL=D*（E*K*V）", // 参考值计算公式
    creditReferenceValueResult: "", // 授信参考值结果
    totalProjectInvestment: "", //项目总投资（T）
    projectEquity: "", //项目资本金（E2）,
    projectFinancingRatio: "", //项目融资比例（S）
    creditOrgStructureId: "", //授信组织架构Id
    creditOrgStructureCode: "", //授信组织架构编码
    creditOrgStructureName: "", //授信组织架构名称
    creditLimitSetMethod: "", //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    composeVarietyControlMethod: groupClientControlMethodType.NOT_CONTROL, //综合授信品种额度控制方式;0:不控制；1：按总额控制；2：按品种控制
    groupClientControlMethod: composeVarietyControlMethodType.NOT_CONTROL, //集团授信单位额度控制方式;0:不控制；1：按总额控制；2：按单位控制
    creditCategory: creditClassification.CENTRALIZED_CREDIT, //授信分类;1:集中授信；2:临时授信
    creditRiskType: creditRiskType.HIGH_RISK, //授信风险类型;1:高风险；2：低风险
    creditEstimateQuota: "0.00", //授信测算风险限额
    creditQuota: "0.00", //授信额度
    carryoverAmount: "0.00", //结转金额
    exposeCreditLimit: "0.00", //敞口授信额度
    effectiveDateActiveRule: "", //授信生效日规则;1:授信核定审批通过日期;2:授信激活日期;3:自定义日期
    effectiveDate: "", //授信生效日
    endDate: "", //授信结束日
    creditTermMonth: monthType.TWELVE, //授信期限个月
    needResolutionFlag: "", //是否需要决议
    resolutionId: "", //决议ID
    resolutionCode: "", //决议编号
    resolutionDate: "", //决议日期
    resolutionMeetingDesc: "", //会议期次
    resolutionStatus: "", //决议状态;0：待决议；1：全票通过；2：非全票通过；11：不通过：
    resolutionResultDesc: "", //决议结果
    activeMethod: "", //激活方式;1:人工；2：自动
    activeDate: "", //激活日期
    stopReason: "", //终止原因
    alreadyUsed: "", //是否已被使用;用于过滤已经被使用的前置单据
    remarks: "", //备注
    previousDetailSeqNo: null,
    failedReason: "",
    fileRefId: "",
    version: 0, //乐观锁
    inputUserId: -1, //创建人ID
    inputUserName: "", //创建人名称
    inputTime: "", //创建时间
    modifyUserId: -1, //更新人ID
    modifyUserName: "", //更新人名称
    modifyTime: "", //更新时间
    transactionStatus: "", //分布式事务状态
    dataStatus: "", //数据状态
    tenantId: "", //租户标识
    subCreditClientList: [],
    guaranteeDetailList: [],
    creditDetailList: [],
    creditVarietyDetailList: [],
    carryoverDetailDtoList: [],
    carryoverSubClientDetailDtoList: [],
    fileIdArr: [],
    addApplyMandatoryFlag: "", //申请是否必须
    addEffectiveDateCreateType: "", //授信生效日规则
    changeApplyMandatoryFlag: "", //变更规则_授信变更申请是否必须
    genCreditAutoActivate: "", //授信是否自动激活
    genCreditLimitMethod: "", //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    genCreditRatingFlag: "", //通用规则-信用评级是否必须
    genMaxLimitControlType: "", //最高限额校验控制类型
    genMaxLimitValidPeriod: "", //最高额度有效期限类型
    groupQuotaAssignTarget: "", //下级单位额度分配权限归属
    creditRatingBusinessStatus: "", //信用评级单据状态
    creditDataSource: "", //数据来源
    cirId: "", //授信调查报告ID
    creditInvestigateReportInfo: null, // 授信调查报告信息
    instructCode: "", //指令号
    linkFlag: false,
    modifyCloseFlag: false, //是否修改页面关闭的授信调查报告
    applyQuato: "" //指令申请额度
  });

  const currencyRef = shallowRef();
  // 币种查询入参
  const currencyParams = reactive({
    officeId: basicInfo.officeId
  });

  // 机构下拉框
  const officeChange = (value: any, info: any) => {
    basicInfo.officeCode = info.officeCode;
    basicInfo.officeName = info.officeName;
    currencyParams.officeId = info.officeId;
    currencyRef.value.initRemoteData();
    basicInfo.targetCurrencyId = "";
    basicInfo.targetCurrencyCode = "";
    basicInfo.targetCurrencyName = "";
  };
  // 币种下拉框
  const currencyChange = (value: any, info: any) => {
    basicInfo.targetCurrencyId = info.currencyId;
    basicInfo.targetCurrencyCode = info.currencyCode;
    basicInfo.targetCurrencyName = info.currencyName;
  };
  const form1 = shallowRef();
  const submitMessage = ref("");
  const saveMessage = ref("");
  const handSubmitClick = () => {
    basicInfo.isSubmit = true;
  };
  // 点击提交 弹窗弹出之前校验整体表单项
  const formValidator = async (isSubmit: boolean) => {
    submitMessage.value = t("credit.creditmanage.creditapply.submitMessage");
    saveMessage.value = t("credit.creditmanage.creditapply.saveConfirm");
    let result = true;
    await form1.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    if (result) {
      if (editCheckEditing()) {
        FMessageBox.report(t("credit.creditmanage.creditapply.editCheckEditingTip"));
        return false;
      }
      if (creditDetailList.length <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditDetailListTip"));
        return false;
      }
      if (
        basicInfo.creditRatingRiskType === creditRiskType.LOW_RISK &&
        basicInfo.creditRiskType === creditRiskType.HIGH_RISK
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditRatingRiskTypeTip"));
        return false;
      }
      if (
        basicInfo.creditStyle !== creditStyleType.SINGLE_LEGAL_CREDIT &&
        creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditCategoryTip"));
        return false;
      }
      if (
        basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL &&
        creditDetailmagnifierData.length > 0
      ) {
        //校验未拆分明细
        if (checkDetailInfo()) {
          return false;
        }
      }
      if (
        basicInfo.carryoverAmount !== "" &&
        basicInfo.carryoverAmount !== null &&
        basicInfo.carryoverAmount > 0 &&
        basicInfo.creditQuota < basicInfo.carryoverAmount
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditQuotaTip"));
        return false;
      }
      if (subClientCheck()) {
        FMessageBox.report(t("credit.creditmanage.creditapply.subCreditClientListCodeTip"));
        return false;
      }
      let message = false;
      //控制限额
      if (Number(basicInfo.creditEstimateQuota) < Number(basicInfo.creditQuota)) {
        if (state.businessRule.genMaxLimitControlType === genMaxLimitControlType.STRONG) {
          FMessageBox.report(t("credit.creditmanage.creditapply.genMaxLimitControlTypeTip1"));
          return false;
        } else if (state.businessRule.genMaxLimitControlType === genMaxLimitControlType.SOFT) {
          submitMessage.value = t("credit.creditmanage.creditapply.genMaxLimitControlTypeTip2");
          saveMessage.value = t("credit.creditmanage.creditapply.genMaxLimitControlTypeTip3");
          message = true;
        }
      }
      const res = await httpTool.post(checkAssessRuleUrl, basicInfo);
      if (res.error) {
        FMessageBox.report(res.message.description);
        return false;
      } else {
        if (res.data && res.data !== "") {
          submitMessage.value = res.data;
          saveMessage.value = res.data;
          if (message) {
            submitMessage.value = t("credit.creditmanage.creditapply.genMaxLimitControlTypeTip") + submitMessage.value;
            saveMessage.value = t("credit.creditmanage.creditapply.genMaxLimitControlTypeTip") + saveMessage.value;
          }
        }
      }
      if (!(basicInfo.carryoverAmount !== "" && basicInfo.carryoverAmount !== null && basicInfo.carryoverAmount > 0)) {
        const checkSameRes = await httpTool.post(checkSameCredit, basicInfo);
        if (checkSameRes.success && checkSameRes.data !== null) {
          submitMessage.value = t("credit.creditmanage.creditapply.checkSameCreditTip", {
            creditNo: checkSameRes.data
          });
          saveMessage.value = t("credit.creditmanage.creditapply.checkSameCreditTip", {
            creditNo: checkSameRes.data
          });
        }
      }
      // 如果信用评级状态为已保存，则联动提交信用评级信息
      if (isSubmit) {
        if (basicInfo.cirId === undefined || basicInfo.cirId === "" || basicInfo.cirId === null) {
          FMessageBox.report(t("credit.creditmanage.creditapply.creditInvestigateReportNotSave"));
          return false;
        }
        const res = await httpTool.post(queryRatingDetailUrl, { id: basicInfo.creditRatingId });
        if (res.data.businessStatus === businessStatus.SAVE || res.data.businessStatus === businessStatus.REFUSE) {
          await FMessageBox.confirm(t("credit.creditmanage.creditapply.submitCreditRatingTip")).then(async () => {
            await httpTool
              .post(submitCreditRatingUrl, {
                id: basicInfo.creditRatingId
              })
              .then((res: any) => {
                if (res && res.error) {
                  FMessageBox.alert(
                    t("credit.creditmanage.creditapply.submitCreditRatingTip2") + res.message.description
                  );
                  result = false;
                }
              });
          });
          return true;
        }
      }
    }

    return result;
  };
  const subClientCheck = () => {
    if (
      basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
      state.businessRule.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
      basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL
    ) {
      for (let index = 0; index < subCreditClientList.length; index++) {
        if (subCreditClientList[index].refDetailSeqNo === "") {
          return true;
        }
      }
    }
    return false;
  };
  //附件
  const upload = ref();
  //上传附件返回的数组信息
  const fileMainInfos = ref<any[]>([]);
  //页面保存逻辑
  const generalSaveInfo = () => {
    if (upload.value.fileData) {
      fileMainInfos.value.splice(0);
      fileMainInfos.value.push(...upload.value.fileData);
      if (fileMainInfos.value.length > 0) {
        basicInfo.fileRefId = fileMainInfos.value.map((item: any) => item.id).join(",");
      }
    }
    if (
      basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
      state.businessRule.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
      basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL
    ) {
      basicInfo.subCreditClientList = subCreditClientList;
    } else {
      basicInfo.subCreditClientList = [];
    }
    basicInfo.guaranteeDetailList = guaranteeDetailList;
    basicInfo.creditDetailList = creditDetailList;
    basicInfo.creditVarietyDetailList = creditVarietyDetailList;
    basicInfo.carryoverDetailDtoList = carryoverList;
    basicInfo.carryoverSubClientDetailDtoList = carryoverSubClientDetailDtoList;
    return basicInfo;
  };
  //查询详情信息
  const getDetailInfo = (info: CreditApplyDto, row: any) => {
    Object.assign(info, row.data);
    if (basicInfo.clientClass === clientClass.INTER_CLIENT) {
      paramsClientType.value = 1;
    } else {
      paramsClientType.value = 2;
    }
    closeEdit();
    row.data.creditDetailList.forEach(element => {
      const elementData = {
        seqNo: element.seqNo,
        clientCode: element.clientCode,
        clientName: element.clientName,
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        creditType: element.creditType,
        refDetailSeqNo: element.refDetailSeqNo,
        creditVarietyCodes: element.creditVarietyCodes,
        creditVarietyList: element.creditVarietyList,
        creditVarietyNames: element.creditVarietyNames,
        creditVarietyCode: element.creditVarietyCode,
        creditControlType: element.creditControlType,
        creditUseType: element.creditUseType,
        quota: element.quota,
        guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
        creditQuota: element.creditQuota,
        assureQuota: element.assureQuota,
        pledgeQuota: element.pledgeQuota,
        impawnQuota: element.impawnQuota,
        noCreditQuota: element.noCreditQuota,
        nextVarietySeqNo: element.nextVarietySeqNo,
        nextClientSeqNo: element.nextClientSeqNo,
        _randomId: random()
      };
      creditDetailList.push(elementData);
      editNotState[elementData._randomId] = false;
    });
    row.data.creditVarietyDetailList.forEach(element => {
      const elementData = {
        seqNo: element.seqNo,
        clientCode: element.clientCode,
        clientName: element.clientName,
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        creditType: element.creditType,
        refDetailSeqNo: element.refDetailSeqNo,
        creditVarietyCodes: element.creditVarietyCodes,
        creditVarietyList: element.creditVarietyList,
        creditVarietyNames: element.creditVarietyNames,
        creditVarietyCode: element.creditVarietyCode,
        creditControlType: element.creditControlType,
        creditUseType: element.creditUseType,
        quota: element.quota,
        guaranteeRatio: Number(element.guaranteeRatio).toFixed(2),
        creditQuota: element.creditQuota,
        assureQuota: element.assureQuota,
        pledgeQuota: element.pledgeQuota,
        impawnQuota: element.impawnQuota,
        noCreditQuota: element.noCreditQuota,
        nextVarietySeqNo: element.nextVarietySeqNo,
        nextClientSeqNo: element.nextClientSeqNo,
        _randomId: random()
      };
      editVarietyNotState[elementData._randomId] = false;
      creditVarietyDetailList.push(elementData);
    });
    carryoverList.splice(0);
    //结转信息处理
    row.data.carryoverDetailDtoList.forEach(element => {
      const elementData = {
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        clientCode: element.clientCode,
        clientName: element.clientName,
        originCreditNo: element.originCreditNo,
        originCreditStyle: element.originCreditStyle,
        originCreditVersion: element.originCreditVersion,
        originCreditType: element.originCreditType,
        originCreditVarietyId: element.originCreditVarietyId,
        originCreditVarietyCode: element.originCreditVarietyCode,
        originCreditVarietyName: element.originCreditVarietyName,
        originGuaranteeRatio: element.originGuaranteeRatio,
        originEffectiveDate: element.originEffectiveDate,
        originEndDate: element.originEndDate,
        originDetailSeqNo: element.originDetailSeqNo,
        originCreditTerm: element.originCreditTerm,
        originCarryoverAmount: element.originCarryoverAmount,
        carryoverExchangeRate: element.carryoverExchangeRate,
        targetCarryoverAmount: element.targetCarryoverAmount,
        targetCreditNo: element.targetCreditNo,
        targetCreditVersion: element.targetCreditVersion,
        targetDetailSeqNo: element.targetDetailSeqNo,
        _randomId: random()
      };
      carryoverList.push(elementData);
    });
    carryoverSubClientDetailDtoList.splice(0);
    row.data.carryoverSubClientDetailDtoList.forEach(element => {
      const elementData = {
        clientId: element.clientId,
        clientHisId: element.clientHisId,
        clientCode: element.clientCode,
        clientName: element.clientName,
        originCreditNo: element.originCreditNo,
        originCreditStyle: element.originCreditStyle,
        originCreditVersion: element.originCreditVersion,
        originCreditType: element.originCreditType,
        originCreditVarietyId: element.originCreditVarietyId,
        originCreditVarietyCode: element.originCreditVarietyCode,
        originCreditVarietyName: element.originCreditVarietyName,
        originGuaranteeRatio: element.originGuaranteeRatio,
        originEffectiveDate: element.originEffectiveDate,
        originEndDate: element.originEndDate,
        originDetailSeqNo: element.originDetailSeqNo,
        originCreditTerm: element.originCreditTerm,
        originCarryoverAmount: element.originCarryoverAmount,
        carryoverExchangeRate: element.carryoverExchangeRate,
        targetCarryoverAmount: element.targetCarryoverAmount,
        targetCreditNo: element.targetCreditNo,
        targetCreditVersion: element.targetCreditVersion,
        targetDetailSeqNo: element.targetDetailSeqNo,
        _randomId: random()
      };
      carryoverSubClientDetailDtoList.push(elementData);
    });

    state.businessRule.groupQuotaAssignTarget = basicInfo.groupQuotaAssignTarget;
    state.businessRule.addApplyMandatoryFlag = basicInfo.addApplyMandatoryFlag; //申请是否必须
    state.businessRule.changeApplyMandatoryFlag = basicInfo.changeApplyMandatoryFlag; //变更规则_授信变更申请是否必须
    state.businessRule.genCreditAutoActivate = basicInfo.genCreditAutoActivate; //授信是否自动激活
    state.businessRule.genCreditLimitMethod = basicInfo.genCreditLimitMethod; //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
    if (info.fileRefId) {
      info.fileIdArr = info.fileRefId.split(","); // 如果后端返回的文件id字符串是用逗号拼接的
    } else {
      info.fileIdArr = [];
    }
    if (upload.value) {
      upload.value.init(info.fileIdArr);
    }
    creditLimitSetMethodTypeChange();
    initModifierData();
  };

  //操作列按钮逻辑
  const editSubClientState = reactive<Record<string, boolean>>({});
  const subClientClone = (scope: any) => {
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (editSubClientState[subCreditClientList[index]._randomId]) {
        FMessageBox.report(t("credit.creditmanage.creditapply.subCreditClientListCloneTip"));
        return false;
      }
    }
    subCreditClientList.push({
      seqNo: "",
      clientId: scope.row.clientId,
      clientHisId: scope.row.clientHisId,
      clientCode: scope.row.clientCode,
      clientName: scope.row.clientName,
      creditType: scope.row.creditType,
      refDetailSeqNo: scope.row.refDetailSeqNo,
      creditVarietyCodes: scope.row.creditVarietyCodes,
      creditVarietyList: scope.row.creditVarietyList,
      creditVarietyNames: scope.row.creditVarietyNames,
      creditVarietyCode: scope.row.creditVarietyCode,
      creditControlType: scope.row.creditControlType,
      creditUseType: scope.row.creditUseType,
      quota: scope.row.quota,
      guaranteeRatio: scope.row.guaranteeRatio,
      creditQuota: scope.row.creditQuota,
      assureQuota: scope.row.assureQuota,
      pledgeQuota: scope.row.pledgeQuota,
      impawnQuota: scope.row.impawnQuota,
      noCreditQuota: scope.row.noCreditQuota,
      _randomId: random()
    });
    editSubClientState[subCreditClientList[subCreditClientList.length - 1]._randomId] = true;
    subCreditClientGrid.value.openEdit(subCreditClientList[subCreditClientList.length - 1]._randomId);
  };
  const subClientDelete = (scope: any) => {
    if (!validateDeleteDeatil(scope.row)) {
      return false;
    }
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (scope.row._randomId === subCreditClientList[index]._randomId) {
        subCreditClientGrid.value.closeEdit(scope.row._randomId);
        subCreditClientList.splice(index, 1);
        break;
      }
    }
    editSubClientState[scope.row._randomId] = false;
  };
  const generalSubClientButtonOption = (scope: any) => {
    return [
      {
        type: "edit",
        buttonText: t("credit.creditmanage.creditapply.edit"),
        isShow: !editSubClientState[scope.row._randomId],
        emitName: "on-edit",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "clone",
        buttonText: t("credit.creditmanage.creditapply.clone"),
        isShow: !editSubClientState[scope.row._randomId],
        emitName: "on-clone",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "close",
        buttonText: t("credit.creditmanage.creditapply.save"),
        isShow: editSubClientState[scope.row._randomId],
        emitName: "on-close",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "delete",
        buttonText: t("credit.creditmanage.creditapply.delete"),
        emitName: "on-delete",
        originalProps: {
          type: "danger",
          icon: null
        }
      }
    ];
  };
  //下级客户授信可编辑表格
  const subCreditClientGrid = shallowRef();
  const subCreditClientList = reactive<any[]>([]);
  const addSubCreditRow = () => {
    subCreditClientList.push({
      seqNo: "",
      clientCode: "",
      clientName: "",
      creditType: creditType.COMPOSITE_CREDIT,
      refDetailSeqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyNames: "",
      creditVarietyCode: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0,
      _randomId: random()
    });
    editSubClientState[subCreditClientList[subCreditClientList.length - 1]._randomId] = true;
    subCreditClientGrid.value.openEdit(subCreditClientList[subCreditClientList.length - 1]._randomId);
  };
  const beforeSubCreditOpen = (scope: any) => {
    grtCreditClientList(scope.row.refDetailSeqNo);
    editSubClientState[scope.row._randomId] = true;
    subCreditClientGrid.value.openEdit(subCreditClientList[scope.$index]._randomId);
  };
  const beforeSubCreditClose = (scope: any) => {
    if (scope.row.refDetailSeqNo === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.refSubClientDetailSeqNoTip"));
      return false;
    }
    if (scope.row.clientCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.clientCodeSeqNoTip"));
      return false;
    }
    getNextNo(scope.row.refDetailSeqNo, true);
    if (
      scope.row.creditType !== creditType.SPECIAL_CREDIT &&
      creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
    ) {
      FMessageBox.report(t("credit.creditmanage.creditapply.creditCategoryDetailTip"));
      return false;
    }
    if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (scope.row.creditQuota <= 0 && scope.row.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditTip"));
        return false;
      }
      if (
        scope.row.noCreditQuota > state.refSeqNoElement.noCreditQuota &&
        scope.row.creditQuota > state.refSeqNoElement.creditQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeSubGtTip"));
        return false;
      }
    } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        scope.row.assureQuota <= 0 &&
        scope.row.creditQuota <= 0 &&
        scope.row.pledgeQuota <= 0 &&
        scope.row.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeTip"));
        return false;
      }
      if (
        scope.row.assureQuota > state.refSeqNoElement.assureQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota ||
        scope.row.pledgeQuota > state.refSeqNoElement.pledgeQuota ||
        scope.row.impawnQuota > state.refSeqNoElement.impawnQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeSubGtTip"));
        return false;
      }
    } else {
      if (scope.row.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.quotaTip"));
        return false;
      }
      if (scope.row.quota > state.refSeqNoElement.quota) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeSubGtTip"));
        return false;
      }
    }
    if (scope.row.creditVarietyCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.creditVarietyCodeTip"));
      return false;
    }
    if (checkClientData(subCreditClientList, scope.row)) {
      return false;
    }
    if (scope.row.seqNo === "") {
      getNextNo(scope.row.refDetailSeqNo, true);
      const nextClientSeqNo = state.nextSeqNo + 1;
      scope.row.seqNo =
        scope.row.refDetailSeqNo + (nextClientSeqNo > 10 ? "-0" + nextClientSeqNo : "-00" + nextClientSeqNo);
      updateNextNo(nextClientSeqNo, scope.row.refDetailSeqNo, true);
    }
    editSubClientState[scope.row._randomId] = false;
    subCreditClientGrid.value.closeEdit(subCreditClientList[scope.$index]._randomId);
  };
  const updateNextNo = (nextSeqNo: string, refDetailSeqNo: string, isClient: boolean) => {
    creditDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        if (isClient) {
          element.nextClientSeqNo = nextSeqNo;
        } else {
          element.nextVarietySeqNo = nextSeqNo;
        }
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        element.nextClientSeqNo = nextSeqNo;
      }
    });
  };

  const getNextNo = (refDetailSeqNo: string, isClient: boolean) => {
    creditDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        state.refSeqNoElement = element;
        if (isClient) {
          state.nextSeqNo = element.nextClientSeqNo;
        } else {
          state.nextSeqNo = element.nextVarietySeqNo;
        }
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (refDetailSeqNo === element.seqNo) {
        state.nextSeqNo = element.nextClientSeqNo;
        state.refSeqNoElement = element;
      }
    });
  };
  // 已选列表
  const checkedSubCreditList = reactive<any[]>([]);
  // 勾选checkbox
  const handleSubCreditSelect = (row: any[]) => {
    if (row.length > 0) {
      checkedSubCreditList.splice(0);
      checkedSubCreditList.push(...row);
    }
  };
  //清除选项
  const clearSubCreditSelection = () => {
    checkedSubCreditList.splice(0);
  };

  //担保信息可编辑表格
  const guaranteeDetailGrid = shallowRef();
  const guaranteeDetailList = reactive<any[]>([]);
  const addGuaranteeDetailRow = () => {
    guaranteeDetailList.push({
      currencyName: "",
      currencyId: "",
      currencyCode: "",
      clientId: "",
      clientCode: "",
      clientName: "",
      collateralUseType: "",
      collateralTypeId: "",
      collateralTypeCode: "",
      collateralTypeName: "",
      _randomId: random()
    });
    guaranteeDetailGrid.value.openEdit(guaranteeDetailList[guaranteeDetailList.length - 1]._randomId);
  };
  const beforeGuaranteeDetailClose = (scope: any) => {
    if (scope.row.currencyName === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.currencyNameTip"));
      return false;
    }
    if (scope.row.clientName === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.clientNameTip"));
      return false;
    }
    if (scope.row.collateralUseType === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.collateralUseTypeTip"));
      return false;
    }
    if (scope.row.collateralTypeName === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.collateralTypeNameTip"));
      return false;
    }
    guaranteeDetailGrid.value.closeEdit(guaranteeDetailList[scope.$index]._randomId);
  };
  let rowIndex = 0;
  //授信明细可编辑表格
  const creditDetailGrid = shallowRef();
  const creditDetailList = reactive<any[]>([]);
  const addCreditDetailRow = () => {
    creditDetailList.push({
      creditType: creditType.SPECIAL_CREDIT,
      seqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyCode: "",
      creditVarietyNames: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0,
      nextVarietySeqNo: 0,
      nextClientSeqNo: 0,
      _randomId: random()
    });
    creditDetailGrid.value.openEdit(creditDetailList[creditDetailList.length - 1]._randomId);
    editNotState[creditDetailList[creditDetailList.length - 1]._randomId] = true;
    rowIndex++;
  };

  const beforeCreditDetailDelete = (scope: any) => {
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (
        subCreditClientList[index].refDetailSeqNo === scope.row.seqNo &&
        subCreditClientList[index].refDetailSeqNo !== "" &&
        scope.row.seqNo !== ""
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditDetailDeleteTip"));
        return false;
      }
    }
    for (let index = 0; index < creditVarietyDetailList.length; index++) {
      if (
        creditVarietyDetailList[index].refDetailSeqNo === scope.row.seqNo &&
        creditVarietyDetailList[index].refDetailSeqNo !== "" &&
        scope.row.seqNo !== ""
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditDetailDeleteTip"));
        return false;
      }
    }
    if (!validateDeleteDeatil(scope.row)) {
      return false;
    }
    return true;
  };
  const beforeCreditDetailClose = (scope: any) => {
    if (
      scope.row.creditType !== creditType.SPECIAL_CREDIT &&
      creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
    ) {
      FMessageBox.report(t("credit.creditmanage.creditapply.creditCategoryDetailTip"));
      return false;
    }
    if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (scope.row.creditQuota <= 0 && scope.row.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditTip"));
        return false;
      }
    } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        scope.row.assureQuota <= 0 &&
        scope.row.creditQuota <= 0 &&
        scope.row.pledgeQuota <= 0 &&
        scope.row.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeTip"));
        return false;
      }
    } else {
      if (scope.row.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.quotaTip"));
        return false;
      }
    }
    if (scope.row.creditVarietyCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.creditVarietyCodeTip"));
      return false;
    }
    if (checkVerData(creditDetailList, scope.row)) {
      return false;
    }
    // if (scope.row.seqNo === undefined || scope.row.seqNo === "") {
    //   const previousDetailSeqNo =
    //     basicInfo.previousDetailSeqNo === null ? 1 : Number(basicInfo.previousDetailSeqNo) + 1;
    //   basicInfo.previousDetailSeqNo = previousDetailSeqNo;
    //   creditDetailList[scope.$index].seqNo =
    //     previousDetailSeqNo < 10 ? "0" + previousDetailSeqNo : String(previousDetailSeqNo);
    // }
    creditDetailGrid.value.closeEdit(creditDetailList[scope.$index]._randomId);
    initModifierData();
    updateSubInfoData();
    calculateQuota();
  };

  const checkClientData = (dataList: any[], data) => {
    const compareData = data.clientCode + data.refDetailSeqNo;
    const checkedList = [];
    dataList.forEach(element => {
      checkedList.push(element.clientCode + element.refDetailSeqNo);
    });
    let count = 0;
    for (let index = 0; index < checkedList.length; index++) {
      if (checkedList[index] === compareData) {
        count += 1;
        if (count > 1) {
          FMessageBox.report(t("credit.creditmanage.creditapply.checkClientDataTip"));
          return true;
        }
      }
    }

    return false;
  };

  const checkVerData = (dataList: any[], data: any) => {
    const checkList = [];
    const checkedList = [];
    dataList.forEach(element => {
      element.creditVarietyList.forEach(e => {
        checkList.push(e.creditVarietyCode + element.guaranteeRatio);
        checkedList.push(element.creditType + e.creditVarietyCode);
      });
    });
    for (let index = 0; index < data.creditVarietyList.length; index++) {
      let count = 0;
      const compore = data.creditType + data.creditVarietyList[index].creditVarietyCode;
      for (let j = 0; j < checkedList.length; j++) {
        if (checkedList[j] === compore) {
          count += 1;
          if (count > 1) {
            FMessageBox.report(t("credit.creditmanage.creditapply.checkVerDataTip"));
            return true;
          }
        }
      }
    }
    return false;
  };

  const checkVerCodeData = (dataList: any[], data) => {
    const checkList = [];
    dataList.forEach(element => {
      element.creditVarietyList.forEach(e => {
        checkList.push(element.refDetailSeqNo + e.creditVarietyCode);
      });
    });
    for (let i = 0; i < data.creditVarietyList.length; i++) {
      const compareData = data.refDetailSeqNo + data.creditVarietyList[i].creditVarietyCode;
      let count = 0;
      for (let index = 0; index < checkList.length; index++) {
        if (checkList[index] === compareData) {
          count += 1;
          if (count > 1) {
            FMessageBox.report(t("credit.creditmanage.creditapply.checkVerCodeDataTip"));
            return true;
          }
        }
      }
    }
    return false;
  };

  const checkDetailInfo = () => {
    //记录已经拆分了的授信明细序号
    const checkedList = [];
    //授信明细记录
    const checkList = [];
    //品种已拆分记录
    const checkVarietyList = [];
    creditVarietyDetailList.forEach(element => {
      checkedList.push(element.refDetailSeqNo);
    });
    creditDetailList.forEach(element => {
      if (checkedList.includes(element.seqNo)) {
        if (creditType.COMPOSITE_CREDIT === element.creditType) {
          element.creditVarietyList.forEach(e => {
            checkList.push(element.seqNo + e.creditVarietyCode);
          });
        }
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        element.creditVarietyList.forEach(e => {
          checkVarietyList.push(element.refDetailSeqNo + e.creditVarietyCode);
        });
      }
    });
    for (let index = 0; index < checkList.length; index++) {
      if (!checkVarietyList.includes(checkList[index])) {
        FMessageBox.report(t("credit.creditmanage.creditapply.checkDetailInfoTip"));
        return true;
      }
    }
    if (
      basicInfo.composeVarietyControlMethod === composeVarietyControlMethodType.VARIETY_CONTROL &&
      creditVarietyDetailList.length === 0
    ) {
      for (let index = 0; index < creditDetailList.length; index++) {
        if (creditType.COMPOSITE_CREDIT === creditDetailList[index].creditType) {
          FMessageBox.report(t("credit.creditmanage.creditapply.creditVarietyDetailListLengthTip"));
          return true;
        }
      }
    }
    return false;
  };

  const formTemporaryValidator = () => {
    if (editCheckEditing()) {
      FMessageBox.report(t("credit.creditmanage.creditapply.editCheckEditingTip"));
      return false;
    }
    return true;
  };

  //可编辑表格校验
  const editCheckEditing = () => {
    if (creditDetailGrid.value.isEditing()) {
      return true;
    }
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (editSubClientState[subCreditClientList[index]._randomId]) {
        return true;
      }
    }
    if (creditVarietyDetailGrid.value && creditVarietyDetailGrid.value.isEditing()) {
      return true;
    }
    return false;
  };

  const creditDetailmagnifierData = reactive<any[]>([]);
  const creditClientmagnifierData = reactive<any[]>([]);

  const initModifierData = () => {
    //重新处理放大镜数据
    creditDetailmagnifierData.splice(0);
    creditDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        creditDetailmagnifierData.push(element);
      }
    });

    //记录已经拆分了的授信明细序号
    const checkedList = [];
    creditVarietyDetailList.forEach(element => {
      checkedList.push(element.refDetailSeqNo);
    });
    creditClientmagnifierData.splice(0);
    creditDetailList.forEach(element => {
      if (creditType.COMPOSITE_CREDIT === element.creditType) {
        if (basicInfo.composeVarietyControlMethod === composeVarietyControlMethodType.NOT_CONTROL) {
          creditClientmagnifierData.push(element);
        } else if (!checkedList.includes(element.seqNo)) {
          creditClientmagnifierData.push(element);
        }
      } else {
        creditClientmagnifierData.push(element);
      }
    });
    if (basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL) {
      creditVarietyDetailList.forEach(element => {
        if (creditType.COMPOSITE_CREDIT === element.creditType) {
          creditClientmagnifierData.push(element);
        }
      });
    }
  };

  const updateSubInfoData = () => {
    //重新处理放大镜数据
    creditDetailList.forEach(element => {
      creditVarietyDetailList.forEach(subEle => {
        if (element.seqNo === subEle.refDetailSeqNo) {
          subEle.creditUseType = element.creditUseType;
          subEle.creditControlType = element.creditControlType;
          subEle.guaranteeRatio = element.guaranteeRatio;
        }
      });
      subCreditClientList.forEach(subEle => {
        if (element.seqNo === subEle.refDetailSeqNo) {
          subEle.creditUseType = element.creditUseType;
          subEle.creditControlType = element.creditControlType;
          subEle.guaranteeRatio = element.guaranteeRatio;
        }
      });
    });
    creditVarietyDetailList.forEach(element => {
      subCreditClientList.forEach(subEle => {
        if (element.seqNo === subEle.refDetailSeqNo) {
          subEle.creditUseType = element.creditUseType;
          subEle.creditControlType = element.creditControlType;
          subEle.guaranteeRatio = element.guaranteeRatio;
        }
      });
    });
  };

  //授信品种明细可编辑表格
  const creditVarietyDetailGrid = shallowRef();
  const creditVarietyDetailList = reactive<any[]>([]);
  const addCreditVarietyDetailRow = () => {
    creditVarietyDetailList.push({
      seqNo: "",
      creditType: creditType.COMPOSITE_CREDIT,
      refDetailSeqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyCode: "",
      creditVarietyNames: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0,
      nextClientSeqNo: 0,
      _randomId: random()
    });

    creditVarietyDetailGrid.value.openEdit(creditVarietyDetailList[creditVarietyDetailList.length - 1]._randomId);
    editVarietyNotState[creditVarietyDetailList[creditVarietyDetailList.length - 1]._randomId] = true;
  };
  const selectVarietyData = reactive([]);
  const grtCreditVarietyList = (seqNo: string) => {
    selectVarietyData.splice(0);
    creditDetailList.forEach(element => {
      if (seqNo === element.seqNo) {
        selectVarietyData.push(...element.creditVarietyList);
      }
    });
  };
  const selectClientData = reactive([]);
  const grtCreditClientList = (seqNo: string) => {
    selectClientData.splice(0);
    creditDetailList.forEach(element => {
      if (seqNo === element.seqNo) {
        selectClientData.push(...element.creditVarietyList);
      }
    });
    creditVarietyDetailList.forEach(element => {
      if (seqNo === element.seqNo) {
        selectClientData.push(...element.creditVarietyList);
      }
    });
  };

  //授信品种明细校验
  const beforeCreditVarietyDetailDelete = (scope: any) => {
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (
        subCreditClientList[index].refDetailSeqNo === scope.row.seqNo &&
        subCreditClientList[index].refDetailSeqNo !== "" &&
        scope.row.seqNo !== ""
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.varietyDetailDeleteTip"));
        return false;
      }
    }
    initModifierData();
    return true;
  };
  const beforeCreditVarietyDetailClose = (scope: any) => {
    if (scope.row.refDetailSeqNo === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.refDetailSeqNoTip"));
      return false;
    }
    getNextNo(scope.row.refDetailSeqNo, false);
    if (
      scope.row.creditType !== creditType.SPECIAL_CREDIT &&
      creditClassification.TEMPORARY_CREDIT === basicInfo.creditCategory
    ) {
      FMessageBox.report(t("credit.creditmanage.creditapply.creditCategoryDetailTip"));
      return false;
    }
    if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (scope.row.creditQuota <= 0 && scope.row.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditTip"));
        return false;
      }
      if (
        scope.row.noCreditQuota > state.refSeqNoElement.noCreditQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeGtTip"));
        return false;
      }
    } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        scope.row.assureQuota <= 0 &&
        scope.row.creditQuota <= 0 &&
        scope.row.pledgeQuota <= 0 &&
        scope.row.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeTip"));
        return false;
      }
      if (
        scope.row.assureQuota > state.refSeqNoElement.assureQuota ||
        scope.row.creditQuota > state.refSeqNoElement.creditQuota ||
        scope.row.pledgeQuota > state.refSeqNoElement.pledgeQuota ||
        scope.row.impawnQuota > state.refSeqNoElement.impawnQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeGtTip"));
        return false;
      }
    } else {
      if (scope.row.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.quotaTip"));
        return false;
      }
      if (scope.row.quota > state.refSeqNoElement.quota) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeGtTip"));
        return false;
      }
    }
    if (scope.row.creditVarietyCode === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.creditVarietyCodeTip"));
      return false;
    }
    if (checkVerCodeData(creditVarietyDetailList, scope.row)) {
      return false;
    }
    //下级客户引用授信明细校验
    for (let index = 0; index < subCreditClientList.length; index++) {
      if (subCreditClientList[index].refDetailSeqNo === scope.row.refDetailSeqNo) {
        FMessageBox.report(t("credit.creditmanage.creditapply.subClientDetailShowTip"));
        return false;
      }
    }
    if (scope.row.seqNo === "") {
      getNextNo(scope.row.refDetailSeqNo, false);
      const nextVarietySeqNo = state.nextSeqNo + 1;
      scope.row.seqNo =
        scope.row.refDetailSeqNo + (nextVarietySeqNo >= 10 ? "-0" + nextVarietySeqNo : "-00" + nextVarietySeqNo);
      updateNextNo(nextVarietySeqNo, scope.row.refDetailSeqNo, false);
    }
    creditVarietyDetailGrid.value.closeEdit(creditVarietyDetailList[scope.$index]._randomId);
    initModifierData();
  };

  const beforeCreditVarietyDetailOpen = (scope: any) => {
    grtCreditVarietyList(scope.row.refDetailSeqNo);
    creditVarietyDetailGrid.value.openEdit(creditVarietyDetailList[scope.$index]._randomId);
  };

  //共享额度
  const sharedQuota = () => {
    if (state.subClientDetail.refDetailSeqNo === "") {
      FMessageBox.report(t("credit.creditmanage.creditapply.refDetailSeqNoGtTip"));
      return false;
    }
    if (checkedSubCreditList.length === 0) {
      FMessageBox.report(t("credit.creditmanage.creditapply.checkedSubCreditListTip"));
      return false;
    }
    getNextNo(state.subClientDetail.refDetailSeqNo, true);
    if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      if (state.subClientDetail.creditQuota <= 0 && state.refSeqNoElement.noCreditQuota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.creditTip"));
        return false;
      }
      if (
        state.subClientDetail.noCreditQuota > state.refSeqNoElement.noCreditQuota &&
        state.subClientDetail.creditQuota > state.refSeqNoElement.creditQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeSubGtTip"));
        return false;
      }
    } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      if (
        state.subClientDetail.assureQuota <= 0 &&
        state.subClientDetail.creditQuota <= 0 &&
        state.subClientDetail.pledgeQuota <= 0 &&
        state.subClientDetail.impawnQuota <= 0
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeTip"));
        return false;
      }
      if (
        state.subClientDetail.assureQuota > state.refSeqNoElement.assureQuota ||
        state.subClientDetail.creditQuota > state.refSeqNoElement.creditQuota ||
        state.subClientDetail.pledgeQuota > state.refSeqNoElement.pledgeQuota ||
        state.subClientDetail.impawnQuota > state.refSeqNoElement.impawnQuota
      ) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeSubGtTip"));
        return false;
      }
    } else {
      if (state.subClientDetail.quota <= 0) {
        FMessageBox.report(t("credit.creditmanage.creditapply.quotaTip"));
        return false;
      }
      if (state.subClientDetail.quota > state.refSeqNoElement.quota) {
        FMessageBox.report(t("credit.creditmanage.creditapply.guaranteeSubGtTip"));
        return false;
      }
    }
    FMessageBox.confirm(t("credit.creditmanage.creditapply.sharedQuotaConfirm"), "Warning", {
      confirmButtonText: t("credit.creditmanage.creditapply.doconfirm"),
      cancelButtonText: t("credit.creditmanage.creditapply.docancel"),
      title: t("credit.creditmanage.creditapply.sharedQuota"),
      customClass: "tree-card-message",
      buttonSize: "default",
      message: h("div", { class: "el-tree-card__messagebox" }, [
        h(FIcon, { size: 50 }, [h(DtgConfirm)]),
        h("span", null, t("credit.creditmanage.creditapply.sharedQuotaConfirm"))
      ])
    }).then(() => {
      let nextClientSeqNo = state.nextSeqNo;
      checkedSubCreditList.forEach(element => {
        element.refDetailSeqNo = state.refSeqNoElement.seqNo;
        element.creditVarietyCodes = state.refSeqNoElement.creditVarietyCodes;
        element.creditVarietyList = state.refSeqNoElement.creditVarietyList;
        element.creditType = state.refSeqNoElement.creditType;
        element.creditVarietyCode = state.refSeqNoElement.creditVarietyCode;
        element.creditVarietyNames = state.refSeqNoElement.creditVarietyNames;
        element.creditControlType = state.refSeqNoElement.creditControlType;
        element.creditUseType = state.refSeqNoElement.creditUseType;
        if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
          element.creditQuota = state.subClientDetail.creditQuota;
          element.noCreditQuota = state.subClientDetail.noCreditQuota;
        } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
          element.creditQuota = state.subClientDetail.creditQuota;
          element.assureQuota = state.subClientDetail.assureQuota;
          element.pledgeQuota = state.subClientDetail.pledgeQuota;
          element.impawnQuota = state.subClientDetail.impawnQuota;
        } else {
          element.quota = state.subClientDetail.quota;
          element.guaranteeRatio = state.refSeqNoElement.guaranteeRatio;
        }
        nextClientSeqNo += 1;
        if (element.seqNo === "") {
          element.seqNo =
            state.subClientDetail.refDetailSeqNo +
            (nextClientSeqNo > 10 ? "-0" + nextClientSeqNo : "-00" + nextClientSeqNo);
        }
      });
      updateNextNo(nextClientSeqNo, state.refSeqNoElement.seqNo, true);
      FMessageBox.report({
        type: "success",
        message: t("credit.creditmanage.creditapply.sharedQuotaSuccess")
      });
      checkedSubCreditList.splice(0);
      state.subClientDetail.creditQuota = 0;
      state.subClientDetail.assureQuota = 0;
      state.subClientDetail.pledgeQuota = 0;
      state.subClientDetail.impawnQuota = 0;
      state.subClientDetail.quota = 0;
      state.subClientDetail.noCreditQuota = 0;
      subCreditClientGrid.value.table.clearSelection();
    });
  };
  //基础信息change
  const creditLimitSetMethodTypeChange = () => {
    if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
      detailState.columns = creditDetailColumns3;
      detailState.varietyDetailcolumns = creditDetailColumns6;
      detailState.subClientColumns = creditDetailColumns9;
    } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
      detailState.columns = creditDetailColumns2;
      detailState.varietyDetailcolumns = creditDetailColumns5;
      detailState.subClientColumns = creditDetailColumns8;
    } else {
      detailState.columns = creditDetailColumns1;
      detailState.varietyDetailcolumns = creditDetailColumns4;
      detailState.subClientColumns = creditDetailColumns7;
    }
    if (state.businessRule.genCreditLimitMethod !== creditLimitSetMethodType.TOTAL_LIMIT) {
      for (let index = 1; index < state.carryoverInfo.notCarryoverDetailColumns.length; index++) {
        if (state.carryoverInfo.notCarryoverDetailColumns[index].prop === "originGuaranteeRatio") {
          state.carryoverInfo.notCarryoverDetailColumns.splice(index, 1);
        }
      }
      for (let index = 1; index < state.carryoverInfo.carryoverDetailColumns.length; index++) {
        if (state.carryoverInfo.carryoverDetailColumns[index].prop === "originGuaranteeRatio") {
          state.carryoverInfo.carryoverDetailColumns.splice(index, 1);
        }
      }
    }
  };
  //组织架构事件
  const creditOrgStructureChange = (row: any) => {
    basicInfo.creditOrgStructureId = row.id; //授信组织架构Id
    basicInfo.creditOrgStructureCode = row.orgStructureCode; //授信组织架构编码
    basicInfo.creditOrgStructureName = row.orgStructureName; //授信组织架构名称
    subCreditClientList.splice(0);
    getSubClientList();
  };
  const creditStyleTypeChange = () => {
    if (basicInfo.creditStyle === creditStyleType.SINGLE_LEGAL_CREDIT) {
      creditVarietyDetailList.splice(0);
      subCreditClientList.splice(0);
    } else {
      getSubClientList();
    }
    initModifierData();
  };
  const clearCreditOrgStructure = () => {
    basicInfo.creditOrgStructureId = ""; //授信组织架构Id
    basicInfo.creditOrgStructureCode = ""; //授信组织架构编码
    basicInfo.creditOrgStructureName = ""; //授信组织架构名称
    subCreditClientList.splice(0);
  };

  //信用评级事件
  const creditRatingCodeChange = (row: any) => {
    basicInfo.creditRatingId = row.id; //信用等级评定Id
    basicInfo.creditRatingCode = row.businessCode; //信用等级评定code
    if (
      row.businessStatus === businessStatus.SAVE ||
      row.businessStatus === businessStatus.REFUSE ||
      row.businessStatus === businessStatus.APPROVING ||
      row.creditRatingStatus == creditRatingStatus.PENDING_APPROVAL
    ) {
      basicInfo.creditGradeName = row.adjustCreditLevel; //信用等级名称
      basicInfo.creditRatingScore = row.adjustScore; //信用等级评估分值
    } else {
      basicInfo.creditGradeName = row.declarationCreditLevel; //信用等级名称
      basicInfo.creditRatingScore = row.initialScore; //信用等级评估分值
    }
    basicInfo.creditGradeCode = row.approvedLevelCode; //信用等级编码
    basicInfo.industryCategory = row.industryName;
    basicInfo.creditRatingEffectiveDate = row.ratingEffectiveDate; //信用等级评估生效日期
    basicInfo.creditRatingInvalidDate = row.ratingExpirationDate; //信用等级评估失效日期
    basicInfo.creditRatingBusinessStatus = row.businessStatus;
    //通过评级模型编号查询评级模型信息
    httpTool.post(getRatingModel, { modelCode: row.ratingModelCode }).then((res: any) => {
      if (res.success) {
        res.data.levels.forEach((item: any) => {
          if (item.creditLevel === row.declarationCreditLevel) {
            basicInfo.creditAdjustmentFactor = item.creditAdjustCoefficient;
          }
        });
      }
    });
    //通过行业ID 查询杠杆比例
    httpTool.post(getCreditLeverageSet, { industryId: row.industryId }).then((res: any) => {
      if (res.success && res.data != null) {
        basicInfo.targetLeverageRatio = res.data.leverageRatio;
        basicInfo.industryCreditRatio = multiply(divide(res.data.loanCreditRatioM, res.data.loanCreditRatioD, 4), 100);
      }
    });
    //查询净资产 并计算授信参考值结果
    httpTool.post(getNetAssets, { clientId: basicInfo.clientId }).then((res: any) => {
      if (res.success) {
        basicInfo.netAssets = res.data;
        basicInfo.creditReferenceValueResult = divide(
          multiply(
            basicInfo.industryCreditRatio,
            multiply(multiply(basicInfo.netAssets, basicInfo.targetLeverageRatio), basicInfo.creditAdjustmentFactor)
          ),
          100
        );
      }
    });
  };
  const clearCreditRatingCode = () => {
    basicInfo.creditRatingId = ""; //信用等级评定Id
    basicInfo.creditRatingCode = ""; //信用等级评定code
    basicInfo.creditGradeId = ""; //信用等级Id
    basicInfo.creditGradeName = ""; //信用等级名称
    basicInfo.creditGradeCode = ""; //信用等级编码
    basicInfo.creditRatingScore = ""; //信用等级评估分值
    basicInfo.creditRatingRiskType = ""; //信用等级评估风险类型;1:高风险；2：低风险
    basicInfo.creditRatingEffectiveDate = ""; //信用等级评估生效日期
    basicInfo.creditRatingInvalidDate = ""; //信用等级评估失效日期
  };

  //客户变更事件
  const clientCodeChange = (row: any) => {
    basicInfo.clientId = row.clientId;
    basicInfo.clientHisId = row.clientHisId;
    basicInfo.clientCode = row.clientCode;
    basicInfo.clientName = row.clientName;
    basicInfo.industryCategoryId = row.industryCategoryId; //行业类别id
    basicInfo.industryCategoryName = row.industryCategoryName; //行业类别名称
    basicInfo.industryCategoryCode = row.industryCategoryCode; //行业类别编码
    basicInfo.enterpriseScale = row.enterpriseScale; //企业规模
    clearCreditOrgStructure();
    calculateEstimateQuota();
    calculateExposeCreditLimit();
    carryoverSubClientDetailDtoList.splice(0);
    carryoverList.splice(0);
  };
  const clearClientChange = () => {
    basicInfo.clientId = "";
    basicInfo.clientHisId = "";
    basicInfo.clientCode = "";
    basicInfo.clientName = "";
    basicInfo.industryCategoryId = ""; //行业类别id
    basicInfo.industryCategoryName = ""; //行业类别名称
    basicInfo.industryCategoryCode = ""; //行业类别编码
    basicInfo.enterpriseScale = ""; //企业规模
    carryoverSubClientDetailDtoList.splice(0);
    carryoverList.splice(0);
  };
  //币种类型变更事件
  const currencyTypeChange = () => {
    if (basicInfo.currencyType === currencyType.MANY_CURRENCY) {
      basicInfo.targetCurrencyId = 1;
      basicInfo.targetCurrencyCode = "CNY";
      basicInfo.targetCurrencyName = t("credit.creditmanage.creditapply.currencyDefaultName");
    }
  };

  const paramsClientType = ref(1); //中客户类型参数
  //客户类型变更事件
  const clientClassChange = () => {
    if (basicInfo.clientClass === clientClass.INTER_CLIENT) {
      paramsClientType.value = 1;
    } else {
      paramsClientType.value = 2;
    }
    clearClientChange();
  };

  const composeVarietyControlMethodChange = () => {
    if (basicInfo.composeVarietyControlMethod === composeVarietyControlMethodType.NOT_CONTROL) {
      subCreditClientList.splice(0);
      creditVarietyDetailList.splice(0);
      initModifierData();
    }
  };

  //公共处理信息
  const state = reactive({
    subClientDetail: {
      seqNo: "",
      creditType: creditType.COMPOSITE_CREDIT,
      refDetailSeqNo: "",
      creditVarietyCodes: [],
      creditVarietyList: [],
      creditVarietyNames: "",
      creditVarietyCode: "",
      creditControlType: creditControlType.STRONG,
      creditUseType: creditUseType.DISPOSABLE,
      quota: 0,
      guaranteeRatio: 0,
      creditQuota: 0,
      assureQuota: 0,
      pledgeQuota: 0,
      impawnQuota: 0,
      noCreditQuota: 0
    },
    //查看客户信息
    clientInfoDetailDialog: false,
    clientInfo: [],
    currentNodeKey: -1,
    clientDto: { clientCode: "", clientName: "" },
    //结转数据
    carryoverInfo: {
      showDialog: false,
      showSubClientDialog: false,
      notCarryoverDetailColumns: notCarryoverDetailColumns,
      carryoverDetailColumns: carryoverDetailColumns
    },
    exposeCreditLimit: 0,
    refSeqNoElement: null,
    nextSeqNo: 0,
    //公共校验规则
    businessRule: {
      addApplyMandatoryFlag: "", //申请是否必须
      addApplyNeedResolutionType: "", //授信申请是否需要决议
      addEffectiveDateCreateType: "", //授信生效日规则
      changeApplyMandatoryFlag: "", //变更规则_授信变更申请是否必须
      changeApplyNeedResolutionRule: "", //变更规则_授信变更申请决议规则
      changeApplyNeedResolutionType: "", //变更规则_授信变更申请是否需要决议
      genCreditAutoActivate: "", //授信是否自动激活
      genCreditLimitMethod: "", //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
      genCreditRatingFlag: "", //通用规则-信用评级是否必须
      genMaxLimitControlType: "", //最高限额校验控制类型
      genMaxLimitValidPeriod: "", //最高额度有效期限类型
      groupQuotaAcceptFlag: "", //下级单位额度分配是否需财务公司受理
      groupQuotaAssignTarget: "" //下级单位额度分配权限归属
    }
  });

  //授信明细动态列表
  const detailState = reactive({
    columns: creditDetailColumns1,
    varietyDetailcolumns: creditDetailColumns4,
    subClientColumns: creditDetailColumns7
  });

  const openDate = ref();
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      openDate.value = res.data.onlineDate;
      basicInfo.executeDate = res.data.onlineDate;
      setStartDate();
    });
  };
  const setStartDate = () => {
    if (basicInfo.effectiveDate === "" || basicInfo.endDate === "") {
      if (effectiveDateActiveRuleType.CUSTOM === state.businessRule.addEffectiveDateCreateType) {
        basicInfo.effectiveDate = openDate.value;
      } else {
        basicInfo.effectiveDate = "";
        basicInfo.endDate = "";
      }
    }
  };

  const getEndDate = () => {
    if (basicInfo.effectiveDate !== "") {
      //根据期限计算结束日期
      const monthTime = new Date(basicInfo.effectiveDate);
      const newTime = monthTime.setMonth(monthTime.getMonth() + basicInfo.creditTermMonth);
      basicInfo.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
    }
  };
  //获取业务规则设置
  const getBusinessRuleSet = (isModify: boolean) => {
    httpTool.post(getBusinessRuleSetUrl).then((res: any) => {
      if (res.success) {
        if (!isModify) {
          Object.assign(state.businessRule, res.data);
          basicInfo.addApplyMandatoryFlag = res.data.addApplyMandatoryFlag; //申请是否必须
          basicInfo.changeApplyMandatoryFlag = res.data.changeApplyMandatoryFlag; //变更规则_授信变更申请是否必须
          basicInfo.genCreditAutoActivate = res.data.genCreditAutoActivate; //授信是否自动激活
          basicInfo.genCreditLimitMethod = res.data.genCreditLimitMethod; //授信额度设置方式;1:按总额度设置; 2:按担保方式设置 3:按信用非信用设置
          state.businessRule.groupQuotaAssignTarget = res.data.groupQuotaAssignTarget;
          basicInfo.groupQuotaAssignTarget = res.data.groupQuotaAssignTarget;
        }
        state.businessRule.genCreditRatingFlag = res.data.genCreditRatingFlag;
        basicInfo.genCreditRatingFlag = res.data.genCreditRatingFlag; //通用规则-信用评级是否必须
        state.businessRule.addEffectiveDateCreateType = res.data.addEffectiveDateCreateType; //授信生效日规则
        state.businessRule.genMaxLimitControlType = res.data.genMaxLimitControlType; //最高限额校验控制类型
        state.businessRule.genMaxLimitValidPeriod = res.data.genMaxLimitValidPeriod; //最高额度有效期限类型

        basicInfo.effectiveDateActiveRule = res.data.addEffectiveDateCreateType; //授信生效日规则
        basicInfo.genMaxLimitControlType = res.data.genMaxLimitControlType; //最高限额校验控制类型
        basicInfo.genMaxLimitValidPeriod = res.data.genMaxLimitValidPeriod; //最高额度有效期限类型
        creditLimitSetMethodTypeChange();
      }
    });
  };
  const calculateQuota = () => {
    let quota = 0;
    creditDetailList.forEach(element => {
      if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT) {
        quota = quota + element.noCreditQuota + element.creditQuota;
      } else if (state.businessRule.genCreditLimitMethod === creditLimitSetMethodType.GUARANTEE_METHOD) {
        quota = quota + element.assureQuota + element.creditQuota + element.pledgeQuota + element.impawnQuota;
      } else {
        quota += element.quota;
      }
    });
    basicInfo.creditQuota = quota;
    basicInfo.exposeCreditLimit = quota - state.exposeCreditLimit;
  };
  //计算风险限额
  const calculateEstimateQuota = () => {
    httpTool.post(calculateEstimateQuotaUrl, basicInfo).then((res: any) => {
      if (res.success) {
        basicInfo.creditEstimateQuota = res.data;
      } else {
        basicInfo.creditEstimateQuota = 0;
      }
    });
  };

  //计算敞口限额
  const calculateExposeCreditLimit = () => {
    httpTool.post(calculateExposeCreditLimitUrl, basicInfo).then((res: any) => {
      if (res.success) {
        state.exposeCreditLimit = res.data;
      } else {
        state.exposeCreditLimit = 0;
      }
    });
  };
  const groupClientControlMethodChange = () => {
    getSubClientList();
  };
  const getSubClientList = () => {
    if (
      basicInfo.creditStyle === creditStyleType.GROUP_CREDIT &&
      state.businessRule.groupQuotaAssignTarget === groupQuotaAssignTargetType.FINANCE &&
      basicInfo.groupClientControlMethod !== groupClientControlMethodType.NOT_CONTROL &&
      basicInfo.creditOrgStructureId > 0
    ) {
      httpTool
        .post(getStructureClientInfo, {
          clientClass: basicInfo.clientClass,
          orgStructureId: basicInfo.creditOrgStructureId
        })
        .then((res: any) => {
          if (res.success) {
            subCreditClientList.splice(0);
            res.data.forEach(element => {
              const _randomId = random();
              subCreditClientList.push({
                seqNo: "",
                clientId: element.clientId,
                clientHisId: element.clientHisId,
                clientCode: element.clientCode,
                clientName: element.clientName,
                creditType: "",
                refDetailSeqNo: "",
                creditVarietyCodes: [],
                creditVarietyList: [],
                creditVarietyNames: "",
                creditVarietyCode: "",
                creditControlType: "",
                creditUseType: "",
                quota: 0,
                guaranteeRatio: 0,
                creditQuota: 0,
                assureQuota: 0,
                pledgeQuota: 0,
                impawnQuota: 0,
                noCreditQuota: 0,
                _randomId: _randomId
              });
              editSubClientState[_randomId] = false;
            });
          }
        });
    } else {
      subCreditClientList.splice(0);
    }
  };
  const orgStructureDialog = shallowRef();
  const openClientDialog = () => {
    orgStructureDialog.value.openStructureDialog();
  };
  const closeEdit = () => {
    if (subCreditClientGrid.value) {
      subCreditClientList.forEach(element => {
        subCreditClientGrid.value.closeEdit(element._randomId);
      });
    }
    if (guaranteeDetailGrid.value) {
      guaranteeDetailList.forEach(element => {
        guaranteeDetailGrid.value.closeEdit(element._randomId);
      });
    }
    if (creditDetailGrid.value) {
      creditDetailList.forEach(element => {
        creditDetailGrid.value.closeEdit(element._randomId);
      });
    }
    if (creditVarietyDetailGrid.value) {
      creditVarietyDetailList.forEach(element => {
        creditVarietyDetailGrid.value.closeEdit(element._randomId);
      });
    }
    subCreditClientList.splice(0);
    guaranteeDetailList.splice(0);
    creditDetailList.splice(0);
    creditVarietyDetailList.splice(0);
  };
  const collateralTypeList = reactive([]);
  const initCollateralTypeList = (row: any) => {
    httpTool.post(getCollateralTypeUrl, { collateralUseType: row.collateralUseType }).then((res: any) => {
      if (res.success && res.data) {
        collateralTypeList.splice(0);
        collateralTypeList.push(...res.data);
      }
    });
  };
  const beforeGuaranteeDetailopen = (scope: any) => {
    initCollateralTypeList(scope.row);
    return true;
  };
  //项目放大镜变更事件
  const projectChange = (row: any) => {
    basicInfo.projectName = row.projectName;
    basicInfo.projectCode = row.projectCode;
    basicInfo.projectId = row.id;
    basicInfo.totalProjectInvestment = row.projectTotalInvestment;
    basicInfo.projectEquity = row.projectCapitalFund;
  };
  const clearProject = () => {
    basicInfo.projectId = null;
    basicInfo.projectCode = "";
    basicInfo.projectName = "";
    basicInfo.totalProjectInvestment = "";
    basicInfo.projectEquity = "";
  };
  const projectCreditChange = () => {
    if (basicInfo.projectCredit === yesOrNo.YES) {
      basicInfo.referenceValueFormula = "CL=（T-E2)*V*S";
      projectFinancingRatioChange();
    } else {
      basicInfo.referenceValueFormula = "CL=D*（E*K*V）";
      basicInfo.projectId = "";
      basicInfo.projectName = "";
      basicInfo.projectCode = "";
      basicInfo.creditReferenceValueResult = divide(
        multiply(
          basicInfo.industryCreditRatio,
          multiply(multiply(basicInfo.netAssets, basicInfo.targetLeverageRatio), basicInfo.creditAdjustmentFactor)
        ),
        100
      );
    }
  };
  const projectFinancingRatioChange = () => {
    basicInfo.creditReferenceValueResult = divide(
      multiply(
        multiply(subtract(basicInfo.totalProjectInvestment, basicInfo.projectEquity), basicInfo.creditAdjustmentFactor),
        basicInfo.projectFinancingRatio
      ),
      100
    );
  };
  // 开始日期和结束日期change事件,计算授信期限,计算规则：结束日-开始日+1
  const getCreditTerm = () => {
    if (basicInfo.effectiveDate !== "" && basicInfo.endDate !== "") {
      // 将日期字符串转换为 Date 对象
      const startDate = new Date(basicInfo.effectiveDate);
      const endDate = new Date(basicInfo.endDate);
      // 计算两个日期之间的毫秒差
      const timeDifference = endDate.getTime() - startDate.getTime();
      // 将毫秒差转换为天数
      const dayDifference = timeDifference / (1000 * 3600 * 24);
      // 返回天数差的绝对值
      basicInfo.creditTermDay = Math.abs(Math.round(dayDifference)) + 1;
    }
  };
  // 授信调查报告关闭页面后原页面值回显
  const setBasicInfo = (info: any) => {
    if (info !== undefined) {
      Object.assign(basicInfo, convert(info));
      Object.assign(creditDetailList, convert(basicInfo.creditDetailList));
      Object.assign(creditVarietyDetailList, convert(basicInfo.creditVarietyDetailList));
      if (basicInfo.fileIdArr === null || basicInfo.fileIdArr.length === 0) {
        if (basicInfo.fileRefId !== null) {
          basicInfo.fileIdArr = basicInfo.fileRefId.split(",");
          if (upload.value) {
            upload.value.init(basicInfo.fileIdArr);
          }
        }
      }
      initModifierData();
    }
  };
  const editNotState = reactive<any>({});
  const generalDetailButtonOption = (scope: any) => {
    return [
      {
        type: "edit",
        buttonText: t("credit.creditmanage.creditapply.btnModify"),
        isShow: !editNotState[scope.row._randomId],
        emitName: "on-edit",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "close",
        buttonText: t("credit.creditmanage.creditapply.btnSave"),
        isShow: editNotState[scope.row._randomId],
        emitName: "on-close",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "remove",
        buttonText: t("credit.creditmanage.creditapply.btnRemove"),
        isShow: true,
        emitName: "on-remove",
        originalProps: {
          type: "danger",
          icon: null
        }
      }
    ];
  };
  // 删除详情
  const deleteDetail = (scope: any) => {
    const result = beforeCreditDetailDelete(scope);
    if (result !== undefined && !result) {
      return;
    }
    creditDetailGrid.value.closeEdit(scope.row._randomId);
    creditDetailList.splice(scope.$index, 1);
    editNotState[scope.row._randomId] = false;
    // 重新为 seqNo 赋值
    creditDetailList.forEach((item, index) => {
      item.seqNo = (index + 1).toString().padStart(2, "0");
    });
    rowIndex--;
    calculateQuota();
  }; //点击编辑
  const openDetailEdit = (scope: any) => {
    creditDetailGrid.value.openEdit(scope.row._randomId);
    editNotState[scope.row._randomId] = true;
    // 关闭其他编辑
    creditDetailList.forEach(item => {
      if (item._randomId !== scope.row._randomId) {
        creditDetailGrid.value.closeEdit(item._randomId);
        editNotState[item._randomId] = false;
      }
    });
  };
  // 关闭详情编辑
  const closeDetailEdit = (scope: any) => {
    const result = beforeCreditDetailClose(scope);
    if (result !== undefined && !result) {
      return;
    }
    if (!scope.row.seqNo) {
      scope.row.seqNo = rowIndex.toString().padStart(2, "0");
    }
    creditDetailGrid.value.closeEdit(scope.row._randomId);
    editNotState[scope.row._randomId] = false;
  };
  const editVarietyNotState = reactive<any>({});
  const generalVarietyDetailButtonOption = (scope: any) => {
    return [
      {
        type: "edit",
        buttonText: t("credit.creditmanage.creditapply.btnModify"),
        isShow: !editVarietyNotState[scope.row._randomId],
        emitName: "on-edit",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "close",
        buttonText: t("credit.creditmanage.creditapply.btnSave"),
        isShow: editVarietyNotState[scope.row._randomId],
        emitName: "on-close",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "remove",
        buttonText: t("credit.creditmanage.creditapply.btnRemove"),
        isShow: true,
        emitName: "on-remove",
        originalProps: {
          type: "danger",
          icon: null
        }
      }
    ];
  };
  // 删除详情
  const deleteVarietyDetail = (scope: any) => {
    beforeCreditVarietyDetailDelete(scope);
    creditVarietyDetailGrid.value.closeEdit(scope.row._randomId);
    creditVarietyDetailList.splice(scope.$index, 1);
    editVarietyNotState[scope.row._randomId] = false;
  };
  //点击编辑
  const openVarietyDetailEdit = (scope: any) => {
    grtCreditVarietyList(scope.row.refDetailSeqNo);
    initModifierData();
    creditVarietyDetailGrid.value.openEdit(scope.row._randomId);
    editVarietyNotState[scope.row._randomId] = true;
    // 关闭其他编辑
    creditVarietyDetailList.forEach(item => {
      if (item._randomId !== scope.row._randomId) {
        creditVarietyDetailGrid.value.closeEdit(item._randomId);
        editVarietyNotState[item._randomId] = false;
      }
    });
  };
  // 关闭详情编辑
  const closeVarietyDetailEdit = (scope: any) => {
    const result = beforeCreditVarietyDetailClose(scope);
    if (result !== undefined && !result) {
      return;
    }
    creditVarietyDetailGrid.value.closeEdit(scope.row._randomId);
    editVarietyNotState[scope.row._randomId] = false;
  };
  const {
    //明细结转列表
    carryoverGrid,
    carryoverList,
    generalButtonOption,
    deleteCarryoverButton,
    //授信明细未结转
    notCarryoverGrid,
    notCarryoverList,
    generalNotCurryoverButtonOption,
    openCarryoverEditButton,
    carryoverButton,
    closeCarryoverEditButton,
    //明细操作
    openCarryover,
    //下级客户操作
    openSubClientCarryover,
    //下级客户处理
    deleteSubClientCarryoverButton,
    carryoverSubClientDetailGrid,
    generalCarryoverSubClientButtonOption,
    carryoverSubClientDetailDtoList,
    //下级客户未结转
    unCarryoverSubClientDetailGrid,
    unCarryoverSubClientDetailDtoList,
    generalUnCarryoverSubClientButtonOption,
    openSubClientCarryoverEditButton,
    closeSubClientCarryoverEditButton,
    carryoveSubClientButton,
    //结转校验
    validateDeleteDeatil
  } = useCarryover(basicInfo, state, creditDetailList, subCreditClientList);
  return {
    basicInfo,
    getDetailInfo: (row: any) => {
      getDetailInfo(basicInfo, row);
    },
    formValidator,
    form1,
    upload,
    generalSaveInfo,
    officeChange,
    currencyChange,
    //下级客户可编辑表格
    subCreditClientGrid,
    subCreditClientList,
    addSubCreditRow,
    beforeSubCreditClose,
    handleSubCreditSelect,
    clearSubCreditSelection,
    //担保明细可编辑表格
    guaranteeDetailGrid,
    guaranteeDetailList,
    addGuaranteeDetailRow,
    beforeGuaranteeDetailClose,
    beforeGuaranteeDetailopen,
    //授信明细可编辑表格
    beforeCreditDetailClose,
    addCreditDetailRow,
    creditDetailList,
    creditDetailGrid,
    //授信品种明细
    creditVarietyDetailGrid,
    creditVarietyDetailList,
    addCreditVarietyDetailRow,
    beforeCreditVarietyDetailClose,
    //变更触发事件
    openCarryover,
    //组织架构事件
    creditOrgStructureChange,
    clearCreditOrgStructure,
    creditStyleTypeChange,
    //信用评级事件
    creditRatingCodeChange,
    clearCreditRatingCode,
    //客户变更事件,
    clientCodeChange,
    clearClientChange,
    //币种类型变更事件
    currencyTypeChange,
    //客户类型变更事件
    clientClassChange,
    paramsClientType,
    //公共处理信息
    state,
    detailState,
    //共享额度
    sharedQuota,
    //结转处理
    carryoverGrid,
    generalButtonOption,
    openCarryoverEditButton,
    closeCarryoverEditButton,
    deleteCarryoverButton,
    carryoverButton,
    carryoverList,
    notCarryoverGrid,
    generalNotCurryoverButtonOption,
    notCarryoverList,
    getOpenDate,
    getBusinessRuleSet,
    //获取结束日期
    getEndDate,
    creditDetailmagnifierData,
    creditClientmagnifierData,
    beforeCreditVarietyDetailOpen,
    beforeSubCreditOpen,
    selectVarietyData,
    grtCreditVarietyList,
    selectClientData,
    grtCreditClientList,
    beforeCreditVarietyDetailDelete,
    beforeCreditDetailDelete,
    submitMessage,
    saveMessage,
    calculateExposeCreditLimit,
    groupClientControlMethodChange,
    composeVarietyControlMethodChange,
    //客户组织架构
    openClientDialog,
    formTemporaryValidator,
    generalSubClientButtonOption,
    subClientClone,
    subClientDelete, //下级客户操作
    openSubClientCarryover,
    //下级客户处理
    deleteSubClientCarryoverButton,
    carryoverSubClientDetailGrid,
    generalCarryoverSubClientButtonOption,
    carryoverSubClientDetailDtoList,
    //下级客户未结转
    unCarryoverSubClientDetailGrid,
    unCarryoverSubClientDetailDtoList,
    generalUnCarryoverSubClientButtonOption,
    openSubClientCarryoverEditButton,
    closeSubClientCarryoverEditButton,
    carryoveSubClientButton,
    //押品
    initCollateralTypeList,
    collateralTypeList,
    orgStructureDialog,
    projectChange,
    clearProject,
    projectCreditChange,
    projectFinancingRatioChange,
    getCreditTerm,
    handSubmitClick,
    setBasicInfo,
    generalDetailButtonOption,
    openDetailEdit,
    closeDetailEdit,
    deleteDetail,
    generalVarietyDetailButtonOption,
    openVarietyDetailEdit,
    closeVarietyDetailEdit,
    deleteVarietyDetail
  };
};
