<template>
  <f-blank-scene :title="t('credit.creditmanage.creditapply.addTitle')">
    <f-multi-form-panel ref="form1" :rules="rules" :model="basicInfo" :column="3">
      <f-panel :title="t('credit.creditmanage.creditapply.basicInfo')" id="form10" :position="1">
        <!-- 指令号 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.instructCode')"
          prop="instructCode"
          v-if="basicInfo.instructCode !== ''"
        >
          <f-input v-model="basicInfo.instructCode" disabled />
        </f-form-item>
        <f-form-item
          :label="t('credit.creditmanage.creditapply.targetCurrencyId')"
          prop="targetCurrencyId"
          :required="true"
        >
          <f-select
            ref="currencyRef"
            v-model="basicInfo.targetCurrencyId"
            value-key="currencyId"
            label="currencyName"
            :url="getCurrencyInfo"
            method="post"
            @change="currencyChange"
            auto-select
            :disabled="basicInfo.currencyType === currencyType.MANY_CURRENCY || basicInfo.instructCode !== ''"
          />
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapply.clientClass')" prop="clientClass" :required="true">
          <f-select
            v-model="basicInfo.clientClass"
            :data="clientClass.pickConst([clientClass.INTER_CLIENT])"
            @change="clientClassChange"
            disabled
          />
        </f-form-item>
        <!--客户相关信息-->
        <f-form-item :label="t('credit.creditmanage.creditapply.clientCode')" prop="clientId" :required="true">
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapply.clientCodeMagnifier')"
            :url="getClientInfo"
            method="post"
            v-model="basicInfo.clientId"
            row-key="clientId"
            row-label="clientCode"
            input-key="codeOrName"
            auto-init
            :params="{
              clientClass: 1,
              isBlack: 1,
              clientStatusId: 1
              // officeId: basicInfo.officeId
            }"
            @change="clientCodeChange"
            @clear="clearClientChange"
            :disabled="basicInfo.instructCode !== ''"
          >
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapply.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapply.clientName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapply.clientName')" prop="clientName">
          <f-input v-model="basicInfo.clientName" disabled
        /></f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapply.enterpriseScale')" prop="enterpriseScale">
          <f-select v-model="basicInfo.enterpriseScale" :data="enterpriseScale" disabled />
        </f-form-item>
        <!-- 是否项目授信 -->
        <f-form-item :label="t('credit.creditmanage.creditapply.isProjectCredit')">
          <f-switch
            v-model="basicInfo.projectCredit"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
            @change="projectCreditChange"
          />
        </f-form-item>
        <!-- 项目授信放大镜 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.projectName')"
          :required="basicInfo.projectCredit === yesOrNo.YES"
          prop="projectId"
        >
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapply.projectInfoMagnifier')"
            :url="projectMagnifier"
            method="post"
            v-model="basicInfo.projectId"
            row-key="id"
            row-label="projectName"
            input-key="projectName"
            auto-init
            :params="{
              clientId: basicInfo.clientId
            }"
            @change="projectChange"
            @clear="clearProject"
            :disabled="basicInfo.projectCredit === yesOrNo.NO"
          >
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapply.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapply.clientName')" />
            <f-magnifier-column prop="projectName" :label="t('credit.creditmanage.creditapply.projectName')" />
          </f-magnifier-single>
        </f-form-item>
        <!--信用评级相关信息-->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.creditRatingCode')"
          :required="state.businessRule.genCreditRatingFlag === yesOrNo.YES"
          prop="creditRatingCode"
        >
          <f-magnifier-single
            :title="t('credit.creditmanage.creditapply.creditRatingCodeMagnifier')"
            :url="getCreditRatingCode"
            method="post"
            v-model="basicInfo.creditRatingCode"
            row-key="businessCode"
            row-label="businessCode"
            input-key="businessCode"
            auto-init
            :params="{
              clientId: basicInfo.clientId,
              clientCode: basicInfo.clientCode,
              projectId: basicInfo.projectId
            }"
            @change="creditRatingCodeChange"
            @clear="clearCreditRatingCode"
          >
            <f-magnifier-column
              prop="businessCode"
              :label="t('credit.creditmanage.creditapply.creditRatingCodeColumn')"
            />
            <f-magnifier-column
              prop="clientType"
              :filter-input="false"
              :label="t('credit.creditmanage.creditapply.clientClass')"
            >
              <template #default="{ row }">
                {{ clientTypeClass.valueToLabel(row?.clientType) }}
              </template>
            </f-magnifier-column>
            <f-magnifier-column prop="clientCode" :label="t('credit.creditmanage.creditapply.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('credit.creditmanage.creditapply.clientName')" />
            <f-magnifier-column
              prop="declarationCreditLevel"
              :label="t('credit.creditmanage.creditapply.creditGradeName')"
            />
            <f-magnifier-column
              prop="initialScore"
              :label="t('credit.creditmanage.creditapply.creditRatingScoreMod')"
            />
            <f-magnifier-column
              prop="effectiveDateRange"
              :filter-input="false"
              :label="t('credit.creditmanage.creditapply.effectiveDateRange')"
            >
              <template #default="{ row }"> {{ row.ratingEffectiveDate + "-" + row.ratingExpirationDate }}</template>
            </f-magnifier-column>
          </f-magnifier-single>
        </f-form-item>
        <!-- 所属行业 -->
        <f-form-item :label="t('credit.creditmanage.creditapply.industry')" prop="industryCategory">
          <f-input v-model="basicInfo.industryCategory" disabled />
        </f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapply.creditGradeName')" prop="creditGradeName">
          <f-input v-model="basicInfo.creditGradeName" disabled
        /></f-form-item>
        <f-form-item :label="t('credit.creditmanage.creditapply.creditRatingScore')" prop="creditRatingScore">
          <f-input v-model="basicInfo.creditRatingScore" disabled
        /></f-form-item>
        <f-form-item
          :label="t('credit.creditmanage.creditapply.creditRatingEffectiveDate')"
          prop="creditRatingEffectiveDate"
        >
          <f-input v-model="basicInfo.creditRatingEffectiveDate" disabled
        /></f-form-item>
        <f-form-item
          :label="t('credit.creditmanage.creditapply.creditRatingInvalidDate')"
          prop="creditRatingInvalidDate"
        >
          <f-input v-model="basicInfo.creditRatingInvalidDate" disabled
        /></f-form-item>
        <!--综合授信品种额度控制方式-->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.composeVarietyControlMethod')"
          prop="composeVarietyControlMethod"
          :required="true"
        >
          <f-select
            v-model="basicInfo.composeVarietyControlMethod"
            :data="composeVarietyControlMethodType"
            @change="composeVarietyControlMethodChange"
          />
        </f-form-item>
      </f-panel>
      <!-- 授信测算 -->
      <f-panel :title="t('credit.creditmanage.creditapply.creditCalculation')" :position="2">
        <!-- 信用等级 -->
        <f-form-item :label="t('credit.creditmanage.creditapply.creditGradeName')" prop="creditGradeName">
          <f-input v-model="basicInfo.creditGradeName" disabled />
        </f-form-item>
        <!-- 信用调节系数(V) -->
        <f-form-item :label="t('credit.creditmanage.creditapply.creditAdjustmentFactor')" prop="creditAdjustmentFactor">
          <f-input v-model="basicInfo.creditAdjustmentFactor" disabled />
        </f-form-item>
        <!-- 净资产（E) -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.netAssets')"
          prop="netAssets"
          v-if="basicInfo.projectCredit === yesOrNo.NO"
        >
          <f-amount v-model="basicInfo.netAssets" :symbol="currencySymbol" disabled tooltip />
        </f-form-item>
        <!-- 所属行业 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.industry')"
          prop="industryCategory"
          v-if="basicInfo.projectCredit === yesOrNo.NO"
        >
          <f-input v-model="basicInfo.industryCategory" disabled />
        </f-form-item>
        <!-- 行业信贷比例（D） -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.industryCreditRatio')"
          prop="industryCreditRatio"
          v-if="basicInfo.projectCredit === yesOrNo.NO"
        >
          <f-number v-model="basicInfo.industryCreditRatio" :precision="4" :min="0.0" :max="100" is-rate disabled />
        </f-form-item>
        <!-- 目标杠杆比率（K） -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.targetLeverageRatio')"
          prop="targetLeverageRatio"
          v-if="basicInfo.projectCredit === yesOrNo.NO"
        >
          <f-number v-model="basicInfo.targetLeverageRatio" :precision="4" :min="0.0" :max="100" is-rate disabled />
        </f-form-item>
        <!-- 项目总投资（T) -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.totalProjectInvestment')"
          prop="totalProjectInvestment"
          v-if="basicInfo.projectCredit === yesOrNo.YES"
        >
          <f-amount v-model="basicInfo.totalProjectInvestment" :symbol="currencySymbol" disabled tooltip />
        </f-form-item>
        <!-- 项目资本金（E2） -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.projectEquity')"
          prop="projectEquity"
          v-if="basicInfo.projectCredit === yesOrNo.YES"
        >
          <f-amount v-model="basicInfo.projectEquity" :symbol="currencySymbol" disabled tooltip />
        </f-form-item>
        <!-- 项目融资比例（S） -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.projectFinancingRatio')"
          prop="projectFinancingRatio"
          v-if="basicInfo.projectCredit === yesOrNo.YES"
        >
          <f-number
            v-model="basicInfo.projectFinancingRatio"
            :precision="2"
            max="100"
            min="0"
            is-rate
            @change="projectFinancingRatioChange"
          />
        </f-form-item>
        <!-- 参考值计算公式 -->
        <f-form-item :label="t('credit.creditmanage.creditapply.referenceValueFormula')" prop="referenceValueFormula">
          <f-input v-model="basicInfo.referenceValueFormula" disabled />
        </f-form-item>
        <!-- 授信参考值结果 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.creditReferenceValueResult')"
          prop="creditReferenceValueResult"
        >
          <f-amount v-model="basicInfo.creditReferenceValueResult" :symbol="currencySymbol" disabled />
        </f-form-item>
        <!-- 申请额度 -->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.applyQuota')"
          prop="projectEquity"
          v-if="basicInfo.creditDataSource === creditDataSource.FUND_MANAGEMENT_PLATFORM"
        >
          <f-amount v-model="basicInfo.applyQuota" :symbol="currencySymbol" disabled tooltip />
        </f-form-item>
        <!--申请期限-->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.applyTerm')"
          prop="creditTermMonth"
          :required="true"
          v-if="basicInfo.creditDataSource === creditDataSource.FUND_MANAGEMENT_PLATFORM"
        >
          <f-number v-model="basicInfo.creditTermMonth" disabled />
        </f-form-item>
      </f-panel>
      <!-- 授信明细 -->
      <f-panel :title="t('credit.creditmanage.creditapply.creditDetail')" id="form3" :position="3">
        <f-table-edit
          ref="creditDetailGrid"
          row-key="_randomId"
          :data="creditDetailList"
          style="width: 100%"
          border
          @add-row="addCreditDetailRow"
        >
          <template #top-operate>
            <f-button type="info" @click.prevent="openCarryover" :disabled="creditDetailList.length === 0">{{
              t("credit.creditmanage.creditapply.carryover")
            }}</f-button>
          </template>
          <f-table-column
            :formatter="item.formatter"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :align="item.align"
            v-for="(item, key) in detailState.columns"
          >
            <!-- 准入控制方式 -->
            <template #edit="scope">
              <template v-if="item.prop === 'creditControlType'">
                <f-select v-model="scope.row[item.prop]" :data="creditControlType" />
              </template>
              <template v-else-if="item.prop === 'creditType'">
                <f-select
                  v-model="scope.row[item.prop]"
                  :data="creditType"
                  @change="
                    () => {
                      scope.row.creditVarietyCodes = '';
                      scope.row.creditVarietyList = '';
                      scope.row.creditVarietyCode = '';
                    }
                  "
                />
              </template>
              <template v-else-if="item.prop === 'creditVarietyNames'">
                <f-select
                  v-model="scope.row.creditVarietyCode"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  :url="getCreditVarietyInfo"
                  method="post"
                  v-if="scope.row.creditType === creditType.SPECIAL_CREDIT"
                  @change="
                    (data, row) => {
                      scope.row.creditVarietyCodes = [row.creditVarietyCode];
                      scope.row.creditVarietyList = [row];
                      scope.row.creditVarietyCode = row.creditVarietyCode;
                      scope.row.creditVarietyNames = row.creditVarietyName;
                    }
                  "
                />
                <f-select
                  v-model="scope.row.creditVarietyCodes"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  :url="getCreditVarietyInfo"
                  method="post"
                  v-if="scope.row.creditType !== creditType.SPECIAL_CREDIT"
                  @change="
                    (codes, rows) => {
                      scope.row.creditVarietyCodes = codes;
                      scope.row.creditVarietyList = rows;
                      scope.row.creditVarietyCode = codes.join(',');
                      scope.row.creditVarietyNames = rows.map(x => x.creditVarietyName).join(',');
                    }
                  "
                  filterable
                  multiple
                  collapse-tags
                  select-all
                />
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                <f-select v-model="scope.row[item.prop]" :data="creditUseType" />
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <f-amount v-model="scope.row[item.prop]" :negative="false" />
              </template>
              <template v-else-if="item.prop === 'guaranteeRatio'">
                <f-number v-model="scope.row.guaranteeRatio" :precision="2" max="100" min="0" />
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
            <template #default="scope">
              <template v-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditLimitType'">
                {{ creditLimitType.valueToLabel(scope.row.creditLimitType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <span class="span-amount">
                  {{ format(scope.row[item.prop]) }}
                </span>
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </f-table-column>
          <template #operate="scope">
            <OperateButton
              :options="generalDetailButtonOption(scope)"
              @on-remove="deleteDetail(scope)"
              @on-edit="openDetailEdit(scope)"
              @on-close="closeDetailEdit(scope)"
            />
          </template>
        </f-table-edit>
      </f-panel>
      <!-- 授信品种明细 -->
      <f-panel
        :title="t('credit.creditmanage.creditapply.creditVarietyDetail')"
        id="form9"
        v-if="
          basicInfo.composeVarietyControlMethod !== composeVarietyControlMethodType.NOT_CONTROL &&
          creditDetailmagnifierData.length > 0
        "
        position="4"
      >
        <f-table-edit
          ref="creditVarietyDetailGrid"
          row-key="_randomId"
          :data="creditVarietyDetailList"
          style="width: 100%"
          border
          @add-row="addCreditVarietyDetailRow"
          :before-open-edit="beforeCreditVarietyDetailOpen"
        >
          <f-table-column
            v-for="(item, key) in detailState.varietyDetailcolumns"
            :formatter="item.formatter"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :align="item.align"
          >
            <!-- 准入控制方式 -->
            <template #edit="scope">
              <template v-if="item.prop === 'refDetailSeqNo'">
                <f-magnifier-single
                  ref="refDetailSeqNoMagnifier"
                  :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                  :table-data="{ data: creditDetailmagnifierData, total: creditDetailmagnifierData.length }"
                  v-model="scope.row.refDetailSeqNo"
                  row-key="seqNo"
                  row-label="seqNo"
                  :pagination="false"
                  :selected-data="{ seqNo: scope.row.refDetailSeqNo }"
                  @change="
                    info => {
                      scope.row.refDetailSeqNo = info.seqNo;
                      scope.row.creditVarietyList = info.creditVarietyList;
                      scope.row.creditVarietyCode = info.creditVarietyCode;
                      scope.row.creditVarietyNames = info.creditVarietyNames;
                      scope.row.creditType = info.creditType;
                      scope.row.creditControlType = info.creditControlType;
                      scope.row.creditUseType = info.creditUseType;
                      scope.row.guaranteeRatio = info.guaranteeRatio;
                      grtCreditVarietyList(info.seqNo);
                    }
                  "
                  @clear="
                    () => {
                      scope.row.refDetailSeqNo = '';
                      scope.row.creditVarietyCodes = '';
                      scope.row.creditVarietyList = '';
                      scope.row.creditVarietyCode = '';
                      scope.row.creditType = '';
                      scope.row.creditControlType = '';
                      scope.row.creditUseType = '';
                      scope.row.guaranteeRatio = '';
                    }
                  "
                >
                  <f-magnifier-column
                    prop="seqNo"
                    :filter-input="false"
                    :label="t('credit.creditmanage.creditapply.seqNo')"
                  />
                </f-magnifier-single>
              </template>
              <template v-else-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template v-else-if="item.prop === 'creditVarietyNames'">
                <f-select
                  v-model="scope.row.creditVarietyCodes"
                  :data="selectVarietyData"
                  value-key="creditVarietyCode"
                  label="creditVarietyName"
                  blank-option
                  filterable
                  multiple
                  collapse-tags
                  select-all
                  @change="
                    (codes, rows) => {
                      scope.row.creditVarietyCodes = codes;
                      scope.row.creditVarietyList = rows;
                      scope.row.creditVarietyCode = codes.join(',');
                      scope.row.creditVarietyNames = rows.map(x => x.creditVarietyName).join(',');
                    }
                  "
                />
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <f-amount v-model="scope.row[item.prop]" :negative="false" />
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
            <template #default="scope">
              <template v-if="item.prop === 'creditControlType'">
                {{ creditControlType.valueToLabel(scope.row.creditControlType) }}
              </template>
              <template v-else-if="item.prop === 'creditType'">
                {{ creditType.valueToLabel(scope.row.creditType) }}
              </template>
              <template v-else-if="item.prop === 'creditUseType'">
                {{ creditUseType.valueToLabel(scope.row.creditUseType) }}
              </template>
              <template
                v-else-if="
                  [
                    'quota',
                    'creditQuota',
                    'assureQuota',
                    'pledgeQuota',
                    'creditQuota',
                    'impawnQuota',
                    'noCreditQuota'
                  ].includes(item.prop)
                "
              >
                <span class="span-amount">
                  {{ format(scope.row[item.prop]) }}
                </span>
              </template>
              <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                {{ format(scope.row[item.prop]) }}
              </template>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </f-table-column>
          <template #operate="scope">
            <OperateButton
              :options="generalVarietyDetailButtonOption(scope)"
              @on-remove="deleteVarietyDetail(scope)"
              @on-edit="openVarietyDetailEdit(scope)"
              @on-close="closeVarietyDetailEdit(scope)"
            />
          </template>
        </f-table-edit>
      </f-panel>
      <!-- 授信信息 -->
      <f-panel :title="t('credit.creditmanage.creditapply.creditInfo')" id="form2" :position="5">
        <!--授信额度-->
        <f-form-item :label="t('credit.creditmanage.creditapply.creditQuota')" prop="creditQuota">
          <f-amount v-model="basicInfo.creditQuota" disabled
        /></f-form-item>
        <!--结转金额-->
        <f-form-item :label="t('credit.creditmanage.creditapply.carryoverAmount')" prop="carryoverAmount">
          <f-amount v-model="basicInfo.carryoverAmount" disabled
        /></f-form-item>
        <!--授信生效日-->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.startDate')"
          prop="effectiveDate"
          :required="effectiveDateActiveRuleType.CUSTOM === state.businessRule.addEffectiveDateCreateType"
        >
          <f-date-picker
            v-model="basicInfo.effectiveDate"
            type="date"
            :disabled="effectiveDateActiveRuleType.CUSTOM !== state.businessRule.addEffectiveDateCreateType"
            @change="getCreditTerm"
          />
        </f-form-item>
        <!--授信结束日-->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.endDate')"
          prop="endDate"
          :required="effectiveDateActiveRuleType.CUSTOM === state.businessRule.addEffectiveDateCreateType"
        >
          <f-date-picker
            v-model="basicInfo.endDate"
            type="date"
            :disabled="effectiveDateActiveRuleType.CUSTOM !== state.businessRule.addEffectiveDateCreateType"
            @change="getCreditTerm"
          />
        </f-form-item>
        <!--授信期限个月-->
        <f-form-item
          :label="t('credit.creditmanage.creditapply.creditTermMonth')"
          prop="creditTermMonth"
          :required="true"
        >
          <f-number v-model="basicInfo.creditTermDay" disabled />
        </f-form-item>
        <!--备注-->
        <f-form-item :label="t('credit.creditmanage.creditapply.remarks')" :employ="2" prop="remarks">
          <f-textarea v-model="basicInfo.remarks" :max-rows="4" :min-rows="1" :maxlength="200" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('credit.creditmanage.creditapply.fileinfo')" id="form6" :position="6">
        <f-form-item :employ="3">
          <f-attm-upload ref="upload" drag multiple />
        </f-form-item>
      </f-panel>
      <f-dialog
        v-model="state.carryoverInfo.showDialog"
        :before-close="
          () => {
            state.carryoverInfo.showDialog = false;
          }
        "
        :title="t('credit.creditmanage.creditapply.carryoverDetail')"
        style="width: 80%"
      >
        <f-panel :title="t('credit.creditmanage.creditapply.carryoverDetailInfo')" id="form6" :asAnchor="false">
          <f-table-edit
            ref="carryoverGrid"
            row-key="_randomId"
            :data="carryoverList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.carryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            />
            <template #operate="scope">
              <OperateButton :options="generalButtonOption(scope)" @on-remove="deleteCarryoverButton(scope)" />
            </template>
          </f-table-edit>
        </f-panel>
        <f-panel :title="t('credit.creditmanage.creditapply.notCarryoverDetailInfo')" id="form7" :asAnchor="false">
          <f-table-edit
            ref="notCarryoverGrid"
            row-key="_randomId"
            :data="notCarryoverList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.notCarryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            >
              <template #edit="scope">
                <template v-if="item.prop === 'targetDetailSeqNo'">
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                    :table-data="{ data: creditDetailList, total: creditDetailList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.TOTAL_LIMIT"
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapply.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapply.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapply.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="quota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.quota')"
                    />
                    <f-magnifier-column
                      prop="guaranteeRatio"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.guaranteeRatio')"
                    />
                  </f-magnifier-single>
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                    :table-data="{ data: creditDetailList, total: creditDetailList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-else-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT"
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapply.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapply.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapply.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="creditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.creditQuota')"
                    />
                    <f-magnifier-column
                      prop="noCreditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.noCreditQuota')"
                    />
                  </f-magnifier-single>
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                    :table-data="{ data: creditDetailList, total: creditDetailList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-else
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapply.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapply.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapply.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="creditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.creditQuota')"
                    />
                    <f-magnifier-column
                      prop="assureQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.assureQuota')"
                    />
                    <f-magnifier-column
                      prop="pledgeQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.pledgeQuota')"
                    />
                    <f-magnifier-column
                      prop="impawnQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.impawnQuota')"
                    />
                  </f-magnifier-single>
                </template>
                <template v-else-if="item.prop === 'originCreditStyle'">
                  {{ creditStyleType.valueToLabel(scope.row.originCreditStyle) }}
                </template>
                <template v-else-if="item.prop === 'originCreditType'">
                  {{ creditType.valueToLabel(scope.row.originCreditType) }}
                </template>
                <template v-else-if="item.prop === 'originCreditTerm'">
                  {{ monthType.valueToLabel(scope.row.originCreditTerm) }}
                </template>
                <template v-else-if="['originCarryoverAmount', 'targetCarryoverAmount'].includes(item.prop)">
                  <span class="span-amount">
                    {{ format(scope.row[item.prop]) }}
                  </span>
                </template>
                <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                  {{ format(scope.row[item.prop]) }}
                </template>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
            </f-table-column>
            <template #operate="scope">
              <OperateButton
                :options="generalNotCurryoverButtonOption(scope)"
                @on-edit="openCarryoverEditButton(scope)"
                @on-carryover="carryoverButton(scope)"
                @on-close="closeCarryoverEditButton(scope)"
              />
            </template>
          </f-table-edit>
        </f-panel>
        <template #footer>
          <f-button type="info" plain @click="state.carryoverInfo.showDialog = false">{{
            t("credit.creditmanage.creditapply.close")
          }}</f-button>
        </template>
      </f-dialog>
      <f-dialog
        v-model="state.carryoverInfo.showSubClientDialog"
        :before-close="
          () => {
            state.carryoverInfo.showSubClientDialog = false;
          }
        "
        :title="t('credit.creditmanage.creditapply.carryoverDetail')"
        style="width: 80%"
      >
        <f-panel :title="t('credit.creditmanage.creditapply.carryoverDetailInfo')" id="form6" :asAnchor="false">
          <f-table-edit
            ref="carryoverSubClientDetailGrid"
            row-key="_randomId"
            :data="carryoverSubClientDetailDtoList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.carryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            />
            <template #operate="scope">
              <OperateButton
                :options="generalCarryoverSubClientButtonOption(scope)"
                @on-remove="deleteSubClientCarryoverButton(scope)"
              />
            </template>
          </f-table-edit>
        </f-panel>
        <f-panel :title="t('credit.creditmanage.creditapply.notCarryoverDetailInfo')" id="form7" :asAnchor="false">
          <f-table-edit
            ref="unCarryoverSubClientDetailGrid"
            row-key="_randomId"
            :data="unCarryoverSubClientDetailDtoList"
            style="width: 100%"
            :showAddBtn="false"
            border
          >
            <f-table-column
              v-for="(item, key) in state.carryoverInfo.notCarryoverDetailColumns"
              :formatter="item.formatter"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              :align="item.align"
            >
              <template #edit="scope">
                <template v-if="item.prop === 'targetDetailSeqNo'">
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                    :table-data="{ data: subCreditClientList, total: subCreditClientList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.TOTAL_LIMIT"
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapply.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapply.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapply.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="quota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.quota')"
                    />
                    <f-magnifier-column
                      prop="guaranteeRatio"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.guaranteeRatio')"
                    />
                  </f-magnifier-single>
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                    :table-data="{ data: subCreditClientList, total: subCreditClientList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-else-if="basicInfo.genCreditLimitMethod === creditLimitSetMethodType.CREDIT_OR_UN_CREDIT"
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapply.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapply.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapply.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="creditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.creditQuota')"
                    />
                    <f-magnifier-column
                      prop="noCreditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.noCreditQuota')"
                    />
                  </f-magnifier-single>
                  <f-magnifier-single
                    ref="refDetailSeqNoMagnifier"
                    :title="t('credit.creditmanage.creditapply.refDetailSeqNoMagnifier')"
                    :table-data="{ data: subCreditClientList, total: subCreditClientList.length }"
                    v-model="scope.row.targetDetailSeqNo"
                    row-key="seqNo"
                    row-label="seqNo"
                    :pagination="false"
                    @change="
                      info => {
                        scope.row.targetDetailSeqNo = info.seqNo;
                      }
                    "
                    @clear="
                      () => {
                        scope.row.targetDetailSeqNo = '';
                      }
                    "
                    v-else
                  >
                    <f-magnifier-column
                      prop="seqNo"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.seqNo')"
                    />
                    <f-magnifier-column
                      prop="creditType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditType' }"
                      :label="t('credit.creditmanage.creditapply.creditType')"
                    />
                    <f-magnifier-column
                      prop="creditVarietyNames"
                      :filter-input="false"
                      :label="t('credit.creditmanage.creditapply.creditVarietyCode')"
                    />
                    <f-magnifier-column
                      prop="creditControlType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditControlType' }"
                      :label="t('credit.creditmanage.creditapply.creditControlType')"
                    />
                    <f-magnifier-column
                      prop="creditUseType"
                      :filter-input="false"
                      :formatter="{ name: 'const', const: 'credit.CreditUseType' }"
                      :label="t('credit.creditmanage.creditapply.creditUseType')"
                    />
                    <f-magnifier-column
                      prop="creditQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.creditQuota')"
                    />
                    <f-magnifier-column
                      prop="assureQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.assureQuota')"
                    />
                    <f-magnifier-column
                      prop="pledgeQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.pledgeQuota')"
                    />
                    <f-magnifier-column
                      prop="impawnQuota"
                      :filter-input="false"
                      formatter="amount"
                      :label="t('credit.creditmanage.creditapply.impawnQuota')"
                    />
                  </f-magnifier-single>
                </template>
                <template v-else-if="item.prop === 'originCreditStyle'">
                  {{ creditStyleType.valueToLabel(scope.row.originCreditStyle) }}
                </template>
                <template v-else-if="item.prop === 'originCreditType'">
                  {{ creditType.valueToLabel(scope.row.originCreditType) }}
                </template>
                <template v-else-if="item.prop === 'originCreditTerm'">
                  {{ monthType.valueToLabel(scope.row.originCreditTerm) }}
                </template>
                <template v-else-if="['originCarryoverAmount', 'targetCarryoverAmount'].includes(item.prop)">
                  <span class="span-amount">
                    {{ format(scope.row[item.prop]) }}
                  </span>
                </template>
                <template v-else-if="['guaranteeRatio'].includes(item.prop)">
                  {{ format(scope.row[item.prop]) }}
                </template>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>
            </f-table-column>
            <template #operate="scope">
              <OperateButton
                :options="generalUnCarryoverSubClientButtonOption(scope)"
                @on-edit="openSubClientCarryoverEditButton(scope)"
                @on-carryover="carryoveSubClientButton(scope)"
                @on-close="closeSubClientCarryoverEditButton(scope)"
              />
            </template>
          </f-table-edit>
        </f-panel>
        <template #footer>
          <f-button type="info" plain @click="state.carryoverInfo.showSubClientDialog = false">{{
            t("credit.creditmanage.creditapply.close")
          }}</f-button>
        </template>
      </f-dialog>
      <OrgStructureDialog ref="orgStructureDialog" :id="basicInfo.creditOrgStructureId" />
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="getCreditInvestigateReportInfoUrl"
        :before-trigger="formValidator"
        :operate-name="t('credit.creditmanage.creditapply.creditInvestigateReport')"
        @submit-success="reportDataSuccess"
        :showConfirm="false"
        :result-confirm="t('credit.creditmanage.creditapply.reportDataSuccess')"
        :result-title="t('credit.creditmanage.creditapply.creditInvestigateReport')"
        :show-result="false"
      />
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="temporaryUrl"
        type="info"
        :before-trigger="formTemporaryValidator"
        :confirm-text="t('credit.creditmanage.creditapply.temporaryConfirm')"
        :batch-confirm-map="{
          success: t('credit.creditmanage.creditapply.temporarySuccess'),
          fail: t('credit.creditmanage.creditapply.temporaryFail')
        }"
        :operate-name="t('credit.creditmanage.creditapply.temporaryTitle')"
        :result-confirm="t('credit.creditmanage.creditapply.temporarySuccess')"
        :result-title="t('credit.creditmanage.creditapply.temporaryTitle')"
        @close="saveDataSuccess"
        v-if="basicInfo.checkStatus === statusEnum.DRAFT || basicInfo.checkStatus === ''"
      />
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="save"
        operate="save"
        :confirm-text="saveMessage"
        :before-trigger="() => formValidator(false)"
        @close="saveDataSuccess"
      />
      <f-submit-state
        :gather-params="generalSaveInfo"
        :url="submitUrl"
        operate="submit"
        :confirm-text="submitMessage"
        :before-trigger="() => formValidator(true)"
        @close="submitDataSuccess"
        @click="handSubmitClick"
      />
      <f-button type="info" @click.prevent="goBack">{{ t("credit.creditmanage.creditapply.linkquery") }}</f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useCreditApplyRules } from "../hooks/useCreditApplyRules";
import { useCreditApply } from "../hooks/useCreditApply";
import useAdd from "../hooks/useAdd";
import OperateButton from "@/components/operate-button/operate-button";
import { useConst } from "@ifs/support";
import { onMounted } from "vue";
import { format } from "@/utils/currency";
import { useCurrency } from "@/hooks";
import {
  getCurrencyInfo,
  save,
  submitUrl,
  getCreditRatingCode,
  getCreditVarietyInfo,
  getClientInfo,
  temporaryUrl,
  projectMagnifier,
  getCreditInvestigateReportInfoUrl,
  getInfo
} from "../url";
import { usePage } from "../hooks/usePage";
import httpTool from "@/utils/http";
const { pageParams } = usePage();
const { t } = useI18n();
//授信方式
const creditStyleType = useConst("credit.CreditStyleType");
//企业规模
const enterpriseScale = useConst("credit.EnterpriseScale");
//客户类别
const clientClass = useConst("credit.ClientClass");
//客户类别
const clientTypeClass = useConst("credit.ClientType");
//授信额度设置方式
const creditType = useConst("credit.CreditType");
//授信使用类型
const creditUseType = useConst("credit.CreditUseType");
//币种类型
const currencyType = useConst("credit.CurrencyType");
//额度类型
const creditLimitType = useConst("credit.CreditLimitType");
//期限类型
const monthType = useConst("credit.MonthType");
//准入控制方式
const creditControlType = useConst("credit.CreditControlType");
//授信生效日规则
const effectiveDateActiveRuleType = useConst("credit.EffectiveDateActiveRuleType");
//数据来源
const creditDataSource = useConst("credit.CreditDataSource");

//是否枚举
const yesOrNo = useConst("counter.YesOrNo");
//授信额度设置方式
const creditLimitSetMethodType = useConst("credit.CreditLimitSetMethodType");
// //综合授信品种额度控制方式
const composeVarietyControlMethodType = useConst("credit.ComposeVarietyControlMethodType");
//状态
const statusEnum = useConst("cashmanage.CheckStatus");
const {
  basicInfo,
  formValidator,
  form1,
  generalSaveInfo,
  upload,
  subCreditClientList,
  //授信明细可编辑表格
  addCreditDetailRow,
  creditDetailList,
  creditDetailGrid,
  //授信品种明细
  creditVarietyDetailGrid,
  creditVarietyDetailList,
  addCreditVarietyDetailRow,
  //变更触发事件
  openCarryover,
  //信用评级事件
  creditRatingCodeChange,
  clearCreditRatingCode,
  clearClientChange,
  clientCodeChange,
  //客户类型变更事件
  clientClassChange,
  //公共处理信息
  state,
  detailState,
  //结转处理
  //结转处理
  carryoverGrid,
  generalButtonOption,
  openCarryoverEditButton,
  closeCarryoverEditButton,
  deleteCarryoverButton,
  carryoverButton,
  carryoverList,
  notCarryoverGrid,
  generalNotCurryoverButtonOption,
  notCarryoverList,
  //初始化加载
  getOpenDate,
  getBusinessRuleSet,
  creditDetailmagnifierData,
  beforeCreditVarietyDetailOpen,
  selectVarietyData,
  grtCreditVarietyList,
  submitMessage,
  saveMessage,
  composeVarietyControlMethodChange,
  orgStructureDialog,
  formTemporaryValidator,
  currencyChange,
  //下级客户处理
  deleteSubClientCarryoverButton,
  carryoverSubClientDetailGrid,
  generalCarryoverSubClientButtonOption,
  carryoverSubClientDetailDtoList,
  //下级客户未结转
  unCarryoverSubClientDetailGrid,
  unCarryoverSubClientDetailDtoList,
  generalUnCarryoverSubClientButtonOption,
  openSubClientCarryoverEditButton,
  closeSubClientCarryoverEditButton,
  carryoveSubClientButton,
  projectChange,
  clearProject,
  projectCreditChange,
  projectFinancingRatioChange,
  getCreditTerm,
  handSubmitClick,
  setBasicInfo,
  getDetailInfo,
  generalDetailButtonOption,
  openDetailEdit,
  closeDetailEdit,
  deleteDetail,
  generalVarietyDetailButtonOption,
  openVarietyDetailEdit,
  closeVarietyDetailEdit,
  deleteVarietyDetail
} = useCreditApply();
const { goBack, goToReport } = useAdd(basicInfo, creditDetailList);
//获取明细信息
const getAutoDetailInfo = (id: number | string) => {
  basicInfo.id = id;
  return httpTool.post(getInfo, basicInfo).then((res: any) => {
    getDetailInfo(res);
  });
};
onMounted(async () => {
  getOpenDate();
  getBusinessRuleSet(false);
  if (pageParams && pageParams?.id > 0) {
    getAutoDetailInfo(pageParams?.id);
  } else {
    setBasicInfo(pageParams);
  }
});
const { currencySymbol } = useCurrency(basicInfo);
const submitDataSuccess = (res: any) => {
  if (res?.success) {
    //返回列表页面
    goBack();
  }
};
const reportDataSuccess = (res: any) => {
  if (res) {
    basicInfo.creditInvestigateReportInfo = res;
    //返回列表页面
    goToReport();
  }
};
//必填项校验
const { rules } = useCreditApplyRules(basicInfo);
const saveDataSuccess = (res: any) => {
  if (res.success) {
    basicInfo.id = res.data.id;
    basicInfo.creditNo = res.data.creditNo;
    basicInfo.applyNo = res.data.applyNo;
    basicInfo.version = res.data.version;
    basicInfo.checkStatus = res.data.checkStatus;
    basicInfo.inputUserId = res.data.inputUserId;
    basicInfo.inputUserName = res.data.inputUserName;
    basicInfo.inputTime = res.data.inputTime;
  }
};
</script>
<style lang="scss" scoped>
.magnifierStyle {
  width: 70%;
}
.buttonStyle {
  width: 30%;
}
.span-amount {
  color: #ff9e00;
  text-align: right;
}
</style>
