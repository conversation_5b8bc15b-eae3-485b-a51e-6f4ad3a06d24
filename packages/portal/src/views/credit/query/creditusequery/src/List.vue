<template>
  <f-query-scene :title="t('credit.query.creditusequery.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="credit-creditmanage-creditusequery-query-001"
        table-comp-id="credit-creditmanage-creditusequery-table-001"
        :table-columns="tableColumns"
        :url="search"
        border
        :form-data="queryFrom"
        :show-header="true"
        :show-print="false"
        auto-reset
        :export-url="exportUrl"
        :export-exclude="['operate']"
        :allowSort="allowSort"
        :show-count-value="false"
        :show-summation-sum="false"
        :countLabel="t('credit.query.creditusequery.record')"
        :countLabelUnit="t('credit.query.creditusequery.recordUnit')"
        :summation-biz-label="t('credit.query.creditusequery.record')"
        :summation-biz-unit="t('credit.query.creditusequery.recordUnit')"
        tableType="Record"
        :post-params="postParams"
        lazy
        :load="lazyLoad"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :export-include-child="true"
        @resetForm="resetForm"
      >
        <template #query-panel>
          <!--总额度币种-->
          <f-form-item :label="t('credit.query.creditusequery.targetCurrencyName')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              value-key="currencyId"
              label="currencyName"
              :url="getCurrencyInfo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              method="post"
            />
          </f-form-item>
          <!--金额-->
          <f-form-item :label="t('credit.query.creditusequery.amount')" prop="amount">
            <f-amount-range
              v-model="queryFrom.amount"
              max="9999999999999999.99"
              tooltip
              value-of-string
              :precision="2"
            />
          </f-form-item>
          <!--状态-->
          <f-form-item :label="t('credit.query.creditusequery.checkStatus')" prop="creditStatuses">
            <f-select
              v-model="queryFrom.creditStatuses"
              :data="creditStatus.omitConst([creditStatus.NO_ACTIVE])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              initIfBlank
            />
          </f-form-item>
          <!--客户类型-->
          <f-form-item :label="t('credit.query.creditusequery.clientClass')" prop="clientClasses">
            <f-select
              v-model="queryFrom.clientClasses"
              :data="clientClass.pickConst([clientClass.INTER_CLIENT])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              initIfBlank
            />
          </f-form-item>
          <!--客户号-->
          <f-form-item :label="t('credit.query.creditusequery.clientCode')" prop="clientIds">
            <f-magnifier-multi
              :title="t('credit.query.creditusequery.clientCodeMagnifier')"
              :url="queryClientInfo"
              method="post"
              v-model="queryFrom.clientIds"
              row-key="clientId"
              selected-key="clientCode"
              selected-label="clientName"
              row-label="clientCode"
              input-key="codeOrName"
              auto-init
              :params="{
                isBlack: 1,
                clientStatusId: 1
              }"
            >
              <f-magnifier-column prop="clientCode" :label="t('credit.query.creditusequery.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('credit.query.creditusequery.clientName')" />
              <f-magnifier-column
                prop="clientClass"
                :label="t('credit.query.creditusequery.clientClass')"
                :formatter="row => clientClass.valueToLabel(row.clientClass)"
                :filterInput="false"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--是否项目授信-->
          <f-form-item :label="t('credit.query.creditusequery.isProjectCredit')" prop="projectCredit">
            <f-select v-model="queryFrom.projectCredit" filterable blank-option init-if-blank :data="yesOrNo" />
          </f-form-item>
          <!--项目名称-->
          <f-form-item :label="t('credit.query.creditusequery.projectName')" for="projectName" prop="projectNameLike">
            <f-input v-model="queryFrom.projectNameLike" />
          </f-form-item>
          <!--授信编号-->
          <f-form-item :label="t('credit.query.creditusequery.creditNo')" prop="creditCodeList">
            <f-magnifier-multi
              :title="t('credit.query.creditusequery.creditCodeMagnifier')"
              :url="getCreditInfoMagnifier"
              method="post"
              v-model="queryFrom.creditCodeList"
              row-key="creditNo"
              row-label="creditNo"
              input-key="creditNo"
              auto-init
            >
              <f-magnifier-column prop="creditNo" :label="t('credit.query.creditusequery.creditNo')" />
              <f-magnifier-column
                prop="clientClass"
                :label="t('credit.query.creditusequery.clientClass')"
                :formatter="row => clientClass.valueToLabel(row.clientClass)"
                :filterInput="false"
              />
              <f-magnifier-column prop="clientCode" :label="t('credit.query.creditusequery.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('credit.query.creditusequery.clientName')" />
              <f-magnifier-column
                prop="targetCurrencyName"
                :label="t('credit.query.creditusequery.targetCurrencyName')"
              />
              <f-magnifier-column
                prop="creditQuota"
                :label="t('credit.query.creditusequery.totalCreditQuota')"
                formatter="amount"
              />
              <f-magnifier-column prop="effectiveDate" :label="t('credit.query.creditusequery.creditStartDate')" />
              <f-magnifier-column prop="endDate" :label="t('credit.query.creditusequery.creditEndDate')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--生效日期-->
          <f-form-item :label="t('credit.query.creditusequery.effectiveDate')" prop="effectiveDate">
            <f-lax-range-date-picker
              v-model="queryFrom.effectiveDate"
              :startDisabledDate="excuteDateStartControl(queryFrom, 'effectiveDate')"
              :endDisabledDate="excuteDateEndControl(queryFrom, 'effectiveDate')"
            />
          </f-form-item>
          <!--结束日期-->
          <f-form-item :label="t('credit.query.creditusequery.endDate')" prop="endDate">
            <f-lax-range-date-picker
              v-model="queryFrom.endDate"
              :startDisabledDate="excuteDateStartControl(queryFrom, 'endDate')"
              :endDisabledDate="excuteDateEndControl(queryFrom, 'endDate')"
            />
          </f-form-item>
          <!--组织架构信息-->
          <f-form-item :label="t('credit.query.creditusequery.creditOrgStructureCode')" prop="creditOrgStructureCode">
            <div style="display: flex; width: 100%">
              <f-magnifier-single
                :title="t('credit.query.creditusequery.creditOrgStructureCodeMagnifier')"
                :url="getcreditOrgStructureInfo"
                method="post"
                v-model="queryFrom.creditOrgStructureId"
                row-key="id"
                row-label="orgStructureName"
                input-key="codeOrName"
                auto-init
                :params="{
                  clientType: queryFrom.clientClasses
                  // officeId: basicInfo.officeId
                }"
                class="magnifierStyle"
                @change="creditOrgStructureChange"
              >
                <f-magnifier-column
                  prop="orgStructureCode"
                  :label="t('credit.query.creditusequery.orgStructureCode')"
                />
                <f-magnifier-column
                  prop="orgStructureName"
                  :label="t('credit.query.creditusequery.orgStructureName')"
                />
              </f-magnifier-single>
              <div class="buttonStyle">
                <f-button type="primary" :disabled="queryFrom.creditOrgStructureId <= 0" @click="openClientDialog">{{
                  t("credit.query.creditusequery.creditDetail.clientInfoDetail")
                }}</f-button>
              </div>
            </div>
          </f-form-item>
        </template>
        <template #creditNo="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.creditNo }}</f-button>
        </template>
        <template #useQuota="{ row }">
          <f-button @click="useDatail(row)" link type="primary">{{ format(row.useQuota) }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
    <OrgStructureDialog ref="orgStructureDialog" :id="queryFrom.creditOrgStructureId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { useI18n } from "vue-i18n";
import Detail from "./components/Detail.vue";
import {
  search,
  exportUrl,
  queryClientInfo,
  getCurrencyInfo,
  getCreditInfoMagnifier,
  getcreditOrgStructureInfo
} from "../url";
import { useConst } from "@ifs/support";
import { useCommon } from "@/hooks/useCommon";
import { format } from "@/utils/currency";

const { excuteDateStartControl, excuteDateEndControl } = useCommon();

const { t } = useI18n();
//客户类型
const clientClass = useConst("credit.ClientClass");
//授信状态
const creditStatus = useConst("credit.CreditStatus");
const yesOrNo = useConst("credit.YesOrNo");

const {
  queryTable,
  tableColumns,
  queryFrom,
  handleOpen,
  id,
  detail,
  allowSort,
  postParams,
  useDatail,
  lazyLoad,
  openClientDialog,
  orgStructureDialog,
  creditOrgStructureChange,
  resetForm
} = useList();
</script>
