export default {
  listTitle: "授信使用情况查询",
  detailTitle: "授信使用情况-查看",
  useDetailTitle: "授信使用情况明细",
  useDetail: "授信使用情况",
  useDetailList: "使用明细",
  seqNo: "授信序号",
  goBack: "返回",
  fileinfo: "附件信息",
  file: "附件",
  failedReason: "驳回失败原因",
  operate: "操作",
  add: "新增",
  linkquery: "链接查找",
  close: "关闭",
  recordUnit: "条",
  record: "记录",
  history: "审批历史",
  creditStyle: "授信方式",
  creditCategory: "授信分类",
  targetCurrencyName: "总额度币种",
  creditRiskType: "授信风险类型",
  clientClass: "客户类型",
  clientCode: "客户号",
  clientName: "客户名称",
  clientCodeMagnifier: "客户号放大镜",
  businessType: "业务类型",
  creditNo: "授信编号",
  applyNo: "单据号",
  creditNoMagnifier: "授信编号放大镜",
  amount: "金额",
  checkStatus: "状态",
  effectiveDate: "生效日期",
  endDate: "结束日期",
  queryHasChild: "是否包含下级",
  changingFlag: "是否正在变更",
  totalCreditQuota: "授信额度",
  useQuota: "已使用额度",
  totalBalance: "可用余额",
  listEffectiveDate: "授信生效日",
  listEndDate: "授信结束日",
  inputUserName: "录入人",
  inputTime: "录入时间",
  businessCode: "业务编号",
  creditType: "授信类型",
  creditVarietyCode: "授信品种",
  useQuotaA: "占用额度",
  releasedQuota: "释放额度",
  applyClientName: "实际占用授信单位",
  useBusinessTypeName: "业务种类",
  useCheckStatus: "状态",
  totalAmount: "申请金额",
  totalAmountB: "占用/释放额度",
  creditAmount: "信用",
  assureAmount: "保证",
  pledgeAmount: "抵押",
  impawnAmount: "质押",
  noCreditAmount: "非信用",
  useType: "使用方式",
  currencyName: "交易币种",
  creditDetail: {
    basicInfo: "基础信息",
    recording: "记录",
    strip: "条",
    add: "新增",
    order: "序号",
    office: "机构",
    currency: "币种",
    operate: "操作",
    linkquery: "链接查找",
    close: "关闭",
    makeSure: "确定",
    accountType: "账户类型",
    clientCode: "客户号",
    clientName: "客户名称",
    inputTime: "录入时间",
    basicInfoTitle: "基础信息",
    validFlag: "是否启用",
    fileinfo: "附件信息",
    accountNoMagnifier: "账户号放大镜",
    accountNo: "账户号",
    accountName: "账户名称",
    accountTypeName: "账户类型",
    validDate: "生效日",
    cashPoolLevel: "级次",
    checkStatus: "状态",
    creditNo: "授信编号",
    creditStyle: "授信方式",
    creditCategory: "授信分类",
    creditRiskType: "授信风险分类",
    creditEstimateQuota: "风险限额",
    startDate: "开始日期",
    clientClass: "客户类型",
    creditTerm: "授信期限",
    creditQuota: "授信额度",
    targetCurrencyName: "总额度币种",
    failedReason: "驳回/失败原因",
    creditQuotaTd: "信用",
    assureQuota: "保证",
    pledgeQuota: "抵押",
    impawnQuota: "质押",
    currencyType: "币种类型",
    inputUserName: "录入人",
    modifyTime: "录入时间",
    carryover: "结转",
    guaranteeDetail: "担保明细",
    subClientCreditDetail: "下级客户明细",
    industryCategoryName: "行业门类",
    industryCategoryCode: "行业类别编号",
    enterpriseScale: "企业规模",
    creditRatingCodeColumn: "评级编号",
    creditRatingCode: "信用等级评定编号",
    creditGradeName: "信用等级",
    creditGradeCode: "信用等级编号",
    effectiveDateRange: "有效期限",
    creditRatingScore: "信用等级评估分值",
    creditRatingScoreMod: "评估分值",
    creditRatingRiskType: "信用等级类型",
    creditRatingEffectiveDate: "等级生效日期",
    creditRatingInvalidDate: "等级失效日期",
    orgStructureCode: "组织架构编号",
    creditOrgStructureCode: "授信组织架构",
    orgStructureName: "组织架构名称",
    creditLimitSetMethod: "授信额度设置方式",
    composeVarietyControlMethod: "综合授信品种额度控制方式",
    groupClientControlMethod: "集团授信单位额度控制方式",
    carryoverAmount: "结转金额",
    exposeCreditLimit: "敞口授信额度",
    effectiveDateActiveRule: "授信生效日规则",
    effectiveDate: "授信生效日",
    endDate: "结束日期",
    creditClassification: "授信分类",
    creditTermMonth: "授信期限",
    creditDetail: "授信明细",
    creditInfo: "授信信息",
    seqNo: "授信序号",
    creditSeqNo: "授信序号",
    creditUseType: "授信使用方式",
    creditControlType: "授信控制方式",
    creditVarietyCode: "授信品种",
    creditType: "授信类型",
    quota: "额度",
    guaranteeRatio: "担保比例(%)",
    collateralUseType: "押品用途分类",
    collateralTypeName: "押品类型",
    guaranteeDesc: "担保物描述",
    groupCreditNoMagnifier: "集团授信序号放大镜",
    groupCreditNo: "集团授信序号",
    amount: "金额",
    sharedQuota: "批量应用",
    carryoverDetailInfo: "已结转授信",
    notCarryoverDetailInfo: "未结转授信",
    edit: "编辑",
    originCreditNo: "原授信编号",
    targetCreditNo: "结转至授信序号",
    targetCarryoverAmount: "实际结转金额",
    carryoverDetail: "结转信息",
    clientInfoDetail: "查看客户信息",
    clientInfoDetailDialog: "客户组织架构",
    remarks: "备注",
    creditVarietyDetail: "授信品种明细",
    refDetailSeqNo: "综合授信序号",
    refDetailSeqNoMagnifier: "综合授信序号放大镜",
    guaranteeType: "担保方式",
    creditLimitType: "额度类型",
    currencyDefaultName: "人民币元",
    noCreditQuota: "非信用",
    groupSeqNo: "集团授信序号",
    collateralNameMagnifier: "出质人/抵押人放大镜",
    collateralName: "出质人/抵押人",
    targetCurrencyId: "总额度币种",
    resolutionInfo: "决议信息",
    resolutionCodeMagnifier: "会议决议编号放大镜",
    resolutionDate: "会议日期",
    resolutionCode: "会议决议编号",
    resolutionMeetingDesc: "会议期次",
    resolutionResultDesc: "决议结果",
    clientBasicInfo: "客户组织架构信息",
    isProjectCredit: "是否项目授信",
    projectName: "项目名称",
    creditTermDay: "授信期限",
    isShowSettleBusiness: "是否显示已结清业务"
  },
  isProjectCredit: "是否项目授信",
  projectName: "项目名称",
  creditCodeMagnifier: "授信编号放大镜",
  creditStartDate: "授信开始日",
  creditEndDate: "授信结束日",
  creditOrgStructureCodeMagnifier: "组织架构放大镜",
  creditOrgStructureCode: "组织架构",
  orgStructureCode: "组织架构编号",
  orgStructureName: "组织架构名称",
  industry: "所属行业",
  creditCalculation: "授信测算",
  creditAdjustmentFactor: "信用调节系数(V)",
  netAssets: "净资产(E)",
  industryCreditRatio: "行业信贷比例(D)",
  targetLeverageRatio: "目标杠杆比例(K)",
  referenceValueFormula: "参考值计算公式",
  creditReferenceValueResult: "授信参考值结果",
  totalProjectInvestment: "项目总投资（T）",
  projectEquity: "项目资本金（E2）",
  projectFinancingRatio: "项目融资比例（S）",
  preCreditQuota: "上期授信额度",
  unRepayAmount: "在途还款金额"
};
