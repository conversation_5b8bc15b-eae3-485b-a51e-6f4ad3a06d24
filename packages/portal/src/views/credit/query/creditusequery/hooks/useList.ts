import { reactive, ref, shallowRef, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import type { CreditApplyInfo } from "../types";
import { useConst } from "@ifs/support";
import { useModelRange } from "@/hooks/conversion";
import { goPage } from "./usePage";
import httpTool from "@/utils/http";
import { getHasChlid, getStructureClientInfo } from "../url";

export const useList = () => {
  const { t } = useI18n();
  //状态
  const yesOrNo = useConst("credit.YesOrNo");
  const creditDetailType = useConst("credit.CreditDetailType");
  const orgStructureDialog = shallowRef();
  const openClientDialog = () => {
    orgStructureDialog.value.openStructureDialog();
  };
  //列表显示列配置
  const tableColumns = [
    {
      width: "200px",
      prop: "creditNo",
      slots: { default: "creditNo" },
      label: t("credit.query.creditusequery.creditNo")
    },
    {
      width: "150px",
      prop: "clientClass",
      label: t("credit.query.creditusequery.clientClass"),
      formatter: { name: "const", const: "credit.ClientClass" }
    },
    {
      width: "150px",
      prop: "clientCode",
      label: t("credit.query.creditusequery.clientCode")
    },
    {
      width: "150px",
      prop: "clientName",
      label: t("credit.query.creditusequery.clientName")
    },
    {
      width: "150px",
      prop: "projectCredit",
      label: t("credit.query.creditusequery.isProjectCredit"),
      formatter: { name: "const", const: "credit.YesOrNo" }
    },
    {
      width: "150px",
      prop: "projectName",
      label: t("credit.query.creditusequery.projectName")
    },
    {
      width: "150px",
      prop: "creditStyle",
      label: t("credit.query.creditusequery.creditStyle"),
      formatter: { name: "const", const: "credit.CreditStyleType" }
    },
    {
      width: "150px",
      prop: "targetCurrencyName",
      label: t("credit.query.creditusequery.targetCurrencyName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "preCreditQuota",
      label: t("credit.query.creditusequery.preCreditQuota"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "totalCreditQuota",
      label: t("credit.query.creditusequery.totalCreditQuota"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "useQuota",
      slots: { default: "useQuota" },
      label: t("credit.query.creditusequery.useQuota"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "unRepayAmount",
      label: t("credit.query.creditusequery.unRepayAmount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "totalBalance",
      label: t("credit.query.creditusequery.totalBalance"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "effectiveDate",
      label: t("credit.query.creditusequery.listEffectiveDate")
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("credit.query.creditusequery.listEndDate")
    },
    {
      width: "150px",
      prop: "creditStatus",
      label: t("credit.query.creditusequery.checkStatus"),
      formatter: { name: "const", const: "credit.CreditStatus" }
    },
    {
      width: "150px",
      prop: "inputUserName",
      label: t("credit.query.creditusequery.inputUserName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "inputTime",
      label: t("credit.query.creditusequery.inputTime"),
      showOverflowTooltip: true
    }
  ];

  const allowSort = [
    "creditNo",
    "clientClass",
    "clientCode",
    "clientName",
    "businessType",
    "creditStyle",
    "targetCurrencyName",
    "creditCategory",
    "creditRiskType",
    "totalCreditQuota",
    "useQuota",
    "totalBalance",
    "effectiveDate",
    "endDate",
    "checkStatus",
    "inputUserName",
    "inputTime",
    "seqNo",
    "creditStatus"
  ];

  //表格模板
  const queryTable = shallowRef();

  //列表查询对象
  const queryFrom = reactive({
    creditStyleTypes: [],
    creditCategorys: [],
    currencyIds: [],
    creditRiskTypes: [],
    amount: [],
    creditStatuses: [],
    clientClasses: [],
    clientIds: [],
    queryHasChild: yesOrNo.NO,
    effectiveDate: [],
    endDate: [],
    creditNo: "",
    projectCredit: "",
    projectNameLike: "",
    creditCodeList: [],
    creditOrgStructureId: -1,
    creditOrgStructClientIds: []
  });

  const { postParams } = useModelRange(["amount", "effectiveDate", "endDate"]);

  //详情抽屉打开
  const id = ref<any>({});
  const detail = shallowRef();
  const handleOpen = (row: CreditApplyInfo) => {
    id.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };

  const useDatail = (row: any) => {
    let seqNo = "";
    if (row.creditDetailType === creditDetailType.CREDIT_UNIT_DETAIL) {
      seqNo = row.seqNo;
    }
    goPage("useDetail", { creditNo: row.creditNo, refSeqNo: seqNo });
  };

  const lazyLoad = (row, node, reslove) => {
    httpTool.post(getHasChlid, { creditNo: row.creditNo, refDetailNo: row.seqNo }).then((res: any) => {
      return reslove(res.data);
    });
  };
  //组织架构事件
  const creditOrgStructureChange = (row: any) => {
    queryFrom.creditOrgStructureId = row.id; //授信组织架构Id
    getSubClientList();
  };
  const getSubClientList = () => {
    if (queryFrom.creditOrgStructureId > 0) {
      httpTool
        .post(getStructureClientInfo, {
          orgStructureId: queryFrom.creditOrgStructureId
        })
        .then((res: any) => {
          if (res.success) {
            subCreditClientList.splice(0);
            res.data.forEach(element => {
              clientIds.push(element.clientId);
            });
          }
        });
    } else {
      creditOrgStructClientIds.splice(0);
    }
  };
  // 重置表单
  const resetForm = () => {
    queryFrom.creditOrgStructureId = "";
  };
  return {
    queryTable,
    tableColumns,
    queryFrom,
    handleOpen,
    id,
    detail,
    allowSort,
    postParams,
    useDatail,
    lazyLoad,
    openClientDialog,
    orgStructureDialog,
    creditOrgStructureChange,
    resetForm
  };
};

export default useList;
