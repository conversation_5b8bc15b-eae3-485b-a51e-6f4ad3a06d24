import httpTool from "@/utils/http";
import { useI18n } from "vue-i18n";
import { reactive, shallowRef, ref, computed, nextTick } from "vue";
import type { BaseRateDto } from "../types";
import { goPage } from "./usePage";
import { deleteUrl, changeSubmitUrl, nextRecentlyEffectiveDateUrl, getSelectDataUrl } from "../url";
import { useConst } from "@ifs/support";
import { DtgEdit } from "@dtg/frontend-plus-icons";
import { useModelRange } from "@/hooks/conversion";
import { useMenuStore } from "@/stores/modules/menu";
import { storeToRefs } from "pinia";
export const useAddList = () => {
  const { t } = useI18n();
  const rowId = ref<number>();
  const submitConfirmText = ref<string>();
  const changeDetail = shallowRef();
  const applicationServiceSelectData = reactive<any[]>([]);
  const checkStatus = useConst("price.CheckStatus");
  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      selectable(row: BaseRateDto) {
        // 已保存、已拒绝的可选
        return [checkStatus.SAVE, checkStatus.REFUSE].includes(row?.checkStatus);
      }
    },
    {
      width: "150px",
      prop: "code",
      groupLabel: t("price.business.baseRateChange.code"),
      compactLabel: t("price.business.baseRateChange.code"),
      slots: { default: "code" },
      groupWidth: "300px",
      label: t("price.business.baseRateChange.code")
    },
    {
      width: "200px",
      prop: "name",
      label: t("price.business.baseRateChange.name")
    },
    {
      width: "200px",
      prop: "baseRateTypeName",
      label: t("price.business.baseRateChange.type")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("price.business.baseRateChange.currency")
    },
    {
      width: "150px",
      prop: "rate",
      label: t("price.business.baseRateChange.rate") + "(%)",
      formatter: "rate",
      align: "right"
    },
    {
      width: "150px",
      prop: "applicationService",
      label: t("price.business.baseRateChange.applicationService"),
      formatter: { name: "const", const: "price.PriceApplicationService" }
    },
    {
      width: "150px",
      prop: "effectiveDate",
      label: t("price.business.baseRateChange.effectiveDate")
    },
    {
      width: "150px",
      prop: "checkStatus",
      label: t("price.business.baseRateChange.status"),
      formatter: { name: "const", const: "price.CheckStatus" }
    },
    {
      width: "200px",
      prop: "modifyTime",
      label: t("price.business.baseRateChange.inputTime")
    },
    {
      width: "220px",
      prop: "operate",
      label: t("price.business.baseRateChange.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];
  // 表格查询对象
  const queryFrom = reactive({
    //币种id
    currencyId: [],
    //利率项目
    name: "",
    //年利率
    rate: null,
    //应用服务
    applicationService: "",
    //类型id
    baseRateTypeId: [],
    //生效日
    effectiveDate: [],
    //状态
    checkStatus: []
  });
  // 表格模板
  const queryTable = shallowRef();
  // 已选列表
  const checkedList = ref<BaseRateDto[]>([]);
  // 抽屉模板
  const drawerRef = shallowRef();
  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length > 0);
  // 控制全选checkbox
  const selectableAll = (rows: BaseRateDto[]) => {
    return !rows;
  };
  // 打开抽屉
  const handleOpen = (row: BaseRateDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      changeDetail.value.setTrueToVisible();
    });
  };
  // 点击修改
  const changeRow = (row: BaseRateDto) => {
    if (row.checkStatus === checkStatus.SAVE || row.checkStatus === checkStatus.REFUSE) {
      goPage("modify", { id: row.id });
    } else {
      goPage("add", { id: row.id });
    }
  };
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  // 批量删除的参数
  const gatherBatchDeleteParams = () => {
    return { listDto: checkedList.value };
  };
  // 批量提交的参数
  const gatherBatchSumitParams = () => {
    return { listDto: checkedList.value };
  };
  // 勾选checkbox
  const handleSelect = (row: BaseRateDto[]) => {
    checkedList.value = row;
  };
  // 列表操作处理
  const generalButtonOption = (row: BaseRateDto) => {
    return [
      {
        type: "change",
        originalProps: {
          icon: DtgEdit,
          type: "primary"
        },
        buttonText: t("price.business.baseRateChange.change"),
        isShow: [checkStatus.APPROVED, checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        emitName: "on-change"
      },
      {
        type: "submit",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: changeSubmitUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          },
          props: {
            resultConfirm: t("price.business.baseRateChange.resultConfirmSubmitSuccess"),
            beforeTrigger: () => {
              submitConfirm(row);
              return true;
            },
            confirmText: submitConfirmText.value
          }
        }
      },
      {
        type: "remove",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ];
  };
  // 定义获取下拉框数据请求参数对象
  const getSelectDataParam = reactive<any>({
    parentPropertyKey: "",
    menuResourceId: null
  });
  // 组装获取下拉框数据请求参数
  const getSelectDataExtraParam = () => {
    const currentMenuPath = "/price/business/baseRateChange";
    // 获取菜单id
    const menuStore = useMenuStore();
    const { menuIdMap } = storeToRefs(menuStore);
    const currentMenu = menuIdMap.value[currentMenuPath];
    if (currentMenu) {
      getSelectDataParam.menuResourceId = currentMenu.menuId;
    }
  };
  //获取下拉框数据
  const getSelectData = async () => {
    getSelectDataExtraParam();
    getSelectDataParam.parentPropertyKey = "PriceApplicationService";
    await httpTool.post(getSelectDataUrl, getSelectDataParam, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        let data = res.data;
        if (data) {
          data = data.filter(p => p.permissionType !== "None");
        }
        applicationServiceSelectData.splice(0, applicationServiceSelectData.length, ...data);
        queryFrom.applicationService = res.data[0].propertyCode;
      }
    });
  };
  const { postParams } = useModelRange(["effectiveDate"]);
  const submitMessage = ref("");
  const beforeDeleteTrigger = () => {
    submitMessage.value = t("price.business.baseRateChange.deleteTip", [checkedList.value.length]);
    return true;
  };
  const deleteRusultConfirm = {
    success: t("price.business.baseRateChange.deleteSuccess"),
    fail: t("price.business.baseRateChange.deleteFail")
  };
  const beforeSubmitTrigger = () => {
    submitMessage.value = t("price.business.baseRateChange.submitTip", [checkedList.value.length]);
    return true;
  };
  const submitRusultConfirm = {
    success:
      queryFrom.applicationService === "DEPOSIT"
        ? t("price.business.baseRateChange.resultConfirmSubmitSuccess")
        : t("price.business.baseRateChange.submitSuccess"),
    fail: t("price.business.baseRateChange.submitFail")
  };

  const clearSelection = () => {
    checkedList.value.splice(0);
  };

  const submitConfirm = async (row: any) => {
    let nextRecentlyEffectiveDate = "";
    await httpTool.post(nextRecentlyEffectiveDateUrl, row).then((res: string) => {
      nextRecentlyEffectiveDate = res.data;
    });
    if (nextRecentlyEffectiveDate === null) {
      submitConfirmText.value = t("price.business.baseRateChange.remind3", [row.effectiveDate]);
    } else {
      submitConfirmText.value = t("price.business.baseRateChange.remind4", [
        row.effectiveDate,
        nextRecentlyEffectiveDate
      ]);
    }
  };

  return {
    tableColumns,
    selectableAll,
    handleSelect,
    isChecked,
    gatherBatchDeleteParams,
    gatherBatchSumitParams,
    generalButtonOption,
    changeRow,
    handleOpen,
    rowId,
    changeDetail,
    drawerRef,
    queryFrom,
    handleSearch,
    queryTable,
    submitMessage,
    beforeDeleteTrigger,
    deleteRusultConfirm,
    beforeSubmitTrigger,
    submitRusultConfirm,
    clearSelection,
    postParams,
    applicationServiceSelectData,
    getSelectData
  };
};
export default useAddList;
