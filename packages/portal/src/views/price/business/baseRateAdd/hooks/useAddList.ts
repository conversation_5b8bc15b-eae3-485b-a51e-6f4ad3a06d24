import httpTool from "@/utils/http";
import { useI18n } from "vue-i18n";
import { reactive, shallowRef, ref, computed, nextTick } from "vue";
import type { BaseRateDto } from "../types";
import { goPage } from "./usePage";
import { deleteUrl, submitUrl, getSelectDataUrl } from "../url";
import { useConst } from "@ifs/support";
import { useModelRange } from "@/hooks/conversion";
import { useMenuStore } from "@/stores/modules/menu";
import { storeToRefs } from "pinia";
export const useAddList = () => {
  const { t } = useI18n();
  const rowId = ref<number>();
  const addDetail = shallowRef();
  const applicationServiceSelectData = reactive<any[]>([]);
  const checkStatus = useConst("price.CheckStatus");
  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      selectable(row: BaseRateDto) {
        // 已保存、已拒绝的可选
        return [checkStatus.SAVE, checkStatus.REFUSE].includes(row?.checkStatus);
      }
    },
    {
      width: "150px",
      prop: "code",
      groupLabel: t("price.business.baseRateAdd.code"),
      compactLabel: t("price.business.baseRateAdd.code"),
      slots: { default: "code" },
      groupWidth: "300px",
      label: t("price.business.baseRateAdd.code")
    },
    {
      width: "200px",
      prop: "name",
      label: t("price.business.baseRateAdd.name")
    },
    {
      width: "200px",
      prop: "baseRateTypeName",
      label: t("price.business.baseRateAdd.type")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("price.business.baseRateAdd.currency")
    },
    {
      width: "150px",
      prop: "rate",
      label: t("price.business.baseRateAdd.rate") + "(%)",
      formatter: "rate",
      align: "right"
    },
    {
      width: "150px",
      prop: "applicationService",
      label: t("price.business.baseRateAdd.applicationService"),
      formatter: { name: "const", const: "price.PriceApplicationService" }
    },
    {
      width: "150px",
      prop: "effectiveDate",
      label: t("price.business.baseRateAdd.effectiveDate")
    },
    {
      width: "150px",
      prop: "checkStatus",
      label: t("price.business.baseRateAdd.status"),
      formatter: { name: "const", const: "price.CheckStatus" }
    },
    {
      width: "200px",
      prop: "inputTime",
      label: t("price.business.baseRateAdd.inputTime")
    },
    {
      width: "220px",
      prop: "operate",
      label: t("price.business.baseRateAdd.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];
  // 表格查询对象
  const queryFrom = reactive({
    //币种id
    currencyId: [],
    //利率项目
    name: "",
    //年利率
    rate: null,
    //应用服务
    applicationService: "",
    //类型id
    baseRateTypeId: [],
    //生效日
    effectiveDate: [],
    //状态
    checkStatus: []
  });
  // 表格模板
  const queryTable = shallowRef();
  // 已选列表
  const checkedList = ref<BaseRateDto[]>([]);
  // 抽屉模板
  const drawerRef = shallowRef();
  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length > 0);
  // 控制全选checkbox
  const selectableAll = (rows: BaseRateDto[]) => {
    return !rows;
  };
  // 打开抽屉
  const handleOpen = (row: BaseRateDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      addDetail.value.setTrueToVisible();
    });
  };
  // 点击修改
  const changeRow = (row: BaseRateDto) => {
    goPage("modify", { id: row.id });
  };
  // 新增跳转
  const add = () => {
    goPage("add");
  };
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  // 批量删除的参数
  const gatherBatchDeleteParams = () => {
    return { listDto: checkedList.value };
  };
  // 批量提交的参数
  const gatherBatchSumitParams = () => {
    return { listDto: checkedList.value };
  };
  // 勾选checkbox
  const handleSelect = (row: BaseRateDto[]) => {
    checkedList.value = row;
  };
  // 列表操作处理
  const generalButtonOption = (row: BaseRateDto) => {
    return [
      {
        type: "modify",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus)
      },
      {
        type: "submit",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: submitUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      },
      {
        type: "remove",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ];
  };
  // 定义获取下拉框数据请求参数对象
  const getSelectDataParam = reactive<any>({
    parentPropertyKey: "",
    menuResourceId: null
  });
  // 组装获取下拉框数据请求参数
  const getSelectDataExtraParam = () => {
    const currentMenuPath = "/price/business/baseRateAdd";
    // 获取菜单id
    const menuStore = useMenuStore();
    const { menuIdMap } = storeToRefs(menuStore);
    const currentMenu = menuIdMap.value[currentMenuPath];
    if (currentMenu) {
      getSelectDataParam.menuResourceId = currentMenu.menuId;
    }
  };
  //获取下拉框数据
  const getSelectData = async () => {
    getSelectDataExtraParam();
    getSelectDataParam.parentPropertyKey = "PriceApplicationService";
    await httpTool.post(getSelectDataUrl, getSelectDataParam, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        let data = res.data;
        if (data) {
          data = data.filter(p => p.permissionType !== "None");
        }
        applicationServiceSelectData.splice(0, applicationServiceSelectData.length, ...data);
        queryFrom.applicationService = res.data[0].propertyCode;
      }
    });
  };
  const { postParams } = useModelRange(["effectiveDate"]);
  const submitMessage = ref("");
  const beforeDeleteTrigger = () => {
    submitMessage.value = t("price.business.baseRateAdd.deleteTip", [checkedList.value.length]);
    return true;
  };
  const deleteRusultConfirm = {
    success: t("price.business.baseRateAdd.deleteSuccess"),
    fail: t("price.business.baseRateAdd.deleteFail")
  };
  const beforeSubmitTrigger = () => {
    submitMessage.value = t("price.business.baseRateAdd.submitTip", [checkedList.value.length]);
    return true;
  };
  const submitRusultConfirm = {
    success: t("price.business.baseRateAdd.submitSuccess"),
    fail: t("price.business.baseRateAdd.submitFail")
  };

  const clearSelection = () => {
    checkedList.value.splice(0);
  };

  return {
    tableColumns,
    selectableAll,
    handleSelect,
    add,
    isChecked,
    gatherBatchDeleteParams,
    gatherBatchSumitParams,
    generalButtonOption,
    changeRow,
    handleOpen,
    rowId,
    addDetail,
    drawerRef,
    queryFrom,
    handleSearch,
    queryTable,
    submitMessage,
    beforeDeleteTrigger,
    deleteRusultConfirm,
    beforeSubmitTrigger,
    submitRusultConfirm,
    clearSelection,
    postParams,
    applicationServiceSelectData,
    getSelectData
  };
};
export default useAddList;
