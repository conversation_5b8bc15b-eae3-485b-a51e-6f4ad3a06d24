import httpTool from "@/utils/http";
import type { BaseRateDto } from "../types";
import { goPage } from "../hooks/usePage";
import { reactive, ref, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { findByIdUrl, openDateUrl, getSelectDataUrl } from "../url";
import { useConst } from "@ifs/support";
import { add, subtract, multiply, divide } from "@/utils/currency";
import { useMenuStore } from "@/stores/modules/menu";
import { storeToRefs } from "pinia";

export const useAddModify = () => {
  const priceBusinessType = useConst("price.PriceBusinessType");
  const depositVarieties = useConst("price.DepositVarieties");
  const yesOrNo = useConst("price.YesOrNo");
  const baseRateType = useConst("price.BaseRateType");
  const loanTerm = useConst("price.LoanTerm");

  const baseRateModifyInfo = reactive<BaseRateDto>({
    id: null,
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    code: "",
    name: "",
    businessType: "",
    termId: null,
    term: null,
    termShow: "",
    loanTerm: "",
    effectiveDate: "",
    rate: null,
    baseRateTypeId: null,
    baseRateTypeName: "",
    baseRateType: "",
    executeDate: "",
    applicationService: "",
    maxFlag: yesOrNo.NO,
    maxFloatPoint: "",
    maxFloatPointNum: 0,
    maxFloatProportion: "",
    maxFloatProportionNum: 0,
    maxRate: 0,
    minFlag: yesOrNo.NO,
    minFloatPoint: "",
    minFloatPointNum: 0,
    minFloatProportion: "",
    minFloatProportionNum: 0,
    minRate: 0,
    originId: null,
    changingFlag: "",
    checkStatus: null,
    version: null
  });

  const { t } = useI18n();
  const form = ref();
  const termRef = ref();
  const loanTermRef = ref();

  const termParams = reactive({
    currencyId: baseRateModifyInfo.currencyId,
    depositVarieties: depositVarieties.FIXED
  });

  const loanTermData = reactive({
    data: []
  });
  const businessTypeSelectData = reactive<any[]>([]);
  const applicationServiceSelectData = reactive<any[]>([]);

  //类型change事件
  const baseRateTypeChange = (value: number, info: any) => {
    baseRateModifyInfo.baseRateTypeName = info.name;
    baseRateModifyInfo.baseRateType = info.baseRateType;
    if (
      baseRateModifyInfo.businessType === priceBusinessType.LOAN &&
      baseRateModifyInfo.baseRateType === baseRateType.TR08
    ) {
      loanTermData.data = loanTerm.pickConst([
        loanTerm.BASE_SIX_MONTH,
        loanTerm.BASE_ONE_YEAR,
        loanTerm.BASE_THREE_YEAR,
        loanTerm.BASE_FIVE_YEAR,
        loanTerm.BASE_MORE_THAN_FIVE_YEAR
      ]);
    }
    if (
      baseRateModifyInfo.businessType === priceBusinessType.LOAN &&
      baseRateModifyInfo.baseRateType === baseRateType.TR05
    ) {
      loanTermData.data = loanTerm.pickConst([loanTerm.LPR_ONE_YEAR, loanTerm.LPR_MORE_THAN_FIVE_YEAR]);
    }
    baseRateModifyInfo.loanTerm = "";
    nextTick(() => {
      loanTermRef.value.initData();
    });
  };

  //业务类型change事件
  const businessTypeChange = () => {
    //控制期限下拉框
    if (
      baseRateModifyInfo.businessType === priceBusinessType.FIXED_DEPOSIT ||
      baseRateModifyInfo.businessType === priceBusinessType.NOTICE_DEPOSIT
    ) {
      if (baseRateModifyInfo.businessType === priceBusinessType.FIXED_DEPOSIT) {
        termParams.depositVarieties = depositVarieties.FIXED;
      } else {
        termParams.depositVarieties = depositVarieties.NOTICE;
      }
      baseRateModifyInfo.termId = null;
      termRef.value.initRemoteData();
    } else {
      baseRateModifyInfo.termId = null;
      baseRateModifyInfo.term = null;
      baseRateModifyInfo.termShow = "";
    }
    //控制贷款期限下拉框
    if (baseRateModifyInfo.businessType !== "") {
      if (
        baseRateModifyInfo.businessType === priceBusinessType.LOAN &&
        baseRateModifyInfo.baseRateType === baseRateType.TR08
      ) {
        loanTermData.data = loanTerm.pickConst([
          loanTerm.BASE_SIX_MONTH,
          loanTerm.BASE_ONE_YEAR,
          loanTerm.BASE_THREE_YEAR,
          loanTerm.BASE_FIVE_YEAR,
          loanTerm.BASE_MORE_THAN_FIVE_YEAR
        ]);
      }
      if (
        baseRateModifyInfo.businessType === priceBusinessType.LOAN &&
        baseRateModifyInfo.baseRateType === baseRateType.TR05
      ) {
        loanTermData.data = loanTerm.pickConst([loanTerm.LPR_ONE_YEAR, loanTerm.LPR_MORE_THAN_FIVE_YEAR]);
      }
    }
    baseRateModifyInfo.loanTerm = "";
    nextTick(() => {
      loanTermRef.value.initData();
    });
  };

  //期限change事件
  const termChange = (value: number, info: any) => {
    baseRateModifyInfo.termId = info.id;
    baseRateModifyInfo.term = info.term;
    baseRateModifyInfo.termShow = info.termShow;
  };

  //最高上调change事件
  const maxFlagChange = () => {
    if (baseRateModifyInfo.maxFlag === yesOrNo.NO) {
      baseRateModifyInfo.maxFloatPointNum = 0;
      baseRateModifyInfo.maxFloatProportionNum = 0;
      baseRateModifyInfo.maxRate = 0;
    }
  };

  //最低下调change事件
  const minFlagChange = () => {
    if (baseRateModifyInfo.minFlag === yesOrNo.NO) {
      baseRateModifyInfo.minFloatPointNum = 0;
      baseRateModifyInfo.minFloatProportionNum = 0;
      baseRateModifyInfo.minRate = 0;
    }
  };

  //计算最高上调/最低下调利率
  const calculationChange = () => {
    if (baseRateModifyInfo.maxFloatPointNum === "") {
      baseRateModifyInfo.maxFloatPointNum = 0;
    }
    if (baseRateModifyInfo.maxFloatProportionNum === "") {
      baseRateModifyInfo.maxFloatProportionNum = 0;
    }
    if (baseRateModifyInfo.minFloatPointNum === "") {
      baseRateModifyInfo.minFloatPointNum = 0;
    }
    if (baseRateModifyInfo.minFloatProportionNum === "") {
      baseRateModifyInfo.minFloatProportionNum = 0;
    }
    if (baseRateModifyInfo.rate !== null) {
      //计算最高上调
      if (baseRateModifyInfo.maxFlag === yesOrNo.YES) {
        if (baseRateModifyInfo.maxFloatProportionNum > 0) {
          baseRateModifyInfo.maxRate = multiply(
            baseRateModifyInfo.rate,
            add(1, divide(baseRateModifyInfo.maxFloatProportionNum, 100, 6))
          );
          if (baseRateModifyInfo.maxFloatPointNum > 0) {
            baseRateModifyInfo.maxRate = add(
              baseRateModifyInfo.maxRate,
              divide(baseRateModifyInfo.maxFloatPointNum, 100, 6)
            );
          }
        } else if (baseRateModifyInfo.maxFloatPointNum > 0) {
          baseRateModifyInfo.maxRate = add(
            baseRateModifyInfo.rate,
            divide(baseRateModifyInfo.maxFloatPointNum, 100, 6)
          );
        } else {
          baseRateModifyInfo.maxRate = baseRateModifyInfo.rate;
        }
        if (baseRateModifyInfo.maxRate > 100) {
          baseRateModifyInfo.maxRate = 100;
        }
      }
      //计算最低下调
      if (baseRateModifyInfo.minFlag === yesOrNo.YES) {
        if (baseRateModifyInfo.minFloatProportionNum > 0) {
          baseRateModifyInfo.minRate = multiply(
            baseRateModifyInfo.rate,
            subtract(1, divide(baseRateModifyInfo.minFloatProportionNum, 100, 6))
          );
          if (baseRateModifyInfo.minFloatPointNum > 0) {
            baseRateModifyInfo.minRate = subtract(
              baseRateModifyInfo.minRate,
              divide(baseRateModifyInfo.minFloatPointNum, 100, 6)
            );
          }
        } else if (baseRateModifyInfo.minFloatPointNum > 0) {
          baseRateModifyInfo.minRate = subtract(
            baseRateModifyInfo.rate,
            divide(baseRateModifyInfo.minFloatPointNum, 100, 6)
          );
        } else {
          baseRateModifyInfo.minRate = baseRateModifyInfo.rate;
        }
        if (baseRateModifyInfo.minRate < 0) {
          baseRateModifyInfo.minRate = 0;
        }
      }
    }
  };

  // 校验规则
  const rules = reactive({
    name: [
      {
        required: true,
        message: t("price.business.baseRateAdd.nameHolder"),
        trigger: "blur"
      }
    ],
    baseRateTypeId: [
      {
        required: true,
        message: t("price.business.baseRateAdd.typeHolder"),
        trigger: "blur"
      }
    ],
    rate: [
      {
        required: true,
        message: t("price.business.baseRateAdd.rateHolder"),
        trigger: "blur"
      }
    ],
    applicationService: [
      {
        required: true,
        message: t("price.business.baseRateAdd.applicationServiceHolder"),
        trigger: "blur"
      }
    ],
    effectiveDate: [
      {
        required: true,
        message: t("price.business.baseRateAdd.effectiveDateHolder"),
        trigger: "blur"
      }
    ]
  });

  // 保存
  const saveInfo = () => {
    return baseRateModifyInfo;
  };

  // 提交
  const submitInfo = () => {
    return baseRateModifyInfo;
  };

  // 删除
  const removeInfo = () => {
    return baseRateModifyInfo;
  };

  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      baseRateModifyInfo.version = res.data.version;
    }
  };

  // 提交成功
  const submitSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };

  // 删除成功
  const deleteSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };

  // 返回列表页
  const goBack = () => {
    goPage("list");
  };

  // 初始化方法
  const modifyCreate = (id: number) => {
    return httpTool.post(findByIdUrl, { id }, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res?.success) {
        Object.assign(baseRateModifyInfo, res.data);
        if (baseRateModifyInfo.businessType === priceBusinessType.FIXED_DEPOSIT) {
          termParams.currencyId = res.data.currencyId;
          termParams.depositVarieties = depositVarieties.FIXED;
        } else {
          termParams.currencyId = res.data.currencyId;
          termParams.depositVarieties = depositVarieties.NOTICE;
        }
        nextTick(() => {
          termRef.value.initRemoteData();
        });
        if (
          baseRateModifyInfo.businessType === priceBusinessType.LOAN &&
          baseRateModifyInfo.baseRateType === baseRateType.TR08
        ) {
          loanTermData.data = loanTerm.pickConst([
            loanTerm.BASE_SIX_MONTH,
            loanTerm.BASE_ONE_YEAR,
            loanTerm.BASE_THREE_YEAR,
            loanTerm.BASE_FIVE_YEAR,
            loanTerm.BASE_MORE_THAN_FIVE_YEAR
          ]);
        }
        if (
          baseRateModifyInfo.businessType === priceBusinessType.LOAN &&
          baseRateModifyInfo.baseRateType === baseRateType.TR05
        ) {
          loanTermData.data = loanTerm.pickConst([loanTerm.LPR_ONE_YEAR, loanTerm.LPR_MORE_THAN_FIVE_YEAR]);
        }
        nextTick(() => {
          loanTermRef.value.initData();
        });
        getOpenDate();
        getSelectDataExtraParam();
        getSelectDataParam.parentPropertyKey = "PriceBusinessType";
        httpTool.post(getSelectDataUrl, getSelectDataParam, { ignoreGlobalErrorTip: false }).then((res: any) => {
          if (res.success) {
            let data = res.data;
            if (data) {
              data = data.filter(p => p.permissionType !== "None");
            }
            businessTypeSelectData.splice(0, businessTypeSelectData.length, ...data);
          }
        });
        getSelectDataParam.parentPropertyKey = "PriceApplicationService";
        httpTool.post(getSelectDataUrl, getSelectDataParam, { ignoreGlobalErrorTip: false }).then((res: any) => {
          if (res.success) {
            let data = res.data;
            if (data) {
              data = data.filter(p => p.permissionType !== "None");
            }
            applicationServiceSelectData.splice(0, applicationServiceSelectData.length, ...data);
          }
        });
      }
    });
  };

  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl, {}, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res?.success) {
        baseRateModifyInfo.executeDate = res.data.onlineDate;
      }
    });
  };

  // 定义获取下拉框数据请求参数对象
  const getSelectDataParam = reactive<any>({
    parentPropertyKey: "",
    menuResourceId: null
  });

  // 组装获取下拉框数据请求参数
  const getSelectDataExtraParam = () => {
    const currentMenuPath = "/price/business/baseRateAdd";
    // 获取菜单id
    const menuStore = useMenuStore();
    const { menuIdMap } = storeToRefs(menuStore);
    const currentMenu = menuIdMap.value[currentMenuPath];
    if (currentMenu) {
      getSelectDataParam.menuResourceId = currentMenu.menuId;
    }
  };

  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate((valid: any) => {
      if (!valid) {
        return result;
      }
      calculationChange();
    });
    return result;
  };
  // 构造审批时参数
  const postApprovalInfo = (params: any) => {
    let isApprovalPass = false;
    let transition = "";
    if (params.ifinanceWorkFlowDto.agreeChoose) {
      isApprovalPass = true;
      transition = params.ifinanceWorkFlowDto.agreeChoose;
    } else {
      transition = params.ifinanceWorkFlowDto.refuseChoose;
    }
    return {
      taskId: params.ifinanceWorkFlowDto.taskId,
      approvalPass: isApprovalPass,
      approvalContent: params.ifinanceWorkFlowDto.idea,
      approveMode: params.ifinanceWorkFlowDto.approveMode,
      transition: transition
    };
  };
  return {
    baseRateModifyInfo,
    rules,
    baseRateTypeChange,
    formValidator,
    form,
    saveInfo,
    submitInfo,
    removeInfo,
    saveSuccess,
    submitSuccess,
    deleteSuccess,
    goBack,
    modifyCreate,
    postApprovalInfo,
    calculationChange,
    termChange,
    maxFlagChange,
    minFlagChange,
    businessTypeChange,
    termRef,
    termParams,
    loanTermRef,
    loanTermData,
    businessTypeSelectData,
    applicationServiceSelectData
  };
};
export default useAddModify;
