import httpTool from "@/utils/http";
import type { BaseRateDto } from "../types";
import { goPage } from "../hooks/usePage";
import { reactive, ref, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { openDateUrl, getSelectDataUrl } from "../url";
import { useConst } from "@ifs/support";
import { useUserStoreHook } from "@/stores/modules/user";
import { add, subtract, multiply, divide } from "@/utils/currency";
import { useMenuStore } from "@/stores/modules/menu";
import { storeToRefs } from "pinia";

export const useAdd = () => {
  const priceBusinessType = useConst("price.PriceBusinessType");
  const depositVarieties = useConst("price.DepositVarieties");
  const yesOrNo = useConst("price.YesOrNo");
  const baseRateType = useConst("price.BaseRateType");
  const loanTerm = useConst("price.LoanTerm");

  const { defaultCurrencyId, defaultCurrencyCode, defaultCurrencyName } = storeToRefs(useUserStoreHook());

  const baseRateAddInfo = reactive<BaseRateDto>({
    id: null,
    currencyId: defaultCurrencyId.value,
    currencyCode: defaultCurrencyCode.value,
    currencyName: defaultCurrencyName.value,
    code: "",
    name: "",
    businessType: "",
    termId: null,
    term: null,
    termShow: "",
    loanTerm: "",
    effectiveDate: "",
    rate: null,
    baseRateTypeId: null,
    baseRateTypeName: "",
    baseRateType: "",
    executeDate: "",
    applicationService: "",
    maxFlag: yesOrNo.NO,
    maxFloatPoint: "",
    maxFloatPointNum: 0,
    maxFloatProportion: "",
    maxFloatProportionNum: 0,
    maxRate: 0,
    minFlag: yesOrNo.NO,
    minFloatPoint: "",
    minFloatPointNum: 0,
    minFloatProportion: "",
    minFloatProportionNum: 0,
    minRate: 0,
    originId: null,
    changingFlag: "",
    checkStatus: null,
    version: null
  });

  const { t } = useI18n();
  const form = ref();
  const termRef = ref();
  const loanTermRef = ref();

  const termParams = reactive({
    currencyId: baseRateAddInfo.currencyId,
    depositVarieties: depositVarieties.FIXED
  });

  const loanTermData = reactive({
    data: []
  });
  const businessTypeSelectData = reactive<any[]>([]);
  const applicationServiceSelectData = reactive<any[]>([]);

  // 币种change事件
  const currencyChange = (value: number, info: any) => {
    baseRateAddInfo.currencyId = info.currencyId;
    baseRateAddInfo.currencyCode = info.currencyCode;
    baseRateAddInfo.currencyName = info.currencyName;
    baseRateAddInfo.termId = null;
    baseRateAddInfo.term = null;
    baseRateAddInfo.termShow = "";
    termParams.currencyId = info.currencyId;
    if (termRef.value) {
      termRef.value.initRemoteData();
    }
  };

  //类型change事件
  const baseRateTypeChange = (value: number, info: any) => {
    baseRateAddInfo.baseRateTypeName = info.name;
    baseRateAddInfo.baseRateType = info.baseRateType;
    if (baseRateAddInfo.businessType === priceBusinessType.LOAN && baseRateAddInfo.baseRateType === baseRateType.TR08) {
      loanTermData.data = loanTerm.pickConst([
        loanTerm.BASE_SIX_MONTH,
        loanTerm.BASE_ONE_YEAR,
        loanTerm.BASE_THREE_YEAR,
        loanTerm.BASE_FIVE_YEAR,
        loanTerm.BASE_MORE_THAN_FIVE_YEAR
      ]);
    }
    if (baseRateAddInfo.businessType === priceBusinessType.LOAN && baseRateAddInfo.baseRateType === baseRateType.TR05) {
      loanTermData.data = loanTerm.pickConst([loanTerm.LPR_ONE_YEAR, loanTerm.LPR_MORE_THAN_FIVE_YEAR]);
    }
    baseRateAddInfo.loanTerm = "";
    nextTick(() => {
      loanTermRef.value.initData();
    });
  };

  //业务类型change事件
  const businessTypeChange = () => {
    //控制期限下拉框
    if (
      baseRateAddInfo.businessType === priceBusinessType.FIXED_DEPOSIT ||
      baseRateAddInfo.businessType === priceBusinessType.NOTICE_DEPOSIT
    ) {
      if (baseRateAddInfo.businessType === priceBusinessType.FIXED_DEPOSIT) {
        termParams.depositVarieties = depositVarieties.FIXED;
      } else {
        termParams.depositVarieties = depositVarieties.NOTICE;
      }
      termRef.value.initRemoteData();
    } else {
      baseRateAddInfo.termId = null;
      baseRateAddInfo.term = null;
      baseRateAddInfo.termShow = "";
    }
    //控制贷款期限下拉框
    if (baseRateAddInfo.businessType !== "") {
      if (
        baseRateAddInfo.businessType === priceBusinessType.LOAN &&
        baseRateAddInfo.baseRateType === baseRateType.TR08
      ) {
        loanTermData.data = loanTerm.pickConst([
          loanTerm.BASE_SIX_MONTH,
          loanTerm.BASE_ONE_YEAR,
          loanTerm.BASE_THREE_YEAR,
          loanTerm.BASE_FIVE_YEAR,
          loanTerm.BASE_MORE_THAN_FIVE_YEAR
        ]);
      }
      if (
        baseRateAddInfo.businessType === priceBusinessType.LOAN &&
        baseRateAddInfo.baseRateType === baseRateType.TR05
      ) {
        loanTermData.data = loanTerm.pickConst([loanTerm.LPR_ONE_YEAR, loanTerm.LPR_MORE_THAN_FIVE_YEAR]);
      }
    }
    baseRateAddInfo.loanTerm = "";
    nextTick(() => {
      loanTermRef.value.initData();
    });
  };

  //期限change事件
  const termChange = (value: number, info: any) => {
    baseRateAddInfo.termId = info.id;
    baseRateAddInfo.term = info.term;
    baseRateAddInfo.termShow = info.termShow;
  };

  //最高上调change事件
  const maxFlagChange = () => {
    if (baseRateAddInfo.maxFlag === yesOrNo.NO) {
      baseRateAddInfo.maxFloatPointNum = 0;
      baseRateAddInfo.maxFloatProportionNum = 0;
      baseRateAddInfo.maxRate = 0;
    }
  };

  //最低下调change事件
  const minFlagChange = () => {
    if (baseRateAddInfo.minFlag === yesOrNo.NO) {
      baseRateAddInfo.minFloatPointNum = 0;
      baseRateAddInfo.minFloatProportionNum = 0;
      baseRateAddInfo.minRate = 0;
    }
  };

  //计算最高上调/最低下调利率
  const calculationChange = () => {
    if (baseRateAddInfo.maxFloatPointNum === "") {
      baseRateAddInfo.maxFloatPointNum = 0;
    }
    if (baseRateAddInfo.maxFloatProportionNum === "") {
      baseRateAddInfo.maxFloatProportionNum = 0;
    }
    if (baseRateAddInfo.minFloatPointNum === "") {
      baseRateAddInfo.minFloatPointNum = 0;
    }
    if (baseRateAddInfo.minFloatProportionNum === "") {
      baseRateAddInfo.minFloatProportionNum = 0;
    }
    if (baseRateAddInfo.rate !== null) {
      //计算最高上调
      if (baseRateAddInfo.maxFlag === yesOrNo.YES) {
        if (baseRateAddInfo.maxFloatProportionNum > 0) {
          baseRateAddInfo.maxRate = multiply(
            baseRateAddInfo.rate,
            add(1, divide(baseRateAddInfo.maxFloatProportionNum, 100, 6))
          );
          if (baseRateAddInfo.maxFloatPointNum > 0) {
            baseRateAddInfo.maxRate = add(baseRateAddInfo.maxRate, divide(baseRateAddInfo.maxFloatPointNum, 100, 6));
          }
        } else if (baseRateAddInfo.maxFloatPointNum > 0) {
          baseRateAddInfo.maxRate = add(baseRateAddInfo.rate, divide(baseRateAddInfo.maxFloatPointNum, 100, 6));
        } else {
          baseRateAddInfo.maxRate = baseRateAddInfo.rate;
        }
        if (baseRateAddInfo.maxRate > 100) {
          baseRateAddInfo.maxRate = 100;
        }
      }
      //计算最低下调
      if (baseRateAddInfo.minFlag === yesOrNo.YES) {
        if (baseRateAddInfo.minFloatProportionNum > 0) {
          baseRateAddInfo.minRate = multiply(
            baseRateAddInfo.rate,
            subtract(1, divide(baseRateAddInfo.minFloatProportionNum, 100, 6))
          );
          if (baseRateAddInfo.minFloatPointNum > 0) {
            baseRateAddInfo.minRate = subtract(
              baseRateAddInfo.minRate,
              divide(baseRateAddInfo.minFloatPointNum, 100, 6)
            );
          }
        } else if (baseRateAddInfo.minFloatPointNum > 0) {
          baseRateAddInfo.minRate = subtract(baseRateAddInfo.rate, divide(baseRateAddInfo.minFloatPointNum, 100, 6));
        } else {
          baseRateAddInfo.minRate = baseRateAddInfo.rate;
        }
        if (baseRateAddInfo.minRate < 0) {
          baseRateAddInfo.minRate = 0;
        }
      }
    }
  };

  // 校验规则
  const rules = reactive({
    currencyId: [
      {
        required: true,
        message: t("price.business.baseRateAdd.currencyHolder"),
        trigger: "blur"
      }
    ],
    code: [
      {
        required: true,
        message: t("price.business.baseRateAdd.codeHolder"),
        trigger: "blur"
      }
    ],
    name: [
      {
        required: true,
        message: t("price.business.baseRateAdd.nameHolder"),
        trigger: "blur"
      }
    ],
    baseRateTypeId: [
      {
        required: true,
        message: t("price.business.baseRateAdd.typeHolder"),
        trigger: "blur"
      }
    ],
    rate: [
      {
        required: true,
        message: t("price.business.baseRateAdd.rateHolder"),
        trigger: "blur"
      }
    ],
    applicationService: [
      {
        required: true,
        message: t("price.business.baseRateAdd.applicationServiceHolder"),
        trigger: "blur"
      }
    ],
    effectiveDate: [
      {
        required: true,
        message: t("price.business.baseRateAdd.effectiveDateHolder"),
        trigger: "blur"
      }
    ]
  });

  // 保存
  const saveInfo = () => {
    return baseRateAddInfo;
  };

  // 提交
  const submitInfo = () => {
    return baseRateAddInfo;
  };

  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      baseRateAddInfo.id = res.data.id;
      baseRateAddInfo.version = res.data.version;
      baseRateAddInfo.checkStatus = res.data.checkStatus;
    }
  };

  // 提交成功
  const submitSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };

  // 返回列表页
  const goBack = () => {
    goPage("list");
  };

  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      baseRateAddInfo.effectiveDate = res.data.onlineDate;
      baseRateAddInfo.executeDate = res.data.onlineDate;
    });
  };

  // 定义获取下拉框数据请求参数对象
  const getSelectDataParam = reactive<any>({
    parentPropertyKey: "",
    menuResourceId: null
  });

  // 组装获取下拉框数据请求参数
  const getSelectDataExtraParam = () => {
    const currentMenuPath = "/price/business/baseRateAdd";
    // 获取菜单id
    const menuStore = useMenuStore();
    const { menuIdMap } = storeToRefs(menuStore);
    const currentMenu = menuIdMap.value[currentMenuPath];
    if (currentMenu) {
      getSelectDataParam.menuResourceId = currentMenu.menuId;
    }
  };

  //获取下拉框数据
  const getSelectData = async () => {
    getSelectDataExtraParam();
    getSelectDataParam.parentPropertyKey = "PriceBusinessType";
    await httpTool.post(getSelectDataUrl, getSelectDataParam, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        let data = res.data;
        if (data) {
          data = data.filter(p => p.permissionType !== "None");
        }
        businessTypeSelectData.splice(0, businessTypeSelectData.length, ...data);
      }
    });
    getSelectDataParam.parentPropertyKey = "PriceApplicationService";
    await httpTool.post(getSelectDataUrl, getSelectDataParam, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        let data = res.data;
        if (data) {
          data = data.filter(p => p.permissionType !== "None");
        }
        applicationServiceSelectData.splice(0, applicationServiceSelectData.length, ...data);
      }
    });
  };

  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate((valid: any) => {
      if (!valid) {
        return result;
      }
      calculationChange();
    });
    return result;
  };

  return {
    baseRateAddInfo,
    rules,
    currencyChange,
    baseRateTypeChange,
    formValidator,
    form,
    saveInfo,
    submitInfo,
    saveSuccess,
    submitSuccess,
    goBack,
    getOpenDate,
    getSelectData,
    calculationChange,
    termChange,
    maxFlagChange,
    minFlagChange,
    businessTypeChange,
    termRef,
    termParams,
    loanTermRef,
    loanTermData,
    businessTypeSelectData,
    applicationServiceSelectData
  };
};
export default useAdd;
