import { useI18n } from "vue-i18n";
import { reactive, shallowRef, ref, nextTick } from "vue";
import type { ExchangeRateDto } from "../types";
import { goPage } from "./usePage";
import { useModelRange } from "@/hooks/conversion";
export const useList = () => {
  const { t } = useI18n();
  const rowId = ref<number>();
  const detail = shallowRef();
  // 表格配置
  const tableColumns = [
    {
      width: "100px",
      prop: "number",
      slots: { default: "number" },
      label: t("price.setting.exchangeRate.number"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "originCurrencyName",
      label: t("price.setting.exchangeRate.originCurrency")
    },
    {
      width: "150px",
      prop: "targetCurrencyName",
      label: t("price.setting.exchangeRate.targetCurrency")
    },
    {
      width: "150px",
      prop: "midPrice",
      label: t("price.setting.exchangeRate.midPrice"),
      formatter: { name: "rate", precision: 4, symbol: "" },
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "buyPrice",
      label: t("price.setting.exchangeRate.buyPrice"),
      formatter: { name: "rate", precision: 4, symbol: "" },
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "salePrice",
      label: t("price.setting.exchangeRate.salePrice"),
      formatter: { name: "rate", precision: 4, symbol: "" },
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "effectiveDate",
      label: t("price.setting.exchangeRate.effectiveDate")
    },
    {
      width: "150px",
      prop: "modifyUserName",
      label: t("price.setting.exchangeRate.modifyUserName")
    },
    {
      width: "150px",
      prop: "modifyTime",
      label: t("price.setting.exchangeRate.modifyTime")
    },
    {
      width: "220px",
      prop: "operate",
      label: t("price.setting.exchangeRate.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];
  const allowSort = [
    "originCurrencyName",
    "targetCurrencyName",
    "buyPrice",
    "salePrice",
    "midPrice",
    "effectiveDate",
    "modifyTime",
    "modifyTime"
  ];
  // 表格查询对象
  const queryFrom = reactive({
    //原币种
    originCurrencyId: [],
    //兑换币种
    targetCurrencyId: [],
    //生效日
    effectiveDate: []
  });
  // 表格模板
  const queryTable = shallowRef();
  // 打开抽屉
  const handleOpen = (row: ExchangeRateDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  // 新增跳转
  const add = () => {
    goPage("add");
  };
  // 点击修改
  const changeRow = (row: ExchangeRateDto) => {
    goPage("modify", { id: row.id });
  };
  // 列表操作处理
  const generalButtonOption = () => {
    return [
      {
        type: "modify",
        isShow: true
      }
    ];
  };
  // 导入成功
  const importSuccess = (res: any) => {
    if (res.success) {
      handleSearch();
    }
  };
  const { postParams } = useModelRange(["effectiveDate"]);
  return {
    tableColumns,
    queryFrom,
    queryTable,
    add,
    changeRow,
    generalButtonOption,
    handleOpen,
    rowId,
    detail,
    allowSort,
    importSuccess,
    postParams
  };
};
export default useList;
