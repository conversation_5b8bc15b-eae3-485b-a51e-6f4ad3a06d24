import httpTool from "@/utils/http";
import type { ExchangeRateDto } from "../types";
import { goPage } from "./usePage";
import { reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { FMessageBox } from "@dtg/frontend-plus";
import { multiply } from "@/utils/currency";
import { openDateUrl } from "../url";

export const useAdd = () => {
  const { t } = useI18n();
  const form = ref();
  const exchangeRateDto = reactive<ExchangeRateDto>({
    id: null,
    originCurrencyId: null,
    originCurrencyCode: "",
    originCurrencyName: "",
    targetCurrencyId: 1,
    targetCurrencyCode: "CNY",
    targetCurrencyName: "人民币元",
    effectiveDate: "",
    buyPrice: 0,
    salePrice: 0,
    midPrice: 0,
    version: null
  });

  // 变更事件
  const originCurrencyChange = (value: number, info: any) => {
    exchangeRateDto.originCurrencyCode = info.currencyCode;
    exchangeRateDto.originCurrencyName = info.currencyName;
  };
  const targetCurrencyChange = (value: number, info: any) => {
    exchangeRateDto.targetCurrencyCode = info.currencyCode;
    exchangeRateDto.targetCurrencyName = info.currencyName;
  };
  // 保存
  const saveInfo = () => {
    return exchangeRateDto;
  };
  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };
  // 返回链接查找页
  const goBack = () => {
    goPage("list");
  };
  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate();
    if (!result) {
      return false;
    }

    if (result) {
      if (exchangeRateDto.buyPrice === 0) {
        FMessageBox.report(t("price.setting.exchangeRate.error1"));
        return false;
      }
      if (exchangeRateDto.salePrice === 0) {
        FMessageBox.report(t("price.setting.exchangeRate.error2"));
        return false;
      }
      if (exchangeRateDto.midPrice === 0) {
        FMessageBox.report(t("price.setting.exchangeRate.error3"));
        return false;
      }
      if (exchangeRateDto.originCurrencyId === exchangeRateDto.targetCurrencyId) {
        FMessageBox.report(t("price.setting.exchangeRate.error4"));
        return false;
      }
    }
    return result;
  };

  const midPriceChange = () => {
    //计算现汇买入/卖出价
    if (exchangeRateDto.midPrice !== null && exchangeRateDto.midPrice !== 0) {
      //买入下浮5%
      exchangeRateDto.buyPrice = multiply(exchangeRateDto.midPrice, 0.95);
      //卖出上浮5%
      exchangeRateDto.salePrice = multiply(exchangeRateDto.midPrice, 1.05);
    }
  };

  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      exchangeRateDto.effectiveDate = res.data.onlineDate;
    });
  };

  return {
    exchangeRateDto,
    form,
    originCurrencyChange,
    targetCurrencyChange,
    formValidator,
    saveInfo,
    saveSuccess,
    goBack,
    midPriceChange,
    getOpenDate
  };
};
export default useAdd;
