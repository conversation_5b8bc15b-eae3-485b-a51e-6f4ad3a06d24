<template>
  <f-dialog :title="t('views.yearCalendar')" v-model="dialogVisible" width="80%" @close="handleClose">
    <div class="month-calendars">
      <f-scrollbar height="500px">
        <div v-for="(monthsRow, rowIndex) in monthsRows" :key="rowIndex" class="months-row">
          <month-calendar
            v-for="(month, colIndex) in monthsRow"
            :key="colIndex"
            :year="year"
            :month="month"
            :holidays="holidays"
          />
        </div>
      </f-scrollbar>
    </div>
    <template #footer>
      <f-button @click="handleClose" type="primary">
        {{ t("views.close") }}
      </f-button>
    </template>
  </f-dialog>
</template>

<script lang="ts">
import MonthCalendar from "./MonthCalendar.tsx"; // 引入TSX编写的月历组件
import { defineComponent, ref, computed } from "vue";
import { useI18n } from "vue-i18n";

export default defineComponent({
  name: "YearCalendarDialog",
  components: {
    MonthCalendar
  },
  props: {
    year: {
      type: Number,
      required: true
    },
    holidays: {
      type: Array as () => Date[],
      default: () => []
    }
  },
  setup(props, { emit, expose }) {
    const dialogVisible = ref(false);
    const { t } = useI18n();
    const monthsRows = computed(() => {
      const months = Array.from({ length: 12 }, (_, i) => i); // 生成0到11的月份数组
      const rows: number[][] = [];
      for (let i = 0; i < months.length; i += 3) {
        rows.push(months.slice(i, i + 3));
      }
      return rows;
    });

    const handleClose = () => {
      dialogVisible.value = false;
      emit("close"); // 通知外层组件弹框已关闭
    };

    const setVisible = (visible: boolean) => {
      dialogVisible.value = visible;
    };

    expose({
      setVisible
    });
    return {
      dialogVisible,
      monthsRows,
      handleClose,
      t
    };
  }
});
</script>
