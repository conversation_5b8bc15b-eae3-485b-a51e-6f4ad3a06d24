<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('productfactory.product.common.listListTitleDetail')"
    destroy-on-close
    @close="closeDrawer"
  >
    <template #default>
      <f-multi-form-panel ref="form" :model="state.productFrom" :column="3">
        <f-panel :title="t('productfactory.product.common.titleProductAttr')" :collapse="false">
          <f-form-item :label="t('productfactory.product.common.productCode')" prop="productCode">
            <f-input
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productCode"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productName')" prop="productName">
            <f-input
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productName"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productStatus')" prop="productStatus">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productStatus"
              value-key="code"
              label="label"
              filterable
              :data="productStatus"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productLine')" prop="productLineCode">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productLineCode"
              value-key="productLineCode"
              label="productLineName"
              filterable
              :url="url.pdLineFindAllUrl"
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productCatalog')" prop="templateType">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              ref="productCatalogSelectRef"
              v-model="state.productFrom.productBsnsDto.productCatalogCode"
              value-key="catalogCode"
              label="catalogName"
              filterable
              :extra-data="{ productLineCode: state.productFrom.productBsnsDto.productLineCode, finalStage: 'YES' }"
              :url="url.pdCatalogFindAllUrl"
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.templateCode')" prop="templateCode">
            <f-input
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.templateCode"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.templateName')" prop="templateName">
            <f-input
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.templateName"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productShortName')" prop="productShortName">
            <f-input
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productShortName"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productStruct')" prop="productStruct">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productStruct"
              value-key="code"
              label="label"
              filterable
              :data="productTemplateType"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productOwner')" prop="productOwner">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productOwner"
              value-key="code"
              label="label"
              filterable
              :data="productOwner"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.businessCharacter')" prop="businessCharacter">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.businessCharacter"
              value-key="code"
              label="label"
              filterable
              :data="businessCharacter"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productDesc')" prop="productDesc">
            <f-input
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.productDesc"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.validDate')" prop="validDate">
            <f-date-picker
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.validDate"
              type="date"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.invalidDate')" prop="invalidDate">
            <f-date-picker
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.invalidDate"
              type="date"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.changeType')" prop="changeType">
            <f-select
              :disabled="formItemDisable(state.formPage, 'disable')"
              v-model="state.productFrom.productBsnsDto.changeType"
              value-key="code"
              label="label"
              filterable
              :data="changeType"
            />
          </f-form-item>
        </f-panel>
        <f-panel :title="t('productfactory.product.common.titleComponentAttr')" :collapse="false">
          <RenderComponent
            :disabled="formItemDisable(state.formPage, 'disable')"
            biz-data-origin="product_bsns"
            :product-info="state.productFrom.productBsnsDto"
            :component-list="state.productComponentList"
            @close="method.handlerCloseRender"
          />
        </f-panel>
        <f-panel :title="t('productfactory.product.common.history')">
          <f-wf-history ref="wfHistory" :params="wfHistoryParams" :is-through="false" />
        </f-panel>
      </f-multi-form-panel>
    </template>
    <template #footer>
      <f-button type="info" plain @click.prevent="closeDrawer">
        {{ t("productfactory.product.common.close") }}
      </f-button>
    </template>
  </f-drawer-scene>
</template>
<script lang="ts" setup>
import RenderComponent from "../../component/RenderComponent.vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useDetail from "../../common/hooks/useDetail";
import { formItemDisable } from "../../common/hooks/viewInfoMap";
import { useHistoryParams } from "../hooks/useDetail";

defineOptions({
  name: "Detail"
});
const props = defineProps({ param: {} });

// 国际化处理
const { t } = useI18n();

// 常量处理
const productTemplateType = useConst("pdf.ProductTemplateType");
const productOwner = useConst("pdf.ProductOwner");
const businessCharacter = useConst("pdf.BusinessCharacter");
const productStatus = useConst("pdf.ProductStatus");
const changeType = useConst("pdf.ChangeType");

const { wfHistory, wfHistoryParams } = useHistoryParams();
// 逻辑处理
const { url, state, method, productCatalogSelectRef, productOpenDrawer, closeDrawer, drawerRef } = useDetail(
  props,
  wfHistory,
  wfHistoryParams
);

defineExpose({ productOpenDrawer, closeDrawer });
</script>
