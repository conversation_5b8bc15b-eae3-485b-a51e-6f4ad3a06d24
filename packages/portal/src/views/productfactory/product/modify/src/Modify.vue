<template>
  <f-blank-scene :title="t('productfactory.product.common.modifyModifyTitle')">
    <f-multi-form-panel ref="form" :model="state.productFrom.productBsnsDto" :rules="relus" :column="3">
      <f-panel :title="t('productfactory.product.common.titleProductAttr')" :collapse="false">
        <f-form-item :label="t('productfactory.product.common.modifyProductCode')" prop="fromProductCode">
          <f-magnifier-single
            :disabled="true"
            :title="t('productfactory.product.common.modifyProductCode')"
            v-model="state.productFrom.productBsnsDto.fromProductCode"
            :url="url.productBsnsFindMagnifyUrl"
            :params="{ id: state.productFrom.productBsnsDto.id }"
            method="post"
            row-key="productCode"
            row-label="productCode"
            input-key="mergeProductLike"
            auto-init
          >
            <f-magnifier-column prop="productCode" :label="t('productfactory.product.common.modifyProductCode')" />
            <f-magnifier-column prop="productName" :label="t('productfactory.product.common.modifyProductName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.modifyProductName')" prop="fromProductName">
          <f-input
            :disabled="state.productFrom.productBsnsDto.changeType === changeType.PRODUCT_STATUS"
            v-model="state.productFrom.productBsnsDto.fromProductName"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.changeType')" prop="changeType">
          <f-select
            v-model="state.productFrom.productBsnsDto.changeType"
            value-key="code"
            label="label"
            filterable
            :data="changeType"
            @change="method.handleChangeTypeSelect"
            disabled
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productStatus')" prop="productStatus">
          <f-select
            :disabled="formItemDisable(state.formPage, 'productStatus')"
            v-model="state.productFrom.productBsnsDto.productStatus"
            value-key="code"
            label="label"
            filterable
            :data="newProductStatus"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productLine')" prop="productLineCode">
          <f-select
            disabled
            v-model="state.productFrom.productBsnsDto.productLineCode"
            value-key="productLineCode"
            label="productLineName"
            filterable
            :url="url.pdLineFindAllUrl"
            method="post"
            @change="method.handleProductLineChange"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productCatalog')" prop="productCatalogCode">
          <f-select
            disabled
            ref="productCatalogSelectRef"
            v-model="state.productFrom.productBsnsDto.productCatalogCode"
            value-key="catalogCode"
            label="catalogName"
            filterable
            :extra-data="{ productLineCode: state.productFrom.productBsnsDto.productLineCode, finalStage: 'YES' }"
            :url="url.pdCatalogFindAllUrl"
            method="post"
            @change="method.handleProductCatalogSelectChange"
            @visible-change="method.handleFindProductCatalog"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.templateCode')" prop="templateCode">
          <f-input disabled v-model="state.productFrom.productBsnsDto.templateCode" />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.templateName')" prop="templateName">
          <f-input v-model="state.productFrom.productBsnsDto.templateName" disabled />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productShortName')" prop="productShortName">
          <f-input
            :disabled="formItemDisable(state.formPage, 'productShortName')"
            v-model="state.productFrom.productBsnsDto.productShortName"
            maxlength="30"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productStruct')" prop="productStruct">
          <f-select
            :disabled="formItemDisable(state.formPage, 'productStruct')"
            v-model="state.productFrom.productBsnsDto.productStruct"
            value-key="code"
            label="label"
            filterable
            :data="productTemplateType"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productOwner')" prop="productOwner">
          <f-select
            :disabled="formItemDisable(state.formPage, 'productOwner')"
            v-model="state.productFrom.productBsnsDto.productOwner"
            value-key="code"
            label="label"
            filterable
            :data="productOwner"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.businessCharacter')" prop="businessCharacter">
          <f-select
            :disabled="formItemDisable(state.formPage, 'businessCharacter')"
            v-model="state.productFrom.productBsnsDto.businessCharacter"
            value-key="code"
            label="label"
            filterable
            :data="businessCharacter"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.validDate')" prop="validDate">
          <f-date-picker
            :disabled="formItemDisable(state.formPage, 'validDate')"
            v-model="state.productFrom.productBsnsDto.validDate"
            type="date"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.invalidDate')" prop="invalidDate">
          <f-date-picker
            :disabled="formItemDisable(state.formPage, 'invalidDate')"
            v-model="state.productFrom.productBsnsDto.invalidDate"
            type="date"
          />
        </f-form-item>
        <f-form-item :label="t('productfactory.product.common.productDesc')" prop="productDesc">
          <f-input
            :disabled="formItemDisable(state.formPage, 'productDesc')"
            v-model="state.productFrom.productBsnsDto.productDesc"
            maxlength="100"
          />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('productfactory.product.common.titleComponentAttr')" :collapse="false">
        <RenderComponent
          :disabled="formItemDisable(state.formPage, 'disabledComponent')"
          biz-data-origin="product_bsns"
          :product-info="state.productFrom.productBsnsDto"
          :component-list="state.productComponentList"
          @close="method.handlerCloseRender"
        />
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :url="url.productBsnsSaveUrl"
        :gather-params="method.handlerProductFrom"
        @close="method.handleSaveClose"
        operate="save"
        :before-trigger="method.formValidator"
      />
      <f-submit-state
        :url="url.productBsnsSubmitUrl"
        :gather-params="method.handlerProductFrom"
        @close="callBack"
        operate="submit"
        :before-trigger="method.formValidator"
      />
      <f-submit-state
        operate="remove"
        type="danger"
        :gather-params="method.handlerDeleteProductFrom"
        :url="url.productBsnsDeleteByIdUrl"
        @close="callBack"
        :confirm-text="t('productfactory.product.common.modifyListOpButtonCancelModifyConfirmMsg')"
        :operate-name="t('productfactory.product.common.modifyListOpButtonCancelModify')"
        :result-confirm="t('productfactory.product.common.modifyListOpButtonCancelModifySuccess')"
        :result-title="t('productfactory.product.common.modifyListOpButtonCancelModify')"
      />
      <f-button v-if="!isSubmit" type="info" plain @click.prevent="method.handleGoList">
        {{ t("productfactory.product.common.list") }}
      </f-button>
      <f-button v-if="isSubmit" type="info" @click.prevent="goSubmit">{{
        t("productfactory.product.common.list")
      }}</f-button>
    </template>
  </f-blank-scene>
</template>
<script lang="ts" setup>
import RenderComponent from "../../component/RenderComponent.vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useModify from "../hooks/useModify";
import { formItemDisable, productRelus } from "../../common/hooks/viewInfoMap";
import { ref, onMounted } from "vue";
import { usePage } from "../hooks/usePage";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
const router = useRouter();
const { pageParams } = usePage();
// 国际化处理
const { t } = useI18n();

// 常量处理
const productTemplateType = useConst("pdf.ProductTemplateType");
const productOwner = useConst("pdf.ProductOwner");
const businessCharacter = useConst("pdf.BusinessCharacter");
const productStatus = useConst("pdf.ProductStatus");
const newProductStatus = productStatus.pickConst([productStatus.SALE, productStatus.STOP_SALE]);
const changeType = useConst("pdf.ChangeType");

// 逻辑处理
const { productCatalogSelectRef, url, state, method, form } = useModify();
// 必填检验
const { relus } = productRelus();

//定义父组件传参, 参数不唯一，根据⻚面需要参数动态添加
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
//修改⻚初始化方法修改
onMounted(() => {
  if (pageParams) {
    method.handlerFindById(pageParams?.id);
  } else {
    method.handlerFindById(Number(props?.id));
    isSubmit.value = true;
  }
});

//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};

const callBack = (res: any) => {
  if (res.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      method.handleGoList();
    }
  }
};
</script>
