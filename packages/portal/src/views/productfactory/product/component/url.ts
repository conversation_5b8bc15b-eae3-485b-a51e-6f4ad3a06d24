// 查询客户所属行业
export const findClientIndustryCategory = "{clientmanage}/api/v1/finance/client/industry-category/remote/server/list";

// 查询客户分组
export const findClientGroup = "{basic}/api/v1/finance/basic/client-group/remote/server/list";

// 查询国家列表
export const findCountryList = "{master-data}/api/v1/finance/query/master-data/dictionary/remote/server/list";

//数据字典请求方法
export const masterDataSelector = "{master-data}/api/v1/finance/query/master-data/dictionary/remote/server/list";

// 查询币种范围列表
export const findCurrencyList = "{system-manage}/api/v1/finance/system/currency/remote/server/all-currency-list";
//根据币种编号查询币种
export const findCurrencyByCode = "{system-manage}/api/v1/finance/system/currency/remote/server/get-by-code";
// 查询机构范围列表
export const findOfficeList = "{system-manage}/api/v1/finance/system/office/remote/server/get-office-list";

// 查询定期存款期限
export const findDepositAllottedTimeList = "{basic}/api/v1/finance/basic/term/remote/server/all-list";

// 查询挂牌利率列表
export const findInterestRateList = "{price}/api/v1/finance/price/board-rate/remote/server/list";

// 查询可售产品单据的业务组件数据
export const findProductBsnsComponentData =
  "{pdf}/api/v1/finance/midground/pdf/product/bsns/data/find-product-component-data";
// 查询可售产品单据的业务组件数据
export const findProductComponentData = "{pdf}/api/v1/finance/midground/pdf/product/data/find-product-component-data";

//贷款组件
//查询授信品种
export const queryCategory =
  "{credit-manage-query}/api/v1/finance/credit-manage/counter/credit-variety/remote/server/magnifier-list";
//查询同业授信品种
export const queryCategoryCode =
  "{interbank-credit-query}/api/v1/finance/interbank-credit/category-set/remote/server/query";
//查询手续费率，弃用
export const freeRateCode = "{mock}/api/v1/finance/price/rate/remote/server/get-free-rate";

//查询产品工厂配置项
export const queryProductFactoryConfig = "{pdf}/api/v1/finance/midground/pdf/product/get-business-properties";
