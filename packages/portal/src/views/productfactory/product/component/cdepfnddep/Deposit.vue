<template>
  <f-form-panel ref="form1" :model="state.bizData" :column="3" :collapse="false">
    <f-form-item :label="t('productfactory.product.component.depositAmtUnitInd')" prop="depositAmtUnitInd" required>
      <f-number v-model="state.bizData.depositAmtUnitInd" :precision="2" min="0" />
    </f-form-item>
    <f-form-item
      :label="t('productfactory.product.component.depositScopeRestrict')"
      prop="depositScopeRestrict"
      required
    >
      <f-select
        v-model="state.bizData.depositScopeRestrict"
        value-key="code"
        label="label"
        filterable
        :data="depositScopeRestrict"
        @change="(val, dataArray) => method.change('depositScopeRestrict', dataArray)"
      />
    </f-form-item>
    <f-form-item :label="t('productfactory.product.component.firstDepositAmt')" prop="firstDepositAmt" required>
      <f-amount v-model="state.bizData.firstDepositAmt" :precision="2" />
    </f-form-item>
    <f-form-item
      :label="t('productfactory.product.component.firstDepositAmtControlType')"
      prop="firstDepositAmtControlType"
      required
    >
      <f-select
        v-model="state.bizData.firstDepositAmtControlType"
        value-key="code"
        label="label"
        filterable
        :data="controlType"
        @change="(val, dataArray) => method.change('firstDepositAmtControlType', dataArray)"
      />
    </f-form-item>
    <f-form-item :label="t('productfactory.product.component.depositMaxAmt')" prop="depositMaxAmt">
      <f-amount v-model="state.bizData.depositMaxAmt" :precision="2" />
    </f-form-item>
  </f-form-panel>
</template>
<script lang="ts" setup>
// 存款存入业务组件
import { useI18n } from "vue-i18n";
import i18nTool from "@/utils/i18n";
import { useConst } from "@ifs/support";
import { componentInfoMap } from "./componentInfoMap";
import useFormProcess from "../common/useFormProcess";
import { useFormRuleMoethod } from "../common/useFormRules";

const { t } = useI18n();
i18nTool.addLocales(
  import.meta.glob("./locale/*.ts", { eager: true }),
  "./locale/",
  "productfactory.product.component"
);

// 常量处理
const depositScopeRestrict = useConst("pdf.DepositScopeRestrict");
const controlType = useConst("pdf.ControlType");

// 定义初始化信息的，包括组件名
defineOptions({ name: "Deposit" });

// 调用组件的参数
const props = defineProps({
  bizComponentData: {
    type: Object,
    default: () => ({})
  }
});

const { form1, formValidator } = useFormRuleMoethod();

const { state, method, handlerProcessComponentData } = useFormProcess(componentInfoMap, props);

// 父组件访问子组件参数
defineExpose({ handlerProcessComponentData, formValidator });
</script>
