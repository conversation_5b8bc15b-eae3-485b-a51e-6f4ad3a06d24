<template>
  <f-query-scene :title="t('productfactory.product.common.copyListTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="state.copyListTableColumns(t)"
        :url="url.productBsnsFindAllUrl"
        :form-data="state.copyQueryFrom"
        :selectable-all="method.handlerSelectTableAll"
        border
        show-header
        auto-init
        :show-collapse="false"
        :show-print="false"
        tile-panel
        auto-reset
        @select="method.handleSelect"
        @select-all="method.handleSelect"
        @clear-selection="method.clearSelection"
        count-prop="examineAmount"
        query-comp-id="productfactory-product-copy-query"
        table-comp-id="productfactory-product-copy-table"
        :export-exclude="['buttons']"
        :export-url="url.productBsnsExportAllUrl"
        :allow-sort="listTableAllowSort"
        table-type="Record"
        :countLabel="t('productfactory.product.common.record')"
        :countLabelUnit="t('productfactory.product.common.recordUnit')"
        :summation-biz-label="t('productfactory.product.common.record')"
        :summation-biz-unit="t('productfactory.product.common.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
      >
        <!-- 表格左上的操作按钮 -->
        <template #operate>
          <f-button type="primary" @click="method.handlerGoAdd()">
            {{ t("productfactory.product.common.add") }}
          </f-button>
          <!-- 批量删除 -->
          <f-submit-state
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="method.gatherBatchParams"
            :url="url.productBsnsDeleteBatch"
            @close="method.handleSearchSuccess"
            compatible
            :confirm-text="deleteMessage"
            :before-trigger="beforrTrigger"
            :batch-confirm-map="deleteResultConfim"
          />
        </template>

        <template #query-panel>
          <f-form-item :label="t('productfactory.product.common.productCode')" prop="productCode">
            <f-input v-model="state.copyQueryFrom.productCode" />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productName')" prop="productName">
            <f-input v-model="state.copyQueryFrom.productName" />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productLineName')" prop="productLineCode">
            <f-select
              v-model="state.copyQueryFrom.productLineCode"
              value-key="productLineCode"
              label="productLineName"
              blank-option
              init-if-blank
              blank-option-value=""
              :url="url.pdLineFindAllUrl"
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('productfactory.product.common.productCatalogName')" prop="productCatalogCode">
            <f-select
              v-model="state.copyQueryFrom.productCatalogCode"
              value-key="catalogCode"
              label="catalogName"
              blank-option
              init-if-blank
              blank-option-value=""
              :extra-data="{ finalStage: 'YES' }"
              :url="url.pdCatalogFindAllUrl"
              method="post"
            />
          </f-form-item>
        </template>
        <template #productCode="{ row }">
          <f-button @click="method.handleOpenDetail(row)" link type="primary">
            {{ row.productCode }}
          </f-button>
        </template>
        <template #buttons="{ row }">
          <OperateButton
            :options="method.handlerGeneralButtonOption('copyList', row, t, 'product_copy', systemOfficeId)"
            @on-modify="method.handlerGoModify(row)"
          />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="listDetail" :param="state.listDetailRow" />
  </f-query-scene>
</template>
<script lang="ts" setup>
import OperateButton from "@/components/operate-button/operate-button";
import { useI18n } from "vue-i18n";
import useList from "../../common/hooks/useList";
import { goPage } from "../hooks/usePage";
import Detail from "./components/Detail.vue";
import { listTableAllowSort } from "../../common/hooks/viewInfoMap";
import { useDefaultValue } from "@/hooks/biz";

// 国际化处理
const { t } = useI18n();
const { systemOfficeId } = useDefaultValue();

// 逻辑处理
const { queryTable, url, state, method, isChecked, listDetail, deleteMessage, beforrTrigger, deleteResultConfim } =
  useList(goPage);
</script>
