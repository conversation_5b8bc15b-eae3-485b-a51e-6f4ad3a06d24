import type { queryFormType, baseInfo } from "../types";
import { reactive, ref, computed, shallowRef } from "vue";
import { useI18n } from "vue-i18n";

export const useManageChange = () => {
  const { t } = useI18n();
  const form = ref();
  // 表格模板
  const queryTable = shallowRef();

  const createList = () => {
    form.value.clearValidate();
    queryTable.value.renderTableData();
  };

  const queryForm = reactive<queryFormType>({
    clientStatuId: 1,
    userNameId: -1,
    userNameDic: "",
    oldClientManager: -1,
    oldClientManagerDic: "",
    newClientManager: -1,
    newClientManagerDic: "",
    statusId: 1
  });

  // 客户放大镜回调
  const restockQueryFormClientName = (row: any) => {
    queryForm.userNameId = row.id;
    if (row.clientManager !== -1) {
      queryForm.oldClientManager = row.clientManager;
    }
    if (row.clientName !== null) {
      queryForm.oldClientManagerDic = row.clientName;
    }
    if (row.clientManager) {
      oldClientManagerParams.userId = row.clientManager;
    }
  };

  // 客户经理清除回调
  const clearQueryFormClientName = () => {
    queryForm.userNameId = -1;
    queryForm.oldClientManager = -1;
    queryForm.oldClientManagerDic = "";
    oldClientManagerParams.userId = -1;
  };
  const oldClientManagerParams = reactive({
    userId: -1
  });
  // 原客户经理放大镜回调
  const restockOldClientManage = (row: any) => {
    queryForm.oldClientManager = row.userId;
    oldClientManagerParams.userId = row.userId;
  };

  // 原客户经理清空回调
  const clearOldClientManage = () => {
    queryForm.oldClientManager = -1;
    oldClientManagerParams.userId = -1;
  };

  // 变更后客户经理回调
  const restockNewClientManage = (row: any) => {
    queryForm.newClientManager = row.userId;
  };

  // 变更后客户经理清空回调
  const clearNewClientManage = () => {
    queryForm.newClientManager = -1;
  };

  // 已选列表
  const checkedList = ref<baseInfo[]>([]);
  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length === 0);
  // 控制全选checkbox
  const selectableAll = (rows: baseInfo[]) => {
    checkedList.value = rows;
  };
  // 勾选checkbox
  const handleSelect = (row: baseInfo[]) => {
    checkedList.value = row;
  };

  // 刷新
  const handleSearch = () => {
    queryForm.clientStatuId = 1;
    queryForm.userNameId = -1;
    queryForm.userNameDic = "";
    queryForm.oldClientManager = -1;
    queryForm.oldClientManagerDic = "";
    queryForm.newClientManager = -1;
    queryForm.newClientManagerDic = "";
    queryForm.statusId = 1;
    oldClientManagerParams.userId = -1;
    createList();
    queryTable.value.clearSelection();
  };

  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      fixed: "left"
    },
    {
      prop: "clientCode",
      label: t("clientmanage.clientinfouphold.managerchange.clientCode"),
      sortable: true
    },
    {
      prop: "clientName",
      label: t("clientmanage.clientinfouphold.managerchange.clientName"),
      sortable: true
    },
    {
      prop: "clientManagerName",
      label: t("clientmanage.clientinfouphold.managerchange.clientManage"),
      sortable: true
    }
  ];

  // 变更客户经理参数
  const getDeleteParams = () => {
    const uuids: string[] = [];
    const ids: number[] = [];
    const classes: number[] = [];
    const _clientCodes: string[] = [];
    const _clientNames: string[] = [];
    const _oldManage: number[] = [];
    const _oldManageName: string[] = [];
    const _newManage: number[] = [];
    const _newManageName: string[] = [];
    checkedList.value.forEach(element => {
      ids.push(element.id);
      uuids.push(element.colUuid);
      classes.push(element.clientClass);
      _clientCodes.push(element.clientCode);
      _clientNames.push(element.clientName);
      _oldManage.push(queryForm.oldClientManager);
      _oldManageName.push(queryForm.oldClientManagerDic);
      _newManage.push(queryForm.newClientManager);
      _newManageName.push(queryForm.newClientManagerDic);
    });
    const params = {
      id: -1,
      ids: ids,
      colUuids: uuids,
      clientClasses: classes,
      oldClientManagers: _oldManage,
      oldClientManagerNames: _oldManageName,
      newClientManagers: _newManage,
      newClientManagerNames: _newManageName,
      clientCodes: _clientCodes,
      clientNames: _clientNames
    };
    return params;
  };

  const formValidator = async () => {
    let result = true;
    await form.value.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };

  const clearSelection = () => {
    checkedList.value.splice(0);
  };
  const defaultSort = { field: "clientCode", order: "asc" };

  const chooseDeleteTip = computed(() => {
    return t("views.chooseSaveTip", [checkedList.value.length]);
  });

  return {
    queryForm,
    tableColumns,
    isChecked,
    queryTable,
    form,
    selectableAll,
    restockQueryFormClientName,
    restockOldClientManage,
    oldClientManagerParams,
    restockNewClientManage,
    handleSelect,
    getDeleteParams,
    createList,
    handleSearch,
    formValidator,
    clearSelection,
    clearQueryFormClientName,
    clearOldClientManage,
    clearNewClientManage,
    defaultSort,
    chooseDeleteTip
  };
};

export default useManageChange;
