import { nextTick, onBeforeMount, reactive, ref, watch } from "vue";
import { FMessage } from "@dtg/frontend-plus";
import httpTool from "@/utils/http";
import { usePage } from "../usePage";
import useBaseInfo from "./useBaseInfo";
import useBusinessInfo from "./useBusinessInfo";
import useContactInfo from "./useContactInfo";
import useExtendedInfo from "./useExtendedInfo";
import useListedInfo from "./useListedInfo";
import useOperationInfo from "./useOperationInfo";
import useRegistrationInfo from "./useRegistrationInfo";
import useShareholder from "./useShareholder";
import useInvestment from "./useInvestment";
import useManagement from "./useManagement";
import useCorporateLitigation from "./useCorporateLitigation";
import useCorporateEvents from "./useCorporateEvents";
import useFileInfo from "./useFileInfo";
import useExtendCustom from "./useExtendCustom";
import { getInfoByIdUrl, getInfoUrl } from "../../url";

export default function useApplication(
  it,
  props,
  emits,
  baseInfoForm,
  businessInfoForm,
  contactInfoForm,
  // extendedInfoForm,
  // listedInfoForm,
  operationInfoForm,
  registrationInfoForm,
  shareholderTable,
  investmentTable,
  managementTable,
  corporateLitigationTable,
  corporateEventsTable,
  supplementTabRef
) {
  const extendCustom = reactive({});

  const { baseInfo, baseInfoMethods, baseInfoHandler } = useBaseInfo(extendCustom);
  const { businessInfo, businessInfoMethods } = useBusinessInfo(extendCustom);
  const { contactInfo, contactInfoMethods } = useContactInfo(extendCustom);
  const { extendedInfo, extendedInfoMethods } = useExtendedInfo(extendCustom);
  const { listedInfo, listedInfoMethods } = useListedInfo(extendCustom);
  const { operationInfo, operationInfoMethods } = useOperationInfo(extendCustom);
  const { registrationInfo, registrationInfoMethods } = useRegistrationInfo(extendCustom);
  const { shareholder, shareholderMethods, shareholderHandler } = useShareholder();
  const { investment, investmentMethods, investmentHandler } = useInvestment();
  const { management, managementMethods, managementHandler } = useManagement();
  const { corporateLitigation, corporateLitigationMethods, corporateLitigationHandler } = useCorporateLitigation();
  const { corporateEvents, corporateEventsMethods, corporateEventsHandler } = useCorporateEvents();
  const { fileInfo, fileInfoMethods } = useFileInfo(it);
  const { extendCustomMethods } = useExtendCustom(extendCustom);
  const otherFileIds = ref([]);

  const basicInfo = props.basicData
    ? props.basicData
    : reactive({
        clientType: "",
        domesticOrForeign: ""
      });

  const renderApplication = param => {
    return new Promise(resolve => {
      basicInfo.clientCtpeId = "";
      basicInfo.domesticOrForeign = "";
      basicInfo.statusId = "";
      baseInfoMethods.renderBaseInfo();
      businessInfoMethods.renderBusinessInfo();
      contactInfoMethods.renderContactInfo();
      extendedInfoMethods.renderExtendedInfo();
      listedInfoMethods.renderListedInfo();
      operationInfoMethods.renderOperationInfo();
      registrationInfoMethods.renderRegistrationInfo();
      shareholderMethods.renderShareholder();
      investmentMethods.renderInvestment();
      managementMethods.renderManagement();
      corporateLitigationMethods.renderCorporateLitigation();
      corporateEventsMethods.renderCorporateEvents();
      fileInfoMethods.renderFileInfo();
      extendCustomMethods.renderExtendCustom();
      otherFileIds.value = [];
      if (!param) {
        resolve(true);
      }
      const url = props.byId ? getInfoByIdUrl : getInfoUrl;
      return httpTool.post(url, param).then(res => {
        if (res.success) {
          const { baseInfoDto } = res.data;
          const _data = {
            ...baseInfoDto,
            // ...replenishDto,
            // ...contactDto,
            version: baseInfoDto.version
          };
          _data.temporary = res.data.temporary;
          _data.option = res.data.option;
          _data.clientName = baseInfoDto.clientName;

          basicInfo.clientCtpeId = _data.clientCtpeId;
          basicInfo.domesticOrForeign = _data.domesticOrForeign;
          basicInfo.statusId = _data.statusId;
          baseInfoMethods.renderBaseInfo({ ..._data, id: baseInfoDto.id });
          businessInfoMethods.renderBusinessInfo(_data);
          contactInfoMethods.renderContactInfo(_data);
          // extendedInfoMethods.renderExtendedInfo(_data);
          // listedInfoMethods.renderListedInfo(_data);
          operationInfoMethods.renderOperationInfo(_data);
          registrationInfoMethods.renderRegistrationInfo(_data);
          shareholderMethods.renderShareholder(res.data);
          investmentMethods.renderInvestment(res.data);
          managementMethods.renderManagement(res.data);
          corporateLitigationMethods.renderCorporateLitigation(res.data);
          corporateEventsMethods.renderCorporateEvents(res.data);
          fileInfoMethods.renderFileInfo(res.data);
          extendCustomMethods.renderExtendCustom(res.data);
          otherFileIds.value = res.data?.otherFileIds || [];

          emits("on-loaded", _data);
          nextTick(() => {
            if (baseInfoForm?.recoverRiskStatus) {
              baseInfoForm.recoverRiskStatus();
            }
          });
          resolve(true);
        } else {
          resolve(true);
        }
      });
    });
  };
  const methods = {
    getDeleteData() {
      return { id: baseInfo.id, version: baseInfo.version };
    },
    getCancelChangeData() {
      return { id: baseInfo.id, version: baseInfo.version };
    },
    getFormData() {
      const dto = {};
      dto.baseInfoDto = {
        ...contactInfo,
        ...operationInfo,
        ...registrationInfo,
        ...baseInfo,
        ...businessInfo
      };
      dto.fileDtoList = fileInfo;
      dto.otherFileIds = otherFileIds.value;
      dto.capitalCompositionDtoList = shareholder;
      dto.investmentDtoList = investment;
      dto.managementDtoList = management;
      dto.corporateLitigationDtoList = corporateLitigation;
      dto.corporateEventsDtoList = corporateEvents;
      dto.clientExtendCustomisationDtoList = extendCustomMethods.getFieldList();
      return dto;
    },
    async validateClientCode() {
      return await baseInfoForm.value.formPanel.form.validateField("clientCode");
    },
    async validateForm(cb = () => {}) {
      const baseInfoResult = await baseInfoForm.value.formPanel.form.validate(cb);
      const businessInfoResult = await businessInfoForm.value.formPanel.form.validate(cb);
      const contactInfoResult = await contactInfoForm.value.formPanel.form.validate(cb);
      // const extendedInfoResult = await extendedInfoForm.value.formPanel.form.validate(cb);
      // const listedInfoResult = await listedInfoForm.value.formPanel.form.validate(cb);
      const operationInfoResult = await operationInfoForm.value.formPanel.form.validate(cb);
      const registrationInfoResult = await registrationInfoForm.value.formPanel.form.validate(cb);
      let result =
        baseInfoResult && businessInfoResult && contactInfoResult && operationInfoResult && registrationInfoResult;
      if (!result) {
        return false;
      }
      if (shareholderTable.value.editTable.isEditing()) {
        FMessage.error(it("shareholderTableEditing"));
        return false;
      }
      if (investmentTable.value.editTable.isEditing()) {
        FMessage.error(it("investmentTableEditing"));
        return false;
      }
      if (managementTable.value.editTable.isEditing()) {
        FMessage.error(it("managementTableEditing"));
        return false;
      }
      if (corporateLitigationTable.value.editTable.isEditing()) {
        FMessage.error(it("corporateLitigationTableEditing"));
        return false;
      }
      if (corporateEventsTable.value.editTable.isEditing()) {
        FMessage.error(it("corporateEventsTableEditing"));
        return false;
      }
      if (supplementTabRef?.value) {
        for (let i = 0; i < supplementTabRef.value.length; i++) {
          result = await supplementTabRef.value[i].form.validate(cb);
          if (!result) {
            return false;
          }
        }
      }
      return true;
    },
    renderApplication,
    renderData(id) {
      renderApplication({ id });
    },
    setExtendCustomFieldList(list) {
      extendCustomMethods.setFieldList(list, props.readonly);
    },
    handleDomesticOrForeignChange(value) {
      if (value && value === "FOREIGN") {
        baseInfo.economicSectorNationalArr = ["27", "31"];
        baseInfo.economicSectorNationalEnum = "31";
      } else {
        baseInfo.economicSectorNationalArr = [];
        baseInfo.economicSectorNationalEnum = "";
      }
    }
  };

  onBeforeMount(async () => {
    if (props.id) {
      methods.renderApplication({ [props.byId || !props.approved ? "id" : "stepId"]: props.id });
    } else {
      const { pageParams } = usePage();
      if (pageParams?.type === "modify") {
        if (pageParams.data) {
          methods.renderApplication({ id: pageParams.data.id });
        }
      }
    }
  });
  watch(
    () => props.id,
    () => {
      if (props.id) {
        methods.renderApplication({ [props.byId || !props.approved ? "id" : "stepId"]: props.id });
      }
    }
  );
  return {
    basicInfo,
    baseInfo,
    businessInfo,
    contactInfo,
    extendedInfo,
    listedInfo,
    operationInfo,
    registrationInfo,
    shareholder,
    investment,
    management,
    corporateLitigation,
    corporateEvents,
    fileInfo,
    otherFileIds,
    extendCustom,
    baseInfoHandler,
    shareholderHandler,
    investmentHandler,
    managementHandler,
    corporateLitigationHandler,
    corporateEventsHandler,
    methods
  };
}
