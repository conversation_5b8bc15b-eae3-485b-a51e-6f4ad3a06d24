import { reactive, shallowRef, onBeforeMount } from "vue";
import { goPage, usePage } from "../hooks/usePage";
import httpTool from "@/utils/http.ts";
import { getChangedField } from "../url";

export const useChangeList = it => {
  const queryTable = shallowRef();

  // 表格查询对象
  const queryFrom = reactive({
    //变更时间
    changeTimeArr: [],
    //变更人
    changeUserName: "",

    clientId: null
  });
  //列表字段
  const tableColumns = [
    {
      prop: "versionNo",
      slots: { default: "versionNo" },
      label: it("versionNo")
    },
    {
      prop: "inputTime",
      label: it("inputTimeChange")
    },
    {
      prop: "changeUserName",
      label: it("loginNameChange")
    }
  ];

  const sortColumnMap = {};
  const columnSort = ["versionNo", "inputTime", "changeUserName"];
  const defaultSort = { field: "versionNo", order: "asc" };

  // 返回列表页
  const goBack = () => {
    goPage("list");
  };

  const postParams = obj => {
    return {
      ...obj,
      changeTimeStart: obj.changeTimeArr[0],
      changeTimeEnd: obj.changeTimeArr[1]
    };
  };

  onBeforeMount(async () => {
    const { pageParams } = usePage();
    if (pageParams?.type === "changeList") {
      if (pageParams.data) {
        queryFrom.clientId = pageParams.data.id;
      }
    }
  });

  const changedFieldList = reactive<string[]>([]);
  const getChangeFieldClass = (field: string) => {
    return changedFieldList.includes(field) ? "inter-client-info-changed-field" : "";
  };
  const initChangeField = async id => {
    const res = await httpTool.post(getChangedField, { id });
    if (res.success && res.data) {
      changedFieldList.length = 0;
      const { changedFields } = res.data.baseInfoDto;
      if (Array.isArray(changedFields)) {
        changedFieldList.push(...changedFields);
      }
    }
    return res;
  };
  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort,
    queryFrom,
    postParams,
    queryTable,
    goBack,
    initChangeField,
    getChangeFieldClass
  };
};

export default useChangeList;
