import { reactive } from "vue";
import { random } from "@/utils/uuid";
import { formatDate } from "@/utils/date";

const resetFileInfo = (it, arr = []) => {
  arr.splice(0);
  Object.assign(arr, [
    {
      _randomId: random(),
      order: 1,
      id: -1,
      fileName: it("fieldName1"),
      documentCode: "",
      activationDate: formatDate(new Date(), "yyyy-MM-dd"),
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName2"
    },
    {
      _randomId: random(),
      order: 2,
      id: -1,
      fileName: it("fieldName2"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName3"
    },
    {
      _randomId: random(),
      order: 3,
      id: -1,
      fileName: it("fieldName3"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName4"
    },
    {
      _randomId: random(),
      order: 4,
      id: -1,
      fileName: it("fieldName4"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName5"
    },
    {
      _randomId: random(),
      order: 5,
      id: -1,
      fileName: it("fieldName5"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName6"
    },
    {
      _randomId: random(),
      order: 6,
      id: -1,
      fileName: it("companyAsso"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName7"
    },
    {
      _randomId: random(),
      order: 7,
      id: -1,
      fileName: it("accOpenLicense"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName8"
    },
    {
      _randomId: random(),
      order: 8,
      id: -1,
      fileName: it("estabFileCn"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName9"
    },
    {
      _randomId: random(),
      order: 9,
      id: -1,
      fileName: it("estabFileNotCn"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName10"
    },
    {
      _randomId: random(),
      order: 10,
      id: -1,
      fileName: it("fieldName7"),
      documentCode: "",
      activationDate: "",
      dueDate: "",
      fileType: "",
      fileId: "",
      prop: "fieldName11"
    }
  ]);
  return arr;
};

export default function useFileInfo(it) {
  const fileInfo = reactive(resetFileInfo(it));
  const fileInfoMethods = {
    renderFileInfo(info) {
      resetFileInfo(it, fileInfo);
      const list = info?.fileDtoList || [];
      for (let i = 0; i < list.length; i++) {
        if (fileInfo[i] && list[i]) {
          Object.assign(fileInfo[i], list[i]);
        }
      }
      return fileInfo;
    }
  };

  return { fileInfo, fileInfoMethods };
}
