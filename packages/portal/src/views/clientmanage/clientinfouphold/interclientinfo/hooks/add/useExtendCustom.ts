import { useConst } from "@ifs/support";

const resetExtendCustom = (target = {}, fieldList = []) => {
  fieldList.forEach(field => {
    target[field.documentName] = field.documentValue;
  });
  return target;
};

export default function useExtendCustom(extendCustom) {
  const YesOrNoEnum = useConst("common.YesOrNo");
  let _fieldList = [];

  const extendCustomMethods = {
    renderExtendCustom(info) {
      return resetExtendCustom(extendCustom, info?.clientExtendCustomisationDtoList || []);
    },
    setFieldList(list, readonly) {
      _fieldList = list;
      // 如果是新增和编辑情况下，需要赋默认值
      if (!readonly) {
        _fieldList.forEach(x => {
          if (x.nisInit === YesOrNoEnum.NO && !extendCustom[x.documentName] && x.defaultValue) {
            extendCustom[x.documentName] = x.defaultValue;
          }
        });
      }
    },
    getFieldList() {
      const arr = [];
      _fieldList.forEach(x => {
        if (x.nisInit === YesOrNoEnum.NO) {
          arr.push({
            documentType: x.documentTypeEnu ?? x.documentType,
            documentName: x.documentName,
            documentValue: extendCustom[x.documentName]
          });
        }
      });
      return arr;
    }
  };

  return { extendCustom, extendCustomMethods };
}
