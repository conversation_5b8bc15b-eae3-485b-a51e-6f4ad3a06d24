<template>
  <f-query-scene :title="it('changeTitle')" teleport-id="panelFooterTest">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="clientmanage-clientinfouphold-interClientInfo-query-002"
        table-comp-id="clientmanage-clientinfouphold-interClientInfo-table-002"
        :form-data="queryFrom"
        :post-params="postParams"
        :table-columns="tableColumns"
        :sort-column-map="sortColumnMap"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :url="interClientcChangeSearch"
        :export-url="changeExportUrl"
        border
        show-header
        auto-reset
        :show-collapse="false"
        auto-init
        tile-panel
        :show-count="false"
        :show-count-value="false"
        :show-print="false"
        :show-summation-sum="false"
        :summation-biz-label="it('record')"
        :summation-biz-unit="it('slip')"
      >
        <template #operate>
          <f-button type="info" @click="goBack">
            {{ it("queryList") }}
          </f-button>
        </template>
        <template #query-panel>
          <!-- 变更日期 -->
          <f-form-item :label="it('changeTimeStart')" prop="changeTimeArr">
            <f-lax-range-date-picker v-model="queryFrom.changeTimeArr" :range-separator="it('end')" />
          </f-form-item>
          <!-- 变更人 -->
          <f-form-item :label="it('loginNameChange')" prop="modifyUserName">
            <f-input v-model="queryFrom.changeUserName" />
          </f-form-item>
        </template>
        <template #versionNo="{ row }">
          <f-button @click="openDetail(row)" link type="primary">{{ row.versionNo }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import Detail from "./components/Detail.vue";
import useChangeList from "../hooks/useChangeList";
import { provide } from "vue";
import { interClientcChangeSearch, changeExportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useListDetail } from "@/hooks/biz";
import { changedInjectKey } from "../types.ts";
const { t } = useI18n();
const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);

const {
  tableColumns,
  sortColumnMap,
  columnSort,
  defaultSort,
  queryFrom,
  postParams,
  queryTable,
  goBack,
  getChangeFieldClass,
  initChangeField
} = useChangeList(it);

const { id, detail, open } = useListDetail();
const openDetail = async (row: Record<string, any>) => {
  id.value = row.id;
  await initChangeField(id.value);
  open();
};

provide(changedInjectKey, getChangeFieldClass);
</script>
<style lang="scss">
.el-drawer-scene
  .el-drawer__body
  .el-panel__body
  .inter-client-info-changed-field
  .el-input.is-disabled
  .el-input__inner {
  color: #c00108 !important;
  -webkit-text-fill-color: #c00108 !important;
}
</style>
