<template>
  <!-- 上市信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('listedInfo')"
    :model="formModel"
    id="panel_listedInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 是否上市 -->
    <f-form-item
      prop="listedCompany"
      :class="getChangeFieldClass('listedCompany')"
      :label="it('listedCompany')"
      v-if="so('listedCompany')"
      :required="reqSwitch('listedCompany')"
    >
      <f-switch
        v-model="formModel.listedCompany"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('listedCompany')"
      />
    </f-form-item>

    <!-- 上市地点1: 新增 -->
    <f-form-item
      prop="listingLocation1"
      :class="getChangeFieldClass('listingLocation1')"
      :label="it('listingLocation1')"
      v-if="so('listingLocation1')"
      :required="req('listingLocation1')"
    >
      <f-input v-model="formModel.listingLocation1" maxlength="30" :disabled="dis('listingLocation1')" />
    </f-form-item>

    <!-- 股票代号1: 新增 -->
    <f-form-item
      prop="stockCode1"
      :class="getChangeFieldClass('stockCode1')"
      :label="it('stockCode1')"
      v-if="so('stockCode1')"
      :required="req('stockCode1')"
    >
      <f-input v-model="formModel.stockCode1" maxlength="30" :disabled="dis('stockCode1')" />
    </f-form-item>

    <!-- 上市地点2: 新增 -->
    <f-form-item
      prop="listingLocation2"
      :class="getChangeFieldClass('listingLocation2')"
      :label="it('listingLocation2')"
      v-if="so('listingLocation2')"
      :required="req('listingLocation2')"
    >
      <f-input v-model="formModel.listingLocation2" maxlength="30" :disabled="dis('listingLocation2')" />
    </f-form-item>

    <!-- 股票代号2: 新增 -->
    <f-form-item
      prop="stockCode2"
      :class="getChangeFieldClass('stockCode2')"
      :label="it('stockCode2')"
      v-if="so('stockCode2')"
      :required="req('stockCode2')"
    >
      <f-input v-model="formModel.stockCode2" maxlength="30" :disabled="dis('stockCode2')" />
    </f-form-item>

    <!-- 上市地点3: 新增 -->
    <f-form-item
      prop="listingLocation3"
      :class="getChangeFieldClass('listingLocation3')"
      :label="it('listingLocation3')"
      v-if="so('listingLocation3')"
      :required="req('listingLocation3')"
    >
      <f-input v-model="formModel.listingLocation3" maxlength="30" :disabled="dis('listingLocation3')" />
    </f-form-item>

    <!-- 股票代号3: 新增 -->
    <f-form-item
      prop="stockCode3"
      :class="getChangeFieldClass('stockCode3')"
      :label="it('stockCode3')"
      v-if="so('stockCode3')"
      :required="req('stockCode3')"
    >
      <f-input v-model="formModel.stockCode3" maxlength="30" :disabled="dis('stockCode3')" />
    </f-form-item>

    <!-- 上市地点4: 新增 -->
    <f-form-item
      prop="listingLocation4"
      :class="getChangeFieldClass('listingLocation4')"
      :label="it('listingLocation4')"
      v-if="so('listingLocation4')"
      :required="req('listingLocation4')"
    >
      <f-input v-model="formModel.listingLocation4" maxlength="30" :disabled="dis('listingLocation4')" />
    </f-form-item>

    <!-- 股票代号4: 新增 -->
    <f-form-item
      prop="stockCode4"
      :class="getChangeFieldClass('stockCode4')"
      :label="it('stockCode4')"
      v-if="so('stockCode4')"
      :required="req('stockCode4')"
    >
      <f-input v-model="formModel.stockCode4" maxlength="30" :disabled="dis('stockCode4')" />
    </f-form-item>

    <!-- 上市地点5: 新增 -->
    <f-form-item
      prop="listingLocation5"
      :class="getChangeFieldClass('listingLocation5')"
      :label="it('listingLocation5')"
      v-if="so('listingLocation5')"
      :required="req('listingLocation5')"
    >
      <f-input v-model="formModel.listingLocation5" maxlength="30" :disabled="dis('listingLocation5')" />
    </f-form-item>

    <!-- 股票代号5: 新增 -->
    <f-form-item
      prop="stockCode5"
      :class="getChangeFieldClass('stockCode5')"
      :label="it('stockCode5')"
      v-if="so('stockCode5')"
      :required="req('stockCode5')"
    >
      <f-input v-model="formModel.stockCode5" maxlength="30" :disabled="dis('stockCode5')" />
    </f-form-item>
    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'listedInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import { ref, inject } from "vue";
import { useI18n } from "vue-i18n";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import { changedInjectKey } from "../../types.ts";

const { t } = useI18n();
const getChangeFieldClass = inject(changedInjectKey, () => "");

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return (
    props.fieldMap[prop]?.info?.documentRequired === "YES" &&
    !props.readonly &&
    formModel.listedCompany === props.constMap.yesOrNoEnum.YES
  ); // 是否必填
};
const reqSwitch = prop => {
  return (
    props.fieldMap[prop]?.info?.documentRequired === "YES" &&
    props.fieldMap[prop]?.info?.permissionType === "RW" &&
    !props.readonly
  ); // 是否必填
};

const formPanel = ref();
const formModel = props.modelInfo;

defineExpose({
  formPanel
});
</script>
