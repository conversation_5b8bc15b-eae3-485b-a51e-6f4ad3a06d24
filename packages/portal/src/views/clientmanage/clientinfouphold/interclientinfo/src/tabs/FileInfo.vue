<template>
  <!--附件信息 -->
  <f-panel :title="it('fileInfo')" id="panel_fileInfo" :open="open">
    <f-table style="width: 100%" border :data="listInfo">
      <f-table-column :label="it('order')" prop="order" />
      <f-table-column :label="it('fieldName')" prop="fileName"> </f-table-column>
      <f-table-column :label="it('documentCode')" prop="documentCode">
        <template #default="{ row }">
          <f-input
            v-if="so(row.prop)"
            v-model="row.documentCode"
            @change="documentCodeChange(row)"
            maxlength="30"
            :disabled="dis(row.prop) || readonly"
          />
        </template>
      </f-table-column>
      <f-table-column :label="it('activationDate')" prop="activationDate">
        <template #default="{ row }">
          <f-date-picker
            v-if="row.order === 1 && so(row.prop)"
            v-model="row.activationDate"
            type="date"
            :disabled="dis(row.prop) || readonly"
          />
        </template>
      </f-table-column>
      <f-table-column :label="it('dueDate')" prop="dueDate">
        <template #default="{ row }">
          <f-date-picker v-if="so(row.prop)" v-model="row.dueDate" type="date" :disabled="dis(row.prop) || readonly" />
        </template>
      </f-table-column>
      <f-table-column :label="it('fileType')" prop="fileType" />
      <f-table-column width="400px" :label="it('fileUpload')" prop="fileId">
        <template #default="{ row, $index }">
          <template v-if="!dis(row.prop) || so(row.prop)">
            <f-button v-if="readonly" @click="handleDownload(row.fileId)" link type="primary">{{
              fileMap[row?.fileId]
            }}</f-button>
            <f-single-upload
              v-else
              v-model="row.fileId"
              :disabled="readonly"
              :on-success="fileData => uploadSuccess(fileData, $index)"
              :on-remove="
                () => {
                  row.fileType = '';
                  row.fileId = '';
                }
              "
            />
          </template>
        </template>
      </f-table-column>
    </f-table>
  </f-panel>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { FMessageBox } from "@dtg/frontend-plus";
import httpTool from "@/utils/http";
import { downloadUrl, uploadSearchUrl } from "../../url";

const { t } = useI18n();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  listInfo: {
    type: Array as () => any[],
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const fileMap = ref({});
const dis = prop => props.fieldMap[prop]?.info?.permissionType === "RO";
const handleDownload = (fileId: string) => {
  httpTool.fileDownload(downloadUrl, { id: fileId });
};

const documentCodeChange = (row: any) => {
  const myreg = /^([0-9]|[a-zA-Z]){8}[-]([0-9]|[a-zA-Z]){1}$/;
  if (row.fileName === it("fieldName3")) {
    if (!myreg.test(row.documentCode)) {
      FMessageBox.report(it("fieldName3Validate"));
      row.documentCode = "";
      return false;
    }
  }
  const regExp = /^([0-9]|[a-zA-Z]){18}$/;
  if (row.fileName === it("fieldName4")) {
    if (!regExp.test(row.documentCode)) {
      FMessageBox.report(it("fieldName4Validate"));
      row.documentCode = "";
      return false;
    }
  }
};

watch(
  () => props.listInfo,
  async val => {
    if (val) {
      const ids = val.map(item => item.fileId).filter(Boolean);
      if (!ids.length) {
        return;
      }
      const res = await httpTool.get(uploadSearchUrl, { ids });
      if (res.success) {
        res.data.forEach(file => {
          fileMap.value[file.id] = file.fileName;
        });
      }
    }
  },
  { immediate: true, deep: true }
);

const uploadSuccess = (fileData: any, index: number) => {
  const _data = props.listInfo;
  _data[index].fileId = fileData.data.id;
  _data[index].fileType = fileData.data.extName;
};
</script>
