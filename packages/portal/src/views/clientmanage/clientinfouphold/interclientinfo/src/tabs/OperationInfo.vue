<template>
  <!--经营信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('operationInfo')"
    :model="formModel"
    id="panel_operationInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 经营（业务）范围 -->
    <f-form-item
      prop="scopeOperation"
      :class="getChangeFieldClass('scopeOperation')"
      :label="it('businessScope')"
      :rules="getMinRule(getMin('scopeOperation'))"
      v-if="so('scopeOperation')"
      :required="req('scopeOperation')"
    >
      <f-input
        v-model="formModel.scopeOperation"
        :maxlength="getMax('scopeOperation')"
        :minlength="getMin('scopeOperation')"
        :disabled="dis('scopeOperation')"
      />
    </f-form-item>
    <!-- 生产规模: 新增 -->
    <f-form-item
      prop="scaleProduction"
      :class="getChangeFieldClass('scaleProduction')"
      :label="it('scaleProduction')"
      v-if="so('scaleProduction')"
      :required="req('scaleProduction')"
    >
      <f-amount
        v-model="formModel.scaleProduction"
        :max="getMax('scaleProduction')"
        :min="getMin('scaleProduction')"
        :disabled="dis('scaleProduction')"
      />
    </f-form-item>
    <!-- 资产规模: 新增 -->
    <f-form-item
      prop="capitalProduction"
      :class="getChangeFieldClass('capitalProduction')"
      :label="it('capitalProduction')"
      v-if="so('capitalProduction')"
      :required="req('capitalProduction')"
    >
      <f-amount
        v-model="formModel.capitalProduction"
        :max="getMax('capitalProduction')"
        :min="getMin('capitalProduction')"
        :disabled="dis('capitalProduction')"
      />
    </f-form-item>
    <!-- 净资产: 新增 -->
    <f-form-item
      prop="netAssets"
      :class="getChangeFieldClass('netAssets')"
      :label="it('netAssets')"
      v-if="so('netAssets')"
      :required="req('netAssets')"
    >
      <f-amount
        v-model="formModel.netAssets"
        :max="getMax('netAssets')"
        :min="getMin('netAssets')"
        :disabled="dis('netAssets')"
      />
    </f-form-item>
    <!-- 资产总额 -->
    <f-form-item
      prop="totalAssets"
      :class="getChangeFieldClass('totalAssets')"
      :label="it('totalMoney')"
      v-if="so('totalAssets')"
      :required="req('totalAssets')"
    >
      <f-amount
        v-model="formModel.totalAssets"
        :max="getMax('totalAssets')"
        :min="getMin('totalAssets')"
        :disabled="dis('totalAssets')"
        symbol=" "
      />
    </f-form-item>
    <!-- 年收入 -->
    <f-form-item
      prop="annualIncome"
      :class="getChangeFieldClass('annualIncome')"
      :label="it('yearMoney')"
      v-if="so('annualIncome')"
      :required="req('annualIncome')"
    >
      <f-amount
        v-model="formModel.annualIncome"
        :max="getMax('annualIncome')"
        :min="getMin('annualIncome')"
        :disabled="dis('annualIncome')"
        symbol=" "
      />
    </f-form-item>
    <!-- 员工总数 -->
    <f-form-item
      prop="employee"
      :class="getChangeFieldClass('employee')"
      :label="it('employee')"
      v-if="so('employee')"
      :required="req('employee')"
    >
      <f-number v-model="formModel.employee" whole-number :disabled="dis('employee')" />
    </f-form-item>
    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'operateInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import { ref, inject } from "vue";
import { useI18n } from "vue-i18n";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import { useInnerField } from "@/views/clientmanage/clientinfouphold/interclientinfo/hooks/useInnerField.ts";
import { changedInjectKey } from "../../types.ts";
import { useMinValid } from "../../hooks/useMinValid.ts";

const { t } = useI18n();
const getChangeFieldClass = inject(changedInjectKey, () => "");
const { getMinRule } = useMinValid();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
});
const { getMax, getMin } = useInnerField(props);
const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return props.fieldMap[prop]?.info?.documentRequired === "YES" && !props.readonly; // 是否必填
};

const formPanel = ref();
const formModel = props.modelInfo;

defineExpose({
  formPanel
});
</script>
