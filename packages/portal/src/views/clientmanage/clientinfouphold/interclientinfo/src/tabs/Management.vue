<template>
  <!-- 企业管理层信息 -->
  <f-panel :title="it('managementInfo')" id="panel_managementInfo" :open="open">
    <f-table-edit
      ref="editTable"
      row-key="_randomId"
      :show-add-btn="!readonly"
      :show-operate="!readonly"
      :data="listInfo"
      @add-row="addData"
    >
      <f-table-column prop="order" type="index" :label="it('order')" />
      <!-- 管理人员姓名 -->
      <f-table-column prop="managementName" :label="it('managementName')" v-if="so('managementName')">
        <template #edit="scope">
          <f-input v-model="scope.row.managementName" maxlength="128" :disabled="dis('managementName')" />
        </template>
      </f-table-column>
      <!-- 部门 -->
      <f-table-column prop="departmentName" :label="it('departmentName')" v-if="so('departmentName')">
        <template #edit="scope">
          <f-input v-model="scope.row.departmentName" maxlength="128" :disabled="dis('departmentName')" />
        </template>
      </f-table-column>
      <!-- 职位 -->
      <f-table-column prop="dutyName" :label="it('dutyName')" v-if="so('dutyName')">
        <template #edit="scope">
          <f-input v-model="scope.row.dutyName" maxlength="128" :disabled="dis('dutyName')" />
        </template>
      </f-table-column>
      <!-- 高管人员类别 -->
      <f-table-column prop="highType" :label="it('highType')" v-if="so('highType')">
        <template #edit="scope">
          <f-number v-model="scope.row.highType" whole-number :disabled="dis('highType')" />
        </template>
      </f-table-column>
      <!-- 备注 -->
      <f-table-column prop="remark" :label="it('remark')" v-if="so('remark')">
        <template #edit="scope">
          <f-input v-model="scope.row.remark" maxlength="128" :disabled="dis('remark')" />
        </template>
      </f-table-column>
    </f-table-edit>
  </f-panel>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  listInfo: {
    type: Array as () => any[],
    default: () => []
  },
  addData: {
    type: Function as () => void,
    required: null
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.fieldMap[prop]?.info?.permissionType === "RO";

const editTable = ref();

defineExpose({
  editTable
});
</script>
