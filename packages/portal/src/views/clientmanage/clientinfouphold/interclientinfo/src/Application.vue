<template>
  <f-vertical-tab-scene v-model:activeName="defaultDisplay" :title="title">
    <template #content-header>
      <slot name="content-header" />
    </template>

    <!-- 基本信息 -->
    <f-scene-panel :title="it('baseInfo')" v-if="approved || showTabList">
      <BaseInfo
        ref="baseInfoForm"
        :model-info="baseInfo"
        :basic-info="basicInfo"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :supplement-field="supplementField"
        :config-map="nacosDto"
        :handler="baseInfoHandler"
        :extend-custom="extendCustom"
        :readonly="readonly"
        :is-add="isAdd"
        @masterClientChange="masterClientChange"
      />
    </f-scene-panel>
    <!-- 注册信息 -->
    <f-scene-panel :title="it('registrationInfo')" v-if="approved || showTabList">
      <RegistrationInfo
        ref="registrationInfoForm"
        :model-info="registrationInfo"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :supplement-field="supplementField"
        :config-map="nacosDto"
        :extend-custom="extendCustom"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 经营信息 -->
    <f-scene-panel :title="it('operationInfo')" v-if="approved || showTabList">
      <OperationInfo
        ref="operationInfoForm"
        :model-info="operationInfo"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :supplement-field="supplementField"
        :config-map="nacosDto"
        :extend-custom="extendCustom"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 联络信息 -->
    <f-scene-panel :title="it('contactInfo')" v-if="approved || showTabList">
      <ContactInfo
        ref="contactInfoForm"
        :model-info="contactInfo"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :supplement-field="supplementField"
        :config-map="nacosDto"
        :extend-custom="extendCustom"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 业务信息 -->
    <f-scene-panel :title="it('businessInfo')" v-if="approved || showTabList">
      <BusinessInfo
        ref="businessInfoForm"
        :model-info="businessInfo"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :supplement-field="supplementField"
        :config-map="nacosDto"
        :extend-custom="extendCustom"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 附件信息 -->
    <f-scene-panel :title="it('fileInfo')" v-if="approved || showTabList">
      <FileInfo
        ref="fileInfoTable"
        :list-info="fileInfo"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :config-map="nacosDto"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 其他附件 -->
    <f-scene-panel :title="it('otherFile')" v-if="approved || showTabList">
      <f-panel :title="it('otherFile')" id="panel_fileInfo_other" :open="true">
        <f-attm-upload
          ref="uploader"
          v-model="otherFileIds"
          :disabled="fieldMap['fieldName12']?.info?.permissionType === 'RO' || readonly"
          :show-upload="fieldMap['fieldName12']?.info?.permissionType != 'RO' || !readonly"
          drag
          multiple
          :is-remove-delete-link="fieldMap['fieldName12']?.info?.permissionType === 'RO' || readonly"
          :is-show-batch-delete="fieldMap['fieldName12']?.info?.permissionType !== 'RO' || !readonly"
        />
      </f-panel>
    </f-scene-panel>
    <!-- 企业资本构成信息 -->
    <f-scene-panel :title="it('shareholderInfo')" v-if="approved || showTabList">
      <Shareholder
        ref="shareholderTable"
        :list-info="shareholder"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :config-map="nacosDto"
        :add-data="shareholderHandler.handleAddData"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 企业对外投资信息 -->
    <f-scene-panel :title="it('investmentInfo')" v-if="approved || showTabList">
      <Investment
        ref="investmentTable"
        :list-info="investment"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :config-map="nacosDto"
        :add-data="investmentHandler.handleAddData"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 企业管理层信息 -->
    <f-scene-panel :title="it('managementInfo')" v-if="approved || showTabList">
      <Management
        ref="managementTable"
        :list-info="management"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :config-map="nacosDto"
        :add-data="managementHandler.handleAddData"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 企业诉讼信息 -->
    <f-scene-panel :title="it('corporateLitigationInfo')" v-if="approved || showTabList">
      <CorporateLitigation
        ref="corporateLitigationTable"
        :list-info="corporateLitigation"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :config-map="nacosDto"
        :add-data="corporateLitigationHandler.handleAddData"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 企业大事记 -->
    <f-scene-panel :title="it('corporateEventsInfo')" v-if="approved || showTabList">
      <CorporateEvents
        ref="corporateEventsTable"
        :list-info="corporateEvents"
        :const-map="clientConstDto"
        :field-map="fieldMap"
        :config-map="nacosDto"
        :add-data="corporateEventsHandler.handleAddData"
        :readonly="readonly"
      />
    </f-scene-panel>
    <!-- 企业大事记 -->
    <f-scene-panel title="" />
    <!-- 审批历史 -->
    <f-scene-panel v-if="!approved && readonly && baseInfo.stepId" :title="it('approvalInfo')">
      <f-panel :title="it('approvalInfo')">
        <f-wf-history
          :params="{
            systemCode: 'T01',
            agencyId: systemOfficeId,
            currencyId: 0,
            transType: '1',
            subTransType: '2',
            recordId: baseInfo.stepId
          }"
          :is-through="false"
        />
      </f-panel>
    </f-scene-panel>
    <!-- 自定义页签 -->
    <template v-if="approved || showTabList">
      <f-scene-panel v-for="(tab, key) in supplementTab" :key="key" :title="tab.showName">
        <f-form-panel
          ref="supplementTabRef"
          :title="tab.showName"
          :model="baseInfo"
          id="panel_supplementTab"
          :open="true"
          :column="3"
          :disabled="readonly"
        >
          <template v-for="(field, docName) in supplementField">
            <DynamicFormItem
              v-if="String(field.parent.id) === key"
              :key="docName"
              :docInit="field.info"
              :model-info="extendCustom"
            />
          </template>
        </f-form-panel>
      </f-scene-panel>
    </template>
    <template #footer><slot name="footer" /></template>
  </f-vertical-tab-scene>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import httpTool from "@/utils/http";
import { formatDate } from "@/utils/date";
import BaseInfo from "./tabs/BaseInfo.vue";
import RegistrationInfo from "./tabs/RegistrationInfo.vue";
import OperationInfo from "./tabs/OperationInfo.vue";
import ContactInfo from "./tabs/ContactInfo.vue";
import BusinessInfo from "./tabs/BusinessInfo.vue";
import Shareholder from "./tabs/Shareholder.vue";
import Investment from "./tabs/Investment.vue";
import FileInfo from "./tabs/FileInfo.vue";
import Management from "./tabs/Management.vue";
import CorporateLitigation from "./tabs/CorporateLitigation.vue";
import CorporateEvents from "./tabs/CorporateEvents.vue";
import DynamicFormItem from "./components/DynamicFormItem.vue";
import useClientConst from "../hooks/useClientConst";
import useApplication from "../hooks/add/useApplication";
import useTemplate from "../hooks/add/useTemplate";
import { useDefaultValue } from "@/hooks/biz";
import { getNacosInfo, innerClientQuery } from "../url";

const { t } = useI18n();
const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);

const { systemOfficeId } = useDefaultValue();

const baseInfoForm = ref();
const businessInfoForm = ref();
const contactInfoForm = ref();
const operationInfoForm = ref();
const registrationInfoForm = ref();
const shareholderTable = ref();
const investmentTable = ref();
const managementTable = ref();
const corporateLitigationTable = ref();
const corporateEventsTable = ref();
const supplementTabRef = ref();

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  basicData: {
    type: Object,
    default: null
  },
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  },
  byId: {
    type: Boolean,
    default: false
  },
  approved: {
    type: Boolean,
    default: false
  },
  isAdd: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(["on-loaded"]);

const { clientConstDto } = useClientConst();

const {
  basicInfo,
  baseInfo,
  businessInfo,
  contactInfo,
  // extendedInfo,
  // listedInfo,
  operationInfo,
  registrationInfo,
  shareholder,
  investment,
  management,
  corporateLitigation,
  corporateEvents,
  fileInfo,
  otherFileIds,
  extendCustom,
  baseInfoHandler,
  shareholderHandler,
  investmentHandler,
  managementHandler,
  corporateLitigationHandler,
  corporateEventsHandler,
  methods
} = useApplication(
  it,
  props,
  emits,
  baseInfoForm,
  businessInfoForm,
  contactInfoForm,
  // extendedInfoForm,
  // listedInfoForm,
  operationInfoForm,
  registrationInfoForm,
  shareholderTable,
  investmentTable,
  managementTable,
  corporateLitigationTable,
  corporateEventsTable,
  supplementTabRef
);

const { supplementTab, fieldMap, supplementField } = useTemplate(clientConstDto.clientClassEnum, basicInfo, methods);

const nacosDto = reactive({
  isOrNoCode: "1",
  isOrNoSupplierCode: "",
  openDate: "",
  isHidden: false
});

const defaultDisplay = ref([]);

const showTabList = computed(() => !!basicInfo.clientCtpeId && !!basicInfo.domesticOrForeign);

const masterClientChange = (val: any) => {
  if (val) {
    registrationInfo.registeredAddress = val.detailedAddress;
    registrationInfo.certName = val.legalPerson;
    registrationInfo.establishedDate = val.esDate;
    registrationInfo.registeredCurrencyCode = val.currency;
    registrationInfo.certificateStartDate = val.bsTermBeginDate;
    registrationInfo.currencyReceivedCode = val.paidCapitalCurrency;
    registrationInfo.registeredCapital = val.reCapital;
    registrationInfo.capitalReceived = val.paidCapital;
    registrationInfo.taxpayerIdentityNo = val.taxpayerId;
    registrationInfo.blEndDate = val.certificateEndDate;
    registrationInfo.country = val.country;
    registrationInfo.certType = val.lrIdType;
    registrationInfo.certNo = val.lrIdCode;
    registrationInfo.provinceName = val.province;
    registrationInfo.province = val.provinceDictKey;
    registrationInfo.cityName = val.city;
    registrationInfo.city = val.cityDictKey;
    registrationInfo.areaName = val.county;
    registrationInfo.area = val.countyDictKey;
    operationInfo.totalAssets = val.totalAssets;
    operationInfo.annualIncome = val.annualIncome;
    operationInfo.employee = val.employeesNumber;
    operationInfo.scopeOperation = val.bsScope;
    businessInfo.isAgriculture = val.isAgriculture;
    contactInfo.contactAddress = val.contactAddress;
  } else {
    registrationInfo.registeredAddress = "";
    registrationInfo.certName = "";
    registrationInfo.establishedDate = "";
    registrationInfo.registeredReceivedCode = "";
    registrationInfo.currencyReceivedCode = "";
    registrationInfo.registeredCapital = "";
    registrationInfo.capitalReceived = "";
    registrationInfo.taxpayerIdentityNo = "";
    registrationInfo.blEndDate = "";
    registrationInfo.country = "";
    registrationInfo.certType = "";
    registrationInfo.certNo = "";
    operationInfo.totalAssets = "";
    operationInfo.annualIncome = "";
    operationInfo.employee = "";
    operationInfo.scopeOperation = "";
    businessInfo.isAgriculture = "";
    contactInfo.contactAddress = "";
  }
};

onMounted(async () => {
  httpTool.post(getNacosInfo).then((res: any) => {
    nacosDto.isOrNoCode = res.data.isOrNoCode;
    nacosDto.isOrNoSupplierCode = res.data.isOrNoSupplierCode;
    nacosDto.isHidden = res.data.hidden;
    nacosDto.openDate = formatDate(new Date(res.data.openDate));
    nacosDto.startTime = formatDate(new Date(res.data.openDate));
  });
});

/** bug 83196 新增页面默认进来上级机构名称显示内部客户查询id为1的客户 */
const params = {
  id: 1
};
const handleDefaultParentOfficeName = async () => {
  return await httpTool.post(innerClientQuery, params);
};
watch(
  () => showTabList.value,
  async val => {
    if (val && props.isAdd) {
      const innerClientInfo = await handleDefaultParentOfficeName();
      if (innerClientInfo.success && innerClientInfo.data && innerClientInfo.data.length === 1) {
        baseInfo.parentOfficeName = innerClientInfo.data[0].clientName;
      }
    }
  }
);

defineExpose(methods);
</script>
