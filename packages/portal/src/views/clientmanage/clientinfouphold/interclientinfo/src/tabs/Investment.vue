<template>
  <!-- 企业对外投资信息 -->
  <f-panel :title="it('investmentInfo')" id="panel_investmentInfo" :open="open">
    <f-table-edit
      ref="editTable"
      row-key="_randomId"
      :show-add-btn="!readonly"
      :show-operate="!readonly"
      :data="listInfo"
      @add-row="addData"
    >
      <f-table-column prop="order" type="index" :label="it('order')" />
      <!-- 股东单位名称 -->
      <f-table-column prop="cnsName" :label="it('cnsName')" v-if="so('cnsName1380')">
        <template #edit="scope">
          <f-input v-model="scope.row.cnsName" maxlength="128" :disabled="dis('cnsName1380')" />
        </template>
      </f-table-column>
      <!-- 入股方式 -->
      <f-table-column prop="entryMode" :label="it('entryMode')" v-if="so('entryMode1381')">
        <template #edit="scope">
          <f-input v-model="scope.row.entryMode" maxlength="128" :disabled="dis('entryMode1381')" />
        </template>
      </f-table-column>
      <!-- 币种 -->
      <f-table-column prop="investmentCurrency" :label="it('investmentCurrency')" v-if="so('investmentCurrency1382')">
        <template #edit="scope">
          <f-select
            v-model="scope.row.investmentCurrency"
            :url="registeredCurrencyIdSelector"
            value-key="currencyId"
            label="currencyName"
            :disabled="dis('investmentCurrency1382')"
            @change="
              (value: string, row: Record<string, any>) => {
                if (row) {
                  scope.row.investmentCurrencyName = row.currencyName;
                } else {
                  scope.row.investmentCurrencyName = '';
                }
              }
            "
          />
        </template>
        <template #default="{ row }">
          {{ row.investmentCurrencyName }}
        </template>
      </f-table-column>
      <!-- 持股比例(%) -->
      <f-table-column prop="investorRatio" :label="it('investorRatio')" v-if="so('investorRatio1383')">
        <template #edit="scope">
          <f-number v-model="scope.row.investorRatio" is-rate :disabled="dis('investorRatio1383')" />
        </template>
      </f-table-column>
      <!-- 出资金额 -->
      <f-table-column
        prop="contributionAmount"
        :label="it('contributionAmount')"
        v-if="so('contributionAmount1384')"
        formatter="amount"
      >
        <template #edit="scope">
          <f-amount v-model="scope.row.contributionAmount" :disabled="dis('contributionAmount1384')" />
        </template>
      </f-table-column>
      <!-- 贷款卡号   -->
      <f-table-column prop="loanCardCode" :label="it('loanCardCode')" v-if="so('loanCardCode1385')">
        <template #edit="scope">
          <f-input v-model="scope.row.loanCardCode" maxlength="128" :disabled="dis('loanCardCode1385')" />
        </template>
      </f-table-column>
    </f-table-edit>
  </f-panel>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { registeredCurrencyIdSelector } from "../../url";

const { t } = useI18n();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  listInfo: {
    type: Array as () => any[],
    default: () => []
  },
  addData: {
    type: Function as () => void,
    required: null
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.fieldMap[prop]?.info?.permissionType === "RO";

const editTable = ref();

defineExpose({
  editTable
});
</script>
