<template>
  <!--扩展信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('extendedInfo')"
    :model="formModel"
    id="panel_extendedInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 企业行业类别 -->
    <f-form-item
      prop="industryCategoryCode"
      :class="getChangeFieldClass('industryCategoryCode')"
      :label="it('industryCategoryCode')"
      v-if="so('industryCategoryCode')"
      :required="req('industryCategoryCode')"
    >
      <f-cascader
        v-model="formModel.industryCategoryCodeArr"
        :props="{ value: 'dictKey', label: 'dictValue' }"
        :options="industryCategoryOptions"
        style="width: 100%"
        @change="handleIndustryCategoryChange"
        :disabled="dis('industryCategoryCode')"
      />
    </f-form-item>
    <!-- 绿色贷款行业: 新增  -->
    <f-form-item
      prop="greenCategoryCode"
      :class="getChangeFieldClass('greenCategoryCode')"
      :label="it('greenCategoryCode')"
      v-if="so('greenCategoryCode')"
      :required="req('greenCategoryCode')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.greenCategoryCode"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'GreenIndustryCategory'
        }"
        @change="handleGreenCategoryCodeChange"
        :disabled="dis('greenCategoryCode')"
      />
    </f-form-item>
    <!-- 企业控股类型 -->
    <f-form-item
      prop="holdingTypeId"
      :class="getChangeFieldClass('holdingTypeId')"
      :label="it('holdingType')"
      v-if="so('holdingTypeId')"
      :required="req('holdingTypeId')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.holdingTypeCode"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'HoldingType'
        }"
        @change="handleHoldingTypeChange"
        :disabled="dis('holdingTypeId')"
      />
    </f-form-item>
    <!-- 是否科创: 新增 -->
    <f-form-item
      prop="isScienceCreate"
      :class="getChangeFieldClass('isScienceCreate')"
      :label="it('isScienceCreate')"
      v-if="so('isScienceCreate')"
      :required="req('isScienceCreate')"
    >
      <f-switch
        v-model="formModel.isScienceCreate"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('isScienceCreate')"
      />
    </f-form-item>
    <!-- 人行征信代码: 新增 -->
    <f-form-item
      prop="peopleBankCreditCode"
      :class="getChangeFieldClass('peopleBankCreditCode')"
      :label="it('peopleBankCreditCode')"
      v-if="so('peopleBankCreditCode')"
      :required="req('peopleBankCreditCode')"
    >
      <f-input v-model="formModel.peopleBankCreditCode" maxlength="30" :disabled="dis('peopleBankCreditCode')" />
    </f-form-item>
    <!-- 工商注册编号: 新增 -->
    <f-form-item
      prop="busiRegisterNo"
      :class="getChangeFieldClass('busiRegisterNo')"
      :label="it('busiRegisterNo')"
      v-if="so('busiRegisterNo')"
      :required="req('busiRegisterNo')"
    >
      <f-input v-model="formModel.busiRegisterNo" maxlength="30" :disabled="dis('busiRegisterNo')" />
    </f-form-item>
    <!-- 登记地行政区划代码: 新增 -->
    <f-form-item
      prop="extendRegisterNo"
      :class="getChangeFieldClass('extendRegisterNo')"
      :label="it('extendRegisterNo')"
      v-if="so('extendRegisterNo')"
      :required="req('extendRegisterNo')"
    >
      <f-input v-model="formModel.extendRegisterNo" maxlength="30" :disabled="dis('extendRegisterNo')" />
    </f-form-item>
    <!-- 经济类型代码: 新增 -->
    <f-form-item
      prop="economicType"
      :class="getChangeFieldClass('economicType')"
      :label="it('economicType')"
      v-if="so('economicType')"
      :required="req('economicType')"
    >
      <f-select v-model="formModel.economicType" :data="constMap.economicTypeEnum" :disabled="dis('economicType')" />
    </f-form-item>
    <!-- 出资人类型: 新增  -->
    <f-form-item
      prop="contributorType"
      :class="getChangeFieldClass('contributorType')"
      :label="it('contributorType')"
      v-if="so('contributorType')"
      :required="req('contributorType')"
    >
      <f-select
        v-model="formModel.contributorType"
        :data="constMap.contributorTypeEnum"
        :disabled="dis('contributorType')"
      />
    </f-form-item>
    <!-- 出资人身份类别: 新增  -->
    <f-form-item
      prop="contributorCategory"
      :class="getChangeFieldClass('contributorCategory')"
      :label="it('contributorCategory')"
      v-if="so('contributorCategory')"
      :required="req('contributorCategory')"
    >
      <f-select
        v-model="formModel.contributorCategory"
        :data="constMap.contributorCategoryEnum"
        :disabled="dis('contributorCategory')"
      />
    </f-form-item>
    <!-- 出资人名称: 新增 -->
    <f-form-item
      prop="contributorName"
      :class="getChangeFieldClass('contributorName')"
      :label="it('contributorName')"
      v-if="so('contributorName')"
      :required="req('contributorName')"
    >
      <f-input v-model="formModel.contributorName" maxlength="30" :disabled="dis('contributorName')" />
    </f-form-item>
    <!-- 出资人身份标识类型: 新增  -->
    <f-form-item
      prop="contributorStatusType"
      :class="getChangeFieldClass('contributorStatusType')"
      :label="it('contributorStatusType')"
      v-if="so('contributorStatusType')"
      :required="req('contributorStatusType')"
    >
      <f-select
        v-model="formModel.contributorStatusType"
        :data="constMap.contributorStatusTypeEnum"
        :disabled="dis('contributorStatusType')"
      />
    </f-form-item>
    <!-- 出资人身份标识号码: 新增 -->
    <f-form-item
      prop="contributorStatusNo"
      :class="getChangeFieldClass('contributorStatusNo')"
      :label="it('contributorStatusNo')"
      v-if="so('contributorStatusNo')"
      :required="req('contributorStatusNo')"
    >
      <f-input v-model="formModel.contributorStatusNo" maxlength="30" :disabled="dis('contributorStatusNo')" />
    </f-form-item>
    <!-- 出资比例: 新增 -->
    <f-form-item
      prop="contributorRatio"
      :class="getChangeFieldClass('contributorRatio')"
      :label="it('contributorRatio')"
      v-if="so('contributorRatio')"
      :required="req('contributorRatio')"
    >
      <f-number v-model="formModel.contributorRatio" is-rate :disabled="dis('contributorRatio')" />
    </f-form-item>
    <!-- 实际控制人身份类别: 新增  -->
    <f-form-item
      prop="controllerCategory"
      :class="getChangeFieldClass('controllerCategory')"
      :label="it('controllerCategory')"
      v-if="so('controllerCategory')"
      :required="req('controllerCategory')"
    >
      <f-select
        v-model="formModel.controllerCategory"
        :data="constMap.contributorCategoryEnum"
        :disabled="dis('controllerCategory')"
      />
    </f-form-item>
    <!-- 实际控制人名称: 新增 -->
    <f-form-item
      prop="controllerName"
      :class="getChangeFieldClass('controllerName')"
      :label="it('controllerName')"
      v-if="so('controllerName')"
      :required="req('controllerName')"
    >
      <f-input v-model="formModel.controllerName" maxlength="30" :disabled="dis('controllerName')" />
    </f-form-item>
    <!-- 实际控制人身份标识类型: 新增  -->
    <f-form-item
      prop="controllerStatusType"
      :class="getChangeFieldClass('controllerStatusType')"
      :label="it('controllerStatusType')"
      v-if="so('controllerStatusType')"
      :required="req('controllerStatusType')"
    >
      <f-select
        v-model="formModel.controllerStatusType"
        :data="constMap.contributorStatusTypeEnum"
        :disabled="dis('controllerStatusType')"
      />
    </f-form-item>
    <!-- 实际控制人身份标识号码: 新增 -->
    <f-form-item
      prop="controllerStatusNo"
      :class="getChangeFieldClass('controllerStatusNo')"
      :label="it('controllerStatusNo')"
      v-if="so('controllerStatusNo')"
      :required="req('controllerStatusNo')"
    >
      <f-input v-model="formModel.controllerStatusNo" maxlength="30" :disabled="dis('controllerStatusNo')" />
    </f-form-item>
    <!-- 上级机构类型: 新增  -->
    <f-form-item
      prop="parentOfficeType"
      :class="getChangeFieldClass('parentOfficeType')"
      :label="it('parentOfficeType')"
      v-if="so('parentOfficeType')"
      :required="req('parentOfficeType')"
    >
      <f-select
        v-model="formModel.parentOfficeType"
        :data="constMap.parentOfficeTypeEnum"
        :disabled="dis('parentOfficeType')"
      />
    </f-form-item>
    <!-- 上级机构名称: 新增 -->
    <f-form-item
      prop="parentOfficeName"
      :class="getChangeFieldClass('parentOfficeName')"
      :label="it('parentOfficeName')"
      v-if="so('parentOfficeName')"
      :required="req('parentOfficeName')"
    >
      <f-input v-model="formModel.parentOfficeName" maxlength="30" :disabled="dis('parentOfficeName')" />
    </f-form-item>
    <!-- 上级机构身份标识类型: 新增  -->
    <f-form-item
      prop="parentOfficeStatusType"
      :class="getChangeFieldClass('parentOfficeStatusType')"
      :label="it('parentOfficeStatusType')"
      v-if="so('parentOfficeStatusType')"
      :required="req('parentOfficeStatusType')"
    >
      <f-select
        v-model="formModel.parentOfficeStatusType"
        :data="constMap.parentOfficeStatusTypeEnum"
        :disabled="dis('parentOfficeStatusType')"
      />
    </f-form-item>
    <!-- 上级机构身份标识码: 新增 -->
    <f-form-item
      prop="parentOfficeStatusNo"
      :class="getChangeFieldClass('parentOfficeStatusNo')"
      :label="it('parentOfficeStatusNo')"
      v-if="so('parentOfficeStatusNo')"
      :required="req('parentOfficeStatusNo')"
    >
      <f-input v-model="formModel.parentOfficeStatusNo" maxlength="30" :disabled="dis('parentOfficeStatusNo')" />
    </f-form-item>
    <!-- 关联关系类型: 新增  -->
    <f-form-item
      prop="relationType"
      :class="getChangeFieldClass('relationType')"
      :label="it('relationType')"
      v-if="so('relationType')"
      :required="req('relationType')"
    >
      <f-select v-model="formModel.relationType" :data="constMap.relationTypeEnum" :disabled="dis('relationType')" />
    </f-form-item>
    <!-- 关联有效标志: 新增 -->
    <f-form-item
      prop="relationFlag"
      :class="getChangeFieldClass('relationFlag')"
      :label="it('relationFlag')"
      v-if="so('relationFlag')"
      :required="req('relationFlag')"
    >
      <f-switch
        v-model="formModel.relationFlag"
        :active-value="constMap.dataStatusEnum.VALID"
        :inactive-value="constMap.dataStatusEnum.INVALID"
        :disabled="dis('relationFlag')"
      />
    </f-form-item>
    <!-- 是否绿色企业: 新增 -->
    <f-form-item
      prop="isGreen"
      :class="getChangeFieldClass('isGreen')"
      :label="it('isGreen')"
      v-if="so('isGreen')"
      :required="req('isGreen')"
    >
      <f-switch
        v-model="formModel.isGreen"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('isGreen')"
      />
    </f-form-item>

    <!-- 绿色分类: 新增 -->
    <f-form-item
      prop="greenCategoryFirCode"
      :class="getChangeFieldClass('greenCategoryFirCode')"
      :label="it('greenCategoryFirCode')"
      v-if="so('greenCategoryFirCode')"
      :required="req('greenCategoryFirCode')"
    >
      <f-cascader
        v-model="formModel.greenCategoryArr"
        :props="{ value: 'dictKey', label: 'dictValue' }"
        :options="greenIndustryCategoryOptions"
        style="width: 100%"
        @change="handleGreenCategoryChange"
        :disabled="dis('greenCategoryFirCode')"
      />
    </f-form-item>

    <!-- 是否科技: 新增 -->
    <f-form-item
      prop="isScience"
      :class="getChangeFieldClass('isScience')"
      :label="it('isScience')"
      v-if="so('isScience')"
      :required="req('isScience')"
    >
      <f-switch
        v-model="formModel.isScience"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('isScience')"
      />
    </f-form-item>

    <!-- 科技贷款分类: 新增  -->
    <f-form-item
      prop="loanScienceType"
      :class="getChangeFieldClass('loanScienceType')"
      :label="it('loanScienceType')"
      v-if="so('loanScienceType')"
      :required="req('loanScienceType')"
    >
      <f-select
        v-model="formModel.loanScienceType"
        :data="constMap.clientLoanScienceEnum"
        :disabled="dis('loanScienceType')"
      />
    </f-form-item>

    <!-- 科技型企业贷款类型: 新增  -->
    <f-form-item
      prop="scienceLoanType"
      :class="getChangeFieldClass('scienceLoanType')"
      :label="it('scienceLoanType')"
      v-if="so('scienceLoanType')"
      :required="req('scienceLoanType')"
    >
      <f-select
        v-model="formModel.scienceLoanType"
        :data="constMap.clientScienceLoanEnum"
        :disabled="dis('scienceLoanType')"
      />
    </f-form-item>

    <!-- 科技相关产业贷款类型: 新增  -->
    <f-form-item
      prop="scienceLoanIndustries"
      :class="getChangeFieldClass('scienceLoanIndustries')"
      :label="it('scienceLoanIndustries')"
      v-if="so('scienceLoanIndustries')"
      :required="req('scienceLoanIndustries')"
    >
      <f-select
        v-model="formModel.scienceLoanIndustries"
        :data="constMap.clientScienceLoanIndustriesEnum"
        :disabled="dis('scienceLoanIndustries')"
      />
    </f-form-item>

    <!-- 高技术制造业分类 -->
    <f-form-item
      prop="manufactHighType"
      :class="getChangeFieldClass('manufactHighType')"
      :label="it('manufactHighType')"
      v-if="so('manufactHighType')"
      :required="req('manufactHighType')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.manufactHighType"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'ManufactHightType'
        }"
        :disabled="dis('manufactHighType')"
      />
    </f-form-item>

    <!-- 高技术服务业分类 -->
    <f-form-item
      prop="serviceHighType"
      :class="getChangeFieldClass('serviceHighType')"
      :label="it('serviceHighType')"
      v-if="so('serviceHighType')"
      :required="req('serviceHighType')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.serviceHighType"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'ServiceHightType'
        }"
        :disabled="dis('serviceHighType')"
      />
    </f-form-item>

    <!-- 战略新兴产业分类 -->
    <f-form-item
      prop="strategicIndustries"
      :class="getChangeFieldClass('strategicIndustries')"
      :label="it('strategicIndustries')"
      v-if="so('strategicIndustries')"
      :required="req('strategicIndustries')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.strategicIndustries"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'StrategicIndustries'
        }"
        :disabled="dis('strategicIndustries')"
      />
    </f-form-item>

    <!-- 知识产权（专利）密集型产业 -->
    <f-form-item
      prop="intellectualIndustries"
      :class="getChangeFieldClass('intellectualIndustries')"
      :label="it('intellectualIndustries')"
      v-if="so('intellectualIndustries')"
      :required="req('intellectualIndustries')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.intellectualIndustries"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'IntellectualIndustries'
        }"
        :disabled="dis('intellectualIndustries')"
      />
    </f-form-item>

    <!-- 是否已开户 -->
    <f-form-item
      prop="isOpened"
      :class="getChangeFieldClass('isOpened')"
      :label="it('isOpened')"
      v-if="so('isOpened')"
      :required="req('isOpened')"
    >
      <f-switch
        v-model="formModel.isOpened"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('isOpened')"
      />
    </f-form-item>

    <!-- 法人代表证件类型 -->
    <f-form-item
      prop="certType"
      :class="getChangeFieldClass('certType')"
      :label="it('certType')"
      v-if="so('certType')"
      :required="req('certType')"
    >
      <f-select v-model="formModel.certiType" :data="constMap.certTypeEnum" :disabled="dis('certType')" />
    </f-form-item>

    <!-- 法人代表证件号码 -->
    <f-form-item
      prop="certNo"
      :class="getChangeFieldClass('certNo')"
      :label="it('certNo')"
      v-if="so('certNo')"
      :required="req('certNo')"
    >
      <f-input v-model="formModel.certiNo" maxlength="30" :disabled="dis('certNo')" />
    </f-form-item>

    <!-- 征信投向 -->
    <f-form-item
      prop="creditReference"
      :class="getChangeFieldClass('creditReference')"
      :label="it('creditReference')"
      v-if="so('creditReference')"
      :required="req('creditReference')"
    >
      <f-input v-model="formModel.creditReference" maxlength="30" :disabled="dis('creditReference')" />
    </f-form-item>

    <!-- 股东名称 -->
    <f-form-item
      prop="shareholderName"
      :class="getChangeFieldClass('shareholderName')"
      :label="it('shareholderName')"
      v-if="so('shareholderName')"
      :required="req('shareholderName')"
    >
      <f-input v-model="formModel.shareholderName" maxlength="30" :disabled="dis('shareholderName')" />
    </f-form-item>

    <!-- 地区代码 -->
    <f-form-item
      prop="areaCode"
      :class="getChangeFieldClass('areaCode')"
      :label="it('areaCode')"
      v-if="so('areaCode')"
      :required="req('areaCode')"
    >
      <f-input v-model="formModel.areaCode" maxlength="30" :disabled="dis('areaCode')" />
    </f-form-item>
    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'supplementInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import { ref, inject } from "vue";
import { useI18n } from "vue-i18n";
import httpTool from "@/utils/http";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import { countrySelector } from "../../url";
import { changedInjectKey } from "../../types.ts";

const { t } = useI18n();
const getChangeFieldClass = inject(changedInjectKey, () => "");

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return props.fieldMap[prop]?.info?.documentRequired === "YES" && !props.readonly; // 是否必填
};

const formPanel = ref();
const formModel = props.modelInfo;
const industryCategoryData = ref([]);
const industryCategoryOptions = ref([]);
const greenIndustryCategoryData = ref([]);
const greenIndustryCategoryOptions = ref([]);

httpTool.post(countrySelector, { dictClassEq: "IndustryCategory" }).then(res => {
  industryCategoryData.value = res.data;
  industryCategoryOptions.value = convertToTree(res.data);
});

httpTool.post(countrySelector, { dictClassEq: "GreenCreditLoan" }).then(res => {
  greenIndustryCategoryData.value = res.data;
  greenIndustryCategoryOptions.value = convertToTree(res.data);
});

const convertToTree = data => {
  const obj = {};
  data.forEach(x => {
    obj[x.dictKey] = x;
  });

  const tree = [];
  data.forEach(x => {
    if (x.parentDictKey) {
      if (!obj[x.parentDictKey].children) {
        obj[x.parentDictKey].children = [];
      }
      obj[x.parentDictKey].children.push(x);
    }
    if (x.dictLevel === 1) {
      tree.push(x);
    }
  });
  return tree;
};

const handleIndustryCategoryChange = args => {
  if (args && args.length > 0) {
    formModel.industryCategoryCode = args[0];
    formModel.industryCategoryName = industryCategoryData.value.find(x => x.dictKey === args[0])?.dictValue;
    formModel.industryCategoryBigCode = args[1];
    formModel.industryCategoryBigName = industryCategoryData.value.find(x => x.dictKey === args[1])?.dictValue;
    formModel.industryCategoryMidCode = args[2];
    formModel.industryCategoryMidName = industryCategoryData.value.find(x => x.dictKey === args[2])?.dictValue;
    formModel.industryCategorySmallCode = args[3];
    formModel.industryCategorySmallName = industryCategoryData.value.find(x => x.dictKey === args[3])?.dictValue;
  } else {
    formModel.industryCategoryCode = "";
    formModel.industryCategoryName = "";
    formModel.industryCategoryBigCode = "";
    formModel.industryCategoryBigName = "";
    formModel.industryCategoryMidCode = "";
    formModel.industryCategoryMidName = "";
    formModel.industryCategorySmallCode = "";
    formModel.industryCategorySmallName = "";
  }
};

const handleGreenCategoryChange = args => {
  if (args && args.length > 0) {
    formModel.greenCategoryFirCode = args[0];
    formModel.greenCategoryFirName = greenIndustryCategoryData.value.find(x => x.dictKey === args[0])?.dictValue;
    formModel.greenCategorySecCode = args[1];
    formModel.greenCategorySecName = greenIndustryCategoryData.value.find(x => x.dictKey === args[1])?.dictValue;
    formModel.greenCategoryThdCode = args[2];
    formModel.greenCategoryThdName = greenIndustryCategoryData.value.find(x => x.dictKey === args[2])?.dictValue;
  } else {
    formModel.greenCategoryFirCode = "";
    formModel.greenCategoryFirName = "";
    formModel.greenCategorySecCode = "";
    formModel.greenCategorySecName = "";
    formModel.greenCategoryThdCode = "";
    formModel.greenCategoryThdName = "";
  }
};

const handleHoldingTypeChange = (val, row) => {
  if (val) {
    formModel.holdingTypeName = row.value;
  } else {
    formModel.holdingTypeName = "";
  }
};

const handleGreenCategoryCodeChange = (val, row) => {
  if (val) {
    formModel.greenCategoryName = row.dictValue;
  } else {
    formModel.greenCategoryName = "";
  }
};
defineExpose({
  formPanel
});
</script>
