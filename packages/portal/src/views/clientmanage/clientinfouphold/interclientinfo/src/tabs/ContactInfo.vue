<template>
  <!--联络信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('contactInfo')"
    :model="formModel"
    id="panel_contactInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 邮编: 新增 -->
    <f-form-item
      prop="contactPostcode"
      :class="getChangeFieldClass('contactPostcode')"
      :label="it('contactPostcode')"
      :rules="getMinRule(getMin('contactPostcode'))"
      v-if="so('contactPostcode')"
      :required="req('contactPostcode')"
    >
      <f-input
        v-model="formModel.contactPostcode"
        :maxlength="getMax('contactPostcode')"
        :minlength="getMin('contactPostcode')"
        :disabled="dis('contactPostcode')"
      />
    </f-form-item>
    <!-- 电话: 新增 -->
    <f-form-item
      prop="contactPhone"
      :class="getChangeFieldClass('contactPhone')"
      :label="it('contactPhone')"
      :rules="getMinRule(getMin('contactPhone'))"
      v-if="so('contactPhone')"
      :required="req('contactPhone')"
    >
      <f-input
        v-model="formModel.contactPhone"
        :maxlength="getMax('contactPhone')"
        :minlength="getMin('contactPhone')"
        :disabled="dis('contactPhone')"
      />
    </f-form-item>
    <!-- 电子邮件 -->
    <f-form-item
      prop="email"
      :class="getChangeFieldClass('email')"
      :label="it('email')"
      :rules="getMinRule(getMin('email'))"
      v-if="so('email')"
      :required="req('email')"
    >
      <f-input
        v-model="formModel.email"
        :maxlength="getMax('email')"
        :minlength="getMin('email')"
        :disabled="dis('email')"
      />
    </f-form-item>
    <!-- 结算业务联系人: 新增 -->
    <f-form-item
      prop="settlementContact"
      :class="getChangeFieldClass('settlementContact')"
      :label="it('settlementContact')"
      :rules="getMinRule(getMin('settlementContact'))"
      v-if="so('settlementContact')"
      :required="req('settlementContact')"
    >
      <f-input
        v-model="formModel.settlementContact"
        :maxlength="getMax('settlementContact')"
        :minlength="getMin('settlementContact')"
        :disabled="dis('settlementContact')"
      />
    </f-form-item>
    <!-- 信贷业务联系人: 新增 -->
    <f-form-item
      prop="loanContact"
      :class="getChangeFieldClass('loanContact')"
      :label="it('loanContact')"
      :rules="getMinRule(getMin('loanContact'))"
      v-if="so('loanContact')"
      :required="req('loanContact')"
    >
      <f-input
        v-model="formModel.loanContact"
        :maxlength="getMax('loanContact')"
        :minlength="getMin('loanContact')"
        :disabled="dis('loanContact')"
      />
    </f-form-item>
    <!-- 联系人1: 新增 -->
    <f-form-item
      prop="contact1"
      :class="getChangeFieldClass('contact1')"
      :label="it('contact1')"
      :rules="getMinRule(getMin('contact1'))"
      v-if="so('contact1')"
      :required="req('contact1')"
    >
      <f-input
        v-model="formModel.contact1"
        :maxlength="getMax('contact1')"
        :minlength="getMin('contact1')"
        :disabled="dis('contact1')"
      />
    </f-form-item>
    <!-- 联系人1手机号: 新增 -->
    <f-form-item
      prop="mobilePhone1"
      :class="getChangeFieldClass('mobilePhone1')"
      :label="it('mobilePhone1')"
      :rules="getMinRule(getMin('mobilePhone1'))"
      v-if="so('mobilePhone1')"
      :required="req('mobilePhone1')"
    >
      <f-input
        v-model="formModel.mobilePhone1"
        :maxlength="getMax('mobilePhone1')"
        :minlength="getMin('mobilePhone1')"
        :disabled="dis('mobilePhone1')"
      />
    </f-form-item>
    <!-- 联系人1固定电话: 新增 -->
    <f-form-item
      prop="landLine1"
      :class="getChangeFieldClass('landLine1')"
      :label="it('landLine1')"
      :rules="getMinRule(getMin('landLine1'))"
      v-if="so('landLine1')"
      :required="req('landLine1')"
    >
      <f-input
        v-model="formModel.landLine1"
        :maxlength="getMax('landLine1')"
        :minlength="getMin('landLine1')"
        :disabled="dis('landLine1')"
      />
    </f-form-item>
    <!-- 联系人2: 新增 -->
    <f-form-item
      prop="contact2"
      :class="getChangeFieldClass('contact2')"
      :label="it('contact2')"
      :rules="getMinRule(getMin('contact2'))"
      v-if="so('contact2')"
      :required="req('contact2')"
    >
      <f-input
        v-model="formModel.contact2"
        :maxlength="getMax('contact2')"
        :minlength="getMin('contact2')"
        :disabled="dis('contact2')"
      />
    </f-form-item>
    <!-- 联系人2手机号: 新增 -->
    <f-form-item
      prop="mobilePhone2"
      :class="getChangeFieldClass('mobilePhone2')"
      :label="it('mobilePhone2')"
      :rules="getMinRule(getMin('mobilePhone2'))"
      v-if="so('mobilePhone2')"
      :required="req('mobilePhone2')"
    >
      <f-input
        v-model="formModel.mobilePhone2"
        :maxlength="getMax('mobilePhone2')"
        :minlength="getMin('mobilePhone2')"
        :disabled="dis('mobilePhone2')"
      />
    </f-form-item>
    <!-- 联系人2固定电话: 新增 -->
    <f-form-item
      prop="landLine2"
      :class="getChangeFieldClass('landLine2')"
      :label="it('landLine2')"
      :rules="getMinRule(getMin('landLine2'))"
      v-if="so('landLine2')"
      :required="req('landLine2')"
    >
      <f-input
        v-model="formModel.landLine2"
        :maxlength="getMax('landLine2')"
        :minlength="getMin('landLine2')"
        :disabled="dis('landLine2')"
      />
    </f-form-item>
    <!-- 联系人3: 新增 -->
    <f-form-item
      prop="contact3"
      :class="getChangeFieldClass('contact3')"
      :label="it('contact3')"
      :rules="getMinRule(getMin('contact3'))"
      v-if="so('contact3')"
      :required="req('contact3')"
    >
      <f-input
        v-model="formModel.contact3"
        :maxlength="getMax('contact3')"
        :minlength="getMin('contact3')"
        :disabled="dis('contact3')"
      />
    </f-form-item>
    <!-- 联系人3手机号: 新增 -->
    <f-form-item
      prop="mobilePhone3"
      :class="getChangeFieldClass('mobilePhone3')"
      :label="it('mobilePhone3')"
      :rules="getMinRule(getMin('mobilePhone3'))"
      v-if="so('mobilePhone3')"
      :required="req('mobilePhone3')"
    >
      <f-input
        v-model="formModel.mobilePhone3"
        :maxlength="getMax('mobilePhone3')"
        :minlength="getMin('mobilePhone3')"
        :disabled="dis('mobilePhone3')"
      />
    </f-form-item>
    <!-- 联系人3固定电话: 新增 -->
    <f-form-item
      prop="landLine3"
      :class="getChangeFieldClass('landLine3')"
      :label="it('landLine3')"
      :rules="getMinRule(getMin('landLine3'))"
      v-if="so('landLine3')"
      :required="req('landLine3')"
    >
      <f-input
        v-model="formModel.landLine3"
        :maxlength="getMax('landLine3')"
        :minlength="getMin('landLine3')"
        :disabled="dis('landLine3')"
      />
    </f-form-item>
    <!-- 客户经理: 新增 todo: 待确认 -->
    <f-form-item
      prop="clientManagerId"
      :class="getChangeFieldClass('clientManagerId')"
      :label="it('clientManager')"
      v-if="so('clientManagerId')"
      :required="req('clientManagerId')"
    >
      <f-magnifier-single
        v-model="formModel.clientManagerId"
        :url="clientManagerMagnifier"
        :title="it('clientManager')"
        method="post"
        row-key="userId"
        row-label="userName"
        auto-init
        :disabled="dis('clientManagerId')"
        @change="handleManagerChange"
        @clear="handleManagerClear"
      >
        <f-magnifier-column prop="userNo" :label="it('clientManagerNo')" />
        <f-magnifier-column prop="userName" :label="it('clientManagerName')" />
      </f-magnifier-single>
    </f-form-item>
    <!-- 建立关系日期: 新增 -->
    <f-form-item
      prop="dateConnection"
      :class="getChangeFieldClass('dateConnection')"
      :label="it('dateConnection')"
      v-if="so('dateConnection')"
      :required="req('dateConnection')"
    >
      <f-date-picker v-model="formModel.dateConnection" type="date" :disabled="dis('dateConnection')" />
    </f-form-item>
    <!-- 服务级别: 新增 -->
    <f-form-item
      prop="serviceLevel"
      :class="getChangeFieldClass('serviceLevel')"
      :label="it('serviceLevel')"
      :rules="getMinRule(getMin('serviceLevel'))"
      v-if="so('serviceLevel')"
      :required="req('serviceLevel')"
    >
      <f-input
        v-model="formModel.serviceLevel"
        :maxlength="getMax('serviceLevel')"
        :minlength="getMin('serviceLevel')"
        :disabled="dis('serviceLevel')"
      />
    </f-form-item>

    <!-- 注册登记地址（省） -->
    <f-form-item
      prop="contactProvince"
      :class="getChangeFieldClass('contactProvince')"
      :label="it('convince')"
      v-if="so('contactProvince')"
      :required="req('contactProvince')"
    >
      <f-select
        v-model="formModel.contactProvince"
        :url="provinceSelector"
        :extra-data="provinceParams"
        value-key="dictKey"
        label="dictValue"
        @change="handleProvinceChange"
        :disabled="dis('contactProvince')"
      />
    </f-form-item>
    <!-- 注册登记地址（市） -->
    <f-form-item
      prop="contactCity"
      :class="getChangeFieldClass('contactCity')"
      :label="it('citySign')"
      v-if="so('contactCity')"
      :required="req('contactCity')"
    >
      <f-select
        ref="cityRef"
        v-model="formModel.contactCity"
        :url="citySelector"
        :extra-data="cityParams"
        value-key="dictKey"
        label="dictValue"
        @change="handleCityChange"
        :disabled="dis('contactCity')"
      />
    </f-form-item>
    <!-- 注册登记地址（区） -->
    <f-form-item
      prop="contactArea"
      :class="getChangeFieldClass('contactArea')"
      :label="it('county')"
      v-if="so('contactArea')"
      :required="req('contactArea')"
    >
      <f-select
        ref="areaRef"
        v-model="formModel.contactArea"
        :url="areaSelector"
        :extra-data="areaParams"
        value-key="dictKey"
        label="dictValue"
        @change="handleAreaChange"
        :disabled="dis('contactArea')"
      />
    </f-form-item>
    <!-- 详细地址 -->
    <f-form-item
      prop="contactAddr"
      :class="getChangeFieldClass('contactAddr')"
      :label="it('contactorAddress')"
      :rules="getMinRule(getMin('contactAddr'))"
      v-if="so('contactAddr')"
      :required="req('contactAddr')"
    >
      <f-input
        v-model="formModel.contactAddr"
        :maxlength="getMax('contactAddr')"
        :minlength="getMin('contactAddr')"
        :disabled="dis('contactAddr')"
      />
    </f-form-item>

    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'contactInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, inject } from "vue";
import { useI18n } from "vue-i18n";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import { clientManagerMagnifier, provinceSelector, citySelector, areaSelector } from "../../url";
import { useInnerField } from "@/views/clientmanage/clientinfouphold/interclientinfo/hooks/useInnerField.ts";
import { changedInjectKey } from "../../types.ts";
import { useMinValid } from "../../hooks/useMinValid.ts";

const { t } = useI18n();
const getChangeFieldClass = inject(changedInjectKey, () => "");
const { getMinRule } = useMinValid();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return props.fieldMap[prop]?.info?.documentRequired === "YES" && !props.readonly; // 是否必填
};
const { getMax, getMin } = useInnerField(props);
const formPanel = ref();
const formModel = props.modelInfo;

const cityRef = ref();
const areaRef = ref();

const provinceParams = computed(() => ({ dictClassEq: "ISO_3166_alpha_2_Country_Dic", parentDictKeyEq: "156" }));
const cityParams = computed(() => ({
  dictClassEq: "ISO_3166_alpha_2_Country_Dic",
  parentDictKeyEq: formModel.contactProvince || -1
}));
const areaParams = computed(() => ({
  dictClassEq: "ISO_3166_alpha_2_Country_Dic",
  parentDictKeyEq: formModel.contactCity || -1
}));
const handleProvinceChange = (val, row) => {
  if (val) {
    formModel.contactProvinceName = row.dictValue;
    formModel.contactProvinceEn = row.dictValueEn;
  } else {
    formModel.contactProvinceName = "";
    formModel.contactProvinceNameEn = "";
  }
  nextTick(() => {
    cityRef.value.initRemoteData();
    areaRef.value.initRemoteData();
  });
};
const handleCityChange = (val, row) => {
  if (val) {
    formModel.contactCityName = row.dictValue;
    formModel.contactCityNameEn = row.dictValueEn;
  } else {
    formModel.contactCityName = "";
    formModel.contactCityEn = "";
  }
  nextTick(() => {
    areaRef.value.initRemoteData();
  });
};

const handleAreaChange = (val, row) => {
  if (val) {
    formModel.areaName = row.dictValue;
    formModel.areaNameEn = row.dictValueEn;
  } else {
    formModel.areaName = "";
    formModel.areaNameEn = "";
  }
};

const handleManagerChange = (row: any) => {
  formModel.clientManagerName = row ? row.userName : "";
};
const handleManagerClear = () => {
  formModel.clientManagerName = "";
};

defineExpose({
  formPanel
});
</script>
