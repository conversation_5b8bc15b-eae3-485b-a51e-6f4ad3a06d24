<template>
  <!-- 企业大事记 -->
  <f-panel :title="it('corporateEventsInfo')" id="panel_corporateEventsInfo" :open="open">
    <f-table-edit
      ref="editTable"
      row-key="_randomId"
      :show-add-btn="!readonly"
      :show-operate="!readonly"
      :data="listInfo"
      @add-row="addData"
    >
      <f-table-column prop="order" type="index" :label="it('order')" />
      <!-- 事件编号 -->
      <f-table-column prop="eventCode" :label="it('eventCode')" v-if="so('eventCode')">
        <template #edit="scope">
          <f-input v-model="scope.row.eventCode" maxlength="128" :disabled="dis('eventCode')" />
        </template>
      </f-table-column>
      <!-- 发生日期  -->
      <f-table-column prop="eventDate" :label="it('eventDate')" v-if="so('eventDate')">
        <template #edit="scope">
          <f-date-picker v-model="scope.row.eventDate" type="date" :disabled="dis('eventDate')" />
        </template>
      </f-table-column>
      <!-- 事件描述  -->
      <f-table-column prop="eventDescription" :label="it('eventDescription')" v-if="so('eventDescription')">
        <template #edit="scope">
          <f-input v-model="scope.row.eventDescription" maxlength="128" :disabled="dis('eventDescription')" />
        </template>
      </f-table-column>
    </f-table-edit>
  </f-panel>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  listInfo: {
    type: Array as () => any[],
    default: () => []
  },
  addData: {
    type: Function as () => void,
    required: null
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.fieldMap[prop]?.info?.permissionType === "RO";

const editTable = ref();

defineExpose({
  editTable
});
</script>
