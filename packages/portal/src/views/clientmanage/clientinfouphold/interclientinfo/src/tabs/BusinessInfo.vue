<template>
  <!--业务信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('businessInfo')"
    :model="formModel"
    id="panel_businessInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 贷款卡号 -->
    <f-form-item
      prop="loanCardCode"
      :class="getChangeFieldClass('loanCardCode')"
      :label="it('loanCardCode')"
      :rules="getMinRule(getMin('loanCardCode'))"
      v-if="so('loanCardCode')"
      :required="req('loanCardCode')"
    >
      <f-input
        v-model="formModel.loanCardCode"
        :maxlength="getMax('loanCardCode')"
        :minlength="getMin('loanCardCode')"
        :disabled="dis('loanCardCode')"
      />
    </f-form-item>
    <!-- lei码 -->
    <f-form-item
      prop="leiCode"
      :class="getChangeFieldClass('leiCode')"
      :label="it('leiCode')"
      :rules="getMinRule(getMin('leiCode'))"
      v-if="so('leiCode')"
      :required="req('leiCode')"
    >
      <f-input
        v-model="formModel.leiCode"
        :maxlength="getMax('leiCode')"
        :minlength="getMin('leiCode')"
        :disabled="dis('leiCode')"
      />
    </f-form-item>
    <!-- 是否个体工商户经营性贷款 -->
    <f-form-item
      prop="individualLona"
      :class="getChangeFieldClass('individualLona')"
      :label="it('individualLona')"
      v-if="so('individualLona')"
      :required="req('individualLona')"
    >
      <f-switch
        v-model="formModel.individualLona"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('individualLona')"
      />
    </f-form-item>
    <!-- 是否小微企业主经营性贷款 -->
    <f-form-item
      prop="microBizLoan"
      :class="getChangeFieldClass('microBizLoan')"
      :label="it('microBizLoan')"
      v-if="so('microBizLoan')"
      :required="req('microBizLoan')"
    >
      <f-switch
        v-model="formModel.microBizLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('microBizLoan')"
      />
    </f-form-item>
    <!-- 是否农户经营性贷款 -->
    <f-form-item
      prop="farmerLoan"
      :class="getChangeFieldClass('farmerLoan')"
      :label="it('farmerLoan')"
      v-if="so('farmerLoan')"
      :required="req('farmerLoan')"
    >
      <f-switch
        v-model="formModel.farmerLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('farmerLoan')"
      />
    </f-form-item>
    <!-- 是否创业担保贷款 -->
    <f-form-item
      prop="newBizLoan"
      :class="getChangeFieldClass('newBizLoan')"
      :label="it('newBizLoan')"
      v-if="so('newBizLoan')"
      :required="req('newBizLoan')"
    >
      <f-switch
        v-model="formModel.newBizLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('newBizLoan')"
      />
    </f-form-item>
    <!-- 是否助学贷款 -->
    <f-form-item
      prop="sutdentLoan"
      :class="getChangeFieldClass('sutdentLoan')"
      :label="it('sutdentLoan')"
      v-if="so('sutdentLoan')"
      :required="req('sutdentLoan')"
    >
      <f-switch
        v-model="formModel.sutdentLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('sutdentLoan')"
      />
    </f-form-item>
    <!-- 是否原建档立卡贫困人口消费贷款 -->
    <f-form-item
      prop="poorLoan"
      :class="getChangeFieldClass('poorLoan')"
      :label="it('poorLoan')"
      v-if="so('poorLoan')"
      :required="req('poorLoan')"
    >
      <f-switch
        v-model="formModel.poorLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('poorLoan')"
      />
    </f-form-item>
    <!-- 是否精准扶贫贷款 -->
    <f-form-item
      prop="povertyAlleviationLoan"
      :class="getChangeFieldClass('povertyAlleviationLoan')"
      :label="it('povertyAlleviationLoan')"
      v-if="so('povertyAlleviationLoan')"
      :required="req('povertyAlleviationLoan')"
    >
      <f-switch
        v-model="formModel.povertyAlleviationLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('povertyAlleviationLoan')"
      />
    </f-form-item>
    <!-- 是否保障性安居工厂贷款 -->
    <f-form-item
      prop="supportabilityLoan"
      :class="getChangeFieldClass('supportabilityLoan')"
      :label="it('supportabilityLoan')"
      v-if="so('supportabilityLoan')"
      :required="req('supportabilityLoan')"
    >
      <f-switch
        v-model="formModel.supportabilityLoan"
        :active-value="constMap.yesOrNoEnum.YES"
        :inactive-value="constMap.yesOrNoEnum.NO"
        :disabled="dis('supportabilityLoan')"
      />
    </f-form-item>
    <!-- 是否涉农 -->
    <f-form-item
      prop="isAgriculture"
      :class="getChangeFieldClass('isAgriculture')"
      :label="it('isAgriculture')"
      v-if="so('isAgriculture')"
      :required="req('isAgriculture')"
    >
      <f-select v-model="formModel.isAgriculture" :data="constMap.yesOrNoEnum" :disabled="dis('isAgriculture')" />
    </f-form-item>
    <!-- 是否绿色企业 -->
    <f-form-item
      prop="isGreen"
      :class="getChangeFieldClass('isGreen')"
      :label="it('isGreen')"
      v-if="so('isGreen')"
      :required="req('isGreen')"
    >
      <f-select
        v-model="formModel.isGreen"
        :data="constMap.yesOrNoEnum"
        :disabled="dis('isGreen')"
        @change="greenChange"
      />
    </f-form-item>
    <!-- 绿色分类: 新增 -->
    <f-form-item
      prop="greenCategoryFirCode"
      :class="getChangeFieldClass('greenCategoryFirCode')"
      :label="it('greenCategoryFirCode')"
      v-if="so('greenCategoryFirCode')"
      :required="req('greenCategoryFirCode')"
    >
      <f-input v-model="formModel.greenCategoryName" disabled v-if="readonly" />
      <f-cascader
        v-else
        v-model="formModel.greenCategoryArr"
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="greenIndustryCategoryOptions"
        style="width: 100%"
        @change="handleGreenCategoryChange"
        :disabled="dis('greenCategoryFirCode') || formModel.isGreen === 'NO'"
      />
    </f-form-item>
    <!-- 是否科技 -->
    <f-form-item
      prop="isScience"
      :class="getChangeFieldClass('isScience')"
      :label="it('isScience')"
      v-if="so('isScience')"
      :required="req('isScience')"
    >
      <f-select v-model="formModel.isScience" :data="constMap.yesOrNoEnum" :disabled="dis('isScience')" />
    </f-form-item>
    <!-- 科技贷款分类: 新增  -->
    <f-form-item
      prop="loanScienceType"
      :class="getChangeFieldClass('loanScienceType')"
      :label="it('loanScienceType')"
      v-if="so('loanScienceType')"
      :required="req('loanScienceType') || formModel.isScience === 'YES'"
    >
      <f-select
        v-model="formModel.loanScienceType"
        :data="constMap.clientLoanScienceEnum"
        :disabled="dis('loanScienceType') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 科技型企业贷款类型: 新增  -->
    <f-form-item
      prop="scienceLoanType"
      :class="getChangeFieldClass('scienceLoanType')"
      :label="it('scienceLoanType')"
      v-if="so('scienceLoanType')"
      :required="req('scienceLoanType') || formModel.isScience === 'YES'"
    >
      <f-select
        v-model="formModel.scienceLoanType"
        :data="constMap.clientScienceLoanEnum"
        :disabled="dis('scienceLoanType') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 科技相关产业贷款类型: 新增  -->
    <f-form-item
      prop="scienceLoanIndustries"
      :class="getChangeFieldClass('scienceLoanIndustries')"
      :label="it('scienceLoanIndustries')"
      v-if="so('scienceLoanIndustries')"
      :required="req('scienceLoanIndustries') || formModel.isScience === 'YES'"
    >
      <f-select
        v-model="formModel.scienceLoanIndustries"
        :data="constMap.clientScienceLoanIndustriesEnum"
        :disabled="dis('scienceLoanIndustries') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 高技术制造业分类 -->
    <f-form-item
      prop="manufactHighType"
      :class="getChangeFieldClass('manufactHighType')"
      :label="it('manufactHighType')"
      v-if="so('manufactHighType')"
      :required="req('manufactHighType') || formModel.isScience === 'YES'"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.manufactHighType"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: 'ManufactHightType'
        }"
        :disabled="dis('manufactHighType') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 高技术服务业分类 -->
    <f-form-item
      prop="serviceHighType"
      :class="getChangeFieldClass('serviceHighType')"
      :label="it('serviceHighType')"
      v-if="so('serviceHighType')"
      :required="req('serviceHighType') || formModel.isScience === 'YES'"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.serviceHighType"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: '3040'
        }"
        :disabled="dis('serviceHighType') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 战略新兴产业分类 -->
    <f-form-item
      prop="strategicIndustries"
      :class="getChangeFieldClass('strategicIndustries')"
      :label="it('strategicIndustries')"
      v-if="so('strategicIndustries')"
      :required="req('strategicIndustries') || formModel.isScience === 'YES'"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.strategicIndustries"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: '3041'
        }"
        :disabled="dis('strategicIndustries') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 知识产权（专利）密集型产业 -->
    <f-form-item
      prop="intellectualIndustries"
      :class="getChangeFieldClass('intellectualIndustries')"
      :label="it('intellectualIndustries')"
      v-if="so('intellectualIndustries')"
      :required="req('intellectualIndustries') || formModel.isScience === 'YES'"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.intellectualIndustries"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          parentDictKeyEq: '3042'
        }"
        :disabled="dis('intellectualIndustries') || formModel.isScience === 'NO'"
      />
    </f-form-item>
    <!-- 内部信用等级: 新增 -->
    <f-form-item
      prop="clientInnerRating"
      :class="getChangeFieldClass('clientInnerRating')"
      :label="it('clientInnerRating')"
      :rules="getMinRule(getMin('clientInnerRating'))"
      v-if="so('clientInnerRating')"
      :required="req('clientInnerRating')"
    >
      <f-input
        v-model="formModel.clientInnerRating"
        :maxlength="getMax('clientInnerRating')"
        :minlength="getMin('clientInnerRating')"
        :disabled="dis('clientInnerRating')"
      />
    </f-form-item>
    <!-- 业务建立渠道 -->
    <f-form-item
      prop="establishChannel"
      :class="getChangeFieldClass('establishChannel')"
      :label="it('busiChannel')"
      v-if="so('establishChannel')"
      :required="req('establishChannel')"
    >
      <f-select
        v-model="formModel.establishChannel"
        :data="constMap.clientEstablishChannel"
        :disabled="dis('establishChannel')"
      />
    </f-form-item>
    <!-- 首次建立信贷关系日期 -->
    <f-form-item
      prop="firstLoanDate"
      :class="getChangeFieldClass('firstLoanDate')"
      :label="it('firstRelateDate')"
      v-if="so('firstLoanDate')"
      :required="req('firstLoanDate')"
    >
      <f-date-picker v-model="formModel.firstLoanDate" type="date" :disabled="dis('firstLoanDate')" />
    </f-form-item>

    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'businessInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import { ref, inject } from "vue";
import { useI18n } from "vue-i18n";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import httpTool from "@/utils/http";
import { countrySelector } from "../../url";
import { useInnerField } from "@/views/clientmanage/clientinfouphold/interclientinfo/hooks/useInnerField.ts";
import { changedInjectKey } from "../../types.ts";
import { useMinValid } from "../../hooks/useMinValid.ts";

const { t } = useI18n();
const getChangeFieldClass = inject(changedInjectKey, () => "");
const { getMinRule } = useMinValid();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return props.fieldMap[prop]?.info?.documentRequired === "YES" && !props.readonly; // 是否必填
};
const { getMax, getMin } = useInnerField(props);
const formPanel = ref();
const formModel = props.modelInfo;

const convertToTree = (data, needLevel = 1) => {
  const obj = {};
  data.forEach(x => {
    obj[x.dictKey] = x;
  });

  const tree = [];
  data.forEach(x => {
    if (x.parentDictKey) {
      if (obj?.[x.parentDictKey]) {
        if (!obj[x.parentDictKey].children) {
          obj[x.parentDictKey].children = [];
        }
        obj[x.parentDictKey].children.push(x);
      }
    }
    if (x.dictLevel === needLevel) {
      tree.push(x);
    }
  });
  return tree;
};

const greenIndustryCategoryData = ref([]);
const greenIndustryCategoryOptions = ref([]);

httpTool.post(countrySelector, { dictClassEq: "GreenCreditLoan", dictLevelIn: [1, 2, 3] }).then(res => {
  greenIndustryCategoryData.value = res.data;
  greenIndustryCategoryOptions.value = convertToTree(res.data);
});

const handleGreenCategoryChange = args => {
  if (args && args.length > 0) {
    formModel.greenCategoryFirCode = args[args.length - 1];
    formModel.greenCategoryFirName = greenIndustryCategoryData.value.find(
      x => x.dictKey === formModel.greenCategoryFirCode
    )?.dictValue;
  } else {
    formModel.greenCategoryFirCode = "";
    formModel.greenCategoryFirName = "";
    formModel.greenCategorySecCode = "";
    formModel.greenCategorySecName = "";
    formModel.greenCategoryThdCode = "";
    formModel.greenCategoryThdName = "";
  }
};

const greenChange = () => {
  formModel.greenCategoryFirCode = "";
  formModel.greenCategoryFirName = "";
  formModel.greenCategorySecCode = "";
  formModel.greenCategorySecName = "";
  formModel.greenCategoryThdCode = "";
  formModel.greenCategoryThdName = "";
};

defineExpose({
  formPanel
});
</script>
