<template>
  <Application ref="application" :basic-data="basicInfo" :title="it('modifyTitle')">
    <template #footer>
      <f-submit-state
        :gather-params="methods.getSaveAppData"
        :before-trigger="methods.validateApp"
        :url="saveUrl"
        operate="save"
        @submit-success="handler.handleSaveSuccess"
      />
      <f-submit-state
        v-if="
          [BusinessStatusEnum.CHANGE_SAVE, BusinessStatusEnum.CHANGE_REFUSED].includes(basicInfo.statusId) &&
          getAuth('interClientListCancelChangeBtn') !== btnAuth.None
        "
        type="danger"
        :result-title="it('cancelChange')"
        :operate-name="it('cancelChange')"
        :confirm-text="it('cancelChangeInfo')"
        :result-confirm="it('cancelChangeSuccess')"
        :gather-params="methods.getCancelChangeData"
        :url="cancelChangeInfoUrl"
        @close="handler.handleOperateSuccess"
      />
      <f-submit-state
        :gather-params="methods.getSubmitAppData"
        :before-trigger="methods.validateApp"
        :url="saveUrl"
        operate="submit"
        is-show-result-btn-group
        @left-button-emit="handler.handleContiuneAdd"
        @right-button-emit="handler.handleGoBack"
        @close="handler.handleOperateSuccess"
      />
      <f-button type="info" plain @click.prevent="handler.handleGoBack">{{ it("queryList") }}</f-button>
    </template>
  </Application>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useNewly from "../hooks/useNewly";
import Application from "./Application.vue";
import { saveUrl, cancelChangeInfoUrl, getBtnAuth } from "../url";
import { useMenuStore } from "@/stores/modules/menu.ts";
import { storeToRefs } from "pinia";
import httpTool from "@/utils/http.ts";

const application = ref();

const { t } = useI18n();
const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);

const BusinessStatusEnum = useConst("common.BusinessStatus");
const btnAuth = useConst("system.BusinessPropertyTypeEnum");

const { basicInfo, handler, methods } = useNewly(it, application);

const propPermissionTypeParam = {
  clientClass: "INTER_CLIENT",
  menuResourceId: null
};
const authList = ref([]);
const getMenuId = () => {
  // 内部客户信息维护菜单路径常量
  const currentMenuPath = "/clientmanage/clientinfouphold/interclientinfo";
  // 菜单信息
  const menuStore = useMenuStore();
  // 菜单id映射集合
  const { menuIdMap } = storeToRefs(menuStore);
  const currentMenu = menuIdMap.value[currentMenuPath];
  let menuResourceId = null;
  if (currentMenu) {
    menuResourceId = currentMenu.menuId;
  }
  propPermissionTypeParam.menuResourceId = menuResourceId;
  return menuResourceId;
};
const getAuth = (name: string) => {
  const arr = authList.value.filter(btn => btn.documentName === name);
  if (arr[0]) {
    return arr[0].permissionType;
  }
  return btnAuth.RW;
};
onMounted(async () => {
  getMenuId();
  const res = await httpTool.post(getBtnAuth, propPermissionTypeParam);
  if (res.success && res.data?.length) {
    authList.value = res.data;
  }
});
</script>
