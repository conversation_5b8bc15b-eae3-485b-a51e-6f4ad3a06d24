<template>
  <!-- 注册信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('registrationInfo')"
    :model="formModel"
    id="panel_registrationInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 注册国家/地区 -->
    <f-form-item
      prop="country"
      :class="getChangeFieldClass('country')"
      :label="it('registerCa')"
      v-if="so('country')"
      :required="req('country')"
    >
      <f-select
        v-model="formModel.country"
        :url="provinceSelector"
        :extra-data="{
          dictClassEq: 'ISO_3166_alpha_2_Country_Dic',
          parentDictKeyEq: 'ISO_3166_alpha_2_Country_Dic'
        }"
        value-key="dictKey"
        label="dictValue"
        :disabled="dis('country')"
      />
    </f-form-item>

    <!-- 注册登记地址（省） -->
    <f-form-item
      prop="province"
      :class="getChangeFieldClass('province')"
      :label="it('convince')"
      v-if="so('province')"
      :required="req('province')"
    >
      <f-select
        v-model="formModel.province"
        :url="provinceSelector"
        :extra-data="provinceParams"
        value-key="dictKey"
        label="dictValue"
        @change="handleProvinceChange"
        :disabled="dis('province')"
      />
    </f-form-item>

    <!-- 注册登记地址（市） -->
    <f-form-item
      prop="city"
      :class="getChangeFieldClass('city')"
      :label="it('citySign')"
      v-if="so('city')"
      :required="req('city')"
    >
      <f-select
        ref="cityRef"
        v-model="formModel.city"
        :url="citySelector"
        :extra-data="cityParams"
        value-key="dictKey"
        label="dictValue"
        @change="handleCityChange"
        :disabled="dis('city')"
      />
    </f-form-item>

    <!-- 注册登记地址（区） -->
    <f-form-item
      prop="area"
      :class="getChangeFieldClass('area')"
      :label="it('county')"
      v-if="so('area')"
      :required="req('area')"
    >
      <f-select
        ref="areaRef"
        v-model="formModel.area"
        :url="areaSelector"
        :extra-data="areaParams"
        value-key="dictKey"
        label="dictValue"
        @change="handleAreaChange"
        :disabled="dis('area')"
      />
    </f-form-item>

    <!-- 详细地址 -->
    <f-form-item
      prop="registeredAddress"
      :class="getChangeFieldClass('registeredAddress')"
      :label="it('registeredAddress')"
      :rules="getMinRule(getMin('registeredAddress'))"
      v-if="so('registeredAddress')"
      :required="req('registeredAddress')"
    >
      <f-input
        v-model="formModel.registeredAddress"
        :maxlength="getMax('registeredAddress')"
        :minlength="getMin('registeredAddress')"
        :disabled="dis('registeredAddress')"
      />
    </f-form-item>

    <!-- 地区 -->
    <f-form-item
      prop="enterpriseArea"
      :class="getChangeFieldClass('enterpriseArea')"
      :label="it('enterpArea')"
      v-if="so('enterpriseArea')"
      :required="req('enterpriseArea')"
    >
      <f-select v-model="formModel.enterpriseArea" :data="constMap.EnterpriseArea" :disabled="dis('enterpriseArea')" />
    </f-form-item>

    <!-- 客户外文地址: 新增 -->
    <f-form-item
      prop="engRegisteredAddress"
      :class="getChangeFieldClass('engRegisteredAddress')"
      :label="it('engRegisteredAddress')"
      :rules="getMinRule(getMin('engRegisteredAddress'))"
      v-if="so('engRegisteredAddress')"
      :required="req('engRegisteredAddress')"
    >
      <f-input
        v-model="formModel.engRegisteredAddress"
        :maxlength="getMax('engRegisteredAddress')"
        :minlength="getMin('engRegisteredAddress')"
        :disabled="dis('engRegisteredAddress')"
      />
    </f-form-item>

    <!-- 法人代表 -->
    <f-form-item
      prop="certName"
      :class="getChangeFieldClass('certName')"
      :label="it('legaler')"
      :rules="getMinRule(getMin('certName'))"
      v-if="so('certName')"
      :required="req('certName')"
    >
      <f-input
        v-model="formModel.certName"
        :maxlength="getMax('certName')"
        :minlength="getMin('certName')"
        :disabled="dis('certName')"
      />
    </f-form-item>

    <!-- 法人代表证件类型 -->
    <f-form-item
      prop="certType"
      :class="getChangeFieldClass('certType')"
      :label="it('legalCertType')"
      v-if="so('certType')"
      :required="req('certType')"
    >
      <f-select v-model="formModel.certType" :data="constMap.certTypeEnum" :disabled="dis('certType')" />
    </f-form-item>

    <!-- 法人代表证件号码 -->
    <f-form-item
      prop="certNo"
      :class="getChangeFieldClass('certNo')"
      :label="it('legalCertCode')"
      :rules="getMinRule(getMin('certNo'))"
      v-if="so('certNo')"
      :required="req('certNo')"
    >
      <f-input
        v-model="formModel.certNo"
        :maxlength="getMax('certNo')"
        :minlength="getMin('certNo')"
        :disabled="dis('certNo')"
      />
    </f-form-item>

    <!-- 金融许可证号码: 新增 -->
    <f-form-item
      :label="it('financialLicenseNumber')"
      prop="financialLicenseNumber"
      :class="getChangeFieldClass('financialLicenseNumber')"
      :rules="getMinRule(getMin('financialLicenseNumber'))"
      v-if="so('financialLicenseNumber')"
      :required="req('financialLicenseNumber')"
    >
      <f-input
        v-model="formModel.financialLicenseNumber"
        :maxlength="getMax('financialLicenseNumber')"
        :minlength="getMin('financialLicenseNumber')"
        :disabled="dis('financialLicenseNumber')"
      />
    </f-form-item>

    <!-- 纳税人识别号: 新增 -->
    <f-form-item
      :label="it('taxpayerNo')"
      prop="taxpayerIdentityNo"
      :class="getChangeFieldClass('taxpayerIdentityNo')"
      :rules="getMinRule(getMin('taxpayerIdentityNo'))"
      v-if="so('taxpayerIdentityNo')"
      :required="req('taxpayerIdentityNo')"
    >
      <f-input
        v-model="formModel.taxpayerIdentityNo"
        :maxlength="getMax('taxpayerIdentityNo')"
        :minlength="getMin('taxpayerIdentityNo')"
        :disabled="dis('taxpayerIdentityNo')"
      />
    </f-form-item>

    <!-- 注册币种 -->
    <f-form-item
      prop="registeredCurrencyCode"
      :class="getChangeFieldClass('registeredCurrencyCode')"
      :label="it('registerCurrcy')"
      v-if="so('registeredCurrencyId')"
      :required="req('registeredCurrencyId')"
    >
      <f-select
        v-model="formModel.registeredCurrencyCode"
        :url="registeredCurrencyIdSelector"
        value-key="currencyCode"
        label="currencyName"
        @change="handleRegisteredCurrencyChange"
        :disabled="dis('registeredCurrencyId')"
      />
    </f-form-item>

    <!-- 注册资本 -->
    <f-form-item
      prop="registeredCapital"
      :class="getChangeFieldClass('registeredCapital')"
      :label="it('registerCap')"
      v-if="so('registeredCapital')"
      :required="req('registeredCapital')"
    >
      <f-amount
        v-model="formModel.registeredCapital"
        :max="getMax('registeredCapital')"
        :min="getMin('registeredCapital')"
        :disabled="dis('registeredCapital')"
        symbol=" "
      />
    </f-form-item>

    <!-- 实收币种 -->
    <f-form-item
      prop="currencyReceivedCode"
      :class="getChangeFieldClass('currencyReceivedCode')"
      :label="it('paidCurrency')"
      v-if="so('currencyReceivedId')"
      :required="req('currencyReceivedId')"
    >
      <f-select
        v-model="formModel.currencyReceivedCode"
        :url="registeredCurrencyIdSelector"
        value-key="currencyCode"
        label="currencyName"
        @change="handleCurrencyReceivedChange"
        :disabled="dis('currencyReceivedId')"
      />
    </f-form-item>

    <!-- 实收资本 -->
    <f-form-item
      prop="capitalReceived"
      :class="getChangeFieldClass('capitalReceived')"
      :label="it('paidCap')"
      v-if="so('capitalReceived')"
      :required="req('capitalReceived')"
    >
      <f-amount
        v-model="formModel.capitalReceived"
        :max="getMax('capitalReceived')"
        :min="getMin('capitalReceived')"
        :disabled="dis('capitalReceived')"
        symbol=" "
      />
    </f-form-item>

    <!-- 成立日期 -->
    <f-form-item
      prop="establishedDate"
      :class="getChangeFieldClass('establishedDate')"
      :label="it('incorporDate')"
      v-if="so('establishedDate')"
      :required="req('establishedDate')"
    >
      <f-date-picker v-model="formModel.establishedDate" type="date" :disabled="dis('establishedDate')" />
    </f-form-item>

    <!-- 营业执照起始日期 -->
    <f-form-item
      prop="certificateStartDate"
      :class="getChangeFieldClass('certificateStartDate')"
      :label="it('busiLicenseStart')"
      v-if="so('certificateStartDate')"
      :required="req('certificateStartDate')"
    >
      <f-date-picker v-model="formModel.certificateStartDate" type="date" :disabled="dis('certificateStartDate')" />
    </f-form-item>

    <!-- 营业执照终止日期 -->
    <f-form-item
      prop="certificateEndDate"
      :class="getChangeFieldClass('certificateEndDate')"
      :label="it('busiLicenseEnd')"
      v-if="so('certificateEndDate')"
      :required="req('certificateEndDate')"
    >
      <f-date-picker v-model="formModel.certificateEndDate" type="date" :disabled="dis('certificateEndDate')" />
    </f-form-item>

    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'registInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, inject } from "vue";
import { useI18n } from "vue-i18n";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import { provinceSelector, citySelector, areaSelector, registeredCurrencyIdSelector, getEnterprise } from "../../url";
import { useInnerField } from "@/views/clientmanage/clientinfouphold/interclientinfo/hooks/useInnerField.ts";
import httpTool from "@/utils/http.ts";
import { changedInjectKey } from "../../types.ts";
import { useMinValid } from "../../hooks/useMinValid.ts";

const { t } = useI18n();
const getChangeFieldClass = inject(changedInjectKey, () => "");
const { getMinRule } = useMinValid();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
});
const { getMax, getMin } = useInnerField(props);
const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return props.fieldMap[prop]?.info?.documentRequired === "YES" && !props.readonly; // 是否必填
};
const cityRef = ref();
const areaRef = ref();

const formPanel = ref();
const formModel = props.modelInfo;

const provinceParams = computed(() => ({ dictClassEq: "ISO_3166_alpha_2_Country_Dic", parentDictKeyEq: "156" }));
const cityParams = computed(() => ({
  dictClassEq: "ISO_3166_alpha_2_Country_Dic",
  parentDictKeyEq: formModel.province || -1
}));
const areaParams = computed(() => ({
  dictClassEq: "ISO_3166_alpha_2_Country_Dic",
  parentDictKeyEq: formModel.city || -1
}));
const handleProvinceChange = async (val, row) => {
  if (val) {
    formModel.provinceName = row.dictValue;
    formModel.provinceNameEn = row.dictValueEn;
    const enterprise = await httpTool.post(getEnterprise, {
      province: val
    });
    if (enterprise.success && enterprise.data) {
      formModel.enterpriseArea = enterprise.data;
    }
  } else {
    formModel.provinceName = "";
    formModel.provinceNameEn = "";
    formModel.enterpriseArea = "";
  }
  nextTick(() => {
    cityRef.value.initRemoteData();
    areaRef.value.initRemoteData();
  });
};
const handleCityChange = (val, row) => {
  if (val) {
    formModel.cityName = row.dictValue;
    formModel.cityNameEn = row.dictValueEn;
  } else {
    formModel.cityName = "";
    formModel.cityNameEn = "";
  }
  nextTick(() => {
    areaRef.value.initRemoteData();
  });
};

const handleAreaChange = (val, row) => {
  if (val) {
    formModel.areaName = row.dictValue;
    formModel.areaNameEn = row.dictValueEn;
  } else {
    formModel.areaName = "";
    formModel.areaNameEn = "";
  }
};

const handleRegisteredCurrencyChange = row => {
  if (row) {
    formModel.registeredCurrencyId = row.currencyId;
    // formModel.registeredCurrencyCode = row.currencyCode;
    formModel.registeredCurrencyName = row.currencyName;
  } else {
    formModel.registeredCurrencyId = null;
    // formModel.registeredCurrencyCode = "";
    formModel.registeredCurrencyName = "";
  }
};

const handleCurrencyReceivedChange = row => {
  if (row) {
    formModel.currencyReceivedId = row.currencyId;
    // formModel.currencyReceivedCode = row.currencyCode;
    formModel.currencyReceivedName = row.currencyName;
  } else {
    formModel.currencyReceivedId = null;
    // formModel.currencyReceivedCode = "";
    formModel.currencyReceivedName = "";
  }
};

defineExpose({
  formPanel
});
</script>
