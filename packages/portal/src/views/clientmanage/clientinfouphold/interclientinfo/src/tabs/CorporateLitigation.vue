<template>
  <!-- 企业诉讼信息 -->
  <f-panel :title="it('corporateLitigationInfo')" id="panel_corporateLitigationInfo" :open="open">
    <f-table-edit
      ref="editTable"
      row-key="_randomId"
      :show-add-btn="!readonly"
      :show-operate="!readonly"
      :data="listInfo"
      @add-row="addData"
    >
      <f-table-column prop="order" type="index" :label="it('order')" />
      <!-- 诉讼记录编号  -->
      <f-table-column prop="litigationCode" :label="it('litigationCode')" v-if="so('litigationCode')">
        <template #edit="scope">
          <f-input v-model="scope.row.litigationCode" maxlength="128" :disabled="dis('litigationCode')" />
        </template>
      </f-table-column>
      <!-- 起诉日期  -->
      <f-table-column prop="litigationDate" :label="it('litigationDate')" v-if="so('litigationDate')">
        <template #edit="scope">
          <f-date-picker v-model="scope.row.litigationDate" type="date" :disabled="dis('litigationDate')" />
        </template>
      </f-table-column>
      <!-- 起诉人 -->
      <f-table-column prop="litigationName" :label="it('litigationName')" v-if="so('litigationName')">
        <template #edit="scope">
          <f-input v-model="scope.row.litigationName" maxlength="128" :disabled="dis('litigationName')" />
        </template>
      </f-table-column>
      <!-- 被起诉人 -->
      <f-table-column prop="litigatedName" :label="it('litigatedName')" v-if="so('litigatedName')">
        <template #edit="scope">
          <f-input v-model="scope.row.litigatedName" maxlength="128" :disabled="dis('litigatedName')" />
        </template>
      </f-table-column>
      <!-- 起诉原因  -->
      <f-table-column prop="litigationReason" :label="it('litigationReason')" v-if="so('litigationReason')">
        <template #edit="scope">
          <f-input v-model="scope.row.litigationReason" maxlength="128" :disabled="dis('litigationReason')" />
        </template>
      </f-table-column>
      <!-- 币种  -->
      <f-table-column prop="currencyId" :label="it('currencyId')" v-if="so('currencyId')">
        <template #edit="scope">
          <f-select
            v-model="scope.row.currencyId"
            :url="registeredCurrencyIdSelector"
            value-key="currencyId"
            label="currencyName"
            :disabled="dis('currencyId')"
            @change="
              (value: string, row: Record<string, any>) => {
                if (row) {
                  scope.row.currencyName = row.currencyName;
                  scope.row.currencyCode = row.currencyName;
                } else {
                  scope.row.currencyName = '';
                  scope.row.currencyCode = '';
                }
              }
            "
          />
        </template>
        <template #default="{ row }">
          {{ row.currencyName }}
        </template>
      </f-table-column>
      <!-- 判决执行金额  -->
      <f-table-column
        prop="judgementAmount"
        :label="it('judgementAmount')"
        v-if="so('judgementAmount')"
        formatter="amount"
      >
        <template #edit="scope">
          <f-amount v-model="scope.row.judgementAmount" :disabled="dis('judgementAmount')" />
        </template>
      </f-table-column>
      <!-- 执行结果  -->
      <f-table-column
        prop="judgementResult"
        :label="it('judgementResult')"
        v-if="so('judgementResult')"
        formatter="amount"
      >
        <template #edit="scope">
          <f-amount v-model="scope.row.judgementResult" :disabled="dis('judgementResult')" />
        </template>
      </f-table-column>
    </f-table-edit>
  </f-panel>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { registeredCurrencyIdSelector } from "../../url";

const { t } = useI18n();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  listInfo: {
    type: Array as () => any[],
    default: () => []
  },
  addData: {
    type: Function as () => void,
    required: null
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.fieldMap[prop]?.info?.permissionType === "RO";

const editTable = ref();

defineExpose({
  editTable
});
</script>
