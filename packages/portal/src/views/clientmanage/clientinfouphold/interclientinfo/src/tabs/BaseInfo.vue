<template>
  <!-- 基本信息 -->
  <f-form-panel
    ref="formPanel"
    :title="it('baseInfo')"
    :model="formModel"
    id="panel_baseInfo"
    :open="open"
    :column="3"
    :disabled="readonly"
  >
    <!-- 客户编码 -->
    <f-form-item
      prop="clientCode"
      :class="getChangeFieldClass('clientCode')"
      :label="it('code')"
      :rules="getMinRule(getMin('clientCode'))"
      v-if="so('clientCode')"
    >
      <f-input
        v-model="formModel.clientCode"
        disabled
        :maxlength="getMax('clientCode')"
        :minlength="getMin('clientCode')"
      />
    </f-form-item>
    <!-- 客户中文名称 -->
    <f-form-item
      prop="clientName"
      :class="getChangeFieldClass('clientName')"
      :label="it('clientName')"
      v-if="so('clientName')"
      :required="req('clientName')"
    >
      <f-input v-model="formModel.clientName" disabled v-if="readonly" />
      <f-magnifier-single
        v-else
        v-model="formModel.orgId"
        :url="masterClient"
        :title="it('clientName')"
        method="post"
        row-key="orgId"
        row-label="orgName"
        input-key="codeOrName"
        auto-init
        :params="{
          excludeInterClient: readonly ? '' : formModel.id ? '' : 'YES',
          socialCreditCodeTrl: !readonly && formModel.id ? formModel.corporationDocno : '',
          orgId: isAdd ? (formModel.orgId ? formModel.orgId : '') : formModel.tmpOrgId ? formModel.tmpOrgId : ''
        }"
        :disabled="dis('clientName')"
        @confirm="masterClientConfirm"
        @change="masterClientChange"
      >
        <f-magnifier-column prop="orgName" :label="it('clientName')" />
        <f-magnifier-column prop="socialCreditCode" :label="it('corporationDocno')" />
      </f-magnifier-single>
    </f-form-item>
    <!-- 客户外文名称 -->
    <f-form-item
      prop="englishName"
      :class="getChangeFieldClass('englishName')"
      :label="it('englishName')"
      :rules="getMinRule(getMin('englishName'))"
      v-if="so('englishName')"
      :required="req('englishName')"
    >
      <f-input
        v-model="formModel.englishName"
        :maxlength="getMax('englishName')"
        :minlength="getMin('englishName')"
        :disabled="dis('englishName')"
      />
    </f-form-item>
    <!-- 客户中文简称: 新增  -->
    <f-form-item
      prop="cnName"
      :class="getChangeFieldClass('cnName')"
      :label="it('cnName')"
      :rules="getMinRule(getMin('cnName'))"
      v-if="so('cnName')"
      :required="req('cnName')"
    >
      <f-input
        v-model="formModel.cnName"
        :maxlength="getMax('cnName')"
        :minlength="getMin('cnName')"
        :disabled="dis('cnName')"
      />
    </f-form-item>
    <!-- 客户外文简称 -->
    <f-form-item
      prop="engName"
      :class="getChangeFieldClass('engName')"
      :label="it('engName')"
      :rules="getMinRule(getMin('engName'))"
      v-if="so('engName')"
      :required="req('engName')"
    >
      <f-input
        v-model="formModel.engName"
        :maxlength="getMax('engName')"
        :minlength="getMin('engName')"
        :disabled="dis('engName')"
      />
    </f-form-item>
    <!-- 机构类型: 新增  -->
    <f-form-item
      prop="orgCategory"
      :class="getChangeFieldClass('orgCategory')"
      :label="it('orgCategory')"
      v-if="so('orgCategory')"
      :required="req('orgCategory')"
    >
      <f-select v-model="formModel.orgCategory" :data="constMap.clientOrgCategoryEnum" :disabled="dis('orgCategory')" />
    </f-form-item>
    <!-- 社会统一信用代码 -->
    <f-form-item
      prop="corporationDocno"
      :class="getChangeFieldClass('corporationDocno')"
      :label="it('corporationDocno')"
      :rules="getMinRule(getMin('corporationDocno'))"
      v-if="so('corporationDocno')"
      :required="req('corporationDocno')"
    >
      <f-input
        v-model="formModel.corporationDocno"
        :maxlength="getMax('corporationDocno')"
        :minlength="getMin('corporationDocno')"
        :disabled="dis('corporationDocno')"
      />
    </f-form-item>
    <!-- 组织机构代码 -->
    <f-form-item
      prop="organCodeCert"
      :class="getChangeFieldClass('organCodeCert')"
      :label="it('organCodeCert')"
      :rules="getMinRule(getMin('organCodeCert'))"
      v-if="so('organCodeCert')"
      :required="req('organCodeCert')"
    >
      <f-input
        v-model="formModel.organCodeCert"
        :maxlength="getMax('organCodeCert')"
        :minlength="getMin('organCodeCert')"
        :disabled="dis('organCodeCert')"
      />
    </f-form-item>
    <!-- 外管局特殊代码 -->
    <f-form-item
      prop="safeCode"
      :class="getChangeFieldClass('safeCode')"
      :label="it('foreSpeci')"
      :rules="getMinRule(getMin('safeCode'))"
      v-if="so('safeCode')"
      :required="req('safeCode')"
    >
      <f-input
        v-model="formModel.safeCode"
        :maxlength="getMax('safeCode')"
        :minlength="getMin('safeCode')"
        :disabled="dis('safeCode')"
      />
    </f-form-item>
    <!-- 组织机构类型 -->
    <f-form-item
      prop="organizational"
      :class="getChangeFieldClass('organizational')"
      :label="it('organizationType')"
      v-if="so('organizational')"
      :required="req('organizational')"
    >
      <f-select
        v-model="formModel.organizational"
        :data="constMap.clientOrganizationalType"
        :disabled="dis('organizational')"
      />
    </f-form-item>
    <!-- 境内/外 -->
    <f-form-item
      prop="domesticOrForeign"
      :class="getChangeFieldClass('domesticOrForeign')"
      :label="it('domesticOrForeign')"
      v-if="so('domesticOrForeign')"
      :required="req('domesticOrForeign')"
    >
      <f-select
        v-model="formModel.domesticOrForeign"
        :data="dis('domesticOrForeign') ? constMap.domesticForeignEnum : domesticForeignConst"
        :placeholder="it('domesticOrForeignPlaceHolder')"
        :disabled="dis('domesticOrForeign')"
      />
    </f-form-item>
    <!-- 法人层级 -->
    <f-form-item
      prop="corLevel"
      :class="getChangeFieldClass('corLevel')"
      :label="it('legalLevel')"
      :rules="getMinRule(getMin('corLevel'))"
      v-if="so('corLevel')"
      :required="req('corLevel')"
    >
      <f-input
        v-model="formModel.corLevel"
        :maxlength="getMax('corLevel')"
        :minlength="getMin('corLevel')"
        :disabled="dis('corLevel')"
      />
    </f-form-item>
    <!-- 管理层级 -->
    <f-form-item
      prop="manageLevel"
      :class="getChangeFieldClass('manageLevel')"
      :label="it('manageLevel')"
      :rules="getMinRule(getMin('manageLevel'))"
      v-if="so('manageLevel')"
      :required="req('manageLevel')"
    >
      <f-input
        v-model="formModel.manageLevel"
        :maxlength="getMax('manageLevel')"
        :minlength="getMin('manageLevel')"
        :disabled="dis('manageLevel')"
      />
    </f-form-item>
    <!-- 上级控股单位 -->
    <f-form-item
      prop="parentId"
      :class="getChangeFieldClass('parentId')"
      :label="it('higherControl')"
      v-if="so('parentId')"
      :required="req('parentId')"
    >
      <f-magnifier-single
        v-model="formModel.parentId"
        :url="parentClientCodeMagnifier"
        :title="it('higherControl')"
        method="post"
        row-key="id"
        row-label="orgName"
        input-key="clientCodeOrName"
        auto-init
        :disabled="dis('parentId')"
        @change="handler.handleParentChange"
        @clear="handler.handleParentClear"
      >
        <f-magnifier-column prop="clientCode" :label="it('clientCode')" />
        <f-magnifier-column prop="orgName" :label="it('clientName')" />
      </f-magnifier-single>
    </f-form-item>
    <!-- 上级管理单位 -->
    <f-form-item
      prop="budgetSuperiorId"
      :class="getChangeFieldClass('budgetSuperiorId')"
      :label="it('highManage')"
      v-if="so('budgetSuperiorId')"
      :required="req('budgetSuperiorId')"
    >
      <f-magnifier-single
        v-model="formModel.budgetSuperiorId"
        :url="parentClientCodeMagnifier"
        :title="it('highManage')"
        method="post"
        row-key="id"
        row-label="orgName"
        input-key="clientCodeOrName"
        auto-init
        :disabled="dis('budgetSuperiorId')"
        @change="handler.handleBudgetChange"
        @clear="handler.handleBudgetClear"
      >
        <f-magnifier-column prop="clientCode" :label="it('clientCode')" />
        <f-magnifier-column prop="orgName" :label="it('clientName')" />
      </f-magnifier-single>
    </f-form-item>
    <!-- 行业门类 -->
    <f-form-item
      prop="industryCategoryCode"
      :class="getChangeFieldClass('industryCategoryCode')"
      :label="it('businessCate')"
      v-if="so('industryCategoryCode')"
      :required="req('industryCategoryCode')"
    >
      <f-select
        v-model="formModel.industryCategoryCode"
        :url="countrySelector"
        :extra-data="{ dictClassEq: 'IndustryCategoryFour', parentDictKeyEq: 'IndustryCategoryFour' }"
        value-key="dictKey"
        label="dictValue"
        :disabled="dis('industryCategoryCode')"
        @change="handleIndustryChange"
      />
    </f-form-item>
    <!-- 行业大类 -->
    <f-form-item
      prop="industryCategoryBigCode"
      :class="getChangeFieldClass('industryCategoryBigCode')"
      :label="it('businessBig')"
      v-if="so('industryCategoryBigCode')"
      :required="req('industryCategoryBigCode')"
    >
      <f-select
        ref="industryBigRef"
        v-model="formModel.industryCategoryBigCode"
        :url="countrySelector"
        :extra-data="bigParams"
        value-key="dictKey"
        label="dictValue"
        :disabled="dis('industryCategoryBigCode')"
        @change="handleBigChange"
      />
    </f-form-item>
    <!-- 行业中类 -->
    <f-form-item
      prop="industryCategoryMidCode"
      :class="getChangeFieldClass('industryCategoryMidCode')"
      :label="it('businessMiddle')"
      v-if="so('industryCategoryMidCode')"
      :required="req('industryCategoryMidCode')"
    >
      <f-select
        ref="industryMidRef"
        v-model="formModel.industryCategoryMidCode"
        :url="countrySelector"
        :extra-data="midParams"
        value-key="dictKey"
        label="dictValue"
        :disabled="dis('industryCategoryMidCode')"
        @change="handleMidChange"
      />
    </f-form-item>
    <!-- 行业小类 -->
    <f-form-item
      prop="industryCategorySmallCode"
      :class="getChangeFieldClass('industryCategorySmallCode')"
      :label="it('businessSmall')"
      v-if="so('industryCategorySmallCode')"
      :required="req('industryCategorySmallCode')"
    >
      <f-select
        ref="industrySmaRef"
        v-model="formModel.industryCategorySmallCode"
        :url="countrySelector"
        :extra-data="smaParams"
        value-key="dictKey"
        label="dictValue"
        :disabled="dis('industryCategorySmallCode')"
        @change="handleSmaChange"
      />
    </f-form-item>
    <!-- 洗钱风险等级: 新增 -->
    <f-form-item
      prop="launderingRisk"
      :class="getChangeFieldClass('launderingRisk')"
      :label="it('antiMoneyRisk')"
      :rules="getMinRule(getMin('launderingRisk'))"
      v-if="so('launderingRisk')"
      :required="req('launderingRisk')"
    >
      <f-input
        v-model="formModel.launderingRisk"
        :maxlength="getMax('launderingRisk')"
        :minlength="getMin('launderingRisk')"
        :disabled="dis('launderingRisk')"
      />
    </f-form-item>
    <!-- 所属板块 -->
    <f-form-item
      prop="plateId"
      :class="getChangeFieldClass('plateId')"
      :label="it('plateId')"
      v-if="so('plateId')"
      :required="req('plateId')"
    >
      <f-select
        :url="platSet"
        v-model="formModel.plateId"
        value-key="id"
        label="name"
        :extra-data="{}"
        :disabled="dis('plateId')"
      />
    </f-form-item>
    <!-- 企业规模 -->
    <f-form-item
      prop="enterpriseScale"
      :class="getChangeFieldClass('enterpriseScale')"
      :label="it('enterpriseScale')"
      v-if="so('enterpriseScale')"
      :required="req('enterpriseScale')"
    >
      <f-select
        v-model="formModel.enterpriseScale"
        :data="constMap.enterpriseScaleEnum.omitConst(['UNKNOWN', 'OTHER'])"
        :disabled="dis('enterpriseScale')"
      />
    </f-form-item>
    <!-- 是否上市公司 -->
    <f-form-item
      prop="listedCompany"
      :class="getChangeFieldClass('listedCompany')"
      :label="it('isMarketCom')"
      v-if="so('listedCompany')"
      :required="req('listedCompany')"
    >
      <f-select v-model="formModel.listedCompany" :data="constMap.yesOrNoEnum" :disabled="dis('listedCompany')" />
    </f-form-item>
    <!-- 企业控股类型 -->
    <f-form-item
      prop="holdingTypeCode"
      :class="getChangeFieldClass('holdingTypeCode')"
      :label="it('holdingType')"
      v-if="so('holdingTypeCode')"
      :required="req('holdingTypeCode')"
    >
      <f-input v-if="readonly && !formModel.holdingTypeCode" v-model="formModel.holdingTypeCode" disabled />
      <f-cascader
        v-else
        v-model="formModel.holdingTypeArr"
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="greenIndustryCategoryOptions"
        style="width: 100%"
        @change="handleGreenCategoryChange"
        :disabled="dis('holdingTypeCode')"
      />
    </f-form-item>
    <!-- 开户许可证核准号: 新增 -->
    <f-form-item
      prop="openingLicenceNo"
      :class="getChangeFieldClass('openingLicenceNo')"
      :label="it('openingLicenceNo')"
      :rules="getMinRule(getMin('openingLicenceNo'))"
      v-if="so('openingLicenceNo')"
      :required="req('openingLicenceNo')"
    >
      <f-input
        v-model="formModel.openingLicenceNo"
        :maxlength="getMax('openingLicenceNo')"
        :minlength="getMin('openingLicenceNo')"
        :disabled="dis('openingLicenceNo')"
      />
    </f-form-item>
    <!-- 基本存款账号 -->
    <f-form-item
      prop="accountNo"
      :class="getChangeFieldClass('accountNo')"
      :label="it('accountNo')"
      :rules="getMinRule(getMin('accountNo'))"
      v-if="so('accountNo')"
      :required="req('accountNo')"
    >
      <f-input
        v-model="formModel.accountNo"
        :maxlength="getMax('accountNo')"
        :minlength="getMin('accountNo')"
        :disabled="dis('accountNo')"
      />
    </f-form-item>
    <!-- 基本账户开户行名称 -->
    <f-form-item
      prop="accountOpeningName"
      :class="getChangeFieldClass('accountOpeningName')"
      :label="it('accountOpeningName')"
      :rules="getMinRule(getMin('accountOpeningName'))"
      v-if="so('accountOpeningName')"
      :required="req('accountOpeningName')"
    >
      <f-input
        v-model="formModel.accountOpeningName"
        :maxlength="getMax('accountOpeningName')"
        :minlength="getMin('accountOpeningName')"
        :disabled="dis('accountOpeningName')"
      />
    </f-form-item>
    <!-- 登记状态 -->
    <f-form-item
      prop="registeredStatus"
      :class="getChangeFieldClass('registeredStatus')"
      :label="it('registerState')"
      v-if="so('registeredStatus')"
      :required="req('registeredStatus')"
    >
      <f-select
        v-model="formModel.registeredStatus"
        :data="constMap.clientRegisteredStatus"
        :disabled="dis('registeredStatus')"
      />
    </f-form-item>
    <!-- 客户状态 -->
    <f-form-item
      prop="clientStatusId"
      :class="getChangeFieldClass('clientStatusId')"
      :label="it('clientStatusId')"
      v-if="so('clientStatusId')"
      :required="req('clientStatusId')"
    >
      <f-select
        v-model="formModel.clientStatusId"
        :data="constMap.clientStatusIdEnum"
        :placeholder="it('clientStatusIdPlaceHolder')"
        :disabled="dis('clientStatusId')"
      />
    </f-form-item>
    <!-- 停用说明 -->
    <f-form-item
      prop="disableDescription"
      :class="getChangeFieldClass('disableDescription')"
      :label="it('disableDescription')"
      :rules="getMinRule(getMin('disableDescription'))"
      v-if="formModel.clientStatusId === 'OUTING'"
      required
    >
      <f-input
        v-model="formModel.disableDescription"
        :maxlength="getMax('disableDescription')"
        :minlength="getMin('disableDescription')"
      />
    </f-form-item>
    <!-- 财务公司客户类型: 新增 -->
    <f-form-item
      prop="financeType"
      :class="getChangeFieldClass('financeType')"
      :label="it('financeType')"
      v-if="so('financeType')"
      :required="req('financeType')"
    >
      <f-select v-model="formModel.financeType" :data="constMap.financeTypeEnum" :disabled="dis('financeType')" />
    </f-form-item>
    <!-- 客户国籍国家代码: 新增 -->
    <f-form-item
      prop="clientCountryCode"
      :class="getChangeFieldClass('clientCountryCode')"
      :label="it('clientCountryCode')"
      v-if="so('clientCountryCode')"
      :required="req('clientCountryCode')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.clientCountryCode"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          dictClassEq: 'ISO_3166_alpha_2_Country_Dic',
          parentDictKeyEq: 'ISO_3166_alpha_2_Country_Dic'
        }"
        :disabled="dis('clientCountryCode')"
      />
    </f-form-item>
    <!-- 客户国籍或注册地国家评级: 新增 -->
    <f-form-item
      prop="countryRating"
      :class="getChangeFieldClass('countryRating')"
      :label="it('countryRating')"
      v-if="so('countryRating')"
      :required="req('countryRating')"
    >
      <f-select v-model="formModel.countryRating" :data="constMap.countryRatingEnum" :disabled="dis('countryRating')" />
    </f-form-item>
    <!-- 运营国家代码: 新增 -->
    <f-form-item
      prop="startingCountryCode"
      :class="getChangeFieldClass('startingCountryCode')"
      :label="it('startingCountryCode')"
      v-if="so('startingCountryCode')"
      :required="req('startingCountryCode')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.startingCountryCode"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          dictClassEq: 'ISO_3166_alpha_2_Country_Dic',
          parentDictKeyEq: 'ISO_3166_alpha_2_Country_Dic'
        }"
        @change="handleStartingCountryChange"
        :disabled="dis('startingCountryCode')"
      />
    </f-form-item>
    <!-- 风险控制国家代码: 新增 -->
    <f-form-item
      prop="riskCountryCode"
      :class="getChangeFieldClass('riskCountryCode')"
      :label="it('riskCountryCode')"
      v-if="so('riskCountryCode')"
      :required="req('riskCountryCode')"
    >
      <f-select
        :url="countrySelector"
        v-model="formModel.riskCountryCode"
        value-key="dictKey"
        label="dictValue"
        :extra-data="{
          dictClassEq: 'ISO_3166_alpha_2_Country_Dic',
          parentDictKeyEq: 'ISO_3166_alpha_2_Country_Dic'
        }"
        @change="handleRiskCountryChange"
        :disabled="dis('riskCountryCode')"
      />
    </f-form-item>
    <!-- 客户外部评级: 新增 -->
    <f-form-item
      prop="clientOutRating"
      :class="getChangeFieldClass('clientOutRating')"
      :label="it('clientOutRating')"
      v-if="so('clientOutRating')"
      :required="req('clientOutRating')"
    >
      <f-select
        v-model="formModel.clientOutRating"
        :data="constMap.countryRatingEnum"
        :disabled="dis('clientOutRating')"
      />
    </f-form-item>
    <!-- 客户外部评级机构: 新增 -->
    <f-form-item
      prop="ratingOutOfficeName"
      :class="getChangeFieldClass('ratingOutOfficeName')"
      :label="it('ratingOutOfficeName')"
      :rules="getMinRule(getMin('ratingOutOfficeName'))"
      v-if="so('ratingOutOfficeName')"
      :required="req('ratingOutOfficeName')"
    >
      <f-input
        v-model="formModel.ratingOutOfficeName"
        :maxlength="getMax('ratingOutOfficeName')"
        :minlength="getMin('ratingOutOfficeName')"
        :disabled="dis('ratingOutOfficeName')"
      />
    </f-form-item>
    <!-- 客户分类 -->
    <f-form-item
      prop="classifyId"
      :class="getChangeFieldClass('classifyId')"
      :label="it('classifyId')"
      v-if="so('classifyId')"
      :required="req('classifyId')"
    >
      <f-cascader
        v-model="formModel.classifyIdArr"
        clearable
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="classifyOptions"
        style="width: 100%"
        :disabled="readonly || dis('classifyId')"
        @change="handleClassifyChange"
      />
    </f-form-item>
    <!-- 客户分类二 -->
    <f-form-item
      prop="classifySecId"
      :class="getChangeFieldClass('classifySecId')"
      :label="it('clientCateTwo')"
      v-if="so('classifySecId')"
      :required="req('classifySecId')"
    >
      <f-cascader
        v-model="formModel.classifyIdArr2"
        clearable
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="classifyOptions2"
        style="width: 100%"
        :disabled="readonly || dis('classifySecId')"
        @change="handleClassify2Change"
      />
    </f-form-item>
    <!-- 客户分类三 -->
    <f-form-item
      prop="classifyThdId"
      :class="getChangeFieldClass('classifyThdId')"
      :label="it('clientCateThree')"
      v-if="so('classifyThdId')"
      :required="req('classifyThdId')"
    >
      <f-cascader
        v-model="formModel.classifyIdArr3"
        clearable
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="classifyOptions3"
        style="width: 100%"
        :disabled="readonly || dis('classifyThdId')"
        @change="handleClassify3Change"
      />
    </f-form-item>
    <!-- 国民经济部门 -->
    <f-form-item
      prop="economicSectorNationalEnum"
      :class="getChangeFieldClass('economicSectorNationalEnum')"
      :label="it('fianceDept')"
      v-if="so('economicSectorNationalEnum')"
      :required="req('economicSectorNationalEnum')"
    >
      <f-cascader
        v-model="formModel.economicSectorNationalArr"
        clearable
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="economicSectorNationalOptions"
        style="width: 100%"
        :disabled="readonly || dis('economicSectorNationalEnum')"
        @change="handleEconomicSectorNationalChange"
      />
    </f-form-item>
    <!-- 居民标志 -->
    <f-form-item
      prop="residentType"
      :class="getChangeFieldClass('residentType')"
      :label="it('rrsidentSigns')"
      v-if="so('residentType')"
      :required="req('residentType')"
    >
      <f-select v-model="formModel.residentType" :data="constMap.clientResidentType" :disabled="dis('residentType')" />
    </f-form-item>
    <!-- 农村城市标志 -->
    <f-form-item
      prop="ruralCitySign"
      :class="getChangeFieldClass('ruralCitySign')"
      :label="it('ruralCitySign')"
      v-if="so('ruralCitySign')"
      :required="req('ruralCitySign')"
    >
      <f-cascader
        v-model="formModel.ruralCitySignArr"
        clearable
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="ruralCitySignOptions"
        style="width: 100%"
        :disabled="dis('ruralCitySign')"
        @change="handleruralCitySignChange"
      />
    </f-form-item>
    <!-- 关联方类型 -->
    <f-form-item
      prop="relatedPartiesType"
      :class="getChangeFieldClass('relatedPartiesType')"
      :label="it('relateType')"
      v-if="so('relatedPartiesType')"
      :required="req('relatedPartiesType')"
    >
      <f-select
        v-model="formModel.relatedPartiesType"
        :data="constMap.clientRelatedPartiesType"
        :disabled="dis('relatedPartiesType')"
      />
    </f-form-item>
    <!-- 是否关联方 -->
    <f-form-item
      prop="relatedPartiesFlag"
      :class="getChangeFieldClass('relatedPartiesFlag')"
      :label="it('isRelate')"
      v-if="so('relatedPartiesFlag')"
      :required="req('relatedPartiesFlag')"
    >
      <f-select
        v-model="formModel.relatedPartiesFlag"
        :data="constMap.yesOrNoEnum"
        :disabled="dis('relatedPartiesFlag')"
      />
    </f-form-item>
    <!-- 持股比率: 新增 -->
    <f-form-item
      prop="shareholdingRatio"
      :class="getChangeFieldClass('shareholdingRatio')"
      :label="it('shareholdingRatio')"
      v-if="so('shareholdingRatio')"
      :required="req('shareholdingRatio')"
    >
      <f-number
        v-model="formModel.shareholdingRatio"
        is-rate
        :disabled="dis('shareholdingRatio')"
        :min="getMin('shareholdingRatio')"
        :max="getMax('shareholdingRatio')"
      />
    </f-form-item>
    <!-- 是否投资级公司 -->
    <f-form-item
      prop="investCompany"
      :class="getChangeFieldClass('investCompany')"
      :label="it('isInvesComp')"
      v-if="so('investCompany')"
      :required="req('investCompany')"
    >
      <f-select v-model="formModel.investCompany" :data="constMap.yesOrNoEnum" :disabled="dis('investCompany')" />
    </f-form-item>
    <!-- 是否科创 -->
    <f-form-item
      prop="isScienceCreate"
      :class="getChangeFieldClass('isScienceCreate')"
      :label="it('isScienceCreate')"
      v-if="so('isScienceCreate')"
      :required="req('isScienceCreate')"
    >
      <f-select v-model="formModel.isScienceCreate" :data="constMap.yesOrNoEnum" :disabled="dis('isScienceCreate')" />
    </f-form-item>
    <!-- 经济类型代码 -->
    <f-form-item
      prop="economicType"
      :class="getChangeFieldClass('economicType')"
      :label="it('economicCode')"
      v-if="so('economicType')"
      :required="req('economicType')"
    >
      <f-select v-model="formModel.economicType" :data="constMap.economicTypeEnum" :disabled="dis('economicType')" />
    </f-form-item>
    <!-- 登记地行政区划代码 -->
    <f-form-item
      prop="extendRegisterNo"
      :class="getChangeFieldClass('extendRegisterNo')"
      :label="it('extendRegisterNo')"
      v-if="so('extendRegisterNo')"
      :required="req('extendRegisterNo')"
    >
      <f-cascader
        v-model="formModel.extendRegisterArr"
        clearable
        :props="{ value: 'dictKey', label: 'dictValue', checkStrictly: true }"
        :options="extendRegisterOptions"
        style="width: 100%"
        :disabled="readonly || dis('extendRegisterNo')"
        @change="handleExtendRegisterChange"
      />
    </f-form-item>
    <!-- 财务人员 -->
    <f-form-item
      prop="treasurerName"
      :class="getChangeFieldClass('treasurerName')"
      :label="it('financeStaff')"
      :rules="getMinRule(getMin('treasurerName'))"
      v-if="so('treasurerName')"
      :required="req('treasurerName')"
    >
      <f-input
        v-model="formModel.treasurerName"
        :maxlength="getMax('treasurerName')"
        :minlength="getMin('treasurerName')"
        :disabled="dis('treasurerName')"
      />
    </f-form-item>
    <!-- 财务人员证件类别 -->
    <f-form-item
      prop="treasurerStatusType"
      :class="getChangeFieldClass('treasurerStatusType')"
      :label="it('financeCertType')"
      v-if="so('treasurerStatusType')"
      :required="req('treasurerStatusType')"
    >
      <f-select
        v-model="formModel.treasurerStatusType"
        :data="constMap.clientTreasurerStatusType"
        :disabled="dis('treasurerStatusType')"
      />
    </f-form-item>
    <!-- 财务人员证件号码 -->
    <f-form-item
      prop="treasurerStatusNo"
      :class="getChangeFieldClass('treasurerStatusNo')"
      :label="it('financeCertCode')"
      :rules="getMinRule(getMin('treasurerStatusNo'))"
      v-if="so('treasurerStatusNo')"
      :required="req('treasurerStatusNo')"
    >
      <f-input
        v-model="formModel.treasurerStatusNo"
        :maxlength="getMax('treasurerStatusNo')"
        :minlength="getMin('treasurerStatusNo')"
        :disabled="dis('treasurerStatusNo')"
      />
    </f-form-item>
    <!-- 负责人员 -->
    <f-form-item
      prop="superintendent"
      :class="getChangeFieldClass('superintendent')"
      :label="it('responseStaff')"
      :rules="getMinRule(getMin('superintendent'))"
      v-if="so('superintendent')"
      :required="req('superintendent')"
    >
      <f-input
        v-model="formModel.superintendent"
        :maxlength="getMax('superintendent')"
        :minlength="getMin('superintendent')"
        :disabled="dis('superintendent')"
      />
    </f-form-item>
    <!-- 负责人员证件号码 -->
    <f-form-item
      prop="superintendentStatusNo"
      :class="getChangeFieldClass('superintendentStatusNo')"
      :label="it('responseCertCode')"
      :rules="getMinRule(getMin('superintendentStatusNo'))"
      v-if="so('superintendentStatusNo')"
      :required="req('superintendentStatusNo')"
    >
      <f-input
        v-model="formModel.superintendentStatusNo"
        :maxlength="getMax('superintendentStatusNo')"
        :minlength="getMin('superintendentStatusNo')"
        :disabled="dis('superintendentStatusNo')"
      />
    </f-form-item>
    <!-- 客户资料类型 -->
    <f-form-item
      prop="clientDataType"
      :class="getChangeFieldClass('clientDataType')"
      :label="it('clientInfoType')"
      v-if="so('clientDataType')"
      :required="req('clientDataType')"
    >
      <f-select v-model="formModel.clientDataType" :data="constMap.clientDataType" :disabled="dis('clientDataType')" />
    </f-form-item>
    <!-- 企业身份标识类型 -->
    <f-form-item
      prop="companyStatusType"
      :class="getChangeFieldClass('companyStatusType')"
      :label="it('enterpIdentType')"
      v-if="so('companyStatusType')"
      :required="req('companyStatusType')"
    >
      <f-select
        v-model="formModel.companyStatusType"
        :data="constMap.clientCompanyStatusType"
        :disabled="dis('companyStatusType')"
      />
    </f-form-item>
    <!-- 企业身份标识号码 -->
    <f-form-item
      prop="companyStatusNo"
      :class="getChangeFieldClass('companyStatusNo')"
      :label="it('enterpIdentCode')"
      :rules="getMinRule(getMin('companyStatusNo'))"
      v-if="so('companyStatusNo')"
      :required="req('companyStatusNo')"
    >
      <f-input
        v-model="formModel.companyStatusNo"
        :maxlength="getMax('companyStatusNo')"
        :minlength="getMin('companyStatusNo')"
        :disabled="dis('companyStatusNo')"
      />
    </f-form-item>
    <!-- 出资人类型: 新增  -->
    <f-form-item
      prop="contributorType"
      :class="getChangeFieldClass('contributorType')"
      :label="it('contributorType')"
      v-if="so('contributorType')"
      :required="req('contributorType')"
    >
      <f-select
        v-model="formModel.contributorType"
        :data="constMap.contributorTypeEnum"
        :disabled="dis('contributorType')"
      />
    </f-form-item>
    <!-- 出资人身份类别: 新增  -->
    <f-form-item
      prop="contributorCategory"
      :class="getChangeFieldClass('contributorCategory')"
      :label="it('contributorCategory')"
      v-if="so('contributorCategory')"
      :required="req('contributorCategory')"
    >
      <f-select
        v-model="formModel.contributorCategory"
        :data="constMap.contributorCategoryEnum"
        :disabled="dis('contributorCategory')"
      />
    </f-form-item>
    <!-- 出资人名称: 新增 -->
    <f-form-item
      prop="contributorName"
      :class="getChangeFieldClass('contributorName')"
      :label="it('contributorName')"
      :rules="getMinRule(getMin('contributorName'))"
      v-if="so('contributorName')"
      :required="req('contributorName')"
    >
      <f-input
        v-model="formModel.contributorName"
        :maxlength="getMax('contributorName')"
        :minlength="getMin('contributorName')"
        :disabled="dis('contributorName')"
      />
    </f-form-item>
    <!-- 出资人身份标识类型: 新增  -->
    <f-form-item
      prop="contributorStatusType"
      :class="getChangeFieldClass('contributorStatusType')"
      :label="it('contributorStatusType')"
      v-if="so('contributorStatusType')"
      :required="req('contributorStatusType')"
    >
      <f-select
        v-model="formModel.contributorStatusType"
        :data="constMap.contributorStatusTypeEnum"
        :disabled="dis('contributorStatusType')"
      />
    </f-form-item>
    <!-- 出资人身份标识号码: 新增 -->
    <f-form-item
      prop="contributorStatusNo"
      :class="getChangeFieldClass('contributorStatusNo')"
      :label="it('contributorStatusNo')"
      :rules="getMinRule(getMin('contributorStatusNo'))"
      v-if="so('contributorStatusNo')"
      :required="req('contributorStatusNo')"
    >
      <f-input
        v-model="formModel.contributorStatusNo"
        :maxlength="getMax('contributorStatusNo')"
        :minlength="getMin('contributorStatusNo')"
        :disabled="dis('contributorStatusNo')"
      />
    </f-form-item>
    <!-- 出资比例: 新增 -->
    <f-form-item
      prop="contributorRatio"
      :class="getChangeFieldClass('contributorRatio')"
      :label="it('contributorRatio')"
      v-if="so('contributorRatio')"
      :required="req('contributorRatio')"
    >
      <f-number
        v-model="formModel.contributorRatio"
        is-rate
        :disabled="dis('contributorRatio')"
        :min="getMin('contributorRatio')"
        :max="getMax('contributorRatio')"
      />
    </f-form-item>
    <!-- 实际控制人身份类别: 新增  -->
    <f-form-item
      prop="controllerCategory"
      :class="getChangeFieldClass('controllerCategory')"
      :label="it('controllerCategory')"
      v-if="so('controllerCategory')"
      :required="req('controllerCategory')"
    >
      <f-select
        v-model="formModel.controllerCategory"
        :data="constMap.contributorCategoryEnum"
        :disabled="dis('controllerCategory')"
      />
    </f-form-item>
    <!-- 实际控制人名称: 新增 -->
    <f-form-item
      prop="controllerName"
      :class="getChangeFieldClass('controllerName')"
      :label="it('controllerName')"
      :rules="getMinRule(getMin('controllerName'))"
      v-if="so('controllerName')"
      :required="req('controllerName')"
    >
      <f-input
        v-model="formModel.controllerName"
        :maxlength="getMax('controllerName')"
        :minlength="getMin('controllerName')"
        :disabled="dis('controllerName')"
      />
    </f-form-item>
    <!-- 实际控制人身份标识类型: 新增  -->
    <f-form-item
      prop="controllerStatusType"
      :class="getChangeFieldClass('controllerStatusType')"
      :label="it('controllerStatusType')"
      v-if="so('controllerStatusType')"
      :required="req('controllerStatusType')"
    >
      <f-select
        v-model="formModel.controllerStatusType"
        :data="constMap.contributorStatusTypeEnum"
        :disabled="dis('controllerStatusType')"
      />
    </f-form-item>
    <!-- 实际控制人身份标识号码: 新增 -->
    <f-form-item
      prop="controllerStatusNo"
      :class="getChangeFieldClass('controllerStatusNo')"
      :label="it('controllerStatusNo')"
      :rules="getMinRule(getMin('controllerStatusNo'))"
      v-if="so('controllerStatusNo')"
      :required="req('controllerStatusNo')"
    >
      <f-input
        v-model="formModel.controllerStatusNo"
        :maxlength="getMax('controllerStatusNo')"
        :minlength="getMin('controllerStatusNo')"
        :disabled="dis('controllerStatusNo')"
      />
    </f-form-item>
    <!-- 上级机构类型: 新增  -->
    <f-form-item
      prop="parentOfficeType"
      :class="getChangeFieldClass('parentOfficeType')"
      :label="it('parentOfficeType')"
      v-if="so('parentOfficeType')"
      :required="req('parentOfficeType')"
    >
      <f-select
        v-model="formModel.parentOfficeType"
        :data="constMap.parentOfficeTypeEnum"
        :disabled="dis('parentOfficeType')"
      />
    </f-form-item>
    <!-- 上级机构名称: 新增 -->
    <f-form-item
      prop="parentOfficeName"
      :class="getChangeFieldClass('parentOfficeName')"
      :label="it('parentOfficeName')"
      :rules="getMinRule(getMin('parentOfficeName'))"
      v-if="so('parentOfficeName')"
      :required="req('parentOfficeName')"
    >
      <f-input
        v-model="formModel.parentOfficeName"
        :maxlength="getMax('parentOfficeName')"
        :minlength="getMin('parentOfficeName')"
        :disabled="dis('parentOfficeName')"
      />
    </f-form-item>
    <!-- 上级机构身份标识类型: 新增  -->
    <f-form-item
      prop="parentOfficeStatusType"
      :class="getChangeFieldClass('parentOfficeStatusType')"
      :label="it('parentOfficeStatusType')"
      v-if="so('parentOfficeStatusType')"
      :required="req('parentOfficeStatusType')"
    >
      <f-select
        v-model="formModel.parentOfficeStatusType"
        :data="constMap.parentOfficeStatusTypeEnum"
        :disabled="dis('parentOfficeStatusType')"
      />
    </f-form-item>
    <!-- 上级机构身份标识码: 新增 -->
    <f-form-item
      prop="parentOfficeStatusNo"
      :class="getChangeFieldClass('parentOfficeStatusNo')"
      :label="it('parentOfficeStatusNo')"
      :rules="getMinRule(getMin('parentOfficeStatusNo'))"
      v-if="so('parentOfficeStatusNo')"
      :required="req('parentOfficeStatusNo')"
    >
      <f-input
        v-model="formModel.parentOfficeStatusNo"
        :maxlength="getMax('parentOfficeStatusNo')"
        :minlength="getMin('parentOfficeStatusNo')"
        :disabled="dis('parentOfficeStatusNo')"
      />
    </f-form-item>
    <!-- 关联关系类型: 新增  -->
    <f-form-item
      prop="relationType"
      :class="getChangeFieldClass('relationType')"
      :label="it('relationType')"
      v-if="so('relationType')"
      :required="req('relationType')"
    >
      <f-select v-model="formModel.relationType" :data="constMap.relationTypeEnum" :disabled="dis('relationType')" />
    </f-form-item>
    <!-- 关联有效标志: 新增 -->
    <f-form-item
      prop="relationFlag"
      :class="getChangeFieldClass('relationFlag')"
      :label="it('relationFlag')"
      v-if="so('relationFlag')"
      :required="req('relationFlag')"
    >
      <f-switch
        v-model="formModel.relationFlag"
        :active-value="constMap.dataStatusEnum.VALID"
        :inactive-value="constMap.dataStatusEnum.INVALID"
        :disabled="dis('relationFlag')"
      />
    </f-form-item>
    <!-- 自定义字段 -->
    <template v-for="(field, docName) in supplementField">
      <DynamicFormItem
        v-if="field.parent.documentName === 'basicInfo'"
        :getChangeFieldClass="getChangeFieldClass"
        :key="docName"
        :docInit="field.info"
        :model-info="extendCustom"
      />
    </template>
  </f-form-panel>
</template>

<script setup lang="ts">
import httpTool from "@/utils/http";
import DynamicFormItem from "../components/DynamicFormItem.vue";
import { useMinValid } from "../../hooks/useMinValid.ts";
import { useInnerField } from "../../hooks/useInnerField.ts";
import { computed, inject, nextTick, ref } from "vue";
import { countrySelector, masterClient, parentClientCodeMagnifier, platSet, queryRisk } from "../../url";
import { useI18n } from "vue-i18n";
import { changedInjectKey } from "../../types.ts";

const getChangeFieldClass = inject(changedInjectKey, () => "");

const { t } = useI18n();

const { getMinRule } = useMinValid();

const props = defineProps({
  open: {
    type: Boolean,
    default: true
  },
  fieldMap: {
    type: Object as Record<string, object>,
    required: true
  },
  constMap: {
    type: Object as Record<string, any>,
    required: true
  },
  configMap: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  handler: {
    type: Object as Record<string, any>,
    default: () => ({})
  },
  supplementField: {
    type: Object as Record<string, any>,
    required: true
  },
  extendCustom: {
    type: Object as Record<string, any>,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  },
  basicInfo: {
    type: Object as Record<string, any>,
    default: () => ({})
  },
  isAdd: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(["masterClientChange"]);

const { getMax, getMin } = useInnerField(props);

const it = key => t("clientmanage.clientinfouphold.interclientinfo." + key);
const so = prop => !!props.fieldMap[prop];
const dis = prop => props.readonly || props.fieldMap[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return props.fieldMap[prop]?.info?.documentRequired === "YES" && !props.readonly; // 是否必填
};
const domesticForeignConst = computed(() =>
  props.constMap.domesticForeignEnum.filter(item => item.value === props.basicInfo.domesticOrForeign)
);

const formPanel = ref();
const formModel = props.modelInfo;

const handleRiskCountryChange = (val, row) => {
  if (val) {
    formModel.riskCountryName = row.dictValue;
  } else {
    formModel.riskCountryName = "";
  }
};

const handleStartingCountryChange = (val, row) => {
  if (val) {
    formModel.startingCountryName = row.dictValue;
  } else {
    formModel.startingCountryName = "";
  }
};

const handleGreenCategoryChange = args => {
  if (args && args.length > 0) {
    const code = args[args.length - 1];
    formModel.holdingTypeCode = code;
    formModel.holdingTypeName = greenIndustryCategoryData.value.find(x => x.dictKey === code)?.dictValue;
  } else {
    formModel.holdingTypeCode = "";
    formModel.holdingTypeName = "";
  }
};

const handleExtendRegisterChange = args => {
  if (args && args.length > 0) {
    formModel.extendRegisterNo = args[args.length - 1];
  } else {
    formModel.extendRegisterNo = "";
  }
};

const handleClassifyChange = args => {
  if (args && args.length > 0) {
    formModel.classifyId = args[args.length - 1];
  } else {
    formModel.classifyId = "";
  }
};

const handleClassify2Change = args => {
  if (args && args.length > 0) {
    formModel.classifySecId = args[args.length - 1];
  } else {
    formModel.classifySecId = "";
  }
};
// 匹配公司
const matchEnOne = [
  "ClientClassify3-1",
  "ClientClassify3-11",
  "ClientClassify3-13",
  "ClientClassify3-15",
  "ClientClassify3-17",
  "ClientClassify3-19"
];
// 匹配机关
const matchEnTwo = ["ClientClassify3-5", "ClientClassify3-51", "ClientClassify3-53", "ClientClassify3-59"];

const handleClassify3Change = args => {
  if (args && args.length > 0) {
    formModel.classifyThdId = args[args.length - 1];
    if (matchEnOne.includes(formModel.classifyThdId)) {
      formModel.economicSectorNationalArr = ["20", "21"];
      formModel.economicSectorNationalEnum = "21";
    } else if (matchEnTwo.includes(formModel.classifyThdId)) {
      formModel.economicSectorNationalArr = ["1", "5"];
      formModel.economicSectorNationalEnum = "5";
    } else {
      formModel.economicSectorNationalArr = [];
      formModel.economicSectorNationalEnum = "";
    }
  } else {
    formModel.classifyThdId = "";
  }
};

const handleEconomicSectorNationalChange = args => {
  if (args && args.length > 0) {
    formModel.economicSectorNationalEnum = args[args.length - 1];
  } else {
    formModel.economicSectorNationalEnum = "";
  }
};

const handleruralCitySignChange = args => {
  if (args && args.length > 0) {
    formModel.ruralCitySign = args[args.length - 1];
  } else {
    formModel.ruralCitySign = "";
  }
};

let riskInit = false;

const masterClientConfirm = async (val: any) => {
  if (!props.isAdd && !riskInit) {
    riskInit = true;
    return;
  }
  const res = await httpTool.post(queryRisk, {
    socialCreditCode: val.socialCreditCode,
    businessStatusList: ["APPROVED"]
  });
  if (res.success) {
    formModel.launderingRisk = res.data?.[0]?.clientcreditgrade;
  } else {
    formModel.launderingRisk = "";
  }
};

const masterClientChange = async (val: any) => {
  emit("masterClientChange", val);
  if (val) {
    formModel.corporationDocno = val.socialCreditCode;
    formModel.englishName = val.orgEnName;
    formModel.cnName = val.orgShortName;
    formModel.organCodeCert = val.orgCode;
    formModel.manageLevel = val.managerLevel;
    formModel.corLevel = val.legalLevel;
    formModel.shareholdingRatio = val.shRate;
    formModel.accountNo = val.depositAccount;
    formModel.accountOpeningName = val.depositAccountName;
    formModel.contributorName = val.investorName;
    formModel.contributorRatio = val.investorRate;
    formModel.controllerName = val.actualCtName;
    formModel.controllerStatusNo = val.actualCtidCode;
    formModel.registeredStatus = val.reStatus;
    formModel.plateId = val.plateId;
    formModel.budgetSuperiorId = val.budgetSuperiorId;
    formModel.parentId = val.parentId;
    formModel.organizational = val.orgType;
    formModel.classifyId = val.customerClass;
    formModel.isScienceCreate = val.isStinnovation;
    const pkey = greenIndustryCategoryData.value?.find(x => x.dictKey === val.shType)?.parentDictKey;
    if (pkey) {
      if (pkey === "HoldingType") {
        formModel.holdingTypeArr = [val.shType];
      } else {
        formModel.holdingTypeArr = [pkey, val.shType];
      }
    } else {
      formModel.holdingTypeArr = [];
    }
    formModel.holdingTypeCode = val.shType;
    formModel.listedCompany = val.listingMark;
    formModel.economicType = val.economicTypeCode;
    formModel.contributorType = val.investorType;
    formModel.contributorCategory = val.investoridType;
    formModel.controllerCategory = val.actualCtType;
    formModel.controllerStatusType = val.actualCtidType;
    formModel.enterpriseScale = val.orgScale;
    formModel.orgCategory = val.companyType;
    formModel.clientName = val.orgName;
  } else {
    formModel.corporationDocno = "";
    formModel.englishName = "";
    formModel.cnName = "";
    formModel.certName = "";
    formModel.organCodeCert = "";
    formModel.manageLevel = "";
    formModel.corLevel = "";
    formModel.shareholdingRatio = "";
    formModel.accountNo = "";
    formModel.accountOpeningName = "";
    formModel.contributorName = "";
    formModel.contributorRatio = "";
    formModel.controllerName = "";
    formModel.controllerStatusNo = "";
    formModel.launderingRisk = "";
    formModel.registeredStatus = "";
    formModel.plateId = "";
    formModel.budgetSuperiorId = "";
    formModel.parentId = "";
    formModel.organizational = "";
    formModel.classifyId = "";
    formModel.isScienceCreate = "";
    formModel.holdingTypeCode = "";
    formModel.listedCompany = "";
    formModel.economicType = "";
    formModel.contributorType = "";
    formModel.contributorCategory = "";
    formModel.controllerCategory = "";
    formModel.controllerStatusType = "";
    formModel.enterpriseScale = "";
    formModel.orgCategory = "";
    formModel.clientName = "";
  }
};

const industryBigRef = ref();
const industryMidRef = ref();
const industrySmaRef = ref();

const bigParams = computed(() => ({
  dictClassEq: "IndustryCategoryFour",
  parentDictKeyEq: formModel.industryCategoryCode || -1
}));
const midParams = computed(() => ({
  dictClassEq: "IndustryCategoryFour",
  parentDictKeyEq: formModel.industryCategoryBigCode || -1
}));
const smaParams = computed(() => ({
  dictClassEq: "IndustryCategoryFour",
  parentDictKeyEq: formModel.industryCategoryMidCode || -1
}));

const handleIndustryChange = (val, row) => {
  if (val) {
    formModel.industryCategoryName = row.dictValue;
  } else {
    formModel.industryCategoryName = "";
  }
  nextTick(() => {
    industryBigRef.value.initRemoteData();
    industryMidRef.value.initRemoteData();
    industrySmaRef.value.initRemoteData();
  });
};
const handleBigChange = (val, row) => {
  if (val) {
    formModel.industryCategoryBigName = row.dictValue;
  } else {
    formModel.industryCategoryBigName = "";
  }
  nextTick(() => {
    industryMidRef.value.initRemoteData();
    industrySmaRef.value.initRemoteData();
  });
};
const handleMidChange = (val, row) => {
  if (val) {
    formModel.industryCategoryMidName = row.dictValue;
  } else {
    formModel.industryCategoryMidName = "";
  }
  nextTick(() => {
    industrySmaRef.value.initRemoteData();
  });
};
const handleSmaChange = (val, row) => {
  if (val) {
    formModel.industryCategorySmallName = row.dictValue;
  } else {
    formModel.industryCategorySmallName = "";
  }
};

const greenIndustryCategoryData = ref([]);
const greenIndustryCategoryOptions = ref([]);
const extendRegisterData = ref([]);
const extendRegisterOptions = ref([]);
const classifyData = ref([]);
const classifyOptions = ref([]);
const classifyData2 = ref([]);
const classifyOptions2 = ref([]);
const classifyData3 = ref([]);
const classifyOptions3 = ref([]);
const economicSectorNationalData = ref([]);
const economicSectorNationalOptions = ref([]);
const ruralCitySignData = ref([]);
const ruralCitySignOptions = ref([]);

httpTool.post(countrySelector, { dictClassEq: "HoldingType" }).then(res => {
  greenIndustryCategoryData.value = res.data;
  greenIndustryCategoryOptions.value = convertToTree(res.data);
});

httpTool.post(countrySelector, { dictClassEq: "ISO_3166_alpha_2_Country_Dic", dictLevelIn: [2, 3, 4] }).then(res => {
  extendRegisterData.value = res.data;
  extendRegisterOptions.value = convertToTree(res.data, 2);
});

httpTool.post(countrySelector, { dictClassEq: "ClientClassify1", dictLevelIn: [1, 2] }).then(res => {
  classifyData.value = res.data;
  classifyOptions.value = convertToTree(res.data);
});

httpTool.post(countrySelector, { dictClassEq: "ClientClassify2", dictLevelIn: [1, 2] }).then(res => {
  classifyData2.value = res.data;
  classifyOptions2.value = convertToTree(res.data);
});

httpTool.post(countrySelector, { dictClassEq: "ClientClassify3", dictLevelIn: [1, 2] }).then(res => {
  classifyData3.value = res.data;
  classifyOptions3.value = convertToTree(res.data);
});

httpTool.post(countrySelector, { dictClassEq: "EconomicSectorNational", dictLevelIn: [1, 2] }).then(res => {
  economicSectorNationalData.value = res.data;
  economicSectorNationalOptions.value = convertToTree(res.data);
});

httpTool.post(countrySelector, { dictClassEq: "ClientRuralCitySign", dictLevelIn: [1, 2] }).then(res => {
  ruralCitySignData.value = res.data;
  ruralCitySignOptions.value = convertToTree(res.data);
});

const convertToTree = (data, needLevel = 1) => {
  const obj = {};
  data.forEach(x => {
    obj[x.dictKey] = x;
  });

  const tree = [];
  data.forEach(x => {
    if (x.parentDictKey) {
      if (obj?.[x.parentDictKey]) {
        if (!obj[x.parentDictKey].children) {
          obj[x.parentDictKey].children = [];
        }
        obj[x.parentDictKey].children.push(x);
      }
    }
    if (x.dictLevel === needLevel) {
      tree.push(x);
    }
  });
  return tree;
};

defineExpose({
  formPanel,
  recoverRiskStatus: () => {
    riskInit = false;
  }
});
</script>
