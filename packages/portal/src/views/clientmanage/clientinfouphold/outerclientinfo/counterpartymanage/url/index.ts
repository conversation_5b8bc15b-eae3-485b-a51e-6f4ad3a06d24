// 列表查询
export const counterpartyManageList =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/list";
// 保存
export const savePlateSet =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/save";
// 查询明细
export const getInfoUrl =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/get-counterparty-manage";

// 交易对手类型下拉框
export const counterpartyTypeSelect =
  "{clientmanage}/api/v1/finance/client-manage/client-template/get-template-select-counterparty";
// 交易对手放大镜
export const codeUrl = "{clientmanage}/api/v1/finance/common/magnifier/query-counterparty-list";
// 中间表客户放大镜
export const middleMainDataUrl = "{clientmanage}/api/v1/finance/common/magnifier/query-middleMain-data";
// 开户行放大镜
export const openBankUrl = "{bankportal}/api/v1/finance/bank-plat/bank-setting/remote/server/list";
//属性下拉框
export const itemInfoUrl = "{clientmanage}/api/v1/finance/common/selector/query-attribute-name";
//导出
export const exportUrl =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/export";
// TODO: 文件上传地址
export const fileUrl = "https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15";

export const dictionaryUrl = "{master-data}/api/v1/finance/query/master-data/dictionary/remote/server/list";

//控股类型
export const holdingTypeSelector = "{clientmanage}/api/v1/finance/common/selector/query-holding-type-list";

export const deleteUrl =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/delete";
// 删除
export const batchDeleteUrl =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/batch-delete";
// 保存校验
export const saveCheck =
  "{clientmanage}/api/v1/finance/clientmanage/clientinfouphold/outerclientinfo/counterparty-manage/save-check";
