import httpTool from "@/utils/http.ts";
import { goPage } from "../hooks/usePage";
import { saveCheck } from "../url";
import { useConst } from "@ifs/support";
import { FMessageBox } from "@dtg/frontend-plus";

export default function useAdd(t, application) {
  const YesOrNoEnum = useConst("common.YesOrNo");
  const methods = {
    getAppData() {
      return application.value.getFormData();
    },
    getDeleteData() {
      const data = application.value.getFormData();
      return { id: data.id, version: data.version };
    },
    async validateApp(cb = () => {}) {
      const formValid = await application.value.validateForm(cb);
      if (!formValid) {
        return false;
      }
      try {
        if (methods.getAppData()?.cpCorporationDocno) {
          const beforeCheck = await httpTool.post(saveCheck, {
            cpCorporationDocno: methods.getAppData()?.cpCorporationDocno
          });
          if (beforeCheck && beforeCheck.success && beforeCheck.data) {
            const corporationDocnoMatch = beforeCheck.data.corporationDocnoMatch;
            const customerCodeMatch = beforeCheck.data.customerCodeMatch;
            if (corporationDocnoMatch && corporationDocnoMatch === YesOrNoEnum.NO) {
              FMessageBox.report(
                t("clientmanage.clientinfouphold.outerclientinfo.counterpartymanage.corporationDocnoMatchTip")
              );
              return false;
            }
            if (customerCodeMatch && customerCodeMatch === YesOrNoEnum.NO) {
              FMessageBox.report(
                t("clientmanage.clientinfouphold.outerclientinfo.counterpartymanage.customerCodeMatchTip")
              );
              return false;
            }
          }
        }
        return true;
      } catch (e) {
        console.error(e);
        return true;
      }
    }
  };

  const handler = {
    handleGoBack() {
      goPage("list");
    },
    handleSaveSuccess(res) {
      if (res.success) {
        goPage("list");
      }
    },
    handleRemoveSuccess(res) {
      if (res.success) {
        goPage("list");
      }
    }
  };

  return {
    methods,
    handler
  };
}
