export default {
  listTitle: "交易对手维护-链接查找",
  addTitle: "交易对手维护-新增",
  modifyTitle: "交易对手维护-修改",
  basicTitle: "交易对手维护-查看",
  add: "新增",
  save: "保存",
  delete: "删除",
  goBack: "链接查找",
  back: "返回",
  deleteSuccess: "删除成功",
  deleteFail: "删除失败",
  batchDeleteTip: "是否批量删除勾选记录？",
  deleteTip: "是否删除？",
  saveTip: "是否保存？",
  saveSucess: "保存成功",
  saveFail: "保存失败",
  operate: "操作",
  close: "关闭",
  countrypartType: "交易对手类型",
  countrypartTypePlaceholder: "请输入交易对手类型",
  codeFrom: "交易对手编号由",
  to: "至",
  nameFrom: "交易对手名称由",
  establishmentdateStart: "成立日期由",
  codeHolder: "请输入交易对手编号",
  nameHolder: "请输入交易对手名称",
  code: "交易对手编号",
  name: "交易对手名称",
  establishmentDate: "成立日期",
  registeredCapital: "注册资本（万元）",
  modifyUserName: "操作人",
  modifyTime: "操作时间",
  basicInfo: "详细资料",
  placeholder: "请输入必填内容",
  openBankHolder: "请输入开户行",
  topBankName: "银行类别",
  bankCode: "开户行编号",
  bankName: "开户行名称",
  record: "记录",
  item: "条",
  extAttrDefin: "扩展属性",
  files: "附件",
  export: "批量导出",
  openBank: "开户行",
  corporationDocno: "统一社会信用代码",
  financialInstitution: "金融机构编码",
  registeredAddress: "注册地址",
  counterpartyCategory: "客户分类",
  isRelatedParties: "是否关联方",
  holdingType: "经济成分",
  economicSectorNational: "国民经济部门",
  clientRatingTotal: "客户信用级别总等级数",
  clientRatingInter: "客户信用评级（内部）",
  clientRatingOut: "客户信用评级（外部）",
  isWhite: "是否白名单",
  isTopBank: "是否总行",
  baseInfo: "基本信息",
  file: "附件",
  domesticOrForeign: "境内/外",
  counterpartyClass: "交易对手分类",
  province: "注册登记地址（省）",
  city: "注册登记地址（市）",
  area: "注册登记地址（区）",
  organCode: "机构参与者代码",
  cpClientCodeList: "交易对手编号",
  cName: "交易对手名称",
  corporationDocnoMatchTip: "未匹配到相同统一社会信用代码的交易对手",
  customerCodeMatchTip: "无ERP交易对手编号"
};
