<template>
  <f-query-scene :title="t('clientmanage.clientinfouphold.clientfinanceinfo.query')">
    <template #query-table>
      <f-query-grid
        ref="queryTableRef"
        query-comp-id="clientmanage-clientinfouphold-clientfinanceinfo-query-001"
        table-comp-id="clientmanage-clientinfouphold-clientfinanceinfo-table-001"
        row-key="id"
        :table-columns="tableColumns"
        :url="clientFinanceSearch"
        :export-exclude="['operate']"
        :export-url="clientFinanceSearchExport"
        border
        :selectable-all="selectableAll"
        :form-data="queryFrom"
        label-width="80px"
        show-header
        auto-reset
        auto-init
        :show-collapse="false"
        :show-print="false"
        tile-panel
        :show-count-value="false"
        :count-label="t('clientmanage.clientinfouphold.clientfinanceinfo.record')"
        :count-label-unit="t('clientmanage.clientinfouphold.clientfinanceinfo.transactions')"
        :show-summation-sum="false"
        :summation-biz-label="t('clientmanage.clientinfouphold.clientfinanceinfo.record')"
        :summation-biz-unit="t('clientmanage.clientinfouphold.clientfinanceinfo.transactions')"
        @select="handleSelect"
        @select-all="handleSelect"
        @query-table="handleSearch"
        @clear-selection="clearSelection"
      >
        <template #operate>
          <f-button type="primary" @click="linkToAdd">{{
            t("clientmanage.clientinfouphold.clientfinanceinfo.doadd")
          }}</f-button>
          <f-submit-state
            operate="remove"
            type="danger"
            is-batch
            :disabled="!isChecked"
            :gather-params="gatherBatchDeleteParams"
            :url="deleteClientFinanceInfo"
            :confirmText="batchRemoveTip"
            :batchConfirmMap="{
              success: t('common.operate.deleteSuccess'),
              fail: t('common.operate.deleteFail')
            }"
            @close="deletePopClose"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('clientmanage.clientinfouphold.clientfinanceinfo.clientCode')" prop="clientId">
            <f-magnifier-single
              v-model="queryFrom.clientId"
              :title="t('clientmanage.clientinfouphold.clientfinanceinfo.clientMagnifier')"
              :url="queryClientInfo"
              method="post"
              :params="{
                clientclass: clientClass.MEMBER,
                statusid: recordStatus.APPROVED
              }"
              auto-init
              row-key="clientId"
              row-label="clientCode"
              input-key="clientCodeOrName"
            >
              <f-magnifier-column
                prop="clientCode"
                query="code"
                :label="t('clientmanage.clientinfouphold.clientfinanceinfo.clientCode')"
              />
              <f-magnifier-column
                prop="clientName"
                query="name"
                :label="t('clientmanage.clientinfouphold.clientfinanceinfo.clientName')"
              />
            </f-magnifier-single>
          </f-form-item>
          <f-form-item :label="t('clientmanage.clientinfouphold.clientfinanceinfo.sources')" prop="sources">
            <f-select
              v-model="queryFrom.sources"
              filterable
              :data="sourcesType"
              method="post"
              blank-option
              blank-option-value=""
              init-if-blank
              onReady="onReady1"
            />
          </f-form-item>
          <f-form-item
            :label="t('clientmanage.clientinfouphold.clientfinanceinfo.reportName')"
            prop="financeReportType"
          >
            <f-select
              v-model="queryFrom.financeReportType"
              filterable
              blank-option
              blank-option-value=""
              init-if-blank
              :data="reportName"
              onReady="onReady1"
            />
          </f-form-item>
          <f-form-item :label="t('clientmanage.clientinfouphold.clientfinanceinfo.reportType')" prop="reportType">
            <f-select
              v-model="queryFrom.reportType"
              filterable
              blank-option
              blank-option-value=""
              init-if-blank
              :data="reportType"
              method="post"
              onReady="onReady1"
            />
          </f-form-item>
          <f-form-item :label="t('clientmanage.clientinfouphold.clientfinanceinfo.reportYear')" prop="reportYear">
            <f-select
              v-model="queryFrom.reportYear"
              value-key="yearValue"
              filterable
              blank-option
              blank-option-value=""
              init-if-blank
              label="yearName"
              :url="queryYearList"
              method="post"
              onReady="onReady1"
            />
          </f-form-item>
          <f-form-item :label="t('clientmanage.clientinfouphold.clientfinanceinfo.reportMonth')" prop="reportMonth">
            <f-select
              v-model="queryFrom.reportMonth"
              value-key="monthValue"
              filterable
              blank-option
              blank-option-value=""
              init-if-blank
              label="monthName"
              :url="queryMouthList"
              method="post"
              onReady="onReady1"
            />
          </f-form-item>
        </template>
        <template #code="{ row }">
          <f-button @click="viewDetail(row)" link type="primary">{{ row.clientCode }}</f-button>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="toModify(row)" />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { nextTick, provide } from "vue";
import Detail from "./components/Detail.vue";
import OperateButton from "@/components/operate-button/operate-button";
import {
  clientFinanceSearch,
  clientFinanceSearchExport,
  deleteClientFinanceInfo,
  queryClientInfo,
  queryMouthList,
  queryYearList
} from "../url";
import { useI18n } from "vue-i18n";
import useFinanceList from "../hooks/useFinanceList";

const { t } = useI18n();

const {
  clientClass,
  reportName,
  reportType,
  recordStatus,
  sourcesType,

  queryTableRef,
  queryFrom,
  tableColumns,
  selectableAll,
  handleSelect,
  clearSelection,
  handleSearch,
  isChecked,
  rowId,
  detail,
  viewDetail,
  generalButtonOption,
  // 删除
  gatherBatchDeleteParams,
  deletePopClose,
  toModify,
  linkToAdd,
  batchRemoveTip
} = useFinanceList();

provide("onReady1", {
  count: 2,
  handler() {
    nextTick(() => {
      handleSearch();
    });
  }
});
</script>
