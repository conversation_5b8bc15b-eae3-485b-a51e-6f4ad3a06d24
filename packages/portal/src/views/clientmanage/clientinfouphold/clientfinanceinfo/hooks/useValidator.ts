import type { FinanceInfoDto, FinanceReportTemplateDefineDto, FinanceReportTemplateFileDto } from "../types";
import httpTool from "@/utils/http";
import { reactive, ref, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { FMessage, FMessageBox } from "@dtg/frontend-plus";
import {
  directDownload,
  financeReportRepeatCheckUrl,
  financeTemplateDefineUrl,
  financeTemplateFileUrl,
  templateFileDownloadUrl
} from "../url";
import { goPage } from "./usePage";

export const useValidator = () => {
  const { t } = useI18n();
  // 常量
  const yesOrNo = useConst("clientmanage.YesOrNoNew");
  const clientClass = useConst("clientmanage.ClientClass");
  const reportName = useConst("clientmanage.ReportName");
  const reportType = useConst("clientmanage.ReportType");
  const reportSubType = useConst("clientmanage.ReportSubType");
  // const recordStatus = useConst("clientmanage.RecordStatus");
  const recordStatus = useConst("common.BusinessStatusValue");
  const sourcesType = useConst("clientmanage.Sources");

  interface SaveData {
    file: any;
    dto: FinanceInfoDto;
  }

  // 表单定义
  const formInfo = shallowRef();

  const financeReportDetailShow = reactive({ value: false });

  // 表单数据
  const basicInfo = reactive<FinanceInfoDto>({
    audit: "YES",
    reportFlag: 1, // 不再需要区分，始终使用1
    lines: []
  });

  const basicInfoData = reactive<SaveData>({
    file: null,
    dto: basicInfo
  });

  // 校验规则
  const formRules = reactive({
    clientCode: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validClient"),
        trigger: "blur"
      }
    ],
    clientName: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validclientName"),
        trigger: "blur"
      }
    ],
    financeReportType: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validReportName"),
        trigger: "blur"
      }
    ],
    reportYear: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validYear"),
        trigger: "blur"
      }
    ],
    reportMonth: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validMouth"),
        trigger: "blur"
      }
    ],
    reportType: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validReportType"),
        trigger: "blur"
      }
    ],
    reportSubType: [
      {
        required: true,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validReportSubType"),
        trigger: "blur"
      }
    ],
    audit: [
      {
        required: true,
        trigger: "blur"
      }
    ],
    templateDefineId: [
      {
        required: true,
        trigger: "blur"
      }
    ],
    auditFirmName: [
      {
        required: basicInfo.audit === yesOrNo.YES,
        message: t("clientmanage.clientinfouphold.clientfinanceinfo.validAuidtName"),
        trigger: "blur"
      }
    ]
  });

  const formValidator = async () => {
    return formInfo.value.form.validate();
  };
  const repeatConfirmText = ref(undefined);

  /**
   * 保存数据前的完整表单校验
   */
  const saveFormValidator = async () => {
    const result = await formValidator();
    if (!result) {
      return false;
    }
    repeatConfirmText.value = undefined;
    const repeat = await httpTool
      .post(financeReportRepeatCheckUrl, basicInfo, { ignoreGlobalErrorTip: false })
      .then(res => res.data);
    if (repeat && !isHandle.value) {
      repeatConfirmText.value = t("clientmanage.clientinfouphold.clientfinanceinfo.reportRepeat");
      return true;
    }
    if (repeat && isHandle.value) {
      FMessageBox.report(t("clientmanage.clientinfouphold.clientfinanceinfo.reportRepeat1"));
      return false;
    }
    if (!basicInfo.lines && !basicInfoData.file) {
      FMessage.error(t("clientmanage.clientinfouphold.clientfinanceinfo.reportData"));
      return false;
    } else {
      return true;
    }
  };

  //模板下拉框
  const templateFileDefineData = reactive<FinanceReportTemplateDefineDto[]>([]);
  const loadTemplateDefineData = async () => {
    const res = await httpTool.post<FinanceReportTemplateDefineDto[]>(
      financeTemplateDefineUrl,
      {},
      { noLoading: true, ignoreGlobalErrorTip: false }
    );
    if (res.success && res.data && Array.isArray(res.data)) {
      res.data.forEach(item => {
        item.label = item.reportNameList.map(n => reportName.valueToLabel(n)).join(",");
        if (item.industryCategory) {
          item.label += " (" + item.industryCategory + ")";
        }
      });
      templateFileDefineData.splice(0, templateFileDefineData.length, ...res.data);
      if (templateFileDefineData.length === 1) {
        basicInfo.templateDefineId = templateFileDefineData[0].id;
      }
    }
    return res;
  };

  const financeReportTemplateFileDto = reactive<FinanceReportTemplateFileDto>({});

  const templateDefineRef = shallowRef();

  const loadTemplateFileInfo = async value => {
    if (value) {
      // 触发一下表单校验，使得select的rules生效
      // await formValidator();
      return httpTool
        .post(
          financeTemplateFileUrl,
          { defineId: value, reportName: basicInfo.financeReportType },
          { ignoreGlobalErrorTip: false }
        )
        .then(res => {
          if (res.success) {
            financeReportTemplateFileDto.rightProps = null;
            Object.assign(financeReportTemplateFileDto, res.data);
            if (
              financeReportTemplateFileDto &&
              financeReportTemplateFileDto.props &&
              financeReportTemplateFileDto.props[0].fileColumnIndexes
            ) {
              const idx = financeReportTemplateFileDto.props[0].fileColumnIndexes;
              if (idx.indexOf(",") > 0) {
                financeReportTemplateFileDto.rightProps = financeReportTemplateFileDto.props;
              }
            }

            basicInfo.templateFileId = financeReportTemplateFileDto.templateFileId;
            if (financeReportDetailShow.value) {
              showHandReport();
            }
          }
          return res;
        });
    }
  };

  const downloadExcel = async () => {
    const res = await httpTool.post(directDownload, {});
    if (res.success) {
      if (res.data) {
        const { fileId } = res.data;
        if (fileId) {
          httpTool.fileDownload(
            templateFileDownloadUrl,
            { id: fileId },
            { noLoading: true, ignoreGlobalErrorTip: false }
          );
        }
      } else {
        FMessageBox.report(t("clientmanage.clientinfouphold.clientfinanceinfo.unSetTemplate"));
      }
    }
  };

  // 是否手工
  const isHandle = ref(false);

  const showHandReport = async () => {
    const validResult = await formInfo.value.form.validateField(["financeReportType", "reportYear", "reportMonth"]);
    if (validResult) {
      const res = await httpTool.post(directDownload, {});
      if (res.success) {
        if (res.data) {
          basicInfo.lines = financeReportTemplateFileDto.lines;
          basicInfo.sources = sourcesType.INPUT;
          financeReportDetailShow.value = true;
        } else {
          FMessageBox.report(t("clientmanage.clientinfouphold.clientfinanceinfo.unSetTemplate"));
        }
      }
    }
    return validResult;
  };

  const handleShowReport = async () => {
    await showHandReport();
    isHandle.value = true;
  };

  /**
   * 报表文件上传前校验，表单必填项
   */
  const beforeFinanceReportUploadHandler = async () => {
    basicInfo.sources = sourcesType.IMPORT;
    const valid = await formValidator();
    return new Promise((resolve, reject) => {
      if (!valid) {
        FMessage.error(t("clientmanage.clientinfouphold.clientfinanceinfo.formDataError"));
        reject(false);
      } else {
        // 导入的情况下，放弃手动填写的数据
        basicInfo.lines = null;
        resolve(true);
      }
    });
  };

  const createReportTitle = () => {
    basicInfo.reportTitle =
      basicInfo.reportYear +
      t("clientmanage.clientinfouphold.clientfinanceinfo.year") +
      basicInfo.reportMonth +
      t("clientmanage.clientinfouphold.clientfinanceinfo.mouth") +
      reportName.valueToLabel(basicInfo.financeReportType);
  };

  // 客户放大镜修改事件
  const onCodeSelected = (row: any) => {
    basicInfo.clientId = row.clientId;
    basicInfo.clientName = row.clientName;
    basicInfo.clientCode = row.clientCode;
    basicInfo.industryCategoryFuzzy = row.industrycategoryfuzzycode;
    basicInfo.industryCategoryBig = row.industrycategorybigcode;
    basicInfo.industryCategoryMid = row.industrycategorymidcode;
  };
  const clientCodeClear = () => {
    basicInfo.clientCode = null;
    basicInfo.clientId = null;
    basicInfo.clientCode = null;
  };

  const reportNameChange = async () => {
    createReportTitle();
    await loadTemplateDefineData();
    if (basicInfo.templateDefineId) {
      loadTemplateFileInfo(basicInfo.templateDefineId);
    }
  };

  const fileUploadChange = file => {
    basicInfoData.file = file;
    basicInfo.sources = sourcesType.IMPORT;
  };
  const fileRemoveHandler = () => {
    basicInfoData.file = null;
  };

  const reportTypeChange = () => {};
  const reportSubTypeChange = () => {};

  const saveInfo = () => {
    // 存在导入文件的情况下，放弃手工录入的数据
    if (basicInfoData.file) {
      basicInfo.lines = null;
    }
    basicInfoData.dto = basicInfo;
    return basicInfoData;
  };

  /**
   * 保存之后回调处理
   */
  const afterPopClose = (res: any) => {
    if (res.success) {
      linkToList();
    }
  };

  const linkToList = () => {
    goPage("list");
  };

  /**
   * 报表动态样式
   * @param item
   */
  const getProjectNameStyle = item => {
    if (!item) {
      return {
        whiteSpace: "pre",
        height: "100%",
        lineHeight: "100%"
      };
    } else {
      return {
        whiteSpace: "pre",
        height: "100%",
        lineHeight: "100%",
        textAlign: !item.textAlign ? "left" : item.textAlign,
        fontWeight: item.fontBold && item.fontBold === "YES" ? "bold" : "normal"
      };
    }
  };

  return {
    formInfo,
    formRules,
    yesOrNo,
    clientClass,
    reportName,
    reportType,
    reportSubType,
    recordStatus,
    sourcesType,

    basicInfo,
    reportNameChange,
    createReportTitle,

    onCodeSelected,
    clientCodeClear,

    templateDefineRef,
    templateFileDefineData,
    loadTemplateDefineData,
    loadTemplateFileInfo,
    beforeFinanceReportUploadHandler,
    financeReportDetailShow,

    downloadExcel,
    showHandReport,
    // 模板信息
    financeReportTemplateFileDto,
    saveInfo,
    saveFormValidator,
    fileUploadChange,
    fileRemoveHandler,

    reportTypeChange,
    reportSubTypeChange,
    linkToList,

    afterPopClose,
    getProjectNameStyle,
    repeatConfirmText,
    handleShowReport
  };
};

export default useValidator;
