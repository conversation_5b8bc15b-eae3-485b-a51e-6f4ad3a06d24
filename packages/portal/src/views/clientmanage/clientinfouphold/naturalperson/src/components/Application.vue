·
<template>
  <f-vertical-tab-scene v-model:activeName="defaultDisplay" :title="title">
    <!-- 基本信息 -->
    <f-scene-panel :title="it('baseInfo')">
      <f-form-panel ref="form" :model="naturalPerson" :column="3" :disabled="readonly" :title="it('baseInfo')">
        <!-- 编号 -->
        <f-form-item prop="personCode" :label="it('code')">
          <f-input v-model="naturalPerson.personCode" disabled />
        </f-form-item>
        <!-- 姓名 -->
        <f-form-item prop="personName" :label="it('saveName')" v-if="fieldMap.personName" :required="req('personName')">
          <f-input
            v-model="naturalPerson.personName"
            :maxlength="getMax('personName')"
            :minlength="getMin('personName')"
            :disabled="dis('personName')"
          />
        </f-form-item>
        <!-- 身份 -->
        <f-form-item prop="identity" :label="it('identityStr')" v-if="fieldMap.identity" :required="req('identity')">
          <f-select v-model="naturalPerson.identity" :data="IdEntityEnum" :disabled="dis('identity')" />
        </f-form-item>
        <!-- 注册登记地址（省） -->
        <f-form-item
          prop="personProvince"
          :label="it('province')"
          :required="req('personProvince')"
          v-if="!readonly && fieldMap.personProvince"
        >
          <f-select
            v-model="naturalPerson.personProvince"
            :url="countrySelector"
            :extra-data="provinceParams"
            value-key="dictKey"
            label="dictValue"
            @change="handleProvinceChange"
            :disabled="dis('personProvince')"
          />
        </f-form-item>
        <f-form-item prop="personProvinceName" :label="it('province')" v-else-if="pageType === 'view'">
          <f-input
            v-model="naturalPerson.personProvinceName"
            :maxlength="getMax('province')"
            :minlength="getMin('province')"
          />
        </f-form-item>
        <!-- 注册登记地址（市） -->
        <f-form-item
          prop="personCity"
          :label="it('city')"
          :required="req('personCity')"
          v-if="!readonly && fieldMap.personCity"
        >
          <f-select
            ref="cityRef"
            v-model="naturalPerson.personCity"
            :url="countrySelector"
            :extra-data="cityParams"
            value-key="dictKey"
            label="dictValue"
            @change="handleCityChange"
            :disabled="dis('personCity')"
          />
        </f-form-item>
        <f-form-item prop="personCityName" :label="it('city')" v-else-if="pageType === 'view'">
          <f-input v-model="naturalPerson.personCityName" :maxlength="getMax('city')" :minlength="getMin('city')" />
        </f-form-item>
        <!-- 注册登记地址（区） -->
        <f-form-item
          prop="personArea"
          :label="it('zone')"
          :required="req('personArea')"
          v-if="!readonly && fieldMap.personArea"
        >
          <f-select
            ref="areaRef"
            v-model="naturalPerson.personArea"
            :url="countrySelector"
            :extra-data="areaParams"
            value-key="dictKey"
            label="dictValue"
            @change="handleAreaChange"
            :disabled="dis('personArea')"
          />
        </f-form-item>
        <f-form-item prop="personAreaName" :label="it('zone')" v-else-if="pageType === 'view'">
          <f-input
            v-model="naturalPerson.personAreaName"
            :maxlength="getMax('personAreaName')"
            :minlength="getMin('personAreaName')"
          />
        </f-form-item>
        <!-- 地址 -->
        <f-form-item prop="address" :label="it('addr')" :employ="2" v-if="fieldMap.address" :required="req('address')">
          <f-input
            v-model="naturalPerson.address"
            :maxlength="getMax('address')"
            :minlength="getMin('address')"
            :disabled="dis('address')"
          />
        </f-form-item>
        <!-- 电话 -->
        <f-form-item prop="telephone" :label="it('telephone')" v-if="fieldMap.telephone" :required="req('telephone')">
          <f-input
            v-model="naturalPerson.telephone"
            :maxlength="getMax('telephone')"
            :minlength="getMin('telephone')"
            :disabled="dis('telephone')"
          />
        </f-form-item>
        <!-- 证件类型 -->
        <f-form-item
          prop="documentType"
          :label="it('documentTypeStr')"
          v-if="fieldMap.documentType"
          :required="req('documentType')"
        >
          <f-select
            v-model="naturalPerson.documentType"
            :data="DocumentTypEnume.slice(0, 7)"
            :disabled="dis('documentType')"
          />
        </f-form-item>
        <!-- 证件号码 -->
        <f-form-item
          prop="identificationNumber"
          :label="it('identificationNumber')"
          v-if="fieldMap.identificationNumber"
          :required="req('identificationNumber')"
        >
          <f-input
            v-model="naturalPerson.identificationNumber"
            :maxlength="getMax('identificationNumber')"
            :minlength="getMin('identificationNumber')"
            :disabled="dis('identificationNumber')"
          />
        </f-form-item>
        <!-- 备注 -->
        <f-form-item
          prop="personRemark"
          :label="it('remark')"
          :employ="2"
          v-if="fieldMap.personRemark"
          :required="req('personRemark')"
        >
          <f-textarea
            v-model="naturalPerson.personRemark"
            :min-rows="3"
            :maxlength="getMax('personRemark')"
            :minlength="getMin('personRemark')"
            :disabled="dis('personRemark')"
          />
        </f-form-item>
        <!-- 自定义字段 -->
        <template v-for="(field, docName) in supplementField">
          <DynamicFormItem
            v-if="field.parent.documentName.startsWith('basicInfo')"
            :key="docName"
            :docInit="field.info"
            :model-info="extendCustom"
            :is-detail="readonly"
          />
        </template>
      </f-form-panel>
    </f-scene-panel>

    <!-- 自定义页签 -->
    <f-scene-panel v-for="(tab, key) in supplementTab" :key="key" :title="tab.showName">
      <f-form-panel
        ref="supplementTabRef"
        :title="tab.showName"
        :model="naturalPerson"
        id="panel_supplementTab"
        :open="true"
        :column="3"
        :disabled="readonly"
      >
        <template v-for="(field, docName) in supplementField">
          <DynamicFormItem
            v-if="String(field.parent.id) === key"
            :key="docName"
            :docInit="field.info"
            :model-info="extendCustom"
            :is-detail="readonly"
          />
        </template>
      </f-form-panel>
    </f-scene-panel>

    <template #footer><slot name="footer" /></template>
  </f-vertical-tab-scene>
</template>
<script setup lang="ts">
import { shallowRef, ref, computed, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useTemplate from "../../hooks/add/useTemplate";
import useNaturalPerson from "../../hooks/add/useNaturalPerson";
import useExtendCustom from "../../hooks/add/useExtendCustom";
import useApplication from "../../hooks/add/useApplication";
import DynamicFormItem from "./DynamicFormItem.vue";
import { countrySelector } from "../../url";

const { t } = useI18n();
const it = key => t("clientmanage.clientinfouphold.naturalperson." + key);

const IdEntityEnum = useConst("clientmanage.IdEntity"); // 身份
const DocumentTypEnume = useConst("clientmanage.NaturalCertificateType"); // 证件类型
// const DocumentTypEnume = useConst("clientmanage.CorporationCertificateTypeNew"); // 证件类型

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  },
  pageType: {
    type: String,
    default: ""
  }
});

const emits = defineEmits(["on-loaded"]);
const form = shallowRef<any>();
const supplementTabRef = shallowRef();

const { naturalPerson, naturalPersonMethods } = useNaturalPerson();
const { extendCustom, extendCustomMethods } = useExtendCustom(naturalPerson);
const { supplementTab, fieldMap, supplementField, getMax, getMin } = useTemplate(extendCustomMethods);

const { methods } = useApplication(
  it,
  props,
  emits,
  naturalPerson,
  naturalPersonMethods,
  extendCustomMethods,
  form,
  supplementTabRef
);

const dis = prop => props.readonly || fieldMap.value[prop]?.info?.permissionType === "RO"; // 只读 是否禁用
const req = prop => {
  return (
    fieldMap.value[prop]?.info?.documentRequired === "YES" &&
    (fieldMap.value[prop]?.info?.permissionType === "RW" || fieldMap.value[prop]?.info?.permissionType === null) &&
    !props.readonly
  ); // 是否必填
};

const defaultDisplay = ref([]);

const cityRef = ref();
const areaRef = ref();

const provinceParams = computed(() => ({ dictClassEq: "ISO_3166_alpha_2_Country_Dic", parentDictKeyEq: "156" }));
const cityParams = computed(() => ({
  dictClassEq: "ISO_3166_alpha_2_Country_Dic",
  parentDictKeyEq: naturalPerson.personProvince || -1
}));
const areaParams = computed(() => ({
  dictClassEq: "ISO_3166_alpha_2_Country_Dic",
  parentDictKeyEq: naturalPerson.personCity || -1
}));
const handleProvinceChange = (val, row) => {
  if (val) {
    naturalPerson.personProvinceName = row.dictValue;
  } else {
    naturalPerson.personProvinceName = "";
  }
  nextTick(() => {
    cityRef.value.initRemoteData();
    areaRef.value.initRemoteData();
  });
};
const handleCityChange = (val, row) => {
  if (val) {
    naturalPerson.personCityName = row.dictValue;
  } else {
    naturalPerson.personCityName = "";
  }
  nextTick(() => {
    areaRef.value.initRemoteData();
  });
};

const handleAreaChange = (val, row) => {
  if (val) {
    naturalPerson.personAreaName = row.dictValue;
  } else {
    naturalPerson.personAreaName = "";
  }
};

defineExpose(methods);
</script>
