<template>
  <f-form-item
    :prop="'extendCustom.' + schema.documentName"
    :label="schema.showName"
    :required="isDetail ? false : schema.documentRequired === 'YES'"
  >
    <f-checkbox-group v-if="schema.documentType === 1" v-model="formModel[schema.documentName]">
      <f-checkbox v-for="item in listItem" :key="item.value" :label="item.value" />
    </f-checkbox-group>
    <f-input v-if="schema.documentType === 2" v-model="formModel[schema.documentName]" :maxlength="schema.maxField" />
    <f-select v-if="schema.documentType === 3" v-model="formModel[schema.documentName]">
      <f-option v-for="item in listItem" :key="item.value" value-key="value" :label="item.value" :value="item.value" />
    </f-select>
    <f-date-picker v-if="schema.documentType === 4" v-model="formModel[schema.documentName]" type="date" />
    <f-number
      v-if="schema.documentType === 7"
      v-model="formModel[schema.documentName]"
      :max="schema.maxField"
      :min="schema.minField"
      whole-number
    />
    <f-number
      v-if="schema.documentType === 8"
      v-model="formModel[schema.documentName]"
      :max="schema.maxField"
      :min="schema.minField"
    />
    <f-amount
      v-if="schema.documentType === 9"
      v-model="formModel[schema.documentName]"
      :max="schema.maxField"
      :min="schema.minField"
    />
  </f-form-item>
</template>

<script setup lang="ts">
import { ref } from "vue";
const props = defineProps({
  docInit: {
    type: Object as Record<string, object>,
    required: true
  },
  modelInfo: {
    type: Object as Record<string, object>,
    required: true
  },
  isDetail: {
    type: Boolean,
    default: false
  }
});

const schema = props.docInit;
const formModel = props.modelInfo;
const listItem = ref([]);

if (schema.documentType === 1 || schema.documentType === 3) {
  listItem.value = JSON.parse(schema.documentInfo);
}
</script>
