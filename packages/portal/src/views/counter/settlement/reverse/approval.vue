<template>
  <div>
    <f-approval-scene
      :approval-params="statep.approvalParams"
      :approval-agree-url="approvalAgreeUrl"
      :approval-refuse-url="approvalRefuseUrl"
      :business-data="state.reverseReturnDto"
      :post-approval-info="postApprovalInfo"
      :do-back="handleDoBack"
      :allow-operate="urlParams?.pageType === WorkDeskPageType.Approval"
      :title="
        urlParams?.pageType === WorkDeskPageType.Approval
          ? t('counter.settlement.reverse.approvalTitleInfo')
          : t('counter.settlement.reverse.detailTitleInfo')
      "
    >
      <f-multi-form-panel ref="form" :model="state.reverseReturnDto" disabled :column="3">
        <f-panel :title="t('counter.settlement.reverse.basicinfo')" id="basicinfo">
          <f-form-item :label="t('counter.settlement.reverse.businessCode')">
            <f-input v-model="state.reverseReturnDto.businessCode" />
          </f-form-item>
          <!--机构、币种-->
          <f-form-item :label="t('counter.settlement.reverse.officeId')" prop="officeName">
            <f-input v-model="state.reverseReturnDto.officeName" />
          </f-form-item>
          <f-form-item :label="t('counter.settlement.reverse.currencyId')" prop="currencyName">
            <f-input v-model="state.reverseReturnDto.currencyName" />
          </f-form-item>
          <!-- 原交易类型 -->
          <f-form-item :label="t('counter.settlement.reverse.originTransactionType')" prop="originTransactionType">
            <f-scene-view
              :search="state.reverseReturnDto.originTransactionType"
              :data="transactionTypeEnum"
              params="value"
              label="label"
            />
          </f-form-item>
          <f-form-item :label="t('counter.settlement.reverse.originExecuteDate')" prop="originExecuteDate">
            <f-date-picker v-model="state.reverseReturnDto.originExecuteDate" type="date" />
          </f-form-item>
          <f-form-item />
          <f-form-item
            :label="t('counter.settlement.reverse.clientNo')"
            prop="clientCode"
            v-if="
              ![
                transactionTypeEnum.FUNDALLOCATION,
                transactionTypeEnum.PAYMENTEXECUTE,
                transactionTypeEnum.EXTERNALPAYMENTEXECUTE
              ].includes(state.reverseReturnDto.originTransactionType)
            "
          >
            <f-input v-model="state.reverseReturnDto.clientCode" />
          </f-form-item>
          <f-form-item
            :label="t('counter.settlement.reverse.clientName')"
            prop="clientName"
            v-if="
              ![
                transactionTypeEnum.FUNDALLOCATION,
                transactionTypeEnum.PAYMENTEXECUTE,
                transactionTypeEnum.EXTERNALPAYMENTEXECUTE
              ].includes(state.reverseReturnDto.originTransactionType)
            "
          >
            <f-input v-model="state.reverseReturnDto.clientName" />
          </f-form-item>

          <f-form-item :label="t('counter.settlement.reverse.originTransNo')" prop="originTransNo">
            <f-input v-model="state.reverseReturnDto.originTransNo" />
          </f-form-item>
        </f-panel>

        <!--银行付款-->
        <PayDetail
          v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKPAY"
          :businessProperties="businessProperties"
          :pay="state.reverseTransDto"
          :param="queryParams"
          :disabled="true"
        />
        <!--银行收款-->
        <ReceiveDetail
          v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKRECEIVE"
          :receive="state.reverseTransDto"
          :param="queryParams"
        />
        <!--内部转账-->
        <TransferDetail
          v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.INTERNALVIREMENT"
          :transfer="state.reverseTransDto"
          :disabled="true"
        />
        <!-- 对私付款 -->
        <PrivatePayDetail
          v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PRIVATEPYAMENT"
          :businessProperties="businessProperties"
          :privatePay="state.reverseTransDto"
          :param="queryParams"
          :disabled="true"
        />
        <!--   资金调拨   -->
        <FundAllocationDetail
          v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.FUNDALLOCATION"
          :form-data="state.reverseTransDto"
        />
      </f-multi-form-panel>
    </f-approval-scene>
  </div>
</template>
<script setup lang="ts">
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { useReverse } from "./hooks/useReverse";
import { approvalAgreeUrl, approvalRefuseUrl } from "./url";
import { useModify } from "./hooks/useModify";
import PayDetail from "./src/components/BankPayDetail.vue";
import ReceiveDetail from "./src/components/BankReceiveDetail.vue";
import TransferDetail from "./src/components/InternalTransferDetail.vue";
import PrivatePayDetail from "./src/components/PrivateDetail.vue";
import { getUrlSearchAll } from "@/utils/url";
import { useRouter } from "vue-router";
import { doBack, WorkDeskPageType } from "@/utils/wfUtils";
import { onMounted, reactive, watchEffect } from "vue";
import useBusinessProperties from "../common/hooks/useBusinessProperties";
import FundAllocationDetail from "./src/components/FundAllocationDetail.vue";

const { t } = useI18n();

//原交易类型常量
const transactionTypeEnum = useConst("common.TransactionType");

const { state, getReverseDetailInfo, queryParams, toApprovalTransType } = useReverse();

// 系统配置项
const { businessProperties, getBusinessProperties } = useBusinessProperties();

const { getDetailInfo, postApprovalInfo } = useModify(
  state.reverseReturnDto,
  state.reverseTransDto,
  getReverseDetailInfo
);

//审批流参数
const urlParams = getUrlSearchAll();
//审批流路由
const router = useRouter();
const handleDoBack = () => {
  doBack(router, urlParams.backRoute);
};
const statep = reactive({
  approvalParams: {} as object
});

// 页面初始化
onMounted(() => {
  getDetailInfo(urlParams?.billId);
  getBusinessProperties();
});
watchEffect(() => {
  if (state.reverseReturnDto.id !== null) {
    // 审批操作参数
    statep.approvalParams = {
      taskId: urlParams.taskId, // 任务id
      cancelType: urlParams.cancelType, // 审批类型
      approveMode: urlParams.approveMode, // 审批模式
      reviewTask: urlParams.reviewTask, // 是否复核节点
      nodeMark: urlParams.nodeMark, // 是否标记节点
      backRoute: urlParams.backRoute, // 返回路由
      billId: urlParams.billId, // 业务单据id
      systemCode: urlParams.systemCode, // 模块编号
      agencyId: state.reverseReturnDto.officeId, // 组织ID(机构ID或成员单位ID)
      currencyId: state.reverseReturnDto.currencyId, // 币种ID
      transType: toApprovalTransType(state.reverseReturnDto.originTransactionType), // 业务类型
      recordId: state.reverseReturnDto.id // 单据id
    };
  }
});
</script>
