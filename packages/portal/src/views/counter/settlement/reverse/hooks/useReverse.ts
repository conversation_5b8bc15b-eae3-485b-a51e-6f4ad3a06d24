import { useConversion } from "@/hooks/conversion";
import httpTool from "@/utils/http";
import { reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { openDateUrl, queryTransDetailInfo, queryAccountBalanceInfo, queryFileUrl } from "../url";
import type { ReverseReturnDto, ReverseTransDto } from "../types";
import { useConst } from "@ifs/support";
import { storeToRefs } from "pinia";
import { useUserStoreHook } from "@/stores/modules/user.ts";

export const useReverse = () => {
  const {
    defaultOfficeId,
    defaultOfficeCode,
    defaultOfficeName,
    defaultCurrencyId,
    defaultCurrencyCode,
    defaultCurrencyName
  } = storeToRefs(useUserStoreHook());

  const reverseReturnDto: ReverseReturnDto = {
    officeId: defaultOfficeId.value,
    officeCode: defaultOfficeCode.value,
    officeName: defaultOfficeName.value,
    currencyId: defaultCurrencyId.value,
    currencyCode: defaultCurrencyCode.value,
    currencyName: defaultCurrencyName.value
  };
  const reverseTransDto: ReverseTransDto = {};
  const state = reactive({
    reverseTransDto: reverseTransDto,
    reverseReturnDto: reverseReturnDto
  });

  //查询交易详情参数
  const queryTransParms = {
    transactionType: "",
    transId: "",
    clientId: "",
    officeId: "",
    currencyId: "",
    interestStartDate: "",
    transNo: ""
  };
  const { t } = useI18n();
  //查询数据库返回数据下拉框，需要转成字符串的字段
  const { convert } = useConversion([""]);
  const transType = useConst("common.TransactionType");
  //是否
  const yesOrNo = useConst("counter.YesOrNo");

  const payChannel = useConst("counter.PayChannel");
  const balanceType = useConst("counter.BalanceType");
  //指令类型
  const instructionType = useConst("common.InstructionType");
  const payWay = useConst("counter.PayWay");

  //CIPS信息显示标识
  const showCIPSInfoFlag = ref(false);
  //支付方式标识
  const payWayFlag = ref(false);
  const billFlag = ref(false);

  //查询条件
  const queryParams = reactive({
    officeId: -1,
    currencyId: -1
  });
  const currencyRef = ref();
  // 币种查询入参
  const currencyParams = reactive({
    officeId: null
  });
  //查询账户可用余额入参
  const accountBalanceParams = reactive({
    accountId: "",
    accountNo: "",
    billCode: "",
    balanceType: "",
    transNo: "",
    instructionType: ""
  });
  // 机构下拉框
  const officeChange = (value: any, info: any) => {
    reverseReturnDto.officeCode = info.officeCode;
    reverseReturnDto.officeName = info.officeName;
    //查询信息
    queryParams.officeId = value;
    currencyParams.officeId = info.officeId;
    reverseReturnDto.currencyId = null;
    currencyRef.value.initRemoteData();
    originTransNoClear();
  };

  // 币种下拉框
  const currencyChange = (value: any, info: any) => {
    reverseReturnDto.currencyCode = info.currencyCode;
    reverseReturnDto.currencyName = info.currencyName;
    queryParams.currencyId = value;
    originTransNoClear();
  };

  //付款方客户放大镜带出的值
  const payClientCodeChange = (info: ReverseReturnDto, row: any) => {
    info.clientId = row.clientId;
    info.clientHisId = row.clientHisId;
    info.clientCode = row.clientCode;
    info.clientName = row.clientName;
    originTransNoClear();
  };

  const payClientCodeClear = () => {
    state.reverseReturnDto.clientId = null;
    state.reverseReturnDto.clientHisId = null;
    state.reverseReturnDto.clientCode = "";
    state.reverseReturnDto.clientName = "";
    originTransNoClear();
  };

  const originTransactionTypeChange = () => {
    originTransNoClear();
  };

  const originExecuteDateChange = () => {
    originTransNoClear();
  };

  //原交易号放大镜带出来的值
  const originTransNoChange = (row: any) => {
    queryTransParms.transactionType = row.transactionType;
    queryTransParms.transNo = row.transNo;
    queryTransParms.transId = row.transId;
    queryTransParms.officeId = row.officeId;
    queryTransParms.currencyId = row.currencyId;
    queryTransParms.interestStartDate = row.interestStartDate;
    queryTransParms.clientId = row.clientId;
    //调用接口查询交易详情(银行收款)
    queryTransDetail(queryTransParms, false);
  };
  const originTransNoClear = () => {
    state.reverseReturnDto.originTransNo = "";
    state.reverseTransDto.transNo = "";
  };
  //调用接口查询交易详情
  const openDate = ref();

  // 查询原交易
  const queryTransDetail = async (queryTransParms: any, isModify: boolean) => {
    //获取开机日
    httpTool.post(openDateUrl).then((res: any) => {
      openDate.value = res.data.onlineDate;
      httpTool.post(queryTransDetailInfo, queryTransParms).then((res: any) => {
        if (res?.success) {
          //冲账信息
          const id = state.reverseReturnDto.id;
          const transId = state.reverseReturnDto.transId;
          const transNo = state.reverseReturnDto.transNo;
          const version = state.reverseReturnDto.version;
          const interestStartDate = state.reverseReturnDto.interestStartDate;
          const executeDate = state.reverseReturnDto.executeDate;
          Object.assign(state.reverseReturnDto, convert(res.data));
          // 修改页面保存新增时的交易号
          if (isModify) {
            state.reverseReturnDto.id = id;
            state.reverseReturnDto.transId = transId;
            state.reverseReturnDto.transNo = transNo;
            state.reverseReturnDto.version = version;
          }
          state.reverseReturnDto.originTransactionType = res.data.transactionType;
          if (res.data.businessDetail !== null) {
            //冲账交易详情信息
            Object.assign(state.reverseTransDto, convert(JSON.parse(res.data.businessDetail)));
            //客户信息组装
            if (undefined !== state.reverseTransDto.payClientId) {
              state.reverseReturnDto.clientId = state.reverseTransDto.payClientId;
              state.reverseReturnDto.clientName = state.reverseTransDto.payClientName;
              state.reverseReturnDto.clientHisId = state.reverseTransDto.payClientHisId;
              state.reverseReturnDto.clientCode = state.reverseTransDto.payClientCode;
            } else {
              state.reverseReturnDto.clientId = state.reverseTransDto.recClientId;
              state.reverseReturnDto.clientName = state.reverseTransDto.recClientName;
              state.reverseReturnDto.clientHisId = state.reverseTransDto.recClientHisId;
              state.reverseReturnDto.clientCode = state.reverseTransDto.recClientCode;
            }

            showCIPSInfoFlag.value = state.reverseTransDto.payChannel === payChannel.CIPS;
            payWayFlag.value =
              state.reverseTransDto.payChannel === payChannel.BANK_CHANNEL &&
              state.reverseTransDto.payWay === payWay.BANK_CONNECTION;
            billFlag.value =
              state.reverseTransDto.payChannel === payChannel.BANK_CHANNEL &&
              state.reverseTransDto.payWay === payWay.BANK_COUNTERTOPS;

            //判断是否是内部转账/对私付款
            if (
              res.data.transactionType === transType.INTERNALVIREMENT ||
              res.data.transactionType === transType.PRIVATEPYAMENT
            ) {
              //组装交易号
              state.reverseTransDto.transNo = res.data.transNo;
            }
          }

          // 查询余额
          const balanceTransType = [transType.BANKPAY, transType.INTERNALVIREMENT, transType.PRIVATEPYAMENT];
          if (balanceTransType.includes(res.data.transactionType)) {
            accountBalanceParams.accountId = state.reverseTransDto.payAcctId;
            accountBalanceParams.accountNo = state.reverseTransDto.payAcctNo;
            accountBalanceParams.balanceType = balanceType.AVAILABLE;
            if (res.data.transactionType === transType.BANKPAY) {
              accountBalanceParams.instructionType = instructionType.BANK_PAY;
            } else if (res.data.transactionType === transType.INTERNALVIREMENT) {
              accountBalanceParams.instructionType = instructionType.INTERNALVIREMENT;
            } else if (res.data.transactionType === transType.PRIVATEPYAMENT) {
              accountBalanceParams.instructionType = instructionType.PRIVATEPYAMENT;
            }
            //调用接口查询账户可用余额
            queryAccountBalance(accountBalanceParams, state.reverseTransDto);
          }

          state.reverseReturnDto.originExecuteDate = res.data.executeDate;
          state.reverseTransDto.amount = -res.data.amount;
          state.reverseTransDto.bankCheckCode = res.data.bankCheckCode;
          if (res.data.transAbstract === null) {
            res.data.transAbstract = "";
          }
          if (res.data.transAbstract === "") {
            state.reverseTransDto.transAbstract = t("counter.settlement.reverse.rever") + res.data.transNo;
          } else {
            state.reverseTransDto.transAbstract =
              t("counter.settlement.reverse.rever") + res.data.transNo + " - " + res.data.transAbstract;
          }
          state.reverseTransDto.originInterestStartDate = res.data.interestStartDate;
          state.reverseTransDto.fillAccountFlag = yesOrNo.NO;
          state.reverseTransDto.originBusinessCode = res.data.instructionNo;
          // 查询附件信息
          const queryFileParams = {
            businessId: res.data.businessId,
            transactionType: res.data.transactionType
          };
          httpTool.post(queryFileUrl, queryFileParams).then((fileRes: any) => {
            if (fileRes?.success) {
              state.reverseTransDto.fileIdArr = fileRes.data.fileIdArr;
            }
          });
          // 新增页面初始化执行日和起息日
          if (!isModify) {
            state.reverseTransDto.executeDate = openDate.value;
            state.reverseTransDto.interestStartDate = res.data.interestStartDate;
            // 部分功能页面不显示起息日，取开机日
            if (
              res.data.transactionType === transType.FUNDALLOCATION ||
              res.data.transactionType === transType.PAYMENTEXECUTE ||
              res.data.transactionType === transType.EXTERNALPAYMENTEXECUTE
            ) {
              state.reverseTransDto.interestStartDate = openDate.value;
              state.reverseTransDto.expectedDate = openDate.value;
            }
          } else {
            state.reverseTransDto.executeDate = executeDate;
            state.reverseTransDto.interestStartDate = interestStartDate;
            state.reverseTransDto.expectedDate = interestStartDate;
          }
          state.reverseReturnDto.isDisabled = true;
        }
      });
    });
  };

  //修改、查看页面详情信息
  const getReverseDetailInfo = (info: ReverseReturnDto, row: any) => {
    Object.assign(info, convert(row.data));
    queryTransParms.transactionType = row.data.originTransactionType;
    queryTransParms.transNo = row.data.originTransNo;
    queryTransDetail(queryTransParms, true);
  };

  //付款方账户放大镜带出的值
  //是否资金池账户
  const poolAccount = ref(yesOrNo.NO);

  //查询账户可用余额
  const queryAccountBalance = async (accountBalanceParams: any, info: any) => {
    accountBalanceParams.transNo = info.transNo;
    httpTool.post(queryAccountBalanceInfo, accountBalanceParams).then((res: any) => {
      if (res?.success) {
        info.accountBalance = res.data.balance;
        info.accountCanUsedBalance = res.data.availableBalance;
        //资金池账户余额
        info.poolCanUsedBalance = res.data.cmAvailableBalance;
        poolAccount.value = res.data.activeCMAccountFlag;
      }
    });
  };

  //表单校验
  const form = ref();
  const formValidator = async () => {
    let result = true;
    await form.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };

  // 审批流交易类型转换
  const toApprovalTransType = (originTransactionType: string) => {
    let transTypeKey = null;
    switch (originTransactionType) {
      case transType.BANKPAY:
        transTypeKey = "Z020170";
        break;
      case transType.BANKRECEIVE:
        transTypeKey = "Z020171";
        break;
      case transType.INTERNALVIREMENT:
        transTypeKey = "Z020172";
        break;
      case transType.PRIVATEPYAMENT:
        transTypeKey = "Z020173";
        break;
      case transType.FUNDALLOCATION:
        transTypeKey = "Z020174";
        break;
      case transType.PAYMENTEXECUTE:
        transTypeKey = "Z020175";
        break;
      case transType.EXTERNALPAYMENTEXECUTE:
        transTypeKey = "Z020176";
        break;
      case transType.DELEGATEPAY:
        transTypeKey = "Z020177";
        break;
      default:
        break;
    }
    return transTypeKey;
  };

  return {
    state,
    openDate,
    getReverseDetailInfo: (row: any) => {
      getReverseDetailInfo(state.reverseReturnDto, row);
    },
    officeChange,
    currencyChange,
    currencyRef,
    currencyParams,
    payClientCodeChange: (row: any) => {
      payClientCodeChange(state.reverseReturnDto, row);
    },
    originTransNoChange: (row: any) => {
      originTransNoChange(row);
    },
    originTransNoClear,
    formValidator,
    form,
    queryParams,
    payClientCodeClear,
    originTransactionTypeChange,
    originExecuteDateChange,
    payWayFlag,
    billFlag,
    showCIPSInfoFlag,
    toApprovalTransType,
    poolAccount
  };
};
