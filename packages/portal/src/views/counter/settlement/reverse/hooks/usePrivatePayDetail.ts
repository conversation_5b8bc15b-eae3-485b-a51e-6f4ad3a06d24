import type { ReverseTransDto } from "../types";
import { formatDate } from "@/utils/date";
import { useCurrency } from "@/hooks";

export const usePrivatePayDetail = (privatePayDto: ReverseTransDto, props?: Record<any, any>) => {
  privatePayDto = props?.privatePay;
  const params = props?.param;

  const { currencySymbol } = useCurrency(privatePayDto);

  const interestStartDateDisable = (time: Date) => {
    return (
      new Date(formatDate(time)).getTime() > new Date(privatePayDto.executeDate).getTime() ||
      new Date(formatDate(time)).getTime() < new Date(privatePayDto.originInterestStartDate).getTime()
    );
  };

  return {
    privatePayDto,
    params,
    interestStartDateDisable,
    currencySymbol
  };
};
export default usePrivatePayDetail;
