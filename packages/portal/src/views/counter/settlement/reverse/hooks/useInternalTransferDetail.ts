import type { ReverseTransDto } from "../types";
import { formatDate } from "@/utils/date";
import { useCurrency } from "@/hooks";

export const useInternalTransferDetail = (internalTransferDto: ReverseTransDto, props?: Record<any, any>) => {
  internalTransferDto = props?.transfer;

  const { currencySymbol } = useCurrency(internalTransferDto);

  const interestStartDateDisable = (time: Date) => {
    return (
      new Date(formatDate(time)).getTime() > new Date(internalTransferDto.executeDate).getTime() ||
      new Date(formatDate(time)).getTime() < new Date(internalTransferDto.originInterestStartDate).getTime()
    );
  };

  return {
    internalTransferDto,
    interestStartDateDisable,
    currencySymbol
  };
};
export default useInternalTransferDetail;
