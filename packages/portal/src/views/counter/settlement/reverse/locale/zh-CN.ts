export default {
  title: "冲账业务-链接查找",
  addTitle: "冲账业务-新增",
  modifyTitle: "冲账业务-修改",
  detailTitle: "冲账业务-查看",
  approvalTitle: "冲账业务",
  detailTitleInfo: "冲账业务-查看",
  approvalTitleInfo: "冲账业务-审批",

  add: "新增",
  batchCancel: "批量撤销",
  linkquery: "链接查找",
  close: "关闭",
  cancelSuccess: "撤销提交成功",

  cancelReason: "撤销原因",
  officeId: "机构",
  currencyId: "币种",
  status: "单据状态",
  executeDate: "执行日",
  end: "-",
  amount: "金额",
  isMechanism: "是否显示机制交易",
  batchNo: "单据号",
  transNo: "交易号",
  businessCode: "单据号",
  transactionFlow: "交易流水",
  from: "由",
  transCode: "交易编号",
  to: "至",
  rever: "冲：",
  cancel: "撤销",
  recordUnit: "条",
  record: "记录",
  operate: "操作",
  transFailReason: "驳回/失败原因",
  transactionType: "业务类型",
  originTransactionType: "原交易类型",
  originTransNo: "原交易号",
  originExecuteDate: "原交易日期",
  basicinfo: "基础信息",
  payinfo: "付款方信息",
  clientCode: "客户号",
  clientNo: "客户编号",
  clientName: "客户名称",
  payClientCode: "付款方客户编号",
  payClientName: "付款方客户名称",
  payAccountNo: "付款方账户号",
  payAccountName: "付款方账户名称",
  accountBalance: "账户余额",
  accountCanUsedBalance: "账户可用余额",
  poolCanUsedBalance: "资金池可用余额",
  openBank: "开户行",
  bankName: "开户行名称",
  accountNo: "开户行账号",
  bankAccountNo: "银行账号",
  payOpenBankName: "开户行名称",
  receiveinfo: "收款方信息",
  receiveClientCode: "收款方客户编号",
  receiveClientName: "收款方客户名称",
  receiveAccountNo: "收款方账户号",
  receiveAccountName: "收款方账户名称",
  province: "省份",
  city: "市",
  remitInBankName: "汇入行名称",
  bankCnapsNo: "汇入行CNAPS号",
  bankOrgNo: "汇入行机构号",
  bankExchangeNo: "汇入行联行号",
  other: "其他",
  upper: "大写",
  interestStartDate: "起息日",
  bankChequeNo: "支票号",
  abstracts: "摘要",
  isPaymentPrivate: "是否对私",
  paymentPurposeName: "付款用途",
  bankbusiid: "系统定义的银行业务编号",
  bsparamid: "业务参数编号",
  signorbusitype: "签约类型或业务类型",
  descofsignoruseinfo: "签约用途或签约类型描述或交易名称",
  noofbusiormode: "协议编号或模式编号",
  modename: "模式名称",
  moneyUseCode: "资金用途编码",
  moneyUseExplain: "资金用途说明",
  fillAccountFlag: "是否发送银企指令",
  fileinfo: "附件信息",
  file: "附件",
  inputTime: "录入时间",
  diffOfficeFlag: "是否跨机构",
  deleteSuccess: "删除成功",
  deleteFail: "删除失败",
  submitSuccess: "提交成功",
  submitFail: "提交失败",

  officeIdPlaceHolder: "请选择机构信息",
  currencyIdPlaceHolder: "请选择币种信息",
  statusPlaceHolder: "请选择状态",
  payClientCodePlaceHolder: "请选择付款方客户编号",
  payAccountNoPlaceHolder: "请选择付款方账户号",
  openBankPlaceHolder: "请选择开户行",
  paymentPurposePlaceHolder: "请选择付款用途",
  moneyUseCodePlaceHolder: "请选择资金用途编码",
  moneyUseExplainPlaceHolder: "请填写资金用途说明",
  interestStartDatePlaceHolder: "请填写起息日",
  executeDatePlaceHolder: "请填写执行日",
  amountPlaceHolder: "请填写金额",
  originTransactionTypePlaceHolder: "请选择原交易类型",
  originExecuteDatePlaceHolder: "请填写原交易日期",
  originTransNoPlaceHolder: "请选择原交易号",

  transNoMagnifier: "交易号放大镜",
  originTransNoMagnifier: "原交易号放大镜",
  payClientCodeMagnifier: "客户编号放大镜",
  payAccountNoMagnifier: "账户号放大镜",
  openBankMagnifier: "开户行放大镜",
  receiveAccountNoMagnifier: "收款方账户号放大镜",
  abstractsMagnifier: "摘要放大镜",
  largeNoProductMagnifier: "大额非生产关键字放大镜",
  paymentPurposeMagnifier: "付款用途放大镜",
  moneyUseCodeMagnifier: "资金用途编码放大镜",
  submitTip: "条数据，是否全部提交？",
  deleteTip: "条数据，是否全部删除？",
  cancelTip: "条数据，是否全部撤销？",
  choose: "共勾选",
  batchSubmit: "批量提交",
  history: "审批历史",
  privatePaySubset: "业务子类型",
  payBankAccountCode: "付款账号",
  payBankAccountName: "付款账户名称",
  payOpenBankName1: "付款账户开户行",
  recBankAccountCode: "收款账号",
  recBankAccountName: "收款账户名称",
  recOpenBankName: "收款账户开户行",
  businessFlowNumber: "业务流水号",
  expectTradeDate: "期望交易日期",
  payWay: "支付方式",
  amountUse: "资金用途",
  cancelReasonNotEmpty: "撤销原因不能为空",
  cancelSucess: "撤销提交成功",
  operationConfirm: "确定",
  operationCancel: "取消",
  loanBaseInfo: "自营贷款详细信息",
  loanNoticeInfo: "放款单详细信息",
  payInfo: "支付信息",
  recAcctInfo: "收款方信息",
  otherInfo: "其他信息",
  cancelFail: "撤销失败",
  businessType: "业务类型",
  loanClientCode: "客户号",
  loanClientName: "客户名称",
  contractCode: "合同号",
  payFormCode: "放款通知单编号",
  noticeCode: "贷款支付通知单号",
  loanBusinessName: "贷款业务类型",
  payMethod: "支付方式",
  payClientNameMagnifier: "付款单位名称放大镜",
  payAcctNo: "付款账户号",
  payAcctName: "付款账户名称",
  inputUserName: "录入人",
  failReason: "驳回/失败原因",
  bankInstCancelReason: "银企指令撤销原因",
  bankInstPayStatus: "银企支付状态",
  loanClientId: "自营贷款客户编号",
  loanClientCodeMagnifier: "自营贷款客户编号放大镜",
  loanClientNameN: "自营贷款客户名称",
  noticeCodeMagnifier: "贷款支付通知单号放大镜",
  clientCodeN: "客户号",
  clientNameN: "客户名称",
  loanType: "贷款类型",
  loanBusinessType: "贷款业务类型",
  payNotice: "放款通知单号",
  payAmount: "支付金额",
  contractStartDate: "合同起始日期",
  contractEndDate: "合同到期日期",
  loanTerm: "贷款期限",
  month: "月",
  payFormStartTime: "放款单起始日",
  payFormEndTime: "放款单到期日",
  payAcctId: "付款方账户编号",
  payAcctMagnifier: "付款方账户号放大镜",
  accountName: "账户名称",
  accountType: "账户类型",
  recAcctId: "收款方账户号",
  recAcctMagnifier: "收款方账户放大镜",
  amountCheckPlaceHold: "请先输入金额!",
  recAccountTypeCheckPlaceHold: "请先选择收款方账户类型",
  remitInProvince: "汇入地(省)",
  remitInCity: "汇入地(市)",
  remitInBank: "汇入行名称",
  transAbstract: "摘要",
  payBank: "汇出行账户号",
  bankAccountCode: "银行账户号",
  bankAccountName: "银行账户名称",
  payBankAcctName: "汇出行名称",
  sendBankPayFlag: "是否发送银企指令",
  recAccountType: "收款方账户类型",
  recAcctNo: "收款人账户号",
  recAcctName: "收款人名称",
  payAccountInfo: "付款方信息",
  recAccountInfo: "收款方信息",
  CIPSInfo: "CIPS高级信息",
  paymentAmount: "用款金额",
  expectedDate: "期望交易日",
  preBusinessType: "前置业务类型",
  preBusinessNo: "前置业务流水号",
  preBusinessNoMagnifier: "前置业务流水号放大镜",
  bussinessType: "业务类型",
  payAccountCode: "付款方账号",
  payAccountCodeMagnifier: "付款方账号放大镜",
  payBankAccountNo: "付款方账号",
  payBankName: "付款方账户开户行",
  recAccountCode: "收款方账号",
  recAccountCodeMagnifier: "收款方账号放大镜",
  recBankAccountNo: "收款方账号",
  recBankName: "收款方账户开户行",
  payOpenBankType: "付款方开户行银行类别",
  payOpenBankTypeMagnifier: "付款方开户行银行类别放大镜",
  openBankTypeCode: "银行编号",
  openBankTypeName: "银行名称",
  recOpenBankType: "收款方开户行银行类别",
  recOpenBankTypeMagnifier: "收款方开户行银行类别放大镜",
  payChannel: "支付通道",
  businessKindCode: "业务种类代码",
  ordersCode: "订单编号",
  ordersDescription: "订单描述",
  payCipsId: "付款方CIPS ID",
  payLeiCode: "付款方LEI码",
  payEld: "付款方EID",
  payCountry: "付款方国家/地区",
  payCountryMagnifier: "付款方国家/地区放大镜",
  financeCompaniesCipsId: "财务公司CIPS ID",
  payName: "付款方名称",
  payAddress: "付款方地址",
  recCountry: "收款方国家/地区",
  recCountryMagnifier: "收款方国家/地区放大镜",
  country: "国家/地区",
  recCipsId: "收款方CIPS ID",
  recLeiCode: "收款方LEI码",
  recEld: "收款方EID",
  recSwiftCode: "收款行SWIFT Code",
  recOpenBankAcctCode: "收款开户行账号",
  recBankAddress: "收款行银行地址",
  billType: "票据类型",
  billCode: "票据号码",
  moneyUseMethod: "资金用途",
  payDirectInvolvementBic: "付款直接参与者BIC",
  recDirectInvolvementBic: "收款直接参与者BIC",
  payDirectInvolvementBank: "收款直接参与行行号",
  recDirectInvolvementCipsId: "收款直接参与者CIPS ID",
  recDirectInvolvementLei: "收款直接参与者LEI码",
  recIndirectInvolvementBank: "收款间接参与行行号",
  recIndirectInvolvementCipsId: "收款间接参与者CIPS ID",
  recIndirectInvolvementLei: "收款间接参与者LEI码",
  intermediaries1Bank: "中介机构1行号",
  intermediaries1CipsId: "中介机构1CIPS ID",
  intermediaries1Lei: "中介机构1LEI码",
  intermediaries1Name: "中介机构1名称",
  intermediaries2Bank: "中介机构2行号",
  intermediaries2CipsId: "中介机构2CIPS ID",
  intermediaries2Lei: "中介机构2LEI码",
  intermediaries2Name: "中介机构2名称",
  rawCurrencyId: "原始币种",
  exchangeRate: "汇率",
  rawAmount: "原始金额",
  businessPriority: "业务优先级",
  recBankPostscript: "致收款方银行附言",
  fileInfo: "附件信息",
  code: "编码",
  name: "名称",
  headerTip: "对收款/付款直接参与者BIC，如果对应行号有BIC，建议填写BIC；如果没有BIC，建议填写收/付款人开户行名称",
  remark: "备注"
};
