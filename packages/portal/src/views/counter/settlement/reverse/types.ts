export const CODE = "counter.settlement.reverse";

export const KEY = Symbol(CODE);

//冲账列表返回Dto
export type ReverseReturnDto = {
  id?: null | number;
  officeId?: null | number;
  officeCode?: string;
  officeName?: string;
  currencyId?: null | number;
  currencyCode?: string;
  currencyName?: string;
  transactionType?: string; //交易类型
  transId?: null | number; //交易号
  transNo?: string; //交易号
  businessCode?: string; //单据编号
  originGlobalSerialNumber?: string; //原全局流水号
  originTransactionType?: string; //原交易类型
  originTransNo?: string; //原交易号
  originExecuteDate?: string; //原交易日期
  clientId?: null | number; //客户id
  clientHisId?: null | number; //客户历史id
  clientCode?: string; //客户编号
  clientName?: string; //客户名称
  payAccountId?: null | number; //付款方账户id
  payAccountNo?: string; //付款方账户号
  payAccountName?: string; //付款方账户名称
  receiveAccountId?: null | number; //收款方账户id
  receiveAccountNo?: string; //收款方账户编号
  receiveAccountName?: string; //收款方账户名称
  amount?: null | number; //冲账金额
  interestStartDate?: string; //起息日
  executeDate?: string; //执行日
  abstracts?: string; //摘要
  cancelReason?: string; //撤销原因
  checkStatus?: string; //单据状态
  transStatus?: string; //交易状态
  transFailReason?: string; //处理失败原因
  version?: null | number;
  reverseBusinessDetail?: string; //冲账业务详情信息
  isDisabled?: boolean; //选中原交易号其他字段置灰
};

//冲账交易详情DTO
export type ReverseTransDto = {
  officeId?: null | number;
  currencyId?: null | number;
  payClientId?: null | number; //付款方客户id
  payClientHisId?: null | number; //付款方客户历史id
  payClientCode?: string; //付款方客户编号
  payClientName?: string; //付款方客户名称
  payAcctId?: null | number; //付款方账户id
  payAcctNo?: string; //付款方账户号
  payAcctName?: string; //付款方账户名称
  extAccountNo?: string; //收款方账户编号
  extAccountName?: string; //收款方账户名称
  bankCnapsNo?: string; //汇入行CNAPS号
  remitInProvince?: null | string; //省份
  remitInCity?: string; //市
  remitInBank?: string; //汇入行名称
  bankOrgNo?: string; //汇入行机构号
  bankExchangeNo?: string; //汇入行联行号
  privateFlag?: string; //是否补账
  fundsUseCode?: string; //资金用途编码
  fundsUseDesc?: string; //资金用途说明
  payBankId?: null | number; //开户行id
  payBankName?: string; //开户行名称
  payBankAcctId?: null | number; //银行账号id
  payBankAcctNo?: string; //银行账号
  bankAccountId?: null | number; //银行账号id
  bankAccountNo?: string; //银行账号
  bankAccountName?: string; //银行账号名称
  bankCheckCode?: string; //支票号
  isPaymentPrivate?: string; //是否对私
  fillAccountFlag?: string; //是否补账
  paymentPurposeId?: null | number; //付款用途id
  paymentPurposeName?: string; //付款用途名称
  amount?: number; //金额
  originInterestStartDate?: string; //原起息日
  interestStartDate?: string; //起息日
  executeDate?: string; //执行日
  transAbstract?: string; //摘要
  transNo?: string; //交易号
  recClientId?: null | number; //收款方客户id
  recClientHisId?: null | number; //收款方客户历史id
  recClientCode?: string; //收款方客户编号
  recClientName?: string; //收款方客户名称
  recAcctId?: null | number; //收款方账户id
  recAcctNo?: string; //收款方账户号
  recAcctName?: string; //收款方账户名称
  recBankId?: number; //收款方开户行ID
  recBankName?: string; //收款方开户行名称
  recBankAcctNo?: string; //收款方银行账号
  diffOfficeFlag?: string; //是否跨机构
  privatePaySubsetName?: string; //对私业务子类型
  originBusinessCode?: string; // 原单据号
  payBankAccountCode?: string;
  payBankAccountName?: string;
  payOpenBankName?: string;
  recBankAccountCode?: string;
  recBankAccountName?: string;
  recOpenBankName?: string;
  payWay?: string;
  amountUse?: string;
  fileIdArr: string[];
};
