//查询
export const search = "{settlement-counter-query}/api/v1/finance/settlement/counter/reverse/reverse-info-list";
//保存
export const saveUrl = "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-save";
//修改
export const updateUrl = "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-update";
//提交
export const submitUrl = "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-submit";
//批量删除
export const batchDelete = "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-delete";
//批量删除
export const deleteBatch =
  "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-batch-delete";
//批量提交
export const batchSubmit =
  "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-batch-submit";
//批量撤销
export const batchCancelUrl =
  "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-batch-cancel";
//撤销
export const cancelUrl = "{settlement-counter-operate}/api/v1/finance/settlement/counter/reverse/reverse-cancel";
//获取明细信息
export const getDetailUrl = "{settlement-counter-query}/api/v1/finance/settlement/counter/reverse/reverse-info";
//审批同意
export const approvalAgreeUrl = "{workflow}/api/v1/finance/workflow/work-desk/do-approval";
//审批拒绝
export const approvalRefuseUrl = "{workflow}/api/v1/finance/workflow/work-desk/do-approval";
//交易号放大镜
export const transNoMagnifier =
  "{settlement-counter-query}/api/v1/finance/settlement/counter/reverse/transno-magnifier";
//原始交易号放大镜 (交易中心提供)
export const originTransNoMagnifier =
  "{transaction-query}/api/v1/finance/trans-center/transaction/remote/server/reverse-trans-magnifier";
//交易详情 (交易中心提供)
export const queryTransDetailInfo =
  "{transaction-query}/api/v1/finance/trans-center/transaction/remote/server/get-trans-detail";
//币种下拉框
export const getCurrencyInfo = "{system-manage}/api/v1/finance/system/currency/remote/server/list";
//机构下拉框
export const getOfficeInfo = "{system-manage}/api/v1/finance/system/office/remote/server/list";
//付款方客户放大镜
export const getClientInfo = "{clientmanage}/api/v1/finance/client/inner-client/remote/server/list";
//收、付款方账户放大镜
export const getAccountInfo = "{inner-account-query}/api/v1/finance/inner-account/remote/server/list";
//开户行放大镜
export const getOpenBankInfo = "{basic}/api/v1/finance/basic/sett-branch/remote/server/list";
//摘要放大镜
export const getAbstractsInfo = "{basic}/api/v1/finance/basic/remark/remote/server/list";
//TODO大额非生产关键字放大镜
export const getLargeNoProductInfo =
  "{treasury-monitor-mock}/api/v1/finance/treasury-monitor/large-amount-keywords/remote/server/list";
//TODO付款用途放大镜
export const getPaymentPurposeInfo = "{bankportal}/api/v1/finance/bank-plat/payment-purpose/remote/server/list";
//资金用途编号放大镜
export const getMoneyUseInfo = "{basic}/api/v1/finance/basic/money-use/remote/server/list";
//省份下拉框
export const getProvinceInfo = "{system-manage}/api/v1/finance/system/province/remote/server/list";
//付款方账户放大镜
export const getInnerAccountInfo = "{inner-account-query}/api/v1/finance/inner-account/remote/server/list";
//付款方账户放大镜
export const getExtraAccountInfo = "{basic}/api/v1/finance/basic/external-account/remote/server/list";
//TODO获取当前开机日
export const openDateUrl = "{dayend}/api/v1/finance/dayend/remote/server/open-date";
//导出
export const exportUrl = "{settlement-counter-query}/api/v1/finance/settlement/counter/reverse/export-list";
//查询账户余额信息
export const queryAccountBalanceInfo =
  "{transaction-query}/api/v1/finance/trans-center/acct-balance/remote/server/get-acct-balance";
//查询账户余额信息
export const queryFileUrl = "{settlement-counter-query}/api/v1/finance/settlement/counter/reverse/query-file";
