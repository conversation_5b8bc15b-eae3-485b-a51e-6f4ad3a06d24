<template>
  <f-panel :title="t('counter.settlement.reverse.receiveinfo')" id="receiveinfo">
    <!--收款方客户编号、收款方客户名称-->
    <f-form-item :label="t('counter.settlement.reverse.receiveClientCode')" prop="recClientCode">
      <f-input v-model="bankReceiveDto.recClientCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.receiveClientName')" prop="recClientName">
      <f-input v-model="bankReceiveDto.recClientName" disabled />
    </f-form-item>
    <!--收款方账户号、收款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountNo')" prop="recAcctNo">
      <f-input v-model="bankReceiveDto.recAcctNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountName')" prop="recAcctName">
      <f-input v-model="bankReceiveDto.recAcctName" disabled />
    </f-form-item>
    <!--开户行、银行账号-->
    <f-form-item :label="t('counter.settlement.reverse.openBank')" prop="recBankName">
      <f-input v-model="bankReceiveDto.recBankName" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.bankAccountNo')" prop="bankAccountNo">
      <f-input v-model="bankReceiveDto.recBankAcctNo" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.payinfo')" id="payinfo">
    <!--付款方账户号、付款方账户名称、开户行名称-->
    <f-form-item :label="t('counter.settlement.reverse.payAccountNo')" prop="payAcctNo">
      <f-input v-model="bankReceiveDto.extAccountNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payAccountName')" prop="payAcctName">
      <f-input v-model="bankReceiveDto.extAccountName" disabled />
    </f-form-item>

    <f-form-item :label="t('counter.settlement.reverse.payOpenBankName')">
      <f-input v-model="bankReceiveDto.payBankName" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.other')" id="other">
    <!--金额-->
    <f-form-item :label="t('counter.settlement.reverse.amount')" prop="amount">
      <f-amount v-model="bankReceiveDto.amount" tooltip disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="bankReceiveDto.amount" />
    </f-form-item>
    <f-form-item />
    <!--起息日、执行日-->
    <f-form-item :label="t('counter.settlement.reverse.interestStartDate')" prop="interestStartDate">
      <f-date-picker v-model="bankReceiveDto.interestStartDate" type="date" :disabled-date="interestStartDateDisable" />
    </f-form-item>

    <f-form-item :label="t('counter.settlement.reverse.executeDate')" prop="executeDate">
      <f-date-picker v-model="bankReceiveDto.executeDate" type="date" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.transNo')">
      <f-input v-model="bankReceiveDto.transNo" disabled />
    </f-form-item>

    <!--摘要-->
    <f-form-item :label="t('counter.settlement.reverse.abstracts')" prop="abstracts" :employ="2">
      <f-input v-model="bankReceiveDto.transAbstract" disabled />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="bankReceiveDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";

import { useReverse } from "../../hooks/useReverse";
import { detailProps } from "./useBankReceive";
import useBankReceiveDetail from "../../hooks/useBankReceiveDetail";

const { t } = useI18n();

const props = defineProps(detailProps);
const { state } = useReverse();
const { bankReceiveDto, interestStartDateDisable } = useBankReceiveDetail(state.reverseTransDto, props);
</script>
