<template>
  <f-panel :title="t('counter.settlement.reverse.payAccountInfo')" id="form2">
    <!--付款方账号-->
    <f-form-item :label="t('counter.settlement.reverse.payAccountCode')" prop="payAccountCode">
      <f-input v-model="fundAllocationDto.payAccountCode" disabled />
    </f-form-item>
    <!--付款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payBankAccountName')" prop="payAccountName">
      <f-input v-model="fundAllocationDto.payAccountName" disabled />
    </f-form-item>
    <!--付款方账户开户行-->
    <f-form-item :label="t('counter.settlement.reverse.payBankName')" prop="payAccountOpenBankName">
      <f-input v-model="fundAllocationDto.payAccountOpenBankName" disabled />
    </f-form-item>
    <!--账户余额-->
    <f-form-item :label="t('counter.settlement.reverse.accountBalance')" prop="accountBalance">
      <f-amount v-model="fundAllocationDto.accountBalance" :symbol="currencySymbol" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.recAccountInfo')" id="form3">
    <!--收款方账号-->
    <f-form-item :label="t('counter.settlement.reverse.recAccountCode')" prop="recAccountCode">
      <f-input v-model="fundAllocationDto.recAccountCode" disabled />
    </f-form-item>
    <!--收款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.recBankAccountName')" prop="recAccountName">
      <f-input v-model="fundAllocationDto.recAccountName" disabled />
    </f-form-item>
    <!--收款方账户开户行-->
    <f-form-item :label="t('counter.settlement.reverse.recBankName')" prop="recAccountOpenBankName">
      <f-input v-model="fundAllocationDto.recAccountOpenBankName" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.payInfo')" id="form4">
    <!--金额-->
    <f-form-item :label="t('counter.settlement.reverse.amount')" prop="amount">
      <f-amount v-model="fundAllocationDto.amount" tooltip :symbol="currencySymbol" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="fundAllocationDto.amount" :symbol="currencySymbol" />
    </f-form-item>
    <!--执行日-->
    <f-form-item :label="t('counter.settlement.reverse.executeDate')" prop="executeDate">
      <f-date-picker v-model="fundAllocationDto.executeDate" type="date" disabled />
    </f-form-item>
    <!--支付方式-->
    <f-form-item :label="t('counter.settlement.reverse.payWay')" prop="payWay" v-if="!showCIPSInfoFlag">
      <f-scene-view :search="fundAllocationDto.payWay" :data="payWay" params="value" label="label" disabled />
    </f-form-item>
    <!--汇入省-->
    <f-form-item
      :label="t('counter.settlement.reverse.remitInProvince')"
      prop="remitInProvince"
      v-if="payWayFlag && !showCIPSInfoFlag"
    >
      <f-input v-model="fundAllocationDto.remitInProvince" disabled />
    </f-form-item>
    <!--汇入市-->
    <f-form-item
      :label="t('counter.settlement.reverse.remitInCity')"
      prop="remitInCity"
      v-if="payWayFlag && !showCIPSInfoFlag"
    >
      <f-input v-model="fundAllocationDto.remitInCity" disabled />
    </f-form-item>
    <!--cnaps-->
    <f-form-item :label="t('counter.settlement.reverse.bankCnapsNo')" v-if="payWayFlag && !showCIPSInfoFlag">
      <f-input v-model="fundAllocationDto.bankCnapsNo" disabled />
    </f-form-item>
    <!--汇入行机构号-->
    <f-form-item
      :label="t('counter.settlement.reverse.bankOrgNo')"
      prop="bankOrgNo"
      v-if="payWayFlag && !showCIPSInfoFlag"
    >
      <f-input v-model="fundAllocationDto.bankOrgNo" disabled />
    </f-form-item>
    <!--汇入行联行号-->
    <f-form-item
      :label="t('counter.settlement.reverse.bankExchangeNo')"
      prop="bankExchangeNo"
      v-if="payWayFlag && !showCIPSInfoFlag"
    >
      <f-input v-model="fundAllocationDto.bankExchangeNo" disabled />
    </f-form-item>
    <!--票据类型-->
    <f-form-item :label="t('counter.settlement.reverse.billType')" prop="billType" v-if="billFlag">
      <f-scene-view :search="fundAllocationDto.billType" :data="billType" params="value" label="label" />
    </f-form-item>
    <!--票据号码-->
    <f-form-item :label="t('counter.settlement.reverse.billCode')" prop="billCode" v-if="billFlag">
      <f-input v-model="fundAllocationDto.billCode" disabled />
    </f-form-item>
    <!--资金用途-->
    <f-form-item :label="t('counter.settlement.reverse.moneyUseMethod')" prop="moneyUseMethod">
      <f-input v-model="fundAllocationDto.moneyUseMethod" disabled />
    </f-form-item>
    <!--备注-->
    <f-form-item :label="t('counter.settlement.reverse.remark')" prop="transAbstract">
      <f-input v-model="fundAllocationDto.transAbstract" disabled />
    </f-form-item>
    <!--发送银企指令-->
    <f-form-item :label="t('counter.settlement.reverse.fillAccountFlag')">
      <f-switch
        v-model="fundAllocationDto.fillAccountFlag"
        :active-value="yesOrNo.YES"
        :inactive-value="yesOrNo.NO"
        disabled
      />
    </f-form-item>
    <!--       是否对私 -->
    <f-form-item :label="t('counter.settlement.reverse.isPaymentPrivate')">
      <f-scene-view
        :search="fundAllocationDto.paymentPrivateFlag"
        :data="yesOrNo"
        params="value"
        label="label"
        disabled
      />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="fundAllocationDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import type { PropType } from "vue";
import type { ReverseTransDto } from "../../types";
import useFundAllocationDetail from "../../hooks/useFundAllocationDetail";
import { useReverse } from "@/views/counter/settlement/reverse/hooks/useReverse.ts";

const { t } = useI18n();
//是否枚举
const yesOrNo = useConst("counter.YesOrNo");
const billType = useConst("counter.BillType");
const payWay = useConst("counter.PayWay");

const props = defineProps({
  formData: {
    type: Object as PropType<ReverseTransDto>,
    required: true
  },
  payWayFlag: {
    type: Boolean,
    default: false
  },
  showCIPSInfoFlag: {
    type: Boolean,
    default: false
  },
  billFlag: {
    type: Boolean,
    default: false
  }
});

const { state } = useReverse();
const { fundAllocationDto, currencySymbol } = useFundAllocationDetail(state.reverseTransDto, props);
</script>
