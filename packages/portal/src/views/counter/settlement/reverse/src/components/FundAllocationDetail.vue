<template>
  <f-panel :title="t('counter.settlement.reverse.payinfo')" id="payinfo">
    <!--付款方账户号、付款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payBankAccountCode')" prop="payBankAccountCode">
      <f-input v-model="fundAllocationDto.payBankAccountCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payBankAccountName')" prop="payBankAccountName">
      <f-input v-model="fundAllocationDto.payBankAccountName" disabled />
    </f-form-item>
    <!--开户行-->
    <f-form-item :label="t('counter.settlement.reverse.payOpenBankName1')" prop="payOpenBankName">
      <f-input v-model="fundAllocationDto.payOpenBankName" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.receiveinfo')" id="receiveinfo">
    <!--收款方账户号、收款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.recBankAccountCode')" prop="recBankAccountCode">
      <f-input v-model="fundAllocationDto.recBankAccountCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.recBankAccountName')" prop="recBankAccountName">
      <f-input v-model="fundAllocationDto.recBankAccountName" disabled />
    </f-form-item>
    <!--开户行-->
    <f-form-item :label="t('counter.settlement.reverse.recOpenBankName')" prop="recOpenBankName">
      <f-input v-model="fundAllocationDto.recOpenBankName" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.other')" id="other">
    <!--金额、业务流水号-->
    <f-form-item :label="t('counter.settlement.reverse.amount')" prop="amount">
      <f-amount v-model="fundAllocationDto.amount" tooltip disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="fundAllocationDto.amount" />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.businessFlowNumber')" prop="originBusinessCode">
      <f-input v-model="fundAllocationDto.originBusinessCode" disabled />
    </f-form-item>
    <!--期望交易日期、支付方式、资金用途-->
    <f-form-item :label="t('counter.settlement.reverse.expectTradeDate')" prop="executeDate">
      <f-date-picker v-model="fundAllocationDto.executeDate" type="date" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payWay')" prop="privatePaySubsetName">
      <f-scene-view :search="fundAllocationDto.payWay" :data="payWay" params="value" label="label" />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.amountUse')" prop="amountUse">
      <f-input v-model="fundAllocationDto.amountUse" disabled />
    </f-form-item>
    <!--备注-->
    <f-form-item :label="t('counter.settlement.reverse.remark')" prop="transAbstract" :employ="2">
      <f-textarea v-model="fundAllocationDto.transAbstract" :min-rows="2" disabled />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="fundAllocationDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useReverse } from "../../hooks/useReverse";
import type { PropType } from "vue";
import type { ReverseTransDto } from "../../types";
import useFundAllocationDetail from "../../hooks/useFundAllocationDetail";

const { t } = useI18n();
const payWay = useConst("counter.PayWay");

const props = defineProps({
  formData: {
    type: Object as PropType<ReverseTransDto>,
    required: true
  }
});
const { state } = useReverse();
const { fundAllocationDto } = useFundAllocationDetail(state.reverseTransDto, props);
</script>
