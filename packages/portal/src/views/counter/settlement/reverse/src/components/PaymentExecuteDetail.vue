<template>
  <f-panel :title="t('counter.settlement.reverse.payAccountInfo')" id="form2">
    <f-form-item :label="t('counter.settlement.reverse.payAccountCode')" prop="payBankAccountCode">
      <f-input v-model="fundAllocationDto.payBankAccountCode" max="35" disabled />
    </f-form-item>
    <!--付款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payBankAccountName')" prop="payBankAccountName">
      <f-input v-model="fundAllocationDto.payBankAccountName" disabled />
    </f-form-item>
    <!--付款方账户开户行-->
    <f-form-item :label="t('counter.settlement.reverse.payBankName')" prop="payOpenBankName">
      <f-input v-model="fundAllocationDto.payOpenBankName" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.recAccountInfo')" id="form3">
    <!--收款方账号-->
    <f-form-item :label="t('counter.settlement.reverse.recAccountCode')" prop="recBankAccountCode">
      <f-input v-model="fundAllocationDto.recBankAccountCode" disabled />
    </f-form-item>
    <!--收款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.recBankAccountName')" prop="recBankAccountName">
      <f-input v-model="fundAllocationDto.recBankAccountName" disabled />
    </f-form-item>
    <!--收款方账户开户行-->
    <f-form-item :label="t('counter.settlement.reverse.recBankName')" prop="recOpenBankName">
      <f-input v-model="fundAllocationDto.recOpenBankName" disabled />
    </f-form-item>
    <!--汇入省-->
    <f-form-item :label="t('counter.settlement.reverse.remitInProvince')" prop="remitInProvince">
      <f-input v-model="fundAllocationDto.remitInProvince" disabled />
    </f-form-item>
    <!--汇入市-->
    <f-form-item :label="t('counter.settlement.reverse.remitInCity')" prop="remitInCity">
      <f-input v-model="fundAllocationDto.remitInCity" disabled />
    </f-form-item>
    <!--cnaps-->
    <f-form-item :label="t('counter.settlement.reverse.bankCnapsNo')" prop="bankCnapsNo">
      <f-input v-model="fundAllocationDto.bankCnapsNo" disabled />
    </f-form-item>
    <!--汇入行机构号-->
    <f-form-item :label="t('counter.settlement.reverse.bankOrgNo')" prop="bankOrgNo">
      <f-input v-model="fundAllocationDto.bankOrgNo" disabled />
    </f-form-item>
    <!--汇入行联行号-->
    <f-form-item :label="t('counter.settlement.reverse.bankExchangeNo')" prop="bankExchangeNo">
      <f-input v-model="fundAllocationDto.bankExchangeNo" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.payInfo')" id="form4">
    <!--用款金额-->
    <f-form-item :label="t('counter.settlement.reverse.paymentAmount')" prop="amount">
      <f-amount v-model="fundAllocationDto.amount" :symbol="currencySymbol" tooltip disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="fundAllocationDto.amount" />
    </f-form-item>
    <!--执行日-->
    <f-form-item :label="t('counter.settlement.reverse.executeDate')" prop="executeDate">
      <f-date-picker v-model="fundAllocationDto.executeDate" type="date" disabled />
    </f-form-item>
    <!--期望交易日-->
    <f-form-item :label="t('counter.settlement.reverse.expectedDate')" prop="expectedDate">
      <f-date-picker v-model="fundAllocationDto.expectedDate" type="date" disabled />
    </f-form-item>
    <!--支付方式-->
    <f-form-item :label="t('counter.settlement.reverse.payWay')" prop="payWay">
      <f-scene-view :search="fundAllocationDto.payWay" :data="payWay" params="code" label="label" />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.fillAccountFlag')">
      <f-switch
        v-model="fundAllocationDto.fillAccountFlag"
        :active-value="yesOrNo.YES"
        :inactive-value="yesOrNo.NO"
        disabled
      />
    </f-form-item>
    <!--备注-->
    <f-form-item :label="t('counter.settlement.reverse.remark')" prop="transAbstract" :employ="2">
      <f-textarea v-model="fundAllocationDto.transAbstract" disabled />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="fundAllocationDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";

import type { PropType } from "vue";
import type { ReverseTransDto } from "../../types";
import { useReverse } from "../../hooks/useReverse";
import useFundAllocationDetail from "../../hooks/useFundAllocationDetail";

const { t } = useI18n();
//是否枚举
const yesOrNo = useConst("counter.YesOrNo");
const payWay = useConst("counter.PayWay");

const props = defineProps({
  formData: {
    type: Object as PropType<ReverseTransDto>,
    required: true
  }
});

const { state } = useReverse();
const { fundAllocationDto, currencySymbol } = useFundAllocationDetail(state.reverseTransDto, props);
</script>
