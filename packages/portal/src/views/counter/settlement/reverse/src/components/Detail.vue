<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('counter.settlement.reverse.detailTitle')"
    destroy-on-close
    @close="setFalseToVisible"
  >
    <f-multi-form-panel ref="form" :model="state.reverseReturnDto" disabled :column="3">
      <f-panel :title="t('counter.settlement.reverse.basicinfo')" id="basicinfo">
        <f-form-item :label="t('counter.settlement.reverse.businessCode')">
          <f-input v-model="state.reverseReturnDto.businessCode" />
        </f-form-item>
        <!--机构、币种-->
        <f-form-item :label="t('counter.settlement.reverse.officeId')" prop="officeName">
          <f-input v-model="state.reverseReturnDto.officeName" />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.currencyId')" prop="currencyName">
          <f-input v-model="state.reverseReturnDto.currencyName" />
        </f-form-item>
        <!-- 原交易类型 -->
        <f-form-item :label="t('counter.settlement.reverse.originTransactionType')" prop="originTransactionType">
          <f-scene-view
            :search="state.reverseReturnDto.originTransactionType"
            :data="transactionTypeEnum"
            params="value"
            label="label"
          />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.originExecuteDate')" prop="originExecuteDate">
          <f-date-picker v-model="state.reverseReturnDto.originExecuteDate" type="date" />
        </f-form-item>
        <f-form-item />
        <f-form-item :label="t('counter.settlement.reverse.clientNo')" prop="clientCode">
          <f-input v-model="state.reverseReturnDto.clientCode" />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.clientName')" prop="clientName">
          <f-input v-model="state.reverseReturnDto.clientName" />
        </f-form-item>

        <f-form-item :label="t('counter.settlement.reverse.originTransNo')" prop="originTransNo">
          <f-input v-model="state.reverseReturnDto.originTransNo" />
        </f-form-item>
      </f-panel>

      <!--银行付款-->
      <PayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKPAY"
        :businessProperties="businessProperties"
        :pay="state.reverseTransDto"
        :param="queryParams"
        :disabled="true"
      />
      <!--银行收款-->
      <ReceiveDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKRECEIVE"
        :receive="state.reverseTransDto"
        :param="queryParams"
      />
      <!--内部转账-->
      <TransferDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.INTERNALVIREMENT"
        :transfer="state.reverseTransDto"
        :disabled="true"
      />
      <!-- 对私付款 -->
      <PrivatePayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PRIVATEPYAMENT"
        :businessProperties="businessProperties"
        :privatePay="state.reverseTransDto"
        :param="queryParams"
        :disabled="true"
      />
      <!--   资金调拨   -->
      <FundAllocationDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.FUNDALLOCATION"
        :form-data="state.reverseTransDto"
      />
      <!--     对外支付 -->
      <ExternalPaymentExecuteDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.EXTERNALPAYMENTEXECUTE"
        :form-data="state.reverseTransDto"
        :bill-flag="billFlag"
        :pay-way-flag="payWayFlag"
        :showCIPSInfoFlag="showCIPSInfoFlag"
      />
      <!--     贷款支付 -->
      <LoanPayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.DELEGATEPAY"
        :form-data="state.reverseTransDto"
      />
      <!--     同业用款执行 -->
      <PaymentExecuteDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PAYMENTEXECUTE"
        :form-data="state.reverseTransDto"
      />
      <f-panel :title="t('counter.settlement.reverse.history')" id="history">
        <f-wf-history ref="wfHistory" :params="wfHistoryParams" :is-through="false" />
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-button type="info" @click="setFalseToVisible">{{ t("counter.settlement.reverse.close") }}</f-button>
    </template>
  </f-drawer-scene>
</template>

<script setup lang="ts">
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { useReverse } from "../../hooks/useReverse";
import useDetail from "../../hooks/useDetail";
import { detailProps } from "./Detail";
import PayDetail from "./BankPayDetail.vue";
import ReceiveDetail from "./BankReceiveDetail.vue";
import TransferDetail from "./InternalTransferDetail.vue";
import PrivatePayDetail from "./PrivateDetail.vue";
import { onMounted } from "vue";
import useBusinessProperties from "../../../common/hooks/useBusinessProperties";
import FundAllocationDetail from "./FundAllocationDetail.vue";
import LoanPayDetail from "./LoanPayDetail.vue";
import ExternalPaymentExecuteDetail from "./ExternalPaymentExecuteDetail.vue";
import PaymentExecuteDetail from "./PaymentExecuteDetail.vue";

const { t } = useI18n();

//原交易类型常量
const transactionTypeEnum = useConst("common.TransactionType");
//必填校验
defineOptions({
  name: "Detail"
});
const { state, getReverseDetailInfo, queryParams, payWayFlag, billFlag, showCIPSInfoFlag } = useReverse();

// 系统配置项
const { businessProperties, getBusinessProperties } = useBusinessProperties();

const props = defineProps(detailProps);
const { setTrueToVisible, setFalseToVisible, drawerRef, wfHistory, wfHistoryParams } = useDetail(
  getReverseDetailInfo,
  props
);

onMounted(() => {
  getBusinessProperties();
});

defineExpose({ setTrueToVisible, setFalseToVisible });
</script>
