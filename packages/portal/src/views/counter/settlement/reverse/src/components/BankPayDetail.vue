<template>
  <f-panel :title="t('counter.settlement.reverse.payinfo')" id="payinfo">
    <!--付款方客户编号、付款方客户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payClientCode')" prop="payClientCode">
      <f-input v-model="bankPayDto.payClientCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payClientName')" prop="payClientName">
      <f-input v-model="bankPayDto.payClientName" disabled />
    </f-form-item>
    <!--付款方账户号、付款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payAccountNo')" prop="payAcctNo">
      <f-input v-model="bankPayDto.payAcctNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payAccountName')" prop="payAccountName">
      <f-input v-model="bankPayDto.payAcctName" disabled />
    </f-form-item>
    <template v-if="!props.disabled">
      <!--账户余额、账户可用余额、资金池可用余额-->
      <f-form-item :label="t('counter.settlement.reverse.accountBalance')" prop="accountBalance">
        <f-amount
          v-model="bankPayDto.accountBalance"
          tooltip
          value-of-string
          :precision="2"
          :symbol="currencySymbol"
          disabled
        />
      </f-form-item>
      <f-form-item :label="t('counter.settlement.reverse.accountCanUsedBalance')" prop="accountCanUsedBalance">
        <f-amount
          v-model="bankPayDto.accountCanUsedBalance"
          tooltip
          value-of-string
          :precision="2"
          :symbol="currencySymbol"
          disabled
        />
      </f-form-item>
      <f-form-item
        :label="t('counter.settlement.reverse.poolCanUsedBalance')"
        prop="poolCanUsedBalance"
        v-if="poolAccount === yesOrNo.YES"
      >
        <f-amount
          v-model="bankPayDto.poolCanUsedBalance"
          tooltip
          value-of-string
          :precision="2"
          :symbol="currencySymbol"
          disabled
        />
      </f-form-item>
    </template>
    <!--开户行、银行账号-->
    <f-form-item :label="t('counter.settlement.reverse.openBank')" prop="payBankName">
      <f-input v-model="bankPayDto.payBankName" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.bankAccountNo')" prop="payBankAcctNo">
      <f-input v-model="bankPayDto.payBankAcctNo" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.receiveinfo')" id="receiveinfo">
    <!--收款方账户号、收款方账户名称、汇入行CNAPS号-->
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountNo')" prop="extAccountNo">
      <f-input v-model="bankPayDto.extAccountNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountName')" prop="extAccountName">
      <f-input v-model="bankPayDto.extAccountName" disabled />
    </f-form-item>

    <f-form-item :label="t('counter.settlement.reverse.bankCnapsNo')">
      <f-input v-model="bankPayDto.bankCnapsNo" disabled />
    </f-form-item>

    <!--省份、市、汇入行名称-->
    <f-form-item :label="t('counter.settlement.reverse.province')">
      <f-input v-model="bankPayDto.remitInProvince" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.city')">
      <f-input v-model="bankPayDto.remitInCity" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.remitInBankName')">
      <f-input v-model="bankPayDto.remitInBank" disabled />
    </f-form-item>
    <!--汇入行机构号、汇入行联行号-->
    <f-form-item :label="t('counter.settlement.reverse.bankOrgNo')">
      <f-input v-model="bankPayDto.bankOrgNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.bankExchangeNo')">
      <f-input v-model="bankPayDto.bankExchangeNo" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.other')" id="form4">
    <!--金额-->
    <f-form-item :label="t('counter.settlement.reverse.amount')" prop="amount">
      <f-amount v-model="bankPayDto.amount" tooltip disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="bankPayDto.amount" />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.bankChequeNo')" prop="bankChequeNo">
      <f-input v-model="bankPayDto.bankCheckCode" disabled />
    </f-form-item>
    <!--起息日、执行日、支票号-->
    <f-form-item :label="t('counter.settlement.reverse.interestStartDate')" prop="interestStartDate">
      <f-date-picker v-model="bankPayDto.interestStartDate" type="date" :disabled-date="interestStartDateDisable" />
    </f-form-item>

    <f-form-item :label="t('counter.settlement.reverse.executeDate')" prop="executeDate">
      <f-date-picker v-model="bankPayDto.executeDate" type="date" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.transNo')">
      <f-input v-model="bankPayDto.transNo" disabled />
    </f-form-item>

    <!--摘要-->
    <f-form-item :label="t('counter.settlement.reverse.abstracts')" prop="abstracts" :employ="2">
      <f-input v-model="bankPayDto.transAbstract" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.fillAccountFlag')">
      <f-switch
        v-model="bankPayDto.fillAccountFlag"
        :active-value="yesOrNo.YES"
        :inactive-value="yesOrNo.NO"
        disabled
      />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="bankPayDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useReverse } from "../../hooks/useReverse";
import { detailProps } from "./useBankPay";
import useBankPayDetail from "../../hooks/useBankPayDetail";

const { t } = useI18n();
//是否枚举
const yesOrNo = useConst("counter.YesOrNo");

const props = defineProps(detailProps);
const { state, poolAccount } = useReverse();
const { bankPayDto, interestStartDateDisable, currencySymbol } = useBankPayDetail(state.reverseTransDto, props);
</script>
