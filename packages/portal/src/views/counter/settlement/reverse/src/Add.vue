<template>
  <f-blank-scene :title="t('counter.settlement.reverse.addTitle')">
    <f-multi-form-panel ref="form" :model="state.reverseReturnDto" :column="3">
      <f-panel :title="t('counter.settlement.reverse.basicinfo')" id="basicinfo">
        <!--机构、币种-->
        <f-form-item :label="t('counter.settlement.reverse.officeId')" prop="officeId" required>
          <f-select
            v-model="state.reverseReturnDto.officeId"
            :placeholder="t('counter.settlement.reverse.officeIdPlaceHolder')"
            value-key="officeId"
            label="officeName"
            :url="getOfficeInfo"
            method="post"
            @change="officeChange"
            auto-select
          />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.currencyId')" prop="currencyId" required>
          <f-select
            ref="currencyRef"
            v-model="state.reverseReturnDto.currencyId"
            :placeholder="t('counter.settlement.reverse.currencyIdPlaceHolder')"
            value-key="currencyId"
            label="currencyName"
            :url="getCurrencyInfo"
            method="post"
            :extra-data="currencyParams"
            @change="currencyChange"
            auto-select
          />
        </f-form-item>
        <f-form-item />
        <!-- 原交易类型 -->
        <f-form-item
          :label="t('counter.settlement.reverse.originTransactionType')"
          prop="originTransactionType"
          required
        >
          <f-select
            v-model="state.reverseReturnDto.originTransactionType"
            :data="
              transactionTypeEnum.pickConst([
                transactionTypeEnum.PRIVATEPYAMENT,
                transactionTypeEnum.BANKRECEIVE,
                transactionTypeEnum.BANKPAY,
                transactionTypeEnum.INTERNALVIREMENT,
                transactionTypeEnum.FUNDALLOCATION,
                transactionTypeEnum.PAYMENTEXECUTE,
                transactionTypeEnum.EXTERNALPAYMENTEXECUTE,
                transactionTypeEnum.DELEGATEPAY
              ])
            "
            :placeholder="t('counter.settlement.reverse.originTransactionTypePlaceHolder')"
            select-all
            init-if-blank
            @change="originTransactionTypeChange"
          />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.originExecuteDate')" prop="originExecuteDate" required>
          <f-date-picker
            v-model="state.reverseReturnDto.originExecuteDate"
            :placeholder="t('counter.settlement.reverse.originExecuteDatePlaceHolder')"
            type="date"
            @change="originExecuteDateChange"
          />
        </f-form-item>
        <f-form-item />
        <f-form-item
          :label="t('counter.settlement.reverse.clientNo')"
          prop="clientId"
          v-if="
            ![
              transactionTypeEnum.FUNDALLOCATION,
              transactionTypeEnum.PAYMENTEXECUTE,
              transactionTypeEnum.EXTERNALPAYMENTEXECUTE
            ].includes(state.reverseReturnDto.originTransactionType)
          "
        >
          <f-magnifier-single
            :title="t('counter.settlement.reverse.payClientCodeMagnifier')"
            :placeholder="t('counter.settlement.reverse.payClientCodePlaceHolder')"
            :url="getClientInfo"
            method="post"
            v-model="state.reverseReturnDto.clientId"
            row-key="clientId"
            row-label="clientCode"
            auto-init
            :params="{
              officeId: state.reverseReturnDto.officeId
            }"
            @change="payClientCodeChange"
            @clear="payClientCodeClear"
          >
            <f-magnifier-column prop="clientCode" :label="t('counter.settlement.reverse.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('counter.settlement.reverse.clientName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('counter.settlement.reverse.clientName')"
          prop="clientName"
          v-if="
            ![
              transactionTypeEnum.FUNDALLOCATION,
              transactionTypeEnum.PAYMENTEXECUTE,
              transactionTypeEnum.EXTERNALPAYMENTEXECUTE
            ].includes(state.reverseReturnDto.originTransactionType)
          "
        >
          <f-input v-model="state.reverseReturnDto.clientName" disabled />
        </f-form-item>

        <f-form-item :label="t('counter.settlement.reverse.originTransNo')" prop="originTransNo" required>
          <f-magnifier-single
            :title="t('counter.settlement.reverse.originTransNoMagnifier')"
            :placeholder="t('counter.settlement.reverse.originTransNoPlaceHolder')"
            :url="originTransNoMagnifier"
            method="post"
            v-model="state.reverseReturnDto.originTransNo"
            row-key="transNo"
            row-label="transNo"
            auto-init
            :params="{
              officeId: state.reverseReturnDto.officeId,
              currencyId: state.reverseReturnDto.currencyId,
              transactionType: state.reverseReturnDto.originTransactionType,
              clientId: state.reverseReturnDto.clientId,
              interestDate: state.reverseReturnDto.originExecuteDate,
              oldExecuteDate: state.reverseReturnDto.originExecuteDate
            }"
            @change="originTransNoChange"
            @clear="originTransNoClear"
          >
            <f-magnifier-column prop="transNo" :label="t('counter.settlement.reverse.originTransNo')" />
            <f-magnifier-column
              prop="transactionType"
              :label="t('counter.settlement.reverse.originTransactionType')"
              :formatter="formatterTransactionType"
              :filterInput="false"
            />
            <f-magnifier-column prop="executeDate" :label="t('counter.settlement.reverse.originExecuteDate')" />
            <f-magnifier-column formatter="amount" prop="amount" :label="t('counter.settlement.reverse.amount')" />
          </f-magnifier-single>
        </f-form-item>
      </f-panel>

      <!--银行付款-->
      <PayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKPAY"
        :businessProperties="businessProperties"
        :pay="state.reverseTransDto"
        :param="queryParams"
        :disabled="false"
      />
      <!--银行收款-->
      <ReceiveDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKRECEIVE"
        :receive="state.reverseTransDto"
        :param="queryParams"
      />
      <!--内部转账-->
      <TransferDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.INTERNALVIREMENT"
        :transfer="state.reverseTransDto"
        :disabled="false"
      />
      <!-- 对私付款 -->
      <PrivatePayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PRIVATEPYAMENT"
        :businessProperties="businessProperties"
        :privatePay="state.reverseTransDto"
        :param="queryParams"
        :disabled="false"
      />
      <!--   资金调拨   -->
      <FundAllocationDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.FUNDALLOCATION"
        :form-data="state.reverseTransDto"
      />
      <!--     对外支付 -->
      <ExternalPaymentExecuteDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.EXTERNALPAYMENTEXECUTE"
        :form-data="state.reverseTransDto"
        :bill-flag="billFlag"
        :pay-way-flag="payWayFlag"
        :showCIPSInfoFlag="showCIPSInfoFlag"
      />
      <!--     贷款支付 -->
      <LoanPayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.DELEGATEPAY"
        :form-data="state.reverseTransDto"
      />
      <!--     同业用款执行 -->
      <PaymentExecuteDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PAYMENTEXECUTE"
        :form-data="state.reverseTransDto"
      />
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="submitInfo"
        :url="submitUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="callBack"
      />
      <f-button type="info" plain @click.prevent="goBack">{{ t("counter.settlement.reverse.linkquery") }}</f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { useReverse } from "../hooks/useReverse";
import { useAdd } from "../hooks/useAdd";
import { getClientInfo, getCurrencyInfo, getOfficeInfo, originTransNoMagnifier, submitUrl } from "../url";
import PayDetail from "./components/BankPayDetail.vue";
import ReceiveDetail from "./components/BankReceiveDetail.vue";
import TransferDetail from "./components/InternalTransferDetail.vue";
import PrivatePayDetail from "./components/PrivateDetail.vue";
import { onMounted } from "vue";
import useBusinessProperties from "../../common/hooks/useBusinessProperties";
import FundAllocationDetail from "./components/FundAllocationDetail.vue";
import ExternalPaymentExecuteDetail from "./components/ExternalPaymentExecuteDetail.vue";
import LoanPayDetail from "./components/LoanPayDetail.vue";
import PaymentExecuteDetail from "./components/PaymentExecuteDetail.vue";

const { t } = useI18n();
//原交易类型常量
const transactionTypeEnum = useConst("common.TransactionType");

// 系统配置项
const { businessProperties, getBusinessProperties } = useBusinessProperties();

const {
  state,
  openDate,
  officeChange,
  currencyChange,
  currencyRef,
  currencyParams,
  payClientCodeChange,
  originTransNoChange,
  originTransNoClear,
  form,
  formValidator,
  queryParams,
  payClientCodeClear,
  originTransactionTypeChange,
  originExecuteDateChange,
  payWayFlag,
  billFlag,
  showCIPSInfoFlag
} = useReverse();

const { submitInfo, goBack, callBack, formatterTransactionType } = useAdd(
  state.reverseReturnDto,
  state.reverseTransDto,
  openDate
);

onMounted(() => {
  getBusinessProperties();
});
</script>
