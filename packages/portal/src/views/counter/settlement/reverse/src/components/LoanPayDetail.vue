<template>
  <f-panel :title="t('counter.settlement.reverse.payinfo')" id="payinfo">
    <!--付款方客户编号、付款方客户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payClientCode')" prop="payClientCode">
      <f-input v-model="fundAllocationDto.payClientCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payClientName')" prop="payClientName">
      <f-input v-model="fundAllocationDto.payClientName" disabled />
    </f-form-item>
    <!--付款方账户号、付款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payAccountNo')" prop="payAcctNo">
      <f-input v-model="fundAllocationDto.payAcctNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payAccountName')" prop="payAccountName">
      <f-input v-model="fundAllocationDto.payAcctName" disabled />
    </f-form-item>
    <!--开户行、银行账号-->
    <f-form-item :label="t('counter.settlement.reverse.openBank')" prop="payBankName">
      <f-input v-model="fundAllocationDto.payBankName" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.bankAccountNo')" prop="payBankAcctNo">
      <f-input v-model="fundAllocationDto.payBankAcctNo" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.receiveinfo')" id="receiveinfo">
    <!--收款方账户号、收款方账户名称、汇入行CNAPS号-->
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountNo')" prop="recAccountCode">
      <f-input v-model="fundAllocationDto.recAccountCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountName')" prop="recAccountName">
      <f-input v-model="fundAllocationDto.recAccountName" disabled />
    </f-form-item>

    <f-form-item :label="t('counter.settlement.reverse.bankCnapsNo')" prop="bankCnapsNo">
      <f-input v-model="fundAllocationDto.bankCnapsNo" disabled />
    </f-form-item>

    <!--省份、市、汇入行名称-->
    <f-form-item :label="t('counter.settlement.reverse.province')" prop="remitInProvince">
      <f-input v-model="fundAllocationDto.remitInProvince" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.city')" prop="remitInCity">
      <f-input v-model="fundAllocationDto.remitInCity" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.remitInBankName')" prop="remitInBank">
      <f-input v-model="fundAllocationDto.remitInBank" disabled />
    </f-form-item>
    <!--汇入行机构号、汇入行联行号-->
    <f-form-item :label="t('counter.settlement.reverse.bankOrgNo')" prop="bankOrgNo">
      <f-input v-model="fundAllocationDto.bankOrgNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.bankExchangeNo')" prop="bankExchangeNo">
      <f-input v-model="fundAllocationDto.bankExchangeNo" disabled />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.other')" id="form4">
    <!--金额-->
    <f-form-item :label="t('counter.settlement.reverse.amount')" prop="amount">
      <f-amount v-model="fundAllocationDto.amount" tooltip disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="fundAllocationDto.amount" />
    </f-form-item>
    <!--起息日、执行日-->
    <f-form-item :label="t('counter.settlement.reverse.interestStartDate')" prop="interestStartDate">
      <f-date-picker
        v-model="fundAllocationDto.interestStartDate"
        type="date"
        :disabled-date="interestStartDateDisable"
      />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.executeDate')" prop="executeDate">
      <f-date-picker v-model="fundAllocationDto.executeDate" type="date" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.transNo')">
      <f-input v-model="fundAllocationDto.transNo" disabled />
    </f-form-item>
    <!--摘要-->
    <f-form-item :label="t('counter.settlement.reverse.abstracts')" prop="abstracts" :employ="2">
      <f-input v-model="fundAllocationDto.transAbstract" disabled />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="fundAllocationDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import type { PropType } from "vue";
import type { ReverseTransDto } from "../../types";
import { useReverse } from "../../hooks/useReverse";
import useFundAllocationDetail from "../../hooks/useFundAllocationDetail";

const { t } = useI18n();

const props = defineProps({
  formData: {
    type: Object as PropType<ReverseTransDto>,
    required: true
  }
});

const { state } = useReverse();
const { fundAllocationDto, interestStartDateDisable } = useFundAllocationDetail(state.reverseTransDto, props);
</script>
