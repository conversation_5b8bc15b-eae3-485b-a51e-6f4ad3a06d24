<template>
  <f-panel :title="t('counter.settlement.reverse.payinfo')" id="payinfo">
    <!--付款方客户编号、付款方客户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payClientCode')" prop="payClientCode">
      <f-input v-model="internalTransferDto.payClientCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payClientName')" prop="payClientName">
      <f-input v-model="internalTransferDto.payClientName" disabled />
    </f-form-item>
    <!--付款方账户号、付款方账户名称-->
    <f-form-item :label="t('counter.settlement.reverse.payAccountNo')" prop="payAcctId">
      <f-input v-model="internalTransferDto.payAcctNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.payAccountName')" prop="payAcctName">
      <f-input v-model="internalTransferDto.payAcctName" disabled />
    </f-form-item>
    <template v-if="!props.disabled">
      <!--账户余额、账户可用余额、资金池可用余额-->
      <f-form-item :label="t('counter.settlement.reverse.accountBalance')" prop="accountBalance">
        <f-amount
          v-model="internalTransferDto.accountBalance"
          tooltip
          value-of-string
          :precision="2"
          :symbol="currencySymbol"
          disabled
        />
      </f-form-item>
      <f-form-item :label="t('counter.settlement.reverse.accountCanUsedBalance')" prop="accountCanUsedBalance">
        <f-amount
          v-model="internalTransferDto.accountCanUsedBalance"
          tooltip
          value-of-string
          :precision="2"
          :symbol="currencySymbol"
          disabled
        />
      </f-form-item>
      <f-form-item
        :label="t('counter.settlement.reverse.poolCanUsedBalance')"
        prop="poolCanUsedBalance"
        v-if="poolAccount === yesOrNo.YES"
      >
        <f-amount
          v-model="internalTransferDto.poolCanUsedBalance"
          tooltip
          value-of-string
          :precision="2"
          :symbol="currencySymbol"
          disabled
        />
      </f-form-item>
    </template>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.receiveinfo')" id="receiveinfo">
    <!--收款方客户编号、收款方客户名称-->
    <f-form-item :label="t('counter.settlement.reverse.receiveClientCode')" prop="recClientCode">
      <f-input v-model="internalTransferDto.recClientCode" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.receiveClientName')" prop="recClientName">
      <f-input v-model="internalTransferDto.recClientName" disabled />
    </f-form-item>
    <!--收款方账户号、收款方账户名称、汇入行CNAPS号-->
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountNo')" prop="recAcctId">
      <f-input v-model="internalTransferDto.recAcctNo" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.receiveAccountName')" prop="recAcctName">
      <f-input v-model="internalTransferDto.recAcctName" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.diffOfficeFlag')" v-if="false">
      <f-switch
        v-model="internalTransferDto.diffOfficeFlag"
        :active-value="yesOrNo.YES"
        :inactive-value="yesOrNo.NO"
        disabled
      />
    </f-form-item>
  </f-panel>
  <f-panel :title="t('counter.settlement.reverse.other')" id="other">
    <!--金额-->
    <f-form-item :label="t('counter.settlement.reverse.amount')" prop="amount">
      <f-amount v-model="internalTransferDto.amount" tooltip disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.upper')">
      <f-amount-chinese v-model="internalTransferDto.amount" />
    </f-form-item>
    <!--起息日、执行日-->
    <f-form-item :label="t('counter.settlement.reverse.interestStartDate')" prop="interestStartDate">
      <f-date-picker
        v-model="internalTransferDto.interestStartDate"
        type="date"
        :disabled-date="interestStartDateDisable"
      />
    </f-form-item>

    <f-form-item :label="t('counter.settlement.reverse.executeDate')" prop="executeDate">
      <f-date-picker v-model="internalTransferDto.executeDate" type="date" disabled />
    </f-form-item>
    <f-form-item :label="t('counter.settlement.reverse.transNo')">
      <f-input v-model="internalTransferDto.transNo" disabled />
    </f-form-item>
    <!--摘要-->
    <f-form-item :label="t('counter.settlement.reverse.abstracts')" prop="abstracts" :employ="2">
      <f-input v-model="internalTransferDto.transAbstract" disabled />
    </f-form-item>
  </f-panel>
  <!-- 附件 -->
  <f-panel :title="t('counter.settlement.reverse.fileinfo')" id="fileinfo">
    <f-form-item :label="t('counter.settlement.reverse.file')" :employ="3">
      <f-attm-upload
        v-model="internalTransferDto.fileIdArr"
        multiple
        disabled
        :show-upload="false"
        :is-show-batch-delete="false"
        :is-remove-delete-link="true"
      />
    </f-form-item>
  </f-panel>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";

import { useReverse } from "../../hooks/useReverse";
import { detailProps } from "./useInternalTransfer";
import useInternalTransferDetail from "../../hooks/useInternalTransferDetail";

const { t } = useI18n();
//是否枚举
const yesOrNo = useConst("counter.YesOrNo");

const props = defineProps(detailProps);
const { state, poolAccount } = useReverse();
const { internalTransferDto, interestStartDateDisable, currencySymbol } = useInternalTransferDetail(
  state.reverseTransDto,
  props
);
</script>
