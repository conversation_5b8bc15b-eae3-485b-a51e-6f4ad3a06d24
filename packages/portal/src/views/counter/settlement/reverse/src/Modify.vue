<template>
  <f-blank-scene :title="t('counter.settlement.reverse.modifyTitle')">
    <f-multi-form-panel ref="form" :model="state.reverseReturnDto" :column="3">
      <f-panel :title="t('counter.settlement.reverse.basicinfo')" id="basicinfo">
        <f-form-item :label="t('counter.settlement.reverse.businessCode')" prop="businessCode">
          <f-input v-model="state.reverseReturnDto.businessCode" disabled />
        </f-form-item>
        <!--机构、币种-->
        <f-form-item :label="t('counter.settlement.reverse.officeId')" prop="officeName">
          <f-input v-model="state.reverseReturnDto.officeName" disabled />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.currencyId')" prop="currencyName">
          <f-input v-model="state.reverseReturnDto.currencyName" disabled />
        </f-form-item>
        <!-- 原交易类型 -->
        <f-form-item :label="t('counter.settlement.reverse.originTransactionType')" prop="originTransactionType">
          <f-scene-view
            :search="state.reverseReturnDto.originTransactionType"
            :data="transactionTypeEnum"
            params="value"
            label="label"
          />
        </f-form-item>
        <f-form-item :label="t('counter.settlement.reverse.originExecuteDate')" prop="originExecuteDate">
          <f-date-picker v-model="state.reverseReturnDto.originExecuteDate" type="date" disabled />
        </f-form-item>
        <f-form-item />
        <f-form-item
          :label="t('counter.settlement.reverse.clientNo')"
          prop="clientCode"
          v-if="
            ![
              transactionTypeEnum.FUNDALLOCATION,
              transactionTypeEnum.PAYMENTEXECUTE,
              transactionTypeEnum.EXTERNALPAYMENTEXECUTE
            ].includes(state.reverseReturnDto.originTransactionType)
          "
        >
          <f-input v-model="state.reverseReturnDto.clientCode" disabled />
        </f-form-item>
        <f-form-item
          :label="t('counter.settlement.reverse.clientName')"
          prop="clientName"
          v-if="
            ![
              transactionTypeEnum.FUNDALLOCATION,
              transactionTypeEnum.PAYMENTEXECUTE,
              transactionTypeEnum.EXTERNALPAYMENTEXECUTE
            ].includes(state.reverseReturnDto.originTransactionType)
          "
        >
          <f-input v-model="state.reverseReturnDto.clientName" disabled />
        </f-form-item>

        <f-form-item :label="t('counter.settlement.reverse.originTransNo')" prop="originTransNo">
          <f-input v-model="state.reverseReturnDto.originTransNo" disabled />
        </f-form-item>
      </f-panel>

      <!--银行付款-->
      <PayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKPAY"
        :businessProperties="businessProperties"
        :pay="state.reverseTransDto"
        :param="queryParams"
        :disabled="false"
      />
      <!--银行收款-->
      <ReceiveDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.BANKRECEIVE"
        :receive="state.reverseTransDto"
        :param="queryParams"
      />
      <!--内部转账-->
      <TransferDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.INTERNALVIREMENT"
        :transfer="state.reverseTransDto"
        :disabled="false"
      />
      <!-- 对私付款 -->
      <PrivatePayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PRIVATEPYAMENT"
        :businessProperties="businessProperties"
        :privatePay="state.reverseTransDto"
        :param="queryParams"
        :disabled="false"
      />
      <!--   资金调拨   -->
      <FundAllocationDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.FUNDALLOCATION"
        :form-data="state.reverseTransDto"
      />
      <!--     对外支付 -->
      <ExternalPaymentExecuteDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.EXTERNALPAYMENTEXECUTE"
        :form-data="state.reverseTransDto"
        :bill-flag="billFlag"
        :pay-way-flag="payWayFlag"
        :showCIPSInfoFlag="showCIPSInfoFlag"
      />
      <!--     贷款支付 -->
      <LoanPayDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.DELEGATEPAY"
        :form-data="state.reverseTransDto"
      />
      <!--     同业用款执行 -->
      <PaymentExecuteDetail
        v-if="state.reverseReturnDto.originTransactionType === transactionTypeEnum.PAYMENTEXECUTE"
        :form-data="state.reverseTransDto"
        :bill-flag="billFlag"
        :pay-way-flag="payWayFlag"
        :showCIPSInfoFlag="showCIPSInfoFlag"
      />
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="submitInfo"
        :url="submitUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="callBack"
      />
      <f-submit-state
        operate="remove"
        type="danger"
        :gather-params="gatherBatchDeleteParams"
        :url="batchDelete"
        @close="callBack"
      />
      <f-button v-if="!isSubmit" type="info" plain @click.prevent="goBack"
        >{{ t("counter.settlement.reverse.linkquery") }}
      </f-button>
      <f-button v-if="isSubmit" type="info" @click.prevent="goSubmit"
        >{{ t("counter.settlement.reverse.linkquery") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { useReverse } from "../hooks/useReverse";
import { useModify } from "../hooks/useModify";
import { batchDelete, submitUrl } from "../url";
import PayDetail from "./components/BankPayDetail.vue";
import ReceiveDetail from "./components/BankReceiveDetail.vue";
import TransferDetail from "./components/InternalTransferDetail.vue";
import PrivatePayDetail from "./components/PrivateDetail.vue";
import { onMounted, ref } from "vue";
import { usePage } from "../hooks/usePage";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
import useBusinessProperties from "../../common/hooks/useBusinessProperties";
import FundAllocationDetail from "./components/FundAllocationDetail.vue";
import LoanPayDetail from "./components/LoanPayDetail.vue";
import ExternalPaymentExecuteDetail from "./components/ExternalPaymentExecuteDetail.vue";
import PaymentExecuteDetail from "./components/PaymentExecuteDetail.vue";

const { t } = useI18n();
const { pageParams } = usePage();

//原交易类型常量
const transactionTypeEnum = useConst("common.TransactionType");
const router = useRouter();
const {
  state,
  form,
  formValidator,
  getReverseDetailInfo,
  queryParams,
  openDate,
  payWayFlag,
  billFlag,
  showCIPSInfoFlag
} = useReverse();

// 系统配置项
const { businessProperties, getBusinessProperties } = useBusinessProperties();

const { submitInfo, goBack, getDetailInfo, gatherBatchDeleteParams } = useModify(
  state.reverseReturnDto,
  state.reverseTransDto,
  getReverseDetailInfo,
  openDate
);

//定义父组件传参, 参数不唯一，根据⻚面需要参数动态添加
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
//修改⻚初始化方法修改
onMounted(() => {
  if (pageParams) {
    getDetailInfo(pageParams?.id);
  } else {
    getDetailInfo(Number(props?.id));
    isSubmit.value = true;
  }
  getBusinessProperties();
});

//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};

const callBack = (res: any) => {
  if (res.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      goBack();
    }
  }
};
</script>
