export default {
  title: "同业用款指令接收",
  viewStatus: "状态",
  viewOperate: "操作",
  viewRecordUnit: "条",
  viewRecord: "记录",
  instructionType: "指令类型",
  instructionNo: "指令号",
  viewExecuteDate: "执行日期",
  fileInfo: "附件信息",
  file: "附件",
  transAbstract: "备注",
  operateRec: "接收",
  infoRec: "确认接收",
  infoRecSuccess: "接收完成",
  operateSubmit: "提交",
  infoSubmit: "是否提交？",
  infoSubmitSuccess: "提交成功",
  modify: "修改",
  cancel: "撤销",
  cancelTip: "是否撤销？",
  cancelSuccess: "撤销成功",
  operateBack: "退回",
  infoBack: "确认退回",
  infoBackSuccess: "退回完成",
  viewBackReason: "退回原因",
  close: "关闭",
  queryList: "链接查找",
  back: "返回",
  payWayPlaceHolder: "付款方账户或收款方账户不可直连支付",
  payBankAccountCode: "付款方账号",
  payAccountCodeMagnifier: "付款方账号放大镜",
  bankCode: "银行账号",
  bankName: "银行账户名称",
  openBankName: "银行账户开户行",
  recBankAccountCode: "收款方账号",
  amount: "金额",
  office: "机构",
  currency: "币种",
  frontApplyCode: "前置业务单据号",
  payBankAccountName: "付款方账户名称",
  payOpenBankName: "付款方账户开户行",
  recBankAccountName: "收款方账户名称",
  recOpenBankName: "收款方账户开户行",
  expectationExecuteDate: "期望执行日",
  executeDate: "执行日期",
  payWay: "支付方式",
  amountUse: "资金用途",
  instStatus: "指令状态",
  failReason: "驳回/失败原因",
  sendBankInstFlag: "是否发送银企指令",
  bankInstBackReason: "银企指令撤销原因",
  bankInstPayStatus: "银企指令支付状态",
  inputTime: "录入时间",
  operate: "操作",
  history: "审批历史",
  cancelReason: "撤销原因",
  repeal: "作废",
  repealSuccess: "作废成功",
  repealConfirmText: "确认是否作废？",
  instructionStatus: "指令状态",

  payment: {
    modifyTitle: "同业用款执行",
    detailTitle: "同业用款执行-查看",
    approvalTitle: "同业用款执行-审批",
    basicInfo: "基础信息",
    payAcctInfo: "付款方信息",
    recAcctInfo: "收款方信息",
    paymentInfo: "用款信息",

    office: "机构",
    currency: "币种",
    applyCode: "单据号",
    frontBusinessType: "前置业务类型",
    frontApplyCode: "前置业务流水号",
    paymentApplyCode: "用款申请单据号",
    payBankAccountCode: "付款方账号",
    payBankAccountName: "付款方账户名称",
    payOpenBankName: "付款方账户开户行",
    recBankAccountCode: "收款方账号",
    recBankAccountName: "收款方账户名称",
    recOpenBankName: "收款方账户开户行",
    remitInProvince: "汇入地(省)",
    remitInCity: "汇入地(市)",
    bankCnapsNo: "汇入行CNAPS号",
    bankOrgNo: "汇入行机构号",
    bankExchangeNo: "汇入行联行号",
    paymentAmount: "用款金额",
    upper: "金额大写",
    executeDate: "执行日",
    expectationExecuteDate: "期望执行日",
    payWay: "支付方式",
    transAbstract: "备注",
    sendBankInstFlag: "是否发送银企指令"
  },
  allocation: {
    modifyTitle: "资金调拨执行",
    detailTitle: "资金调拨执行-查看",
    approvalTitle: "资金调拨执行-审批",
    basicInfo: "基础信息",
    payAcctInfo: "付款方信息",
    recAcctInfo: "收款方信息",
    payInfo: "调拨信息",
    printExecute: "打印执行单",
    printConfirm: "打印确认单",

    office: "机构",
    currency: "币种",
    applyCode: "单据号",
    confirmBusinessCode: "确认单据号",
    allocationTypeName: "调拨业务类型",
    payBankAccountCode: "付款方账号",
    payBankAccountName: "付款方账户名称",
    payOpenBankName: "付款方账户开户行",
    recBankAccountCode: "收款方账号",
    recBankAccountName: "收款方账户名称",
    recOpenBankName: "收款方账户开户行",
    remitInProvince: "汇入地(省)",
    remitInCity: "汇入地(市)",
    bankCnapsNo: "汇入行CNAPS号",
    bankOrgNo: "汇入行机构号",
    bankExchangeNo: "汇入行联行号",
    allocationAmount: "调拨金额",
    upper: "金额大写",
    executeDate: "执行日期",
    expectationExecuteDate: "预期执行日",
    payWay: "支付方式",
    transAbstract: "备注",
    sendBankInstFlag: "是否发送银行指令"
  }
};
