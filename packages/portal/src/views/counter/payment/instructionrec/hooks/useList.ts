import { computed, nextTick, reactive, ref, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import type { PaymentExecute } from "../types";
import { openDateUrl, recFundAllocationUrl, paymentNullifyUrl, recUrl, nullityFundAllocationUrl } from "../url";
import httpTool from "@/utils/http";
import { useConst } from "@ifs/support";
import { goPage } from "../hooks/usePage";
import { useModelRange } from "@/hooks/conversion";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useList = () => {
  const { t } = useI18n();
  const { departmentid } = storeToRefs(useUserStoreHook());

  const instructionTypeConstantAll = useConst("common.InstructionType");

  //状态
  const instStatusEnum = useConst("counter.instructionStatus");

  //列表显示列配置
  const tableColumns = [
    {
      width: "200px",
      prop: "instructionNo",
      slots: { default: "instructionNo" },
      label: t("counter.settlement.paymentinstructionrec.instructionNo"),
      headerAlign: "center",
      fixed: "left"
    },
    {
      width: "120px",
      prop: "instructionType",
      slots: { default: "instructionType" },
      label: t("counter.settlement.paymentinstructionrec.instructionType"),
      headerAlign: "center",
      formatter: { name: "const", const: "counter.InstructionType" }
    },
    {
      width: "150px",
      prop: "officeName",
      slots: { default: "officeName" },
      label: t("counter.settlement.paymentinstructionrec.office"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "currencyName",
      slots: { default: "currencyName" },
      label: t("counter.settlement.paymentinstructionrec.currency"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "frontApplyCode",
      label: t("counter.settlement.paymentinstructionrec.frontApplyCode"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "payBankAccountCode",
      label: t("counter.settlement.paymentinstructionrec.payBankAccountCode"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "payBankAccountName",
      label: t("counter.settlement.paymentinstructionrec.payBankAccountName"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "payOpenBankName",
      label: t("counter.settlement.paymentinstructionrec.payOpenBankName"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "recBankAccountCode",
      label: t("counter.settlement.paymentinstructionrec.recBankAccountCode"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "recBankAccountName",
      label: t("counter.settlement.paymentinstructionrec.recBankAccountName"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "recOpenBankName",
      label: t("counter.settlement.paymentinstructionrec.recOpenBankName"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "amount",
      slots: { default: "amount" },
      label: t("counter.settlement.paymentinstructionrec.amount"),
      formatter: "amount",
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "expectationExecuteDate",
      label: t("counter.settlement.paymentinstructionrec.executeDate"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "payWay",
      label: t("counter.settlement.paymentinstructionrec.payWay"),
      headerAlign: "center",
      formatter: { name: "const", const: "counter.PaymentPayWay" }
    },
    {
      width: "150px",
      prop: "amountUse",
      label: t("counter.settlement.paymentinstructionrec.amountUse")
    },
    {
      width: "150px",
      prop: "instStatus",
      slots: { default: "instStatus" },
      label: t("counter.settlement.paymentinstructionrec.instStatus"),
      headerAlign: "center",
      formatter: { name: "const", const: "counter.instructionStatus" }
    },
    {
      width: "150px",
      prop: "sendBankInstFlag",
      slots: { default: "sendBankInstFlag" },
      formatter: { name: "const", const: "counter.YesOrNo" },
      label: t("counter.settlement.paymentinstructionrec.sendBankInstFlag"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "failReason",
      slots: { default: "failReason" },
      label: t("counter.settlement.paymentinstructionrec.failReason"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "bankInstBackReason",
      slots: { default: "bankInstBackReason" },
      label: t("counter.settlement.paymentinstructionrec.bankInstBackReason"),
      headerAlign: "center"
    },
    {
      width: "150px",
      prop: "bankInstPayStatus",
      slots: { default: "bankInstPayStatus" },
      label: t("counter.settlement.paymentinstructionrec.bankInstPayStatus"),
      headerAlign: "center",
      formatter: { name: "const", const: "counter.BankInstPayStatus" }
    },
    {
      width: "150px",
      prop: "inputTime",
      slots: { default: "inputTime" },
      label: t("counter.settlement.paymentinstructionrec.inputTime"),
      headerAlign: "center"
    },
    {
      width: "260px",
      prop: "operate",
      label: t("counter.settlement.paymentinstructionrec.operate"),
      slots: { default: "buttons" },
      fixed: "right",
      headerAlign: "center"
    }
  ];

  const allowSort = [
    "instructionNo",
    "instructionType",
    "officeName",
    "currencyName",
    "frontApplyCode",
    "payBankAccountCode",
    "payBankAccountName",
    "payOpenBankName",
    "recBankAccountCode",
    "recBankAccountName",
    "recOpenBankName",
    "amount",
    "expectationExecuteDate",
    "payWay",
    "amountUse",
    "instStatus",
    "sendBankInstFlag",
    "failReason",
    "bankInstBackReason",
    "bankInstPayStatus",
    "inputTime"
  ];

  //表格模板
  const queryTable = shallowRef();

  //列表查询对象
  const queryForm = reactive({
    officeIds: [],
    currencyIds: [],
    instructionTypes: [],
    amount: [],
    payAccountIds: [],
    executeDate: [],
    recBankAccountCode: "",
    instructionNo: "",
    department: departmentid.value,
    instructionStatus: []
  });

  //列表查询
  const handleSuccess = (row: any) => {
    if (row.success) {
      queryTable.value.renderTableData();
      queryTable.value.clearSelection();
    }
  };

  //已选列表数据
  const checkedList = ref<PaymentExecute[]>([]);

  //勾选checkbox
  const handleSelect = (row: PaymentExecute[]) => {
    checkedList.value = row;
  };

  const handClearSelection = () => {
    checkedList.value.splice(0);
  };

  const isChecked = computed(() => checkedList.value.length > 0);

  //获取开机日
  const openDateStr = ref();
  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(openDateUrl).then((res: any) => {
        openDateStr.value = res.data.onlineDate;
        resolve([res.data.onlineDate, res.data.onlineDate]);
        nextTick(() => {
          queryTable.value.renderTableData();
        });
      });
    });
  };

  const getDetail = (row: PaymentExecute) => {
    if (row.instructionType === instructionTypeConstantAll.PAYMENTEXECUTE) {
      goPage("payment", { dto: row });
    } else if (row.instructionType === instructionTypeConstantAll.FUNDALLOCATION) {
      goPage("allocation", { dto: row });
    }
  };

  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };

  const gatherBatchParams = () => {
    return { list: checkedList.value };
  };

  const rowInfo = ref();
  const detail = shallowRef();
  const handleOpen = (row: PaymentExecute) => {
    rowInfo.value = row;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };

  //操作列按钮
  const generalButtonOption = (row: any) => {
    return reactive([
      {
        type: "modify",
        isShow: [instStatusEnum.REFUSED].includes(row.instStatus)
      },
      {
        type: "submit",
        isShow: [instStatusEnum.REFUSED].includes(row.instStatus),
        submitComOpt: {
          url: row.instructionType === instructionTypeConstantAll.FUNDALLOCATION ? recFundAllocationUrl : recUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      },
      {
        type: "repeal",
        isShow: row.instStatus === instStatusEnum.REFUSED,
        originalProps: {
          type: "primary"
        },
        submitComOpt: {
          url:
            row.instructionType === instructionTypeConstantAll.FUNDALLOCATION
              ? nullityFundAllocationUrl
              : paymentNullifyUrl,
          gatherParams: () => {
            return row;
          },
          props: {
            operateName: t("counter.settlement.paymentinstructionrec.repeal"),
            confirmText: t("counter.settlement.paymentinstructionrec.repealConfirmText"),
            resultConfirm: t("counter.settlement.paymentinstructionrec.repealSuccess"),
            resultTitle: t("counter.settlement.paymentinstructionrec.repeal")
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ]);
  };

  const cancelForm = shallowRef();

  const formValidator = async () => {
    let result = true;
    await cancelForm.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };

  const { postParams } = useModelRange(["amount", "executeDate"]);

  const defaultSort = { field: "instructionNo", order: "desc" };

  return {
    queryTable,
    tableColumns,
    queryForm,
    handleSelect,
    handClearSelection,
    handleSuccess,
    allowSort,
    getOpenDate,
    getDetail,
    generalButtonOption,
    isChecked,
    handleSearch,
    gatherBatchParams,
    detail,
    rowInfo,
    handleOpen,
    postParams,
    defaultSort,
    cancelForm,
    formValidator,
    openDateStr
  };
};

export default useList;
