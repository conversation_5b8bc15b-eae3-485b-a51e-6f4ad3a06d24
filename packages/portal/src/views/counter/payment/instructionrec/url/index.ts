//指令退回
export const backUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/payment/payment-execute/instruction-bank";
//指令接收
export const recUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/payment/payment-execute/instruction-rec";
//同业用款指令列表
export const search =
  "{transaction-query}/api/v1/finance/query/trans-center/instruction/payment-instruction/remote/server/get-payment-instruction";
//导出同业用款指令列表
export const exportUrl =
  "{transaction-operate}/api/v1/finance/query/trans-center/instruction/payment-instruction/remote/server/export-list";
//获取指令明细信息
export const getInstructionDetailInfo =
  "{transaction-query}/api/v1/finance/query/trans-center/instruction/payment-instruction/remote/server/get-payment-instruction-detail";
//直连/非直连校验
export const bankLinkCheck =
  "{transaction-query}/api/v1/finance/trans-center/payment-set/instruction-rec-rule/remote/server/payment-rule-check";
//币种下拉框
export const getCurrencyInfo = "{system-manage}/api/v1/finance/system/currency/remote/server/list";
//机构下拉框
export const getOfficeInfo = "{system-manage}/api/v1/finance/system/office/remote/server/list";
//查询银行账户
export const queryBankAccount =
  "{settlement-counter-query}/api/v1/finance/settlement/counter/allocation/common-account-set/remote/server/query-common-account-list";
//获取当前开机日
export const openDateUrl = "{dayend}/api/v1/finance/dayend/remote/server/open-date";
//同业用款执行撤销
export const paymentCancelUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/payment/payment-execute/instruction-cancel";
//同业用款执行作废
export const paymentNullifyUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/payment/payment-execute/instruction-nullify";

//获取用款执行单据信息
export const getExecuteApplyInfo =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/payment/payment-execute/get-by-id";
//审批同意
export const approvalAgreeUrl = "{workflow}/api/v1/finance/workflow/work-desk/do-approval";
//审批拒绝
export const approvalRefuseUrl = "{workflow}/api/v1/finance/workflow/work-desk/do-approval";
//资金调拨指令退回
export const backFundAllocationUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/allocation/fund-allocation-execute/instruction-back";
//资金调拨指令接收
export const recFundAllocationUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/allocation/fund-allocation-execute/instruction-rec";
//资金调拨指令撤销
export const cancelFundAllocationUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/allocation/fund-allocation-execute/instruction-cancel";
//资金调拨指令作废
export const nullityFundAllocationUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/allocation/fund-allocation-execute/instruction-nullity";
//获取资金调拨执行单据信息
export const getFundAllocationExecuteApplyInfo =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/allocation/fund-allocation-execute/get-by-id";
//获取资金调拨指令明细信息
export const getFundAllocationInstructionDetailInfo =
  "{transaction-query}/api/v1/finance/query/trans-center/instruction/payment-instruction/remote/server/get-fund-allocation-instruction-detail";
//失败原因
export const getFailReason =
  "{transaction-query}/api/v1/finance/trans-center/instruction-rec/revoke-back-rule/fail-reason";
