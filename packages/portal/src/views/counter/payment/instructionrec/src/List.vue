<template>
  <f-query-scene :title="t('counter.settlement.paymentinstructionrec.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="counter-settlement-paymentinstructionrec-query-001"
        table-comp-id="counter-settlement-paymentinstructionrec-table-001"
        :table-columns="tableColumns"
        :url="search"
        border
        select-all="true"
        :form-data="queryForm"
        show-header
        auto-reset
        :auto-init="false"
        :post-params="postParams"
        :default-sort="defaultSort"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="handClearSelection"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('counter.settlement.paymentinstructionrec.viewRecord')"
        :count-label-unit="t('counter.settlement.paymentinstructionrec.viewRecordUnit')"
        :summation-biz-label="t('counter.settlement.paymentinstructionrec.viewRecord')"
        :summation-biz-unit="t('counter.settlement.paymentinstructionrec.viewRecordUnit')"
        :allow-sort="allowSort"
        :show-count-value="false"
        :show-summation-sum="false"
        table-type="Record"
        :show-top="true"
        :show-print="false"
        table-show
      >
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item :label="t('counter.settlement.paymentinstructionrec.office')" prop="officeIds">
            <f-select
              v-model="queryForm.officeIds"
              :url="getOfficeInfo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              value-key="officeId"
              label="officeName"
              method="post"
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item :label="t('counter.settlement.paymentinstructionrec.currency')" prop="currencyIds">
            <f-select
              v-model="queryForm.currencyIds"
              :url="getCurrencyInfo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              value-key="currencyId"
              label="currencyName"
              method="post"
            />
          </f-form-item>
          <!--指令类型-->
          <f-form-item :label="t('counter.settlement.paymentinstructionrec.instructionType')" prop="instructionTypes">
            <f-select
              v-model="queryForm.instructionTypes"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :data="instructionTypeConstant"
            />
          </f-form-item>
          <!--指令状态-->
          <f-form-item
            :label="t('counter.settlement.paymentinstructionrec.instructionStatus')"
            prop="instructionStatus"
          >
            <f-select
              v-model="queryForm.instructionStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :data="
                instructionStatusConstantAll.omitConst([
                  instructionStatusConstantAll.BACK,
                  instructionStatusConstantAll.PART_ACCEPT,
                  instructionStatusConstantAll.RECEIVE_PENDING_PROCESSING,
                  instructionStatusConstantAll.CANCEL_FAIL
                ])
              "
            />
          </f-form-item>
          <!-- 执行日 -->
          <f-form-item :label="t('counter.settlement.paymentinstructionrec.viewExecuteDate')" prop="executeDate">
            <f-lax-range-date-picker
              v-model="queryForm.executeDate"
              :startDisabledDate="excuteDateStartControl(queryForm, 'executeDate')"
              :endDisabledDate="excuteDateEndControl(queryForm, 'executeDate')"
              :widgetInit="getOpenDate"
            />
          </f-form-item>
          <!--付款方账号-->
          <f-form-item
            :label="t('counter.settlement.paymentinstructionrec.payBankAccountCode')"
            for="payBankAccountIds"
            prop="payBankAccountIds"
          >
            <f-magnifier-multi
              :title="t('counter.settlement.paymentinstructionrec.payAccountCodeMagnifier')"
              :url="queryBankAccount"
              method="post"
              v-model="queryForm.payBankAccountIds"
              row-key="id"
              row-label="bankAccountNo"
              input-key="bankAccNoOrName"
              auto-init
            >
              <f-magnifier-column
                prop="bankAccountCode"
                :label="t('counter.settlement.paymentinstructionrec.bankCode')"
              />
              <f-magnifier-column
                prop="bankAccountName"
                :label="t('counter.settlement.paymentinstructionrec.bankName')"
              />
              <f-magnifier-column prop="bankName" :label="t('counter.settlement.paymentinstructionrec.openBankName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--收款方账号-->
          <f-form-item
            :label="t('counter.settlement.paymentinstructionrec.recBankAccountCode')"
            prop="recBankAccountCode"
          >
            <f-input v-model="queryForm.recBankAccountCode" />
          </f-form-item>
          <!-- 金额 -->
          <f-form-item :label="t('counter.settlement.paymentinstructionrec.amount')" prop="amount">
            <f-amount-range
              v-model="queryForm.amount"
              max="*************.99"
              symbol=" "
              tooltip
              value-of-string
              :precision="2"
            />
          </f-form-item>
          <!--指令号-->
          <f-form-item :label="t('counter.settlement.paymentinstructionrec.instructionNo')" prop="instructionNo">
            <f-input v-model="queryForm.instructionNo" />
          </f-form-item>
        </template>
        <template #instructionNo="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.instructionNo }}</f-button>
        </template>
        <template #buttons="{ row }">
          <f-button
            @click="getDetail(row)"
            link
            type="primary"
            v-if="
              [instructionStatusConstantAll.SAVE, instructionStatusConstantAll.PART_ACCEPT].includes(row.instStatus)
            "
          >
            {{ t("counter.settlement.paymentinstructionrec.operateRec") }}
          </f-button>
          <OperateButton :options="generalButtonOption(row)" @on-modify="getDetail(row)" @confirm="getDetail(row)">
            <template #suffix>
              <!--撤销-->
              <f-submit-state
                v-if="row.instStatus === instructionStatusConstantAll.COMPLETED && row.executeDate === openDateStr"
                :gather-params="
                  () => {
                    return row;
                  }
                "
                :url="
                  row.instructionType === instructionTypeConstantAll.FUNDALLOCATION
                    ? cancelFundAllocationUrl
                    : paymentCancelUrl
                "
                operate="cancel"
                :is-batch="false"
                :operate-name="t('counter.settlement.paymentinstructionrec.cancel')"
                confirm-text=" "
                link
                :icon="DtgCopy"
                :is-show-result-btn-group="false"
                :result-confirm="t('counter.settlement.paymentinstructionrec.cancelSuccess')"
                :result-title="t('counter.settlement.paymentinstructionrec.cancel')"
                @close="handleSearch"
                :beforeConfirm="formValidator"
              >
                <template #confirmEdit>
                  <f-multi-form-panel ref="cancelForm" :model="row">
                    <f-form-item
                      :label="t('counter.settlement.paymentinstructionrec.cancelReason')"
                      prop="cancelReason"
                      required
                    >
                      <f-select
                        v-model="row.cancelReason"
                        :url="getFailReason"
                        value-key="value"
                        label="value"
                        method="post"
                        filterable
                        allow-create
                        collapse-tags
                        :clearable="true"
                      >
                        <f-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                      </f-select>
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
              <f-process-tracking-dialog
                :params="{
                  recordId: row.businessId, // 单据唯一id
                  systemCode: 'Z02',
                  transType: row.instructionType === instructionTypeConstantAll.FUNDALLOCATION ? 'Z020152' : 'Z020045',
                  agencyId: row.officeId,
                  currencyId: row.currencyId,
                  globalSerialNo: row?.globalSerialNo
                }"
              />
            </template>
          </OperateButton>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :instructionInfo="rowInfo" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import {
  search,
  exportUrl,
  getOfficeInfo,
  getCurrencyInfo,
  queryBankAccount,
  cancelFundAllocationUrl,
  getFailReason,
  paymentCancelUrl
} from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import Detail from "./details/Detail.vue";
import { useCommon } from "@/hooks/useCommon";
import { useEntrace } from "@/hooks/useEntrace.ts";

const { excuteDateStartControl, excuteDateEndControl } = useCommon();

const { t } = useI18n();

const instructionTypeConstantAll = useConst("common.InstructionType");

const instructionTypeConstant = instructionTypeConstantAll.pickConst([
  instructionTypeConstantAll.PAYMENTEXECUTE,
  instructionTypeConstantAll.FUNDALLOCATION
]);
const instructionStatusConstantAll = useConst("counter.instructionStatus");

const {
  queryTable,
  tableColumns,
  queryForm,
  handleSelect,
  handClearSelection,
  allowSort,
  getDetail,
  generalButtonOption,
  detail,
  rowInfo,
  handleOpen,
  getOpenDate,
  postParams,
  defaultSort,
  handleSearch,
  cancelForm,
  formValidator,
  openDateStr
} = useList();

// 赋值首页带过来的参数
const { onEnterPage, dashboardScene } = useEntrace();
onEnterPage(urlParams => {
  if (urlParams.scene === dashboardScene.MY_DASHBOARD_HANDLE) {
    // 我的看板
    if (urlParams.businessType === "FUND_ALLOCATION_EXECUTE") {
      queryForm.instructionStatus = [];
      queryForm.executeDate = [];
      queryForm.instructionTypes = ["FUNDALLOCATION"];
      queryForm.instructionStatus = [urlParams.businessStatus];
    }
  } else if (urlParams.scene === dashboardScene.ACCEPTANCE) {
    // 待受理
    queryForm.instructionStatus = [];
    queryForm.executeDate = [];
    queryForm.instructionStatus = [urlParams.instructionStatus];
    queryForm.officeIds = [Number(urlParams.officeId)];
    queryForm.currencyIds = [Number(urlParams.currencyId)];
  }
});
</script>
