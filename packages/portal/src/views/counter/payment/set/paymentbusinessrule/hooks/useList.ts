import type { PaymentBusinessRule } from "../types";
import { computed, reactive, ref, shallowRef, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { deleteUrl } from "../url";
import { goPage } from "./usePage";
import { useModelRange } from "@/hooks/conversion";
import { useOpenDate } from "@/hooks";
export const useList = () => {
  const { t } = useI18n();

  //列表显示列配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      fixed: "left"
    },
    {
      width: "100px",
      slots: { default: "index" },
      label: t("counter.settlement.paymentbusinessrule.index"),
      fixed: "left"
    },
    {
      width: "150px",
      prop: "businessType",
      label: t("counter.settlement.paymentbusinessrule.businessType"),
      formatter: { name: "const", const: "counter.PaymentBusinessType" }
    },
    {
      width: "150px",
      prop: "accountingFlag",
      label: t("counter.settlement.paymentbusinessrule.accountingFlag"),
      formatter: { name: "const", const: "counter.CreateOrNo" }
    },
    {
      width: "180px",
      prop: "autoBusinessExecuteFlag",
      label: t("counter.settlement.paymentbusinessrule.businessExecuteAutoFlag"),
      formatter: { name: "const", const: "counter.YesOrNo" }
    },
    {
      width: "220px",
      prop: "autoApplyStatus",
      label: t("counter.settlement.paymentbusinessrule.applyStatus"),
      formatter: { name: "const", const: "counter.CheckStatus" }
    },
    {
      width: "150px",
      prop: "modifyUserName",
      label: t("counter.settlement.paymentbusinessrule.modifyUserName")
    },
    {
      width: "150px",
      prop: "modifyTime",
      label: t("counter.settlement.paymentbusinessrule.modifyTime")
    },
    {
      prop: "operate",
      width: "220px",
      label: t("counter.settlement.paymentbusinessrule.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];

  const allowSort = [
    "businessType",
    "accountingFlag",
    "autoApplyStatus",
    "autoBusinessExecuteFlag",
    "modifyUserName",
    "modifyTime"
  ];

  //表格模板
  const queryTable = shallowRef();

  const { openDate } = useOpenDate();

  //列表查询对象
  const queryForm = reactive({
    businessTypes: [],
    date: [null, openDate.value],
    userName: ""
  });

  //列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };

  //已选列表数据
  const checkedList = ref<PaymentBusinessRule[]>([]);

  //控制全选checkbox
  const selectableAll = (row: PaymentBusinessRule[]) => {
    return row;
  };

  //勾选checkbox
  const handleSelect = (row: PaymentBusinessRule[]) => {
    checkedList.value = row;
  };

  //是否选中checkbox，控制批量删除按钮是否可以操作
  const isChecked = computed(() => checkedList.value.length > 0);

  const handClearSelection = () => {
    checkedList.value.splice(0);
  };

  //批量操作的参数
  const gatherBatchParams = () => {
    return { ruleDtoList: checkedList.value };
  };

  //新增按钮跳转
  const add = () => {
    goPage("add");
  };

  //点击修改超链接
  const modify = (row: PaymentBusinessRule) => {
    goPage("modify", { id: row.id });
  };

  //详情抽屉打开
  const rowId = ref<number>();
  const detail = shallowRef();
  const handleOpen = (row: PaymentBusinessRule) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };

  //操作列按钮
  const generalButtonOption = (row: any) => {
    return reactive([
      {
        type: "modify",
        isShow: true
      },
      {
        type: "remove",
        isShow: true,
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return row;
          },
          close: () => {
            handleSearch();
          }
        }
      }
    ]);
  };

  const submitMessage = ref("");
  const beforeDeleteTrigger = () => {
    submitMessage.value = t("counter.settlement.paymentbusinessrule.deleteTip", [checkedList.value.length]);
    return true;
  };
  const deleteResultConfirm = {
    success: t("counter.settlement.paymentbusinessrule.deleteSuccess"),
    fail: t("counter.settlement.paymentbusinessrule.deleteFail")
  };

  const { postParams } = useModelRange(["date"]);

  return {
    queryTable,
    tableColumns,
    selectableAll,
    queryForm,
    handleSelect,
    add,
    isChecked,
    gatherBatchParams,
    handleSearch,
    generalButtonOption,
    modify,
    handleOpen,
    rowId,
    detail,
    openDate,
    handClearSelection,
    submitMessage,
    beforeDeleteTrigger,
    deleteResultConfirm,
    allowSort,
    postParams
  };
};

export default useList;
