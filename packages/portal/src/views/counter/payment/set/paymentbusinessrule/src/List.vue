<template>
  <f-query-scene :title="t('counter.settlement.paymentbusinessrule.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="counter-settlement-paymentbusinessrule-query-001"
        table-comp-id="counter-settlement-paymentbusinessrule-table-001"
        :table-columns="tableColumns"
        :url="search"
        border
        :select-all="selectableAll"
        :form-data="queryForm"
        show-header
        :show-print="false"
        auto-reset
        :auto-init="false"
        :post-params="postParams"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="handClearSelection"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('counter.settlement.paymentbusinessrule.record')"
        :count-label-unit="t('counter.settlement.paymentbusinessrule.recordUnit')"
        :summation-biz-label="t('counter.settlement.paymentbusinessrule.record')"
        :summation-biz-unit="t('counter.settlement.paymentbusinessrule.recordUnit')"
        :allow-sort="allowSort"
        :show-count-value="false"
        :show-summation-sum="false"
        table-type="Record"
        :default-sort="{ field: 'modifyTime', order: 'desc' }"
      >
        <template #operate>
          <f-button type="primary" @click="add">{{ t("counter.settlement.paymentbusinessrule.add") }}</f-button>
          <f-submit-state
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="batchDeleteUrl"
            @close="handleSearch"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteResultConfirm"
          />
        </template>
        <template #query-panel>
          <!-- 前置业务类型 -->
          <f-form-item :label="t('counter.settlement.paymentbusinessrule.businessType')" prop="businessTypes">
            <f-select
              v-model="queryForm.businessTypes"
              :data="paymentBusinessType"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 操作时间 -->
          <f-form-item :label="t('counter.settlement.paymentbusinessrule.modifyTime')" prop="date">
            <f-lax-range-date-picker
              v-model="queryForm.date"
              :startDisabledDate="excuteDateStartControl(queryForm, 'date')"
              :endDisabledDate="excuteDateEndControl(queryForm, 'date')"
            />
          </f-form-item>
          <!-- 操作人 -->
          <f-form-item :label="t('counter.settlement.paymentbusinessrule.modifyUserName')" prop="userName">
            <f-input v-model="queryForm.userName" />
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="modify(row)" />
        </template>
        <template #index="{ $index, row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ $index + 1 }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { search, batchDeleteUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import Detail from "./components/Detail.vue";
import { useConst } from "@ifs/support";
import OperateButton from "@/components/operate-button/operate-button";
import { useCommon } from "@/hooks/useCommon";

const { t } = useI18n();

const { excuteDateStartControl, excuteDateEndControl } = useCommon();

//前置业务类型
const paymentBusinessType = useConst("counter.PaymentBusinessType");

const {
  queryTable,
  tableColumns,
  selectableAll,
  queryForm,
  handleSelect,
  add,
  isChecked,
  gatherBatchParams,
  handleSearch,
  generalButtonOption,
  modify,
  handleOpen,
  rowId,
  detail,
  handClearSelection,
  submitMessage,
  beforeDeleteTrigger,
  deleteResultConfirm,
  allowSort,
  postParams
} = useList();
</script>
