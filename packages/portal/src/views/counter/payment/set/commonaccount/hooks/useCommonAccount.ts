import { computed, reactive, ref, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { deleteUrl, batchSave } from "../url";
import type { CommonAccountSetDto } from "../types";

export const useCommonAccount = () => {
  const { t } = useI18n();

  const tableGrid = shallowRef();

  const queryForm = reactive({
    bankAccountIds: []
  });

  const yesOrNo = useConst("counter.YesOrNo");

  const bankAccountChange = (rows: any) => {
    if (rows !== null && rows !== undefined && rows.length > 0) {
      //组装参数
      const batchSaveParams = { accountSetDtoList: [] };
      rows.forEach(row => {
        const temp: CommonAccountSetDto = {
          officeId: row.officeId,
          officeCode: row.officeCode,
          officeName: row.officeName,
          currencyId: row.currencyId,
          currencyCode: row.currencyCode,
          currencyName: row.currencyName,
          bankAccountId: row.id,
          bankAccountCode: row.bankAccountNo,
          bankAccountName: row.bankAccountName,
          openBankId: row.bankId,
          openBankCode: row.bankCode,
          openBankName: row.bankName,
          bankId: row.bankTypeId,
          bankCode: row.bankTypeCode,
          bankName: row.bankTypeName,
          openDate: row.accountByOpenDate.slice(0, 10),
          accountAttributes: row.accountProperty,
          isDirectLink: row.isDirectlink === 1 ? yesOrNo.YES : yesOrNo.NO,
          remitInProvince: row.remitInProvince,
          remitInCity: row.remitInCity,
          bankCnapsNo: row.bankCnapsNo,
          bankOrgNo: row.bankOrgNo,
          bankExchangeNo: row.bankExchangeNo
        };
        batchSaveParams.accountSetDtoList.push(temp);
      });
      //调用批量保存
      httpTool.post(batchSave, batchSaveParams).then((res: any) => {
        if (res?.success) {
          tableGrid.value.renderTableData();
          queryForm.bankAccountIds = [];
        }
      });
    }
  };

  const handClearSelection = () => {
    checkedList.value.splice(0);
  };

  const handleSearch = () => {
    tableGrid.value.renderTableData();
    tableGrid.value.clearSelection();
  };

  const generalButtonOption = (row: any) => {
    return [
      {
        type: "remove",
        isShow: true,
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return row;
          },
          close: () => {
            handleSearch();
          }
        }
      }
    ];
  };

  const tableColum = [
    {
      prop: "selection",
      type: "selection"
    },
    {
      width: "100px",
      type: "index",
      label: t("counter.settlement.commonaccount.index")
    },
    {
      width: "150px",
      prop: "officeName",
      label: t("counter.settlement.commonaccount.office")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("counter.settlement.commonaccount.currency")
    },
    {
      width: "150px",
      prop: "bankAccountCode",
      label: t("counter.settlement.commonaccount.bankAccountNo")
    },
    {
      width: "180px",
      prop: "bankAccountName",
      label: t("counter.settlement.commonaccount.bankAccountName"),
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "openBankName",
      label: t("counter.settlement.commonaccount.openBankName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "accountAttributes",
      label: t("counter.settlement.commonaccount.accountAttributes"),
      formatter: { name: "const", const: "counter.BankAccountAttributes" }
    },
    {
      width: "150px",
      prop: "openDate",
      label: t("counter.settlement.commonaccount.openDate")
    },
    {
      prop: "operate",
      width: "150px",
      label: t("counter.settlement.commonaccount.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];

  const checkedList = ref<CommonAccountSetDto[]>([]);

  //控制全选checkbox
  const selectableAll = (row: CommonAccountSetDto[]) => {
    return row;
  };

  //勾选checkbox
  const handleSelect = (row: CommonAccountSetDto[]) => {
    checkedList.value = row;
  };

  const submitMessage = ref("");

  const beforeDeleteTrigger = () => {
    submitMessage.value = t("counter.settlement.commonaccount.deleteTip", [checkedList.value.length]);
    return true;
  };

  const deleteResultConfirm = {
    success: t("counter.settlement.commonaccount.deleteSuccess"),
    fail: t("counter.settlement.commonaccount.deleteFail")
  };

  //批量操作的参数
  const gatherBatchParams = () => {
    return { list: checkedList.value };
  };

  const batchOperateColumn = [
    {
      field: "bankAccountCode",
      title: t("counter.settlement.commonaccount.bankAccountNo")
    },
    {
      field: "bankAccountName",
      title: t("counter.settlement.commonaccount.bankAccountName")
    },
    {
      field: "failReason",
      title: t("counter.settlement.commonaccount.failReason")
    }
  ];

  const isChecked = computed(() => checkedList.value.length > 0);

  return {
    queryForm,
    bankAccountChange,
    generalButtonOption,
    tableGrid,
    tableColum,
    checkedList,
    selectableAll,
    handleSelect,
    handleSearch,
    handClearSelection,
    submitMessage,
    beforeDeleteTrigger,
    deleteResultConfirm,
    gatherBatchParams,
    isChecked,
    batchOperateColumn
  };
};
