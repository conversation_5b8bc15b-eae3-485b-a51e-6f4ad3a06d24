import type { PaymentInstRecRuleInfo, PaymentInstRecRuleAccount } from "../types";
import httpTool from "@/utils/http";
import { reactive, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import { queryBankTypeUrl, getDetailUrl } from "../url";
import { useI18n } from "vue-i18n";
import { FMessageBox } from "@dtg/frontend-plus";

export const usePaymentInstructionRule = () => {
  const operateType = useConst("counter.PaymentInstructionRuleAccountType");
  const yesOrNo = useConst("counter.YesOrNo");
  const bankAccountAttributes = useConst("counter.BankAccountAttributes");
  const { t } = useI18n();

  const instructionRule = reactive<PaymentInstRecRuleInfo>({
    ruleDto: {
      autoProcessFlag: null, //是否自动处理
      messageReminderFlag: null //是否消息提醒
    },
    ruleBankTypeList: [],
    ruleAccountList: [],
    unBankTypeChose: []
  });

  const tableData = reactive({
    bankTypeList: [],
    exceptionAccountList: [],
    officeAccountList: []
  });

  const queryTempInfo = reactive({
    exceptionBankAccount: [],
    officeBankAccount: []
  });

  const form = shallowRef();

  /**
   * 例外账户放大镜change
   */
  const exceptionBankAccountChange = (rows: any) => {
    if (rows !== null && rows !== undefined && rows.length > 0) {
      const exceptionAccountList = [];
      rows.forEach(row => {
        const temp = tableData.exceptionAccountList.find(account => account.bankAccountId === row.id);
        if (temp === undefined || temp === null) {
          exceptionAccountList.push(buildAccountInfo(row, operateType.DIRECT_LINK_EXCEPTION));
        }
      });
      if (exceptionAccountList.length > 0) {
        tableData.exceptionAccountList.push(...exceptionAccountList);
      }
    }
    //变更完清除当前已选择数据
    queryTempInfo.exceptionBankAccount = [];
  };

  /**
   * 非直连收款方放大镜change
   */
  const officeBankAccountChange = (rows: any) => {
    if (rows !== null && rows !== undefined && rows.length > 0) {
      const officeAccountList = [];
      rows.forEach(row => {
        const temp = tableData.officeAccountList.find(account => account.bankAccountId === row.id);
        if (temp === undefined || temp === null) {
          officeAccountList.push(buildAccountInfo(row, operateType.NOR_DIRECT_LINK_ACCOUNT));
        }
      });
      if (officeAccountList.length > 0) {
        tableData.officeAccountList.push(...officeAccountList);
      }
    }
    //变更完清除当前已选择数据
    queryTempInfo.officeBankAccount = [];
  };

  const buildAccountInfo = (row: any, type: string) => {
    const bankAccountInfo: PaymentInstRecRuleAccount = {
      operateType: type,
      officeId: row.officeId,
      officeCode: row.officeCode,
      officeName: row.officeName,
      currencyId: row.currencyId,
      currencyCode: row.currencyCode,
      currencyName: row.currencyName,
      bankAccountId: row.id,
      bankAccountCode: row.bankAccountNo,
      bankAccountName: row.bankAccountName,
      openBankId: row.bankId,
      openBankCode: row.bankCode,
      openBankName: row.bankName,
      bankId: row.bankTypeId,
      bankCode: row.bankTypeCode,
      bankName: row.bankTypeName,
      isDirectLink: row.isDirectlink === 1 ? yesOrNo.YES : yesOrNo.NO,
      openDate: row.accountByOpenDate.slice(0, 10),
      accountAttributes: getAccountProperty(row.accountProperty)
    };
    return bankAccountInfo;
  };

  const getAccountProperty = (accountProperty: number) => {
    let accountAttributes = "";
    if (accountProperty === 0) {
      accountAttributes = bankAccountAttributes.NORMAL_ACCOUNT;
    } else if (accountProperty === 1) {
      accountAttributes = bankAccountAttributes.GROUP_GENERAL_ACCOUNT;
    } else if (accountProperty === 2) {
      accountAttributes = bankAccountAttributes.GROUP_SUB_ACCOUNT;
    } else if (accountProperty === 3) {
      accountAttributes = bankAccountAttributes.FUND_POOL_ACCOUNT;
    } else if (accountProperty === 4) {
      accountAttributes = bankAccountAttributes.FUND_POOL_SUB_ACCOUNT;
    } else {
      accountAttributes = bankAccountAttributes.FUND_POOL_SUB_ACCOUNT_GROUP;
    }
    return accountAttributes;
  };

  const choseChangeMethod = (value: string[], direction: "left" | "right", movedKeys: string[]) => {
    if (direction === "right") {
      instructionRule.ruleBankTypeList.length = 0;
      value.forEach(value => {
        const temp = instructionRule.unBankTypeChose.filter(type => type.bankId === value);
        if (temp !== undefined && temp.length > 0) {
          instructionRule.ruleBankTypeList.push(...temp);
        }
      });
    } else {
      const temp = instructionRule.ruleBankTypeList.filter(item => !movedKeys.includes(item.bankId));
      instructionRule.ruleBankTypeList.length = 0;
      instructionRule.ruleBankTypeList.push(...temp);
      if (instructionRule.ruleBankTypeList.length === 0) {
        tableData.exceptionAccountList = [];
      }
    }
    getBankTypeCodes();
  };

  const bankTypeCodes = reactive([]);
  const getBankTypeCodes = () => {
    bankTypeCodes.splice(0);
    instructionRule.ruleBankTypeList.forEach(element => {
      bankTypeCodes.push(element.bankCode);
    });
  };

  /**
   * 删除数据
   */
  const exceptionAccountClick = (scope: any) => {
    tableData.exceptionAccountList.splice(scope.$index, 1);
  };

  /**
   * 删除数据
   */
  const officeAccountClick = (scope: any) => {
    tableData.officeAccountList.splice(scope.$index, 1);
  };

  const saveInfo = () => {
    if (tableData.exceptionAccountList.length > 0) {
      instructionRule.ruleAccountList.push(...tableData.exceptionAccountList);
    }
    if (tableData.officeAccountList.length > 0) {
      instructionRule.ruleAccountList.push(...tableData.officeAccountList);
    }
    return instructionRule;
  };

  const close = () => {
    instructionRule.ruleAccountList = [];
  };

  const tabledTitleChange = () => {
    return {
      backgroundColor: "#f4f8fb",
      marginBottom: "9px",
      borderBottom: "1px solid #dce1e7",
      textAlign: "center",
      fontSize: "12px",
      padding: "5px 0",
      color: "#333333"
    };
  };

  /**
   * 业务初始化信息
   */
  const pageCreate = () => {
    httpTool.post(getDetailUrl).then((res: any) => {
      if (res?.success) {
        instructionRule.ruleDto.autoProcessFlag = res.data.ruleDto?.autoProcessFlag;
        instructionRule.ruleDto.messageReminderFlag = res.data.ruleDto?.messageReminderFlag;
        instructionRule.ruleDto.id = res.data.ruleDto?.id;
        if (res.data.ruleAccountList !== undefined && res.data.ruleAccountList.length > 0) {
          tableData.exceptionAccountList.length = 0;
          tableData.officeAccountList.length = 0;
          res.data.ruleAccountList.forEach(item => {
            if (item.operateType === operateType.DIRECT_LINK_EXCEPTION) {
              tableData.exceptionAccountList.push(item);
            }
            if (item.operateType === operateType.NOR_DIRECT_LINK_ACCOUNT) {
              tableData.officeAccountList.push(item);
            }
          });
        }
        httpTool.post(queryBankTypeUrl, { isTopBank: 1 }).then((res: any) => {
          if (res?.success) {
            res.data.forEach((res: any) => {
              instructionRule.unBankTypeChose.push({
                bankId: res.id,
                bankCode: res.code,
                bankName: res.name
              });
            });
          }
        });
        if (res.data.ruleBankTypeList !== undefined && res.data.ruleBankTypeList.length > 0) {
          instructionRule.ruleBankTypeList.push(...res.data.ruleBankTypeList);
          tableData.bankTypeList.push(...res.data.ruleBankTypeList.map(type => type.bankId));
        }
        getBankTypeCodes();
      }
    });
  };
  const handleBeforeTrigger = () => {
    if (tableData.bankTypeList.length === 0) {
      FMessageBox.report(t("counter.settlement.paymentinstructionrule.payDirectTip"));
      return false;
    }
    return true;
  };
  return {
    instructionRule,
    form,
    queryTempInfo,
    exceptionBankAccountChange,
    officeBankAccountChange,
    saveInfo,
    tableData,
    exceptionAccountClick,
    officeAccountClick,
    tabledTitleChange,
    pageCreate,
    choseChangeMethod,
    close,
    bankTypeCodes,
    handleBeforeTrigger
  };
};
