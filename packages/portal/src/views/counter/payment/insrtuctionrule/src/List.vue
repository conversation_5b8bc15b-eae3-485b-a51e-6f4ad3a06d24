<template>
  <f-blank-scene :title="t('counter.settlement.paymentinstructionrule.title')" :isPoint="false">
    <f-multi-form-panel ref="form" :model="instructionRule" :column="3">
      <f-panel :title="t('counter.settlement.paymentinstructionrule.payDirectLinkTitle')">
        <!--直连银行类别-->
        <f-form-item
          :label="t('counter.settlement.paymentinstructionrule.directLinkBankType')"
          prop="ruleBankTypeList"
          :employ="3"
        >
          <f-transfer
            v-model="tableData.bankTypeList"
            target-order="push"
            :titles="[' ', ' ']"
            :props="{
              key: 'bankId',
              label: 'bankName'
            }"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            :enable-drag="true"
            filterable
            :data="instructionRule.unBankTypeChose"
            @change="choseChangeMethod"
          />
        </f-form-item>
        <!--例外账户-->
        <f-form-item :label="t('counter.settlement.paymentinstructionrule.exceptionBankAccount')">
          <f-magnifier-multi
            :title="t('counter.settlement.paymentinstructionrule.exceptionBankAccountMagnifier')"
            :url="queryBankAccount"
            method="post"
            v-model="queryTempInfo.exceptionBankAccount"
            row-key="id"
            row-label="bankAccountNo"
            input-key="codeOrName"
            selected-key="bankAccountNo"
            selected-label="bankAccountName"
            :params="{
              ownerType: 1,
              bankTypeCodes: bankTypeCodes
            }"
            @confirm="exceptionBankAccountChange"
            :disabled="!instructionRule.ruleBankTypeList.length > 0"
          >
            <f-magnifier-column
              prop="bankAccountNo"
              :label="t('counter.settlement.paymentinstructionrule.accountNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('counter.settlement.paymentinstructionrule.accountName')"
            />
            <f-magnifier-column prop="bankName" :label="t('counter.settlement.paymentinstructionrule.openBank')" />
          </f-magnifier-multi>
        </f-form-item>
        <!--例外账户列表-->
        <f-table
          ref="exceptionAccountGrid"
          :data="tableData.exceptionAccountList"
          :header-cell-style="tabledTitleChange()"
          border
        >
          <f-table-column
            v-for="(item, key) in bankAcctColumns"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :formatter="item.formatter"
            :type="item.type"
            :width="item.width"
            :align="item.align"
          >
            <template #default="scope" v-if="item.prop === 'operate'">
              <f-button link type="danger" @click="exceptionAccountClick(scope)">
                {{ t("counter.settlement.paymentinstructionrule.delete") }}
              </f-button>
            </template>
          </f-table-column>
        </f-table>
      </f-panel>
      <f-panel :title="t('counter.settlement.paymentinstructionrule.recNotDirectLinkTitle')">
        <!--财务公司账户-->
        <f-form-item :label="t('counter.settlement.paymentinstructionrule.officeBankAccount')">
          <f-magnifier-multi
            :title="t('counter.settlement.paymentinstructionrule.officeBankAccountMagnifier')"
            :url="queryBankAccount"
            method="post"
            v-model="queryTempInfo.officeBankAccount"
            row-key="id"
            row-label="bankAccountNo"
            input-key="codeOrName"
            selected-key="bankAccountNo"
            selected-label="bankAccountName"
            :params="{
              ownerType: 1
            }"
            @confirm="officeBankAccountChange"
          >
            <f-magnifier-column
              prop="bankAccountNo"
              :label="t('counter.settlement.paymentinstructionrule.bankAccountNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('counter.settlement.paymentinstructionrule.bankAccountName')"
            />
            <f-magnifier-column prop="bankName" :label="t('counter.settlement.paymentinstructionrule.openBank')" />
          </f-magnifier-multi>
        </f-form-item>
        <!--非直连收款方账户-->
        <f-table
          ref="exceptionAccountGrid"
          :data="tableData.officeAccountList"
          :header-cell-style="tabledTitleChange()"
          border
        >
          <f-table-column
            v-for="(item, key) in bankAcctColumns"
            :key="key"
            :label="item.label"
            :prop="item.prop"
            :formatter="item.formatter"
            :type="item.type"
            :width="item.width"
            :align="item.align"
          >
            <template #default="scope" v-if="item.prop === 'operate'">
              <f-button link type="danger" @click="officeAccountClick(scope)">
                {{ t("counter.settlement.paymentinstructionrule.delete") }}
              </f-button>
            </template>
          </f-table-column>
        </f-table>
      </f-panel>
      <f-panel :title="t('counter.settlement.paymentinstructionrule.messageReminderNotDirectLinkTitle')">
        <!--消息提醒-->
        <f-form-item
          :label="t('counter.settlement.paymentinstructionrule.messageReminderFlag')"
          prop="ruleDto['messageReminderFlag']"
        >
          <f-switch
            v-model="instructionRule.ruleDto['messageReminderFlag']"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
          />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('counter.settlement.paymentinstructionrule.autoProcessDirectLinkTitle')">
        <!--是否自动处理-->
        <f-form-item
          :label="t('counter.settlement.paymentinstructionrule.autoProcess')"
          prop="ruleDto['autoProcessFlag']"
        >
          <f-switch
            v-model="instructionRule.ruleDto['autoProcessFlag']"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
          />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="saveInfo"
        :url="saveUrl"
        operate="save"
        @close="close"
        :before-trigger="handleBeforeTrigger"
      />
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { usePaymentInstructionRule } from "../hooks/usePaymentInstructionRule";
import { saveUrl, queryBankAccount } from "../url";
import { useTableColumn } from "../hooks/useTableColumn";

const { t } = useI18n();

//是否
const yesOrNo = useConst("counter.YesOrNo");

const {
  instructionRule,
  form,
  queryTempInfo,
  exceptionBankAccountChange,
  officeBankAccountChange,
  saveInfo,
  tableData,
  exceptionAccountClick,
  officeAccountClick,
  tabledTitleChange,
  pageCreate,
  choseChangeMethod,
  close,
  bankTypeCodes,
  handleBeforeTrigger
} = usePaymentInstructionRule();

const { bankAcctColumns } = useTableColumn();

onMounted(() => {
  pageCreate();
});
</script>
