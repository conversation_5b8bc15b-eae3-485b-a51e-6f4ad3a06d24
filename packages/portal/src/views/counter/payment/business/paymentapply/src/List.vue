<template>
  <f-query-scene :title="t('counter.settlement.paymentapply.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="counter-loan-paymentapply-query-002"
        table-comp-id="counter-loan-paymentapply-table-002"
        :table-columns="tableColumns"
        :url="search"
        border
        :select-all="selectableAll"
        :form-data="queryForm"
        show-header
        :show-print="false"
        auto-reset
        auto-init
        :post-params="postParams"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="handClearSelection"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('counter.settlement.paymentapply.record')"
        :count-label-unit="t('counter.settlement.paymentapply.recordUnit')"
        :summation-biz-label="t('counter.settlement.paymentapply.record')"
        :summation-biz-unit="t('counter.settlement.paymentapply.recordUnit')"
        :allow-sort="allowSort"
        :show-count-value="false"
        :show-summation-sum="false"
        table-type="Record"
      >
        <template #operate>
          <!--批量提交-->
          <f-submit-state
            :is-batch="true"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="batchSubmitUrl"
            @close="handleSearch"
            :operate-name="t('counter.settlement.paymentapply.batchSubmit')"
            :confirm-text="submitMessage"
            :before-trigger="beforeSubmitTrigger"
            :batch-confirm-map="submitResultConfirm"
          />
        </template>
        <template #query-panel>
          <!--机构-->
          <f-form-item :label="t('counter.settlement.paymentapply.office')" prop="officeIds">
            <f-select
              v-model="queryForm.officeIds"
              :url="getOfficeInfo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              value-key="officeId"
              label="officeName"
              method="post"
            />
          </f-form-item>
          <!--币种-->
          <f-form-item :label="t('counter.settlement.paymentapply.currency')" prop="currencyIds">
            <f-select
              v-model="queryForm.currencyIds"
              :url="getCurrencyInfo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              value-key="currencyId"
              label="currencyName"
              method="post"
            />
          </f-form-item>
          <!--单据号-->
          <f-form-item :label="t('counter.settlement.paymentapply.applyCode')" prop="applyCode">
            <f-input v-model="queryForm.applyCode" />
          </f-form-item>
          <!--单据状态-->
          <f-form-item :label="t('counter.settlement.paymentapply.businessStatus')" prop="checkStatus">
            <f-select
              v-model="queryForm.checkStatus"
              :data="checkStatusEnum.omitConst([checkStatusEnum.COMPLETED])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--前置业务类型-->
          <f-form-item :label="t('counter.settlement.paymentapply.frontBusinessType')" prop="paymentBusinessTypes">
            <f-select
              v-model="queryForm.paymentBusinessTypes"
              :data="paymentBusinessType"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--前置业务单据号-->
          <f-form-item :label="t('counter.settlement.paymentapply.frontApplyCode')" prop="frontApplyCode">
            <f-input v-model="queryForm.frontApplyCode" />
          </f-form-item>
          <!--申请日-->
          <f-form-item :label="t('counter.settlement.paymentapply.applyDate')" prop="applyDate">
            <f-lax-range-date-picker v-model="queryForm.applyDate" />
          </f-form-item>
          <!-- 金额 -->
          <f-form-item :label="t('counter.settlement.paymentapply.paymentAmount')" prop="amount">
            <f-amount-range
              v-model="queryForm.amount"
              max="*************.99"
              min="0"
              tooltip
              value-of-string
              :precision="2"
            />
          </f-form-item>
          <!--付款方账号-->
          <f-form-item :label="t('counter.settlement.paymentapply.payBankAccountCode')" prop="payAccountCodes">
            <f-magnifier-multi
              :title="t('counter.settlement.paymentapply.payAccountCodeMagnifier')"
              :url="queryBankAccount"
              method="post"
              v-model="queryForm.payAccountIds"
              row-key="bankAccountCode"
              row-label="bankAccountCode"
              input-key="accountCodeAndName"
              auto-init
            >
              <f-magnifier-column prop="bankAccountCode" :label="t('counter.settlement.paymentapply.bankCode')" />
              <f-magnifier-column prop="bankAccountName" :label="t('counter.settlement.paymentapply.bankName')" />
              <f-magnifier-column prop="openBankName" :label="t('counter.settlement.paymentapply.openBankName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--付款方开户行-->
          <f-form-item :label="t('counter.settlement.paymentapply.payOpenBankName')" prop="payBankIds">
            <f-magnifier-multi
              :title="t('counter.settlement.paymentapply.openBankMagnifier')"
              :url="queryBankType"
              method="post"
              v-model="queryForm.payBankIds"
              row-key="code"
              row-label="name"
              input-key="codeAndName"
            >
              <f-magnifier-column prop="code" :label="t('counter.settlement.paymentapply.openBankTypeCode')" />
              <f-magnifier-column prop="name" :label="t('counter.settlement.paymentapply.openBankTypeName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--收款方账户-->
          <f-form-item :label="t('counter.settlement.paymentapply.recBankAccountCode')" prop="recAccountCode<">
            <f-input v-model="queryForm.recAccountCode" />
          </f-form-item>
          <!--收款方开户行银行类别-->
          <f-form-item :label="t('counter.settlement.paymentapply.recOpenBankName')" prop="recBankIds">
            <f-magnifier-multi
              :title="t('counter.settlement.paymentapply.openBankMagnifier')"
              :url="queryBankType"
              method="post"
              v-model="queryForm.recBankIds"
              row-key="code"
              row-label="name"
              input-key="codeAndName"
            >
              <f-magnifier-column prop="code" :label="t('counter.settlement.paymentapply.openBankTypeCode')" />
              <f-magnifier-column prop="name" :label="t('counter.settlement.paymentapply.openBankTypeName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--业务状态-->
          <f-form-item :label="t('counter.settlement.paymentapply.paymentBusinessStatus')" prop="paymentBusinessStatus">
            <f-select
              v-model="queryForm.paymentBusinessStatus"
              :data="paymentBusinessStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--是否需要调拨-->
          <f-form-item :label="t('counter.settlement.paymentapply.allocationFlag')" prop="allocationFlag">
            <f-select
              v-model="queryForm.allocationFlag"
              :data="yesOrNo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
        </template>
        <template #businessCode="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.applyCode }}</f-button>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="modify(row)">
            <!--撤销-->
            <template #suffix>
              <f-submit-state
                v-if="
                  row.checkStatus === checkStatusEnum.APPROVED &&
                  (row.paymentStatus === paymentBusinessStatus.PENDING_EXECUTION ||
                    row.paymentStatus === paymentBusinessStatus.EXECUTION_CANCEL)
                "
                :gather-params="
                  () => {
                    return row;
                  }
                "
                :url="cancelUrl"
                operate="cancel"
                :is-batch="false"
                :operate-name="t('counter.settlement.paymentapply.cancel')"
                confirm-text=" "
                link
                :icon="DtgCopy"
                :is-show-result-btn-group="false"
                :result-confirm="t('counter.settlement.paymentapply.cancelSuccess')"
                :result-title="t('counter.settlement.paymentapply.cancel')"
                @close="handleSearch"
                :beforeConfirm="formValidator"
              >
                <template #confirmEdit>
                  <f-multi-form-panel ref="cancelForm" :model="row">
                    <f-form-item
                      :label="t('counter.settlement.paymentapply.cancelReason')"
                      prop="cancelReason"
                      required
                    >
                      <f-input v-model="row.cancelReason" />
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
              <f-process-tracking-dialog
                :params="{
                  recordId: row.id, // 单据唯一id
                  systemCode: 'Z02',
                  transType: 'Z020150',
                  agencyId: row.officeId,
                  currencyId: row.currencyId,
                  globalSerialNo: row?.globalSerialNo
                }"
              />
            </template>
          </OperateButton>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import {
  search,
  getOfficeInfo,
  getCurrencyInfo,
  exportUrl,
  queryBankAccount,
  queryBankType,
  batchSubmitUrl,
  cancelUrl
} from "../url";
import { useI18n } from "vue-i18n";
import Detail from "./components/Detail.vue";
import { useConst } from "@ifs/support";
import OperateButton from "@/components/operate-button/operate-button";
import { DtgCopy } from "@dtg/frontend-plus-icons";
import { useEntrace } from "@/hooks/useEntrace.ts";

const { t } = useI18n();

//状态
const checkStatusEnum = useConst("counter.CheckStatus");
//前置业务类型
const paymentBusinessType = useConst("counter.PaymentBusinessType");
//是否
const yesOrNo = useConst("counter.YesOrNo");
//业务状态
const paymentBusinessStatus = useConst("counter.PaymentBusinessStatus");

const {
  queryTable,
  tableColumns,
  selectableAll,
  queryForm,
  handleSelect,
  isChecked,
  gatherBatchParams,
  handleSearch,
  generalButtonOption,
  modify,
  handleOpen,
  rowId,
  detail,
  handClearSelection,
  submitMessage,
  allowSort,
  postParams,
  beforeSubmitTrigger,
  submitResultConfirm,
  cancelForm,
  formValidator
} = useList();

const { onEnterPage } = useEntrace();

onEnterPage(urlParams => {
  queryForm.checkStatus = [urlParams.checkStatus];
  queryForm.paymentBusinessStatus = [urlParams.paymentBusinessStatus];
});
</script>
