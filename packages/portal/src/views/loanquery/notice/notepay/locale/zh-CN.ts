export default {
  title: "贷款放款通知单查询",
  close: "关闭",
  record: "记录",
  recordUnit: "条",
  month: "月",

  payAccountNo: "账户编号",
  payAccountName: "账户名称",

  queryLabel: {
    loanApplyCodeList: "贷款申请书编号",
    contractCodeList: "合同编号",
    loanTypeList: "贷款业务种类",
    creditLoanBizType: "贷款业务类型",
    productIdList: "贷款产品名称",
    businessCodeList: "放款通知单号",
    loanTerm: "贷款期限(月)",
    loanClientIdList: "借款客户名称",
    consignClientIdList: "委托客户名称",
    contractStatusList: "合同状态",
    payAmount: "放款金额",
    payDate: "放款日期",
    inputTime: "录入日期",
    businessStatusList: "单据状态",
    recPrincipalAcctNoList: "放款账户"
  },
  columnName: {
    invoiceInfo: "单据信息",
    contractInfo: "合同信息",
    processingInstitution: "机构信息", //按bug88889要求 调整字段显示 办理机构 为 机构信息
    loanProduct: "贷款产品",
    contractDuration: "合同期限",
    borrower: "借款单位", //按bug88889要求 调整字段显示 借款客户 为 借款单位
    trustee: "委托单位", //按bug88889要求 调整字段显示 委托客户 为 委托单位
    contractAmount: "合同金额",
    disbursementInvoiceAmount: "放款单金额",
    executionRate: "执行利率",
    disbursementDuration: "放款单期限"
  },
  applyNo: "单据号",
  inputUserName: "录入人",
  inputDate: "录入时间",
  businessStatus: "单据状态",
  contractCode: "合同编号",
  businessCode: "单据号",
  officeName: "机构",
  currencyName: "币种",
  loanType: "贷款种类",
  creditLoanBizType: "贷款类型",
  productName: "贷款产品",
  productName1: "产品名称",
  productCode: "产品编号",
  loanClientCode: "借款单位编号",
  loanClientName: "借款单位名称",
  consignClientCode: "委托单位编号",
  consignClientName: "委托单位名称",
  contractAmount: "合同金额",
  contractStatus: "合同状态",
  contractBalance: "合同余额",
  payAmount: "放款单金额",
  balance: "放款单余额",
  contractRate: "合同执行利率(%)",
  executeRate: "放款单执行利率(%)",
  payDate: "放款日期",
  loanApplyCode: "贷款申请书编号",
  loanTerm: "贷款期限",
  loanStartDate: "开始日",
  loanEndDate: "到期日",
  name: "名称",
  code: "编号",
  indefinitely: "暂无期限",

  magnifyingGlass: {
    loanApplyCodeList: "贷款申请书编号",
    clientName: "客户名称",
    loanTypeList: "贷款业务种类",
    applicationAmount: "申请金额",

    contractCode: "合同编号",
    loanClientName: "借款单位名称",
    contractAmount: "合同金额",
    loanStartDate: "合同开始日期",
    loanEndDate: "合同结束日期",

    productCode: "产品编号",
    productName: "产品名称",

    //contractCode: "合同编号",
    //loanClientName:"借款单位名称",
    businessCodeList: "放款单号",
    payDate: "放款日期",
    payAmount: "放款金额",

    loanClientCode: "借款单位编号",
    //loanClientName: "借款单位名称",

    consignClientCode: "委托单位编号",
    consignClientName: "委托单位名称"
  }
};
