import { useI18n } from "vue-i18n";
import { reactive, shallowRef } from "vue";
import { useModelRange } from "@/hooks";
import { formatDate } from "@/utils/date";

export const useList = () => {
  const { t } = useI18n();

  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      width: "30px"
    },
    {
      prop: "businessCode",
      label: t("loanquery.notice.notepay.businessCode"),
      groupLabel: t("loanquery.notice.notepay.columnName.invoiceInfo"),
      groupWidth: "300px",
      width: "150px",
      slots: { default: "businessCode" },
      fixed: "left"
    },
    // 按bug88893要求，将合同编号信息设置为固定列
    {
      prop: "contractCode",
      label: t("loanquery.notice.notepay.contractCode"),
      groupLabel: t("loanquery.notice.notepay.columnName.contractInfo"),
      groupWidth: "300px",
      width: "150px",
      fixed: "left"
    },
    {
      prop: "officeName",
      label: t("loanquery.notice.notepay.officeName"),
      groupLabel: t("loanquery.notice.notepay.columnName.processingInstitution"),
      visible: false,
      groupVisible: true,
      compactVisible: true,
      groupWidth: "200px"
    },
    {
      prop: "currencyName",
      label: t("loanquery.notice.notepay.currencyName"),
      compact: "officeName",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    {
      prop: "loanApplyCode",
      label: t("loanquery.notice.notepay.loanApplyCode"),
      compact: "contractCode",
      width: "150px"
    },
    {
      prop: "loanType",
      label: t("loanquery.notice.notepay.loanType"),
      groupLabel: t("loanquery.notice.notepay.columnName.loanProduct"),
      groupWidth: "300px",
      formatter: { name: "const", const: "loancounter.LoanBizType" }
    },
    {
      prop: "loanBusinessType",
      label: t("loanquery.notice.notepay.creditLoanBizType"),
      compact: "loanType",
      compactVisible: false,
      formatter: { name: "const", const: "loancounter.LoanBusinessCategory" }
    },
    {
      prop: "loanTerm",
      label: t("loanquery.notice.notepay.loanTerm"),
      groupLabel: t("loanquery.notice.notepay.columnName.contractDuration"),
      groupWidth: "200px",
      slots: { default: "month" }
    },
    {
      prop: "contractStartDate",
      label: t("loanquery.notice.notepay.loanStartDate"),
      compactLabel: "",
      compact: "loanTerm",
      width: "100px"
    },
    {
      prop: "contractEndDate",
      label: t("loanquery.notice.notepay.loanEndDate"),
      compactLabel: "",
      compact: "loanTerm",
      width: "100px",
      formatter: "date"
    },
    {
      prop: "loanClientCode",
      label: t("loanquery.notice.notepay.loanClientCode"),
      groupLabel: t("loanquery.notice.notepay.columnName.borrower"),
      compactLabel: t("loanquery.notice.notepay.code"),
      visible: false,
      groupVisible: true,
      compactVisible: true,
      groupWidth: "200px"
    },
    {
      prop: "loanClientName",
      label: t("loanquery.notice.notepay.loanClientName"),
      compactLabel: t("loanquery.notice.notepay.name"),
      compact: "loanClientCode"
    },
    {
      prop: "consignClientCode",
      compactLabel: t("loanquery.notice.notepay.code"),
      groupLabel: t("loanquery.notice.notepay.columnName.trustee"),
      visible: false,
      groupVisible: true,
      compactVisible: true,
      groupWidth: "200px"
    },
    {
      prop: "consignClientName",
      label: t("loanquery.notice.notepay.consignClientName"),
      compactLabel: t("loanquery.notice.notepay.name"),
      compact: "consignClientCode"
    },
    {
      prop: "contractAmount",
      label: t("loanquery.notice.notepay.contractAmount"),
      groupLabel: t("loanquery.notice.notepay.columnName.contractAmount"),
      formatter: "amount",
      width: "100px",
      groupWidth: "300px"
    },
    {
      prop: "contractBalance",
      label: t("loanquery.notice.notepay.contractBalance"),
      compact: "contractAmount",
      formatter: "amount",
      width: "100px"
    },
    {
      prop: "payAmount",
      label: t("loanquery.notice.notepay.payAmount"),
      groupLabel: t("loanquery.notice.notepay.columnName.disbursementInvoiceAmount"),
      width: "150px",
      formatter: "amount",
      groupWidth: "200px"
    },
    {
      prop: "balance",
      label: t("loanquery.notice.notepay.balance"),
      compact: "payAmount",
      formatter: "amount",
      width: "100px"
    },
    {
      prop: "contractRate",
      label: t("loanquery.notice.notepay.contractRate"),
      groupLabel: t("loanquery.notice.notepay.columnName.executionRate"),
      groupWidth: "200px",
      formatter: "amount"
    },
    {
      prop: "recPrincipalAcctNo",
      label: t("loanquery.notice.notepay.recPrincipalAcctNo"),
      compact: "contractAmount",
      width: "200px"
    },
    {
      prop: "payDate",
      label: t("loanquery.notice.notepay.payDate"),
      groupLabel: t("loanquery.notice.notepay.columnName.disbursementDuration"),
      groupWidth: "200px",
      width: "100px",
      formatter: "date"
    },
    {
      prop: "contractStatus",
      label: t("loanquery.notice.notepay.contractStatus"),
      compact: "contractCode",
      formatter: { name: "const", const: "loancounter.ContractBusinessStatus" }
    },
    {
      prop: "inputUserName",
      label: t("loanquery.notice.notepay.inputUserName"),
      compact: "businessCode"
    },
    {
      prop: "inputTime",
      label: t("loanquery.notice.notepay.inputDate"),
      compact: "businessCode",
      width: "100px",
      formatter: "date"
    },
    {
      prop: "businessStatus",
      label: t("loanquery.notice.notepay.businessStatus"),
      compact: "businessCode",
      formatter: { name: "const", const: "loancounter.BusinessStatus" }
    },

    {
      prop: "productCode",
      label: t("loanquery.notice.notepay.productCode"),
      compact: "loanType",
      visible: false,
      compactVisible: true
    },
    {
      prop: "productName",
      label: t("loanquery.notice.notepay.productName"),
      compact: "loanType",
      visible: false,
      compactVisible: true
    },
    {
      prop: "executeRate",
      label: t("loanquery.notice.notepay.executeRate"),
      compact: "contractRate",
      visible: false,
      compactVisible: true,
      formatter: "amount"
    },
    {
      prop: "loanEndDate",
      label: t("loanquery.notice.notepay.loanEndDate"),
      compact: "payDate",
      visible: false,
      compactVisible: true,
      formatter: "date"
    }
  ];
  const allowSort = tableColumns
    .filter(
      x =>
        ![
          "selection",
          "businessCode",
          "contractCode",
          "loanApplyCode",
          "loanType",
          "LoanBusinessType",
          "loanTerm",
          "contractStartDate",
          "contractEndDate",
          "loanClientName",
          "contractAmount",
          "contractStatus",
          "payAmount",
          "contractRate",
          "payDate",
          "contractBalance",
          "balance",
          "inputUserName",
          "inputTime",
          "businessStatus"
        ].includes(x.prop)
    )
    .map(x => x.prop);

  const { postParams } = useModelRange(["payAmount", "payDate", "inputTime", "loanTerm"]);
  // 表格查询对象
  const queryForm = reactive({
    //贷款申请书编号
    loanApplyCodeList: [],
    //合同编号
    contractCodeList: [],
    //贷款业务种类
    loanTypeList: [],
    //贷款业务类型
    loanBusinessTypeList: [],
    //贷款产品名称
    productIdList: [],
    //放款通知单号
    businessCodeList: [],
    //贷款期限
    loanTerm: [],
    //贷款客户名称
    loanClientIdList: [],
    //委托客户名称
    consignClientIdList: [],
    //合同状态
    contractStatusList: [],
    //放款金额
    payAmount: [],
    //放款日期
    payDate: [],
    //录入日期
    inputTime: [],
    //单据状态
    businessStatusList: [],
    //放款账户
    recPrincipalAcctNoList: []
  });

  // 表格模板
  const queryTable = shallowRef();

  // 获取当前日期
  const getCurrentDate = () => {
    return new Promise(resolve => {
      resolve([formatDate(new Date()), formatDate(new Date())]);
    });
  };

  //详情抽屉打开
  return {
    tableColumns,
    queryForm,
    postParams,
    queryTable,
    allowSort,
    getCurrentDate
  };
};
export default useList;
