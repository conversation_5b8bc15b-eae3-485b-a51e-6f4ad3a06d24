<template>
  <f-query-scene :title="t('loanquery.notice.notepay.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="loanquery.notice.notepay-query-001"
        table-comp-id="loanquery.notice.notepay-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        :post-params="postParams"
        border
        :formData="queryForm"
        :show-header="true"
        auto-reset
        :auto-init="true"
        :export-url="exportUrl"
        :summation-biz-label="t('loanquery.notice.notepay.record')"
        :summation-biz-unit="t('loanquery.notice.notepay.recordUnit')"
        :tile-panel="false"
        :count-label="t('loanquery.notice.notepay.record')"
        :count-label-unit="t('loanquery.notice.notepay.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :allowSort="allowSort"
        show-collapse
        :isSingle="false"
      >
        <template #query-panel>
          <!--贷款申请书编号-->
          <f-form-item :label="t('loanquery.notice.notepay.queryLabel.loanApplyCodeList')" prop="loanApplyCodeList">
            <f-magnifier-multi
              v-model="queryForm.loanApplyCodeList"
              :title="t('loanquery.notice.notepay.queryLabel.loanApplyCodeList')"
              :url="queryLoanApply"
              row-key="loanApplyCode"
              row-label="loanApplyCode"
              selected-key="loanApplyCode"
              selected-label="loanApplyCode"
              input-key="loanApplyCode"
              :collapse-tags-tooltip="true"
              filterable
              :params="{
                loanType: ['TRUST_LOAN', 'CONSIGN_LOAN', 'SYNDICATED_LOAN', 'FACTORING', 'OVERDRAW']
              }"
            >
              <f-magnifier-column
                prop="loanApplyCode"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanApplyCodeList')"
                filter-input
              />
              <f-magnifier-column
                prop="loanClientName"
                :label="t('loanquery.notice.notepay.magnifyingGlass.clientName')"
                filter-input
              />
              <f-magnifier-column
                prop="loanType"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanTypeList')"
                filter-input
                :filter-select="loanType"
                :formatter="row => loanType.valueToLabel(row.loanType)"
              />
              <f-magnifier-column
                prop="loanAmount"
                :label="t('loanquery.notice.notepay.magnifyingGlass.applicationAmount')"
                filter-input
                formatter="amount"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--合同编号-->
          <f-form-item prop="contractCodeList" :label="t('loanquery.notice.notepay.queryLabel.contractCodeList')">
            <f-magnifier-multi
              v-model="queryForm.contractCodeList"
              :title="t('loanquery.notice.notepay.queryLabel.contractCodeList')"
              :url="queryContract"
              row-key="contractCode"
              row-label="contractCode"
              :params="{ loanTypeList: ['TRUST_LOAN', 'CONSIGN_LOAN', 'SYNDICATED_LOAN', 'FACTORING', 'OVERDRAW'] }"
              filterable
            >
              <f-magnifier-column
                prop="contractCode"
                :label="t('loanquery.notice.notepay.magnifyingGlass.contractCode')"
                filter-input
              />
              <f-magnifier-column
                prop="loanClientName"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanClientName')"
                filter-input
              />
              <f-magnifier-column
                prop="contractTotalAmount"
                :label="t('loanquery.notice.notepay.magnifyingGlass.contractAmount')"
                filter-input
                formatter="amount"
              />
              <f-magnifier-column
                prop="contractStartDate"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanStartDate')"
                filter-input
              />
              <f-magnifier-column
                prop="contractEndDate"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanEndDate')"
                filter-input
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--贷款业务种类-->
          <f-form-item prop="loanTypeList" :label="t('loanquery.notice.notepay.queryLabel.loanTypeList')">
            <f-select
              v-model="queryForm.loanTypeList"
              :data="loanType"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--贷款业务类型-->
          <f-form-item prop="loanBusinessTypeList" :label="t('loanquery.notice.notepay.queryLabel.creditLoanBizType')">
            <f-select
              v-model="queryForm.loanBusinessTypeList"
              :data="loanCategory"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--        TODO 贷款产品名称放大镜无法正确显示 -->
          <f-form-item prop="productIdList" :label="t('loanquery.notice.notepay.queryLabel.productIdList')">
            <f-magnifier-multi
              v-model="queryForm.productIdList"
              :title="t('loanquery.notice.notepay.queryLabel.productIdList')"
              :url="queryProduct"
              row-key="id"
              row-label="productName"
              filterable
              auto-select
              :params="{
                productLine: 'LOAN'
              }"
            >
              <f-magnifier-column prop="productCode" :label="t('loanquery.notice.notepay.productCode')" />
              <f-magnifier-column prop="productName" :label="t('loanquery.notice.notepay.productName1')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--          放款通知单号放大镜-->
          <f-form-item prop="businessCodeList" :label="t('loanquery.notice.notepay.queryLabel.businessCodeList')">
            <f-magnifier-multi
              v-model="queryForm.businessCodeList"
              :title="t('loanquery.notice.notepay.queryLabel.businessCodeList')"
              :url="listUrl"
              row-key="businessCode"
              row-label="businessCode"
              filterable
            >
              <f-magnifier-column
                prop="contractCode"
                :label="t('loanquery.notice.notepay.magnifyingGlass.contractCode')"
                filter-input
              />
              <f-magnifier-column
                prop="loanClientName"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanClientName')"
                filter-input
              />
              <f-magnifier-column
                prop="businessCode"
                :label="t('loanquery.notice.notepay.magnifyingGlass.businessCodeList')"
                filter-input
              />
              <f-magnifier-column
                prop="payDate"
                :label="t('loanquery.notice.notepay.magnifyingGlass.payDate')"
                formatter="date"
                filter-input
              />
              <f-magnifier-column
                prop="payAmount"
                :label="t('loanquery.notice.notepay.magnifyingGlass.payAmount')"
                filter-input
                formatter="amount"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--          贷款期限-->
          <f-form-item :label="t('loanquery.notice.notepay.queryLabel.loanTerm')" prop="loanTerm">
            <f-amount-range v-model="queryForm.loanTerm" value-of-string />
          </f-form-item>
          <!--          借款客户名称-->
          <f-form-item prop="loanClientIdList" :label="t('loanquery.notice.notepay.queryLabel.loanClientIdList')">
            <f-magnifier-multi
              :title="t('loanquery.notice.notepay.queryLabel.loanClientIdList')"
              :url="getClientInfo"
              method="post"
              v-model="queryForm.loanClientIdList"
              row-key="clientId"
              row-label="clientName"
              input-key="clientName"
              auto-init
            >
              <f-magnifier-column
                prop="clientCode"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanClientCode')"
              />
              <f-magnifier-column
                prop="clientName"
                :label="t('loanquery.notice.notepay.magnifyingGlass.loanClientName')"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--      委托客户名称-->
          <f-form-item prop="consignClientIdList" :label="t('loanquery.notice.notepay.queryLabel.consignClientIdList')">
            <f-magnifier-multi
              :title="t('loanquery.notice.notepay.queryLabel.consignClientIdList')"
              :url="getClientInfo"
              method="post"
              v-model="queryForm.consignClientIdList"
              row-key="clientId"
              row-label="clientName"
              input-key="clientName"
              auto-init
            >
              <f-magnifier-column
                prop="clientCode"
                :label="t('loanquery.notice.notepay.magnifyingGlass.consignClientCode')"
              />
              <f-magnifier-column
                prop="clientName"
                :label="t('loanquery.notice.notepay.magnifyingGlass.consignClientName')"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--          合同状态 -->
          <!--          TODO 是需求中的合同状态吗？ 查出来合同状态是以审批但枚举中没有-->
          <f-form-item prop="contractStatusList" :label="t('loanquery.notice.notepay.queryLabel.contractStatusList')">
            <f-select
              v-model="queryForm.contractStatusList"
              :data="contractStatus"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--          放款金额-->
          <f-form-item :label="t('loanquery.notice.notepay.queryLabel.payAmount')" prop="payAmount">
            <f-amount-range v-model="queryForm.payAmount" value-of-string />
          </f-form-item>
          <!--          放款日期-->
          <f-form-item :label="t('loanquery.notice.notepay.queryLabel.payDate')" prop="payDate">
            <f-lax-range-date-picker
              v-model="queryForm.payDate"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :widgetInit="getCurrentDate"
            />
          </f-form-item>
          <!--          录入日期-->
          <f-form-item :label="t('loanquery.notice.notepay.queryLabel.inputTime')" prop="inputTime">
            <f-lax-range-date-picker
              v-model="queryForm.inputTime"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :widgetInit="getCurrentDate"
            />
          </f-form-item>
          <!--          单据状态  已暂存、已保存、审批中、已拒绝、已审批、撤销中、已撤销-->
          <f-form-item prop="businessStatusList" :label="t('loanquery.notice.notepay.queryLabel.businessStatusList')">
            <!--按bug88888要求 去除多选下拉框init-if-blank属性，使其默认为空-->
            <f-select
              v-model="queryForm.businessStatusList"
              :data="businessStatus"
              multiple
              filterable
              collapse-tags
              select-all
            />
          </f-form-item>
          <!--放款账户-->
          <f-form-item
            :label="t('loanquery.notice.notepay.queryLabel.recPrincipalAcctNoList')"
            prop="recPrincipalAcctNoList"
          >
            <f-magnifier-multi
              v-model="queryForm.recPrincipalAcctNoList"
              :url="listPayAccountNoUrl"
              :title="t('loanquery.notice.notepay.payAccountNo')"
              auto-init
              method="post"
              row-key="accountCode"
              row-label="accountCode"
              input-key="accountCode"
            >
              <f-magnifier-column prop="accountCode" :label="t('loanquery.notice.notepay.payAccountNo')" />
              <f-magnifier-column prop="accountName" :label="t('loanquery.notice.notepay.payAccountName')" />
            </f-magnifier-multi>
          </f-form-item>
        </template>
        <template #businessCode="{ row }">
          <f-button type="text" @click="openDetail(row)">
            {{ row.businessCode }}
          </f-button>
        </template>
        <template #month="{ row }">
          {{
            row.loanTerm === null
              ? t("loanquery.notice.notepay.indefinitely")
              : row.loanTerm + t("loanquery.notice.notepay.month")
          }}
        </template>
      </f-query-grid>
    </template>
    <View ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useList from "../hooks/useList";
import { View } from "@/views/loancounter/notice/pay";

const { t } = useI18n();

import {
  listUrl,
  exportUrl,
  getClientInfo,
  queryContract,
  queryLoanApply,
  queryProduct,
  listPayAccountNoUrl
} from "../url";

import { shallowRef, nextTick } from "vue";

const detail = shallowRef();
const id = shallowRef();
const openDetail = (row: any) => {
  id.value = row.id;
  nextTick(() => {
    detail.value.setVisible(true);
  });
};

//贷款类型
const allLoanType = useConst("loancounter.LoanBizType");
const loanType = allLoanType.pickConst([
  allLoanType.TRUST_LOAN,
  allLoanType.CONSIGN_LOAN,
  allLoanType.FACTORING,
  allLoanType.OVERDRAW
]);
const loanCategory = useConst("loancounter.LoanBusinessCategory");

const contractStatusEnum = useConst("loancounter.ContractBusinessStatus");
const contractStatus = contractStatusEnum.pickConst([
  contractStatusEnum.NOT_EXECUTE,
  contractStatusEnum.EXECUTING,
  contractStatusEnum.FINISH,
  contractStatusEnum.OVERDUE,
  contractStatusEnum.EXTEND,
  contractStatusEnum.DELAY_DEBT,
  contractStatusEnum.BAD_DEBT,
  contractStatusEnum.VERIFICATION,
  contractStatusEnum.VERIFIED_OFF
]);
const businessStatusEnum = useConst("common.BusinessStatus");
const businessStatus = businessStatusEnum.pickConst([
  businessStatusEnum.SAVE,
  businessStatusEnum.APPROVING,
  businessStatusEnum.APPROVED,
  businessStatusEnum.REFUSE,
  businessStatusEnum.CANCELLING,
  businessStatusEnum.CANCEL,
  businessStatusEnum.DRAFT,
  businessStatusEnum.SETTLED,
  businessStatusEnum.SETTLE_FAIL
]);
const { tableColumns, queryForm, postParams, queryTable, allowSort, getCurrentDate } = useList();
</script>
