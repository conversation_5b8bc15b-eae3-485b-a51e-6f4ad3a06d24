<template>
  <f-query-scene :title="t('loanquery.contract.executionstatus.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="loanquery.contract.executionstatus-query-001"
        table-comp-id="loanquery.contract.executionstatus-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        :post-params="postParams"
        border
        :formData="queryForm"
        :show-header="true"
        auto-reset
        :auto-init="true"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :summation-biz-label="t('loanquery.contract.executionstatus.record')"
        :summation-biz-unit="t('loanquery.contract.executionstatus.recordUnit')"
        :tile-panel="false"
        :count-label="t('loanquery.contract.executionstatus.record')"
        :count-label-unit="t('loanquery.contract.executionstatus.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :allowSort="allowSort"
        show-collapse
        :isSingle="false"
      >
        <template #query-panel>
          <!--合同编号-->
          <f-form-item
            prop="contractCodeList"
            :label="t('loanquery.contract.executionstatus.queryLabel.contractCodeList')"
          >
            <f-magnifier-multi
              v-model="queryForm.contractCodeList"
              :title="t('loanquery.contract.executionstatus.queryLabel.contractCodeList')"
              :url="queryContract"
              row-key="contractCode"
              row-label="contractCode"
              :params="{ loanTypeList: ['TRUST_LOAN', 'CONSIGN_LOAN', 'SYNDICATED_LOAN', 'FACTORING', 'OVERDRAW'] }"
              filterable
            >
              <f-magnifier-column
                prop="contractCode"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.contractCode')"
                filter-input
              />
              <f-magnifier-column
                prop="loanClientName"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.loanClientName')"
                filter-input
              />
              <f-magnifier-column
                prop="contractTotalAmount"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.contractAmount')"
                filter-input
                formatter="amount"
              />
              <f-magnifier-column
                prop="contractStartDate"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.loanStartDate')"
                filter-input
              />
              <!--              TODO 起止日期怎么展示？-->
              <f-magnifier-column
                prop="contractEndDate"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.loanEndDate')"
                filter-input
              />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 借款单位-->
          <f-form-item
            prop="loanClientIdList"
            :label="t('loanquery.contract.executionstatus.queryLabel.loanClientIdList')"
          >
            <f-magnifier-multi
              :title="t('loanquery.contract.executionstatus.queryLabel.loanClientIdList')"
              :url="getClientInfo"
              method="post"
              v-model="queryForm.loanClientIdList"
              row-key="clientId"
              row-label="clientName"
              input-key="clientName"
              auto-init
            >
              <f-magnifier-column
                prop="clientCode"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.loanClientCode')"
              />
              <f-magnifier-column
                prop="clientName"
                :label="t('loanquery.contract.executionstatus.magnifyingGlass.loanClientName')"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--按bug89277要求，去除委托单位查询条件-->
          <!--合同状态 -->
          <f-form-item
            prop="contractStatusList"
            :label="t('loanquery.contract.executionstatus.queryLabel.contractStatusList')"
          >
            <!--按bug89277要求 去除多选下拉框blank-option和init-if-blank属性，使其默认为空-->
            <f-select v-model="queryForm.contractStatusList" :data="contractStatus" multiple select-all collapse-tags />
          </f-form-item>
          <!--贷款业务种类-->
          <f-form-item prop="loanTypeList" :label="t('loanquery.contract.executionstatus.queryLabel.loanTypeList')">
            <!--按bug89283要求 贷款业务种类去掉跨境融通类型，故此调整loanType为loanTypeEnum-->
            <!--按bug89277要求 去除多选下拉框blank-option和init-if-blank属性，使其默认为空-->
            <f-select v-model="queryForm.loanTypeList" :data="loanTypeEnum" multiple select-all collapse-tags />
          </f-form-item>
          <!-- 贷款业务类型  -->
          <f-form-item
            prop=" loanBusinessTypeList"
            :label="t('loanquery.contract.executionstatus.queryLabel.loanBusinessCategoryList')"
          >
            <f-select
              v-model="queryForm.loanBusinessTypeList"
              :data="loanCategory"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--         合同金额 -->
          <f-form-item :label="t('loanquery.contract.executionstatus.queryLabel.contractAmount')" prop="contractAmount">
            <f-amount-range v-model="queryForm.contractAmount" value-of-string />
          </f-form-item>
          <!--  期限-->
          <f-form-item :label="t('loanquery.contract.executionstatus.queryLabel.contractTerm')" prop="contractTerm">
            <f-amount-range
              v-model="queryForm.contractTerm"
              whole-number
              value-of-string
              :negative="false"
              symbol=" "
            />
          </f-form-item>
          <!--贷款开始日-->
          <f-form-item
            :label="t('loanquery.contract.executionstatus.queryLabel.contractStartDate')"
            prop="contractStartDate"
          >
            <f-date-picker v-model="queryForm.contractStartDate" type="daterange" />
          </f-form-item>
          <!--          贷款结束日-->
          <f-form-item
            :label="t('loanquery.contract.executionstatus.queryLabel.contractEndDate')"
            prop="contractEndDate"
          >
            <f-date-picker v-model="queryForm.contractEndDate" type="daterange" />
          </f-form-item>
          <!-- 贷款产品名称 -->
          <f-form-item :label="t('loanquery.contract.executionstatus.queryLabel.productIdList')" prop="productIdList">
            <f-magnifier-multi
              v-model="queryForm.productIdList"
              :title="t('loanquery.contract.executionstatus.queryLabel.productIdList')"
              :url="queryProductBasic"
              row-key="id"
              row-label="productName"
              collapse-tags-tooltip
              :params="{
                productLine: 'LOAN'
              }"
            >
              <f-magnifier-column prop="productCode" :label="t('loanquery.contract.executionstatus.productCode')" />
              <f-magnifier-column prop="productName" :label="t('loanquery.contract.executionstatus.productName')" />
            </f-magnifier-multi>
          </f-form-item>
          <f-form-item :label="t('loanquery.contract.executionstatus.queryLabel.inputTime')" prop="inputTime">
            <f-lax-range-date-picker
              v-model="queryForm.inputTime"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :widgetInit="getCurrentDate"
            />
          </f-form-item>
        </template>
        <template #contractCode="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.contractCode }}</f-button>
        </template>
        <template #month="{ row }">
          {{
            row.loanTerm === null
              ? t("loanquery.contract.executionstatus.indefinitely")
              : row.loanTerm + t("loanquery.contract.executionstatus.month")
          }}
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" :contractCode="rowContractCode" :loanClientName="rowLoanClientName" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useList from "../hooks/useList";
import Detail from "./Detail.vue";

const { t } = useI18n();

import { listUrl, exportUrl, getClientInfo, queryContract, queryProductBasic } from "../url";

//贷款类型
const loanType = useConst("loancounter.LoanBizType");
// 按bug89283要求 贷款业务种类去掉跨境融通类型，故添加如下枚举
const loanTypeEnum = loanType.pickConst([
  loanType.TRUST_LOAN,
  loanType.CONSIGN_LOAN,
  loanType.FACTORING,
  loanType.OVERDRAW,
  loanType.SYNDICATED_LOAN
]);

const loanCategory = useConst("core.LoanBusinessCategory");

const contractStatusEnum = useConst("loan.LoanContractStatus");
const contractStatus = contractStatusEnum.pickConst([
  contractStatusEnum.NOT_EXECUTE,
  contractStatusEnum.EXECUTING,
  contractStatusEnum.FINISH,
  contractStatusEnum.OVERDUE,
  contractStatusEnum.EXTEND,
  contractStatusEnum.DELAY_DEBT,
  contractStatusEnum.BAD_DEBT
]);

const {
  rowId,
  rowContractCode,
  rowLoanClientName,
  detail,
  tableColumns,
  queryForm,
  postParams,
  queryTable,
  allowSort,
  handleOpen,
  getCurrentDate
} = useList();
</script>
