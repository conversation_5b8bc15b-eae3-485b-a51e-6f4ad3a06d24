<template>
  <f-query-scene :title="t('loanquery.extension.contract.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="loanquery.extension.contract-query-001"
        table-comp-id="loanquery.extension.contract-table-001"
        :table-columns="tableColumns"
        :url="listPage"
        border
        :formData="queryForm"
        :show-header="true"
        auto-reset
        :auto-init="true"
        :post-params="postParams"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :summation-biz-label="t('loanquery.extension.contract.record')"
        :summation-biz-unit="t('loanquery.extension.contract.recordUnit')"
        :tile-panel="false"
        :count-label="t('loanquery.extension.contract.record')"
        :count-label-unit="t('loanquery.extension.contract.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="true"
        tableType="Record"
        :allowSort="allowSort"
        show-collapse
        :isSingle="false"
      >
        <template #query-panel>
          <!-- 贷款业务种类 -->
          <f-form-item :label="t('loanquery.extension.contract.loanType')" prop="loanTypeList">
            <!--按bug89163要求 去除多选下拉框blank-option和init-if-blank属性，使其默认为空-->
            <f-select
              v-model="queryForm.loanTypeList"
              :data="loanTypeEnum"
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 借款单位 -->
          <f-form-item :label="t('loanquery.extension.contract.borrowClientId')" prop="borrowClientIdList">
            <f-magnifier-multi
              :title="t('loanquery.extension.contract.clientIdMagnifier')"
              :url="getClientInfo"
              method="post"
              v-model="queryForm.borrowClientIdList"
              row-key="clientId"
              row-label="clientName"
              input-key="clientName"
              auto-init
            >
              <!--按bug89167要求 调整放大镜内部显示名称与外部放大镜名称一致-->
              <f-magnifier-column prop="clientCode" :label="t('loanquery.extension.contract.borrowClientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loanquery.extension.contract.borrowClientName')" />
            </f-magnifier-multi>
          </f-form-item>

          <!-- 委托单位 -->
          <f-form-item :label="t('loanquery.extension.contract.consignClientId')" prop="consignClientIdList">
            <f-magnifier-multi
              :title="t('loanquery.extension.contract.clientIdMagnifier')"
              :url="getClientInfo"
              method="post"
              v-model="queryForm.consignClientIdList"
              row-key="clientId"
              row-label="clientName"
              input-key="clientName"
              auto-init
            >
              <!--按bug89167要求 调整放大镜内部显示名称与外部放大镜名称一致-->
              <f-magnifier-column prop="clientCode" :label="t('loanquery.extension.contract.consignClientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loanquery.extension.contract.consignClientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 展期申请单编号 -->
          <f-form-item :label="t('loanquery.extension.contract.extensionApplyCode')" prop="extensionApplyCode">
            <f-input v-model="queryForm.extensionApplyCode" :maxlength="50" />
          </f-form-item>
          <!-- 展期合同编号 -->
          <f-form-item :label="t('loanquery.extension.contract.extensionContractCode')" prop="extensionContractCode">
            <f-input v-model="queryForm.extensionContractCode" :maxlength="50" />
          </f-form-item>
          <!-- 展期金额 -->
          <f-form-item :label="t('loanquery.extension.contract.extensionAmount')" prop="extensionAmount">
            <f-amount-range v-model="queryForm.extensionAmount" value-of-string />
          </f-form-item>
          <!-- 是否调整利率 -->
          <f-form-item :label="t('loanquery.extension.contract.rateAdjustment')" prop="rateAdjustment">
            <f-select
              v-model="queryForm.rateAdjustment"
              :data="yesOrNo"
              clearable
              blank-option
              init-if-blank
              filterable
            />
          </f-form-item>
          <!-- 单据状态 -->
          <f-form-item :label="t('loanquery.extension.contract.businessStatus')" prop="businessStatusList">
            <!--按bug89163要求 去除多选下拉框blank-option和init-if-blank属性，使其默认为空-->
            <f-select
              v-model="queryForm.businessStatusList"
              :data="statusEnum"
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 录入日期 -->
          <f-form-item :label="t('loanquery.extension.contract.inputTime')" prop="inputTime">
            <f-lax-range-date-picker
              v-model="queryForm.inputTime"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :widgetInit="getCurrentDate"
            />
          </f-form-item>

          <!-- 合同编号 -->
          <f-form-item :label="t('pages.loanCounter.contractNo')" prop="contractCodeList">
            <f-magnifier-multi
              v-model="queryForm.contractCodeList"
              :title="t('pages.loanCounter.contractNo')"
              :url="getContractInfo"
              row-key="contractCode"
              row-label="contractCode"
              :params="{ loanTypeList: ['TRUST_LOAN', 'CONSIGN_LOAN', 'SYNDICATED_LOAN', 'FACTORING', 'OVERDRAW'] }"
              filterable
            >
              <f-magnifier-column prop="contractCode" :label="t('pages.loanCounter.contractNo')" filter-input />
              <f-magnifier-column prop="loanClientName" :label="t('pages.loanCounter.borrower')" filter-input />
              <f-magnifier-column
                prop="contractTotalAmount"
                :label="t('pages.loanCounter.contractAmount')"
                filter-input
                formatter="amount"
              />
              <f-magnifier-column
                prop="contractStartDate"
                :label="t('loanquery.extension.contract.contractStartDate')"
                filter-input
              />
              <f-magnifier-column
                prop="contractEndDate"
                :label="t('loanquery.extension.contract.contractEndDate')"
                filter-input
              />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 贷款业务类型 -->
          <f-form-item :label="t('loanquery.extension.contract.creditLoanBizType')" prop="loanBusinessTypeList">
            <!--按bug89163要求 去除多选下拉框blank-option和init-if-blank属性，使其默认为空-->
            <f-select
              v-model="queryForm.loanBusinessTypeList"
              :data="creditLoanBizTypeEnum"
              clearable
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
        </template>
        <template #extensionApplyCode="{ row }">
          <f-button @click="handleApplyOpen(row)" link type="primary">{{ row.extensionApplyCode }}</f-button>
        </template>
        <template #extensionContractCode="{ row }">
          <f-button @click="handleContractOpen(row)" link type="primary">{{ row.extensionContractCode }}</f-button>
        </template>
        <template #month="{ row }">
          {{
            row.loanTerm === null
              ? t("loanquery.extension.contract.NoTerm")
              : row.loanTerm + t("loanquery.extension.contract.month")
          }}
        </template>
      </f-query-grid>
    </template>
    <ApplyDetail ref="applyDetail" :id="rowId" />
    <ContractDetail ref="contractDetail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { listPage, exportUrl, getContractInfo, getClientInfo } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { Detail as ApplyDetail } from "@/views/loancounter/extension/apply";
import { Detail as ContractDetail } from "@/views/loancounter/extension/contract";

const { t } = useI18n();

const statusEnum = useConst("loancounter.BusinessStatus");
const loanType = useConst("loancounter.LoanType");
const yesOrNo = useConst("common.YesOrNo");
const loanTypeEnum = loanType.pickConst([
  loanType.TRUST_LOAN,
  loanType.CONSIGN_LOAN,
  // 按bug89163要求 去掉保理类型
  // loanType.FACTORING,
  loanType.OVERDRAW,
  loanType.SYNDICATED_LOAN
]);

const {
  rowId,
  applyDetail,
  contractDetail,
  tableColumns,
  queryForm,
  queryTable,
  allowSort,
  handleApplyOpen,
  handleContractOpen,
  postParams,
  loanBusinessType,
  getCurrentDate
} = useList();
const creditLoanBizTypeEnum = loanBusinessType.pickConst([
  loanBusinessType.WORKING_CAPITAL_LOAN,
  loanBusinessType.FIXED_ASSET_LOAN,
  loanBusinessType.PROJECT_FINANCING,
  loanBusinessType.OTHER_LOANS
]);
</script>
