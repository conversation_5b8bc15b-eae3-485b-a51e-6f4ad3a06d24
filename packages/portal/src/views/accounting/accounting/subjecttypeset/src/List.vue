<template>
  <f-query-scene :title="t('accounting.accounting.subjecttypeset.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :table-columns="tableColumns"
        :url="search"
        border
        :form-data="queryForm"
        show-header
        auto-reset
        auto-init
        :show-collapse="false"
        :show-print="false"
        tile-panel
        @select="handleSelect"
        @select-all="selectableAll"
        :show-count-value="false"
        @clear-selection="clearSelection"
        :count-label="t('accounting.accounting.subjecttypeset.recording')"
        :count-label-unit="t('accounting.accounting.subjecttypeset.strip')"
        :show-summation-sum="false"
        :summation-biz-label="t('accounting.accounting.subjecttypeset.recording')"
        :summation-biz-unit="t('accounting.accounting.subjecttypeset.strip')"
        query-comp-id="accounting-accounting-subjecttypeset-query"
        table-comp-id="accounting-accounting-subjecttypeset-table"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :allow-sort="allowSort"
      >
        <template #operate>
          <f-button type="primary" @click="add">{{ t("accounting.accounting.subjecttypeset.add") }}</f-button>
          <f-submit-state
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="isChecked"
            :gather-params="gatherBatchDeleteParams"
            :url="batchDeleteUrl"
            @close="handleSuccess"
            :batch-confirm-map="deleteResultConfirm"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('accounting.accounting.subjecttypeset.officeName')" prop="officeIds">
            <f-select
              v-model="queryForm.officeIds"
              value-key="officeId"
              label="officeName"
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
              :url="queryOffice"
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.subjecttypeset.currencyName')" prop="currencyIds">
            <f-select
              v-model="queryForm.currencyIds"
              value-key="currencyId"
              label="currencyName"
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
              :url="queryCurrency"
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.subjecttypeset.subjectTypeGroupName')" prop="subjectTypeGroups">
            <f-select
              v-model="queryForm.subjectTypeGroups"
              :data="subjectTypeGroup"
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.subjecttypeset.subjectType')" prop="subjectType">
            <f-input v-model="queryForm.subjectType" maxlength="30" />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.subjecttypeset.subjectCode')" prop="subjectCode">
            <f-input v-model="queryForm.subjectCode" maxlength="30" />
          </f-form-item>
        </template>
        <template #subjectType="{ row }">
          <f-button @click="handleOpenDetail(row)" link type="primary">
            {{ row.subjectType }}
          </f-button>
        </template>
        <template #isSubClass="{ row }">
          <f-button
            v-if="row.isSubClass === yesOrNo.YES && checkIsSubClassShow(row)"
            @click="handleOpenDialog(row)"
            link
            type="primary"
          >
            {{ yesOrNo.valueToLabel(row.isSubClass) }}
          </f-button>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="handleOpen(row)" />
        </template>
      </f-query-grid>
    </template>

    <!-- 下级分类设置弹窗 -->
    <f-dialog
      ref="dialogRef"
      v-model="dialogIsSubVisible"
      :title="t('accounting.accounting.subjecttypeset.lastTitle')"
      destroy-on-close
      @close="clearSubSelection"
    >
      <div style="margin-bottom: 20px">
        <f-submit-state
          :is-batch="true"
          operate="remove"
          type="danger"
          :gather-params="gatherBatchSubDeleteParams"
          :url="url.delete"
          @close="handleSubSuccess"
          :before-trigger="tableValidator"
          :disabled="isSubCheckedList"
          :batch-confirm-map="deleteResultConfirm"
          compatible
        />
      </div>
      <f-table-edit
        ref="tableGrid"
        row-key="randomId"
        :data="tableData"
        style="width: 100%"
        @add-row="addTableRow"
        :before-close-edit="saveTableRow"
        :before-delete="deleteTableRow"
        @select="handleSelectionChange"
        @select-all="handleSelectionChange"
        @clear-selection="subClassClearSelection"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        border
      >
        <f-table-column type="selection" :selectable="row => row.id !== null" />
        <f-table-column prop="subjectType" :label="t('accounting.accounting.subjecttypeset.subjectType')" />

        <!-- 定期 -->
        <f-table-column
          prop="bankTypeStr"
          :label="t('accounting.accounting.subjecttypeset.bankType')"
          v-if="isInterBankFixedSubClass && subjectParams.subClassArray.includes(1)"
          :showOverflowTooltip="true"
        >
          <template #edit="scope">
            <f-magnifier-single
              v-model="scope.row.bankTypeStr"
              :url="queryBankType"
              :title="t('accounting.accounting.subjecttypeset.bankType')"
              row-key="name"
              row-label="name"
              input-key="codeAndName"
              filterable
              :selected-data="{
                name: scope.row.bankTypeStr
              }"
              @change="handleBankTypeChange($event, scope.row)"
            >
              <f-magnifier-column prop="code" :label="t('accounting.accounting.subjecttypeset.bankCode')" />
              <f-magnifier-column prop="name" :label="t('accounting.accounting.subjecttypeset.bankTypeName')" />
            </f-magnifier-single>
          </template>
        </f-table-column>
        <f-table-column
          prop="bankAccountStr"
          :label="t('accounting.accounting.subjecttypeset.bankAccount')"
          v-if="isInterBankFixedSubClass && subjectParams.subClassArray.includes(2)"
          :showOverflowTooltip="true"
        >
          <template #edit="scope">
            <f-magnifier-single
              v-model="scope.row.bankAccountStr"
              :url="queryBankCode"
              :title="t('accounting.accounting.subjecttypeset.bankAccount')"
              row-key="bankAccountNo"
              row-label="bankAccountNo"
              input-key="bankAccNoOrName"
              :params="{
                officeId: scope.row.officeId,
                currencyId: scope.row.currencyId,
                bankTypeCode: scope.row.bankType
              }"
              filterable
              :selected-data="{
                bankAccountNo: scope.row.bankAccountStr
              }"
              @change="handleBankAccountChange($event, scope.row)"
            >
              <f-magnifier-column
                prop="bankAccountNo"
                :label="t('accounting.accounting.subjecttypeset.bankAccountCode')"
              />
              <f-magnifier-column
                prop="bankAccountName"
                :label="t('accounting.accounting.subjecttypeset.bankAccountName')"
              />
              <f-magnifier-column prop="bankName" :label="t('accounting.accounting.subjecttypeset.bankName')" />
            </f-magnifier-single>
          </template>
        </f-table-column>
        <f-table-column
          prop="fixedTermStr"
          :label="t('accounting.accounting.subjecttypeset.fixedTerm')"
          v-if="isInterBankFixedSubClass && subjectParams.subClassArray.includes(3)"
        >
          <template #edit="scope">
            <f-magnifier-single
              v-model="scope.row.fixedTermStr"
              :url="queryInterBankFixedTermUrl"
              :title="t('accounting.accounting.subjecttypeset.fixedTerm')"
              row-key="termShow"
              row-label="termShow"
              input-key="termShow"
              :params="{
                currencyIds: [scope.row.currencyId],
                depositTypes: [interBankDepositType.FIXED_DEPOSIT]
              }"
              filterable
              :selected-data="{
                id: scope.row.fixedTerm
              }"
              @change="handleFixTermChange($event, scope.row)"
            >
              <f-magnifier-column prop="termShow" :label="t('accounting.accounting.subjecttypeset.fixedTerm')" />
            </f-magnifier-single>
          </template>
        </f-table-column>

        <!--贴现-->
        <f-table-column
          prop="billmedia"
          :label="t('accounting.accounting.subjecttypeset.billmedia')"
          v-if="isTransIsSubClass && subjectParams.subClassArray.includes(1)"
          :formatter="billMediumFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.billmedia" :data="billMedium" />
          </template>
        </f-table-column>
        <f-table-column
          prop="draftType"
          :label="t('accounting.accounting.subjecttypeset.draftType')"
          v-if="isTransIsSubClass && subjectParams.subClassArray.includes(2)"
          :formatter="billTypeFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.draftType" :data="billType" />
          </template>
        </f-table-column>
        <f-table-column
          prop="billSource"
          :label="t('accounting.accounting.subjecttypeset.billSource')"
          v-if="isTransIsSubClass && subjectParams.subClassArray.includes(3)"
          :formatter="billSourceFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.billSource" :data="billSource" />
          </template>
        </f-table-column>

        <!-- 拆借 -->
        <f-table-column
          prop="lendingCategory"
          :label="t('accounting.accounting.subjecttypeset.lendingCategory')"
          v-if="isInterbankLendingSubClass && subjectParams.subClassArray.includes(1)"
          :formatter="lendingCategoryFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.lendingCategory" :data="lendingCategory" />
          </template>
        </f-table-column>
        <f-table-column
          prop="counterpartyClass"
          :label="t('accounting.accounting.subjecttypeset.counterpartyClass')"
          v-if="isInterbankLendingSubClass && subjectParams.subClassArray.includes(2)"
          :formatter="counterpartyClassFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.counterpartyClass" :data="counterpartyClass" />
          </template>
        </f-table-column>

        <!-- 代开 -->
        <f-table-column
          prop="replaceBusinessType"
          :label="t('accounting.accounting.subjecttypeset.replaceBusinessType')"
          v-if="isReplaceBusinessSubClass"
          :formatter="replaceBusinessSubTypeFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.replaceBusinessType" :data="replaceBusinessSubType" />
          </template>
        </f-table-column>

        <!-- 重空 -->
        <f-table-column
          prop="blankVoucherType"
          :label="t('accounting.accounting.subjecttypeset.blankVoucherType')"
          v-if="isBlankVoucherSubClass && subjectParams.subClassArray.includes(1)"
          :formatter="blankVoucherTypeFormatter"
        >
          <template #edit="scope">
            <f-select v-model="scope.row.blankVoucherType" :data="blankVoucherTypeName" />
          </template>
        </f-table-column>
        <f-table-column
          prop="bankTypeStr"
          :label="t('accounting.accounting.subjecttypeset.bankType')"
          v-if="isBlankVoucherSubClass && subjectParams.subClassArray.includes(2)"
        >
          <template #edit="scope">
            <f-magnifier-single
              :title="t('accounting.accounting.subjecttypeset.bankType')"
              :url="queryBankTypeDic"
              v-model="scope.row.bankTypeStr"
              row-key="name"
              row-label="name"
              input-key="codeAndName"
              :params="{
                voucherType: scope.row.blankVoucherType
              }"
              filterable
              :selected-data="{
                name: scope.row.bankTypeStr
              }"
              @change="blankVoucherBankCheck($event, scope.row)"
            >
              <f-magnifier-column prop="code" :label="t('accounting.accounting.subjecttypeset.bankCode')" />
              <f-magnifier-column prop="name" :label="t('accounting.accounting.subjecttypeset.bankTypeName')" />
            </f-magnifier-single>
          </template>
        </f-table-column>
        <f-table-column
          prop="voucherTypeName"
          :label="t('accounting.accounting.subjecttypeset.voucherTypeName')"
          v-if="isBlankVoucherSubClass && subjectParams.subClassArray.includes(3)"
        >
          <template #edit="scope">
            <f-magnifier-single
              :title="t('accounting.accounting.subjecttypeset.voucherTypeName')"
              :url="queryVoucherName"
              v-model="scope.row.voucherTypeName"
              row-key="TYPENAME"
              row-label="TYPENAME"
              input-key="typeNameTemp"
              :params="{
                TYPENAME: scope.row.voucherTypeName
              }"
              filterable
              :selected-data="{
                TYPENAME: scope.row.voucherTypeName
              }"
            >
              <f-magnifier-column prop="TYPENAME" :label="t('accounting.accounting.subjecttypeset.voucherTypeName')" />
            </f-magnifier-single>
          </template>
        </f-table-column>

        <!-- 投资 -->
        <f-table-column
          prop="productTypeName"
          :label="t('accounting.accounting.subjecttypeset.productTypeCode')"
          v-if="isInvestmentSubClass"
        >
          <template #edit="scope">
            <f-magnifier-single
              :title="t('accounting.accounting.subjecttypeset.productTypeCode')"
              :url="queryProductType"
              v-model="scope.row.productTypeName"
              row-key="name"
              row-label="name"
              input-key="condition"
              :params="{
                officeId: scope.row.officeId
              }"
              filterable
              :selected-data="{
                code: scope.row.productTypeCode
              }"
              @change="holdProductTypeCode"
            >
              <f-magnifier-column prop="code" :label="t('accounting.accounting.subjecttypeset.productCode')" />
              <f-magnifier-column prop="name" :label="t('accounting.accounting.subjecttypeset.productName')" />
            </f-magnifier-single>
          </template>
        </f-table-column>

        <f-table-column prop="subjectCode" :label="t('accounting.accounting.subjecttypeset.subjectNo')">
          <template #edit="scope">
            <f-magnifier-single
              :title="t('accounting.accounting.subjecttypeset.subjectNo')"
              :url="querySubjectCode"
              v-model="scope.row.subjectCode"
              row-key="SUBJECTCODE"
              row-label="SUBJECTCODE"
              input-key="_SUBJECTCODE"
              :params="{
                officeId: scope.row.officeId,
                currencyId: scope.row.currencyId
              }"
              filterable
              :selected-data="{
                SUBJECTCODE: scope.row.subjectCode
              }"
            >
              <f-magnifier-column prop="SUBJECTCODE" :label="t('accounting.accounting.subjecttypeset.subjectNo')" />
              <f-magnifier-column prop="SUBJECTNAME" :label="t('accounting.accounting.subjecttypeset.subjectName')" />
            </f-magnifier-single>
          </template>
        </f-table-column>
      </f-table-edit>
      <template #footer>
        <span class="dialog-footer">
          <f-button type="info" @click="dialogIsSubVisible = false">{{
            t("accounting.accounting.subjecttypeset.close")
          }}</f-button>
        </span>
      </template>
    </f-dialog>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import {
  search,
  exportUrl,
  batchDeleteUrl,
  queryOffice,
  queryCurrency,
  queryBankType,
  queryBankCode,
  queryInterBankFixedTermUrl,
  querySubjectCode,
  queryBankTypeDic,
  queryVoucherName,
  queryProductType
} from "../url";
import useSubjectTypeSetList from "../hooks/useSubjectTypeSetList";
import OperateButton from "@/components/operate-button/operate-button";
import Detail from "./components/Details.vue";

const subjectTypeGroupEnum = useConst("accounting.SubjectTypeGroup");
const subjectTypeGroup = subjectTypeGroupEnum.pickConst([
  subjectTypeGroupEnum.INTERBANKCURRENT,
  subjectTypeGroupEnum.INTERBANKREGULAR,
  subjectTypeGroupEnum.TRANSDIS,
  subjectTypeGroupEnum.TRANSREDIS,
  subjectTypeGroupEnum.INTERBANKLENDING,
  subjectTypeGroupEnum.INTERBANKDEPOSITCERTIFICATE
]);
const yesOrNo = useConst("accounting.YesOrNo");
const billMedium = useConst("accounting.BillMedium");
const billType = useConst("accounting.BillType");
const billSource = useConst("accounting.BillSource");
const lendingCategoryEnum = useConst("accounting.LendingCategory");
const lendingCategory = lendingCategoryEnum.pickConst([lendingCategoryEnum.ONE, lendingCategoryEnum.SEVEN]);
const counterpartyClass = useConst("accounting.CounterpartyClass");
const replaceBusinessSubType = useConst("accounting.ReplaceBusinessSubType");
const blankVoucherType = useConst("accounting.BlankVoucherType");
const interBankDepositType = useConst("interbankdeposit.InterBankDepositType");
const blankVoucherTypeName = blankVoucherType.pickConst([blankVoucherType.PURCHASE, blankVoucherType.SELFPRINT]);

const { t } = useI18n();
const {
  queryForm,
  add,
  tableColumns,
  isChecked,
  selectableAll,
  handleOpen,
  handleOpenDetail,
  handleSelect,
  gatherBatchDeleteParams,
  generalButtonOption,
  handleSuccess,
  clearSelection,
  handleOpenDialog,
  detail,
  rowId,
  queryTable,
  dialogIsSubVisible,
  tableGrid,
  tableData,
  addTableRow,
  saveTableRow,
  deleteTableRow,
  isTransIsSubClass,
  isReplaceBusinessSubClass,
  isInterbankLendingSubClass,
  isBlankVoucherSubClass,
  isInvestmentSubClass,
  isInterBankFixedSubClass,
  dialogRef,
  subjectParams,
  billMediumFormatter,
  billTypeFormatter,
  billSourceFormatter,
  lendingCategoryFormatter,
  counterpartyClassFormatter,
  replaceBusinessSubTypeFormatter,
  blankVoucherTypeFormatter,
  holdProductTypeCode,
  handleSelectionChange,
  url,
  isSubCheckedList,
  gatherBatchSubDeleteParams,
  handleSubSuccess,
  tableValidator,
  clearSubSelection,
  handleBankAccountChange,
  blankVoucherBankCheck,
  checkIsSubClassShow,
  allowSort,
  handleBankTypeChange,
  handleFixTermChange,
  deleteResultConfirm,
  subClassClearSelection
} = useSubjectTypeSetList();
</script>
