export default {
  listTitle: "科目类型设置-链接查找",
  addTitle: "科目类型设置-新增",
  modifyTitle: "科目类型设置-修改",
  datilTitle: "科目类型设置-查看",
  lastTitle: "科目类型下级分类设置",
  subjectType: "科目类型",
  officeName: "机构",
  currencyName: "币种",
  subjectTypeGroupName: "科目类型组",
  isSubClass: "下级分类",
  subClassStr: "分类",
  subjectCode: "科目号",
  operate: "操作",
  subjectName: "科目名称",
  subjectNo: "科目编号",
  remarks: "备注",

  officeIdPlace: "机构不能为空!",
  currencyIdPlace: "币种不能为空!",
  subjectTypeGroup: "科目类型组不能为空!",
  subjectTypeIdPlace: "科目类型不能为空!",
  subjectCodePlace: "科目代码不能为空!",
  subjectCodeError: "科目编号不能为空",

  bankType: "银行类别",
  bankTypeEorer: "银行类别不能为空",
  bankAccountError: "银行账号不能为空",
  fixedTermError: "定期期限不能为空",
  bankCode: "银行简码",
  bankTypeName: "银行类别名称",
  bankAccount: "银行账户",
  bankAccountCode: "账户编号",
  bankAccountName: "账户名称",
  bankName: "银行类别",
  fixedTerm: "定期期限",
  termVal: "期限",
  saveSuccess: "保存成功",
  deleteSuccess: "批量删除成功",
  signDeleteSuccess: "删除成功",
  billmedia: "票据介质",
  billmediaError: "票据介质不能为空",
  draftType: "票据类型",
  draftTypeError: "票据类型不能为空",
  billSource: "票据来源",
  billSourceError: "票价来源不能为空",
  lendingCategory: "拆借品种",
  lendingCategoryError: "拆借品种不能为空",
  counterpartyClass: "交易对手分类",
  counterpartyClassError: "交易对手分类不能为空",
  replaceBusinessType: "代开业务子类型",
  replaceBusinessTypeError: "代开业务子类型不能为空",
  blankVoucherType: "重空类型",
  blankVoucherTypeError: "重空类型不能为空",
  voucherTypeName: "重空名称",
  voucherTypeNameError: "重空名称不能为空",
  productTypeCode: "产品类型",
  productTypeCodeError: "产品类型不能为空",
  productCode: "产品类型编号",
  productName: "产品类型名称",

  recording: "记录",
  strip: "条",
  add: "新增",
  close: "关闭",
  goBack: "链接查找",
  errorMessage: "数据已被操作",
  subClassError: "请至少选择一个下级分类",
  deleteFail: "批量删除失败",
  inputInfoRepeat: "录入数据重复,请检查",
  deleteCheckEdit: "存在正在编辑的行,不允许做删除操作"
};
