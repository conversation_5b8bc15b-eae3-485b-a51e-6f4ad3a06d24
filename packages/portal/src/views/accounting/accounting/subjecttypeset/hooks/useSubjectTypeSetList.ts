import { reactive, ref, computed, shallowRef, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { deleteUrl } from "../url";
import { goPage } from "../hooks/usePage";
import type { queryForm, baseInfo, deleteInfo } from "../types";
import { random } from "@/utils/uuid";
import { useConst } from "@ifs/support";
import {
  searchRegularClass,
  saveRegularClass,
  deleteRegularClass,
  searchDiscountingList,
  saveDiscountingList,
  deleteDiscountingList,
  searchIblSubClassList,
  saveIblSubClassList,
  deleteIblSubClassList,
  searchReplaceClass,
  saveReplaceClass,
  deleteReplaceClass,
  searchBlankVoucher,
  saveBlankVoucher,
  deleteBlankVoucher,
  searchInvestment,
  saveInvestment,
  deleteInvestment
} from "../url";
import httpTool from "@/utils/http";
import { FMessageBox } from "@dtg/frontend-plus";

// 跳转新增页
const add = () => {
  goPage("add");
};

// 跳转修改页
const handleOpen = (row: baseInfo) => {
  goPage("modify", { id: row.id });
};

const _queryForm = {
  officeIds: [],
  currencyIds: [],
  subjectTypeGroups: [],
  subjectType: "",
  subjectCode: ""
};

export const useSubjectTypeSetList = () => {
  const { t } = useI18n();

  const queryForm = reactive<queryForm>(JSON.parse(JSON.stringify(_queryForm)));
  // 科目类型组
  const subjectGroup = useConst("accounting.SubjectTypeGroup");
  const billMedium = useConst("accounting.BillMedium");
  const billType = useConst("accounting.BillType");
  const billSource = useConst("accounting.BillSource");
  const lendingCategoryEnum = useConst("accounting.LendingCategory");
  const lendingCategory = lendingCategoryEnum.pickConst([lendingCategoryEnum.ONE, lendingCategoryEnum.SEVEN]);
  const counterpartyClass = useConst("accounting.CounterpartyClass");
  const replaceBusinessSubType = useConst("accounting.ReplaceBusinessSubType");
  const blankVoucherType = useConst("accounting.BlankVoucherType");

  const editState = reactive<any>({});

  // 下级分类弹窗
  const dialogIsSubVisible = ref(false);

  // 表格模板
  const queryTable = shallowRef();
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };

  // 是否展示贴现下级类型
  const isTransIsSubClass = ref(false);
  // 是否展示代开下级类型
  const isReplaceBusinessSubClass = ref(false);
  // 是否展示拆借下级类型
  const isInterbankLendingSubClass = ref(false);
  // 是否展示重空下级类型
  const isBlankVoucherSubClass = ref(false);
  // 是否展示投资下级类型
  const isInvestmentSubClass = ref(false);
  // 是否展示定期下级类型
  const isInterBankFixedSubClass = ref(false);
  // 科目类型中间变量
  const subjectParams = {
    id: null,
    officeId: null,
    currencyId: null,
    subjectTypeGroup: null,
    subjectType: null,
    subClassArray: [1]
  };
  // 接口地址
  const url = {
    saveUrl: "",
    delete: ""
  };
  // 弹窗ref
  const dialogRef = ref();
  // 产品类型参数
  const productParams = {
    productTypeId: -1,
    productTypeName: ""
  };

  // 打开下级分类弹窗
  const handleOpenDialog = (row: any) => {
    dialogIsSubVisible.value = true;
    // 先将所有的下级分类置为false
    isTransIsSubClass.value = false;
    isReplaceBusinessSubClass.value = false;
    isInterbankLendingSubClass.value = false;
    isBlankVoucherSubClass.value = false;
    isInvestmentSubClass.value = false;
    isInterBankFixedSubClass.value = false;
    // 将科目类型组赋值
    Object.assign(subjectParams, row);
    subjectParams.subClassArray.splice(0); // 先清空数组
    // 中间变量
    const array = row.subClass.split(",");
    subjectParams.subClassArray = array.map(data => {
      return +data;
    });
    freshTableData(row);
  };

  // 刷新表格信息
  const freshTableData = (row: any) => {
    tableData.splice(0);
    const params = {
      glSubjectTypeSetId: row.glSubjectTypeSetId || row.id,
      // 贴现类业务查询参数
      parentId: row.glSubjectTypeSetId || row.id
    };
    // 判断科目类型组
    // 定期
    if (row.subjectTypeGroup === subjectGroup.INTERBANKREGULAR) {
      httpTool.post(searchRegularClass, params).then((res: any) => {
        search(res);
      });
      isInterBankFixedSubClass.value = true;
      url.saveUrl = saveRegularClass;
      url.delete = deleteRegularClass;
    } // 贴现
    else if (row.subjectTypeGroup === subjectGroup.TRANSDIS || row.subjectTypeGroup === subjectGroup.TRANSREDIS) {
      httpTool.post(searchDiscountingList, params).then((res: any) => {
        search(res);
      });
      isTransIsSubClass.value = true;
      url.saveUrl = saveDiscountingList;
      url.delete = deleteDiscountingList;
    } // 拆借业务
    else if (row.subjectTypeGroup === subjectGroup.INTERBANKLENDING) {
      httpTool.post(searchIblSubClassList, params).then((res: any) => {
        search(res);
      });
      isInterbankLendingSubClass.value = true;
      url.saveUrl = saveIblSubClassList;
      url.delete = deleteIblSubClassList;
    } // 代开业务
    else if (row.subjectTypeGroup === subjectGroup.REPLACEBUSINESS) {
      httpTool.post(searchReplaceClass, params).then((res: any) => {
        search(res);
      });
      isReplaceBusinessSubClass.value = true;
      url.saveUrl = saveReplaceClass;
      url.delete = deleteReplaceClass;
    } //重空业务
    else if (row.subjectTypeGroup === subjectGroup.BLANKVOUCHER) {
      httpTool.post(searchBlankVoucher, params).then((res: any) => {
        search(res);
      });
      isBlankVoucherSubClass.value = true;
      url.saveUrl = saveBlankVoucher;
      url.delete = deleteBlankVoucher;
    } // 投资业务
    else if (row.subjectTypeGroup === subjectGroup.INVESTMENT) {
      httpTool.post(searchInvestment, params).then((res: any) => {
        search(res);
      });
      isInvestmentSubClass.value = true;
      url.saveUrl = saveInvestment;
      url.delete = deleteInvestment;
    }
  };

  // 已选列表
  const checkedList = ref<baseInfo[]>([]);
  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length === 0);
  // 控制全选checkbox
  const selectableAll = (rows: baseInfo[]) => {
    checkedList.value = rows;
  };
  // 勾选checkbox
  const handleSelect = (row: baseInfo[]) => {
    checkedList.value = row;
  };
  const clearSelection = () => {
    checkedList.value.splice(0);
  };
  // 批量删除的参数
  const gatherBatchDeleteParams = () => {
    return {
      ids: checkedList.value.map(x => x.id),
      colUuids: checkedList.value.map(x => x.coluuid)
    };
  };

  const rowId = ref<number>();
  const detail = shallowRef();
  // 打开抽屉
  const handleOpenDetail = (row: baseInfo) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };

  const generalButtonOption = (row: baseInfo) => {
    return reactive([
      {
        type: "modify",
        isShow: true
      },
      {
        type: "remove",
        isShow: true,
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return {
              ids: [row.id],
              colUuids: [row.coluuid]
            };
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ]);
  };

  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true
    },
    {
      prop: "subjectType",
      label: t("accounting.accounting.subjecttypeset.subjectType"),
      slots: {
        default: "subjectType"
      },
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "officeName",
      label: t("accounting.accounting.subjecttypeset.officeName"),
      showOverflowTooltip: true
    },
    {
      width: "100px",
      prop: "currencyName",
      label: t("accounting.accounting.subjecttypeset.currencyName"),
      showOverflowTooltip: true
    },
    {
      width: "120px",
      prop: "subjectTypeGroupName",
      label: t("accounting.accounting.subjecttypeset.subjectTypeGroupName"),
      showOverflowTooltip: true
    },
    {
      width: "100px",
      prop: "isSubClassStr",
      label: t("accounting.accounting.subjecttypeset.isSubClass"),
      slots: {
        default: "isSubClass"
      },
      showOverflowTooltip: true
    },
    {
      width: "160px",
      prop: "subClassStr",
      label: t("accounting.accounting.subjecttypeset.subClassStr"),
      showOverflowTooltip: true
    },
    {
      width: "120px",
      prop: "subjectCode",
      label: t("accounting.accounting.subjecttypeset.subjectCode"),
      showOverflowTooltip: true
    },
    {
      label: t("accounting.accounting.subjecttypeset.operate"),
      width: "180px",
      prop: "operate",
      slots: {
        default: "buttons"
      },
      fixed: "right"
    }
  ];

  // 批量删除成功回调
  const handleSuccess = (row: any) => {
    if (row.success) {
      handleSearch();
    }
  };

  // 可编辑表格相关
  const tableGrid = ref();

  // 已选列表
  const subjectCheckedList = ref<deleteInfo[]>([]);
  // 是否选中checkbox
  const isSubCheckedList = computed(() => subjectCheckedList.value.length === 0);
  // 勾选checkbox
  const handleSelectionChange = (row: deleteInfo[]) => {
    subjectCheckedList.value = row;
  };
  // 批量删除参数
  const gatherBatchSubDeleteParams = () => {
    return {
      ids: subjectCheckedList.value.map(x => x.id),
      colUuids: subjectCheckedList.value.map(x => x.colUuid)
    };
  };
  // 批量删除成功回调
  const handleSubSuccess = () => {
    tableData.forEach((element: any) => {
      editState[element.randomId] = false;
    });
    subjectCheckedList.value.splice(0);
    tableGrid.value.table.clearSelection();
    tableGrid.value.updateTop();
    // 刷新表格信息
    freshTableData(subjectParams);
  };
  // 是否选中信息判断
  const tableValidator = () => {
    let result = true;
    if (subjectCheckedList.value.length === 0) {
      result = false;
    }
    let checkDeleteEdit = false;
    tableData.forEach((element: any) => {
      if (editState[element.randomId]) {
        checkDeleteEdit = true;
      }
    });
    if (checkDeleteEdit) {
      FMessageBox.report(t("accounting.accounting.subjecttypeset.deleteCheckEdit"));
      result = false;
    }
    return result;
  };

  // 列表数据
  const tableData = reactive<any[]>([]);
  const addTableRow = () => {
    const randomIdValue = random();
    tableData.push({
      id: null,
      officeId: subjectParams.officeId,
      currencyId: subjectParams.currencyId,
      subjectType: subjectParams.subjectType,
      subjectTypeGroup: subjectParams.subjectTypeGroup,
      glSubjectTypeSetId: subjectParams.id,
      subjectCode: null,
      randomId: randomIdValue,
      // 定期
      bankType: "",
      bankTypeStr: "",
      bankAccount: null,
      bankAccountStr: "",
      bankAcctName: "",
      fixedTerm: null,
      fixedTermStr: "",
      // 贴现
      billmedia: null,
      draftType: null,
      billSource: null,
      // 拆借
      lendingCategory: null,
      counterpartyClass: null,
      // 代开
      replaceBusinessType: null,
      // 重空
      bankId: null,
      blankVoucherType: null,
      voucherTypeName: null,
      // 投资
      productTypeId: null,
      productTypeCode: null,
      productTypeName: null
    });
    tableGrid.value.openEdit(randomIdValue);
    editState[randomIdValue] = true;
  };
  // 保存
  const saveTableRow = async (scope: any) => {
    // 校验必输项
    // 校验定期必输项
    if (scope.row.subjectTypeGroup === subjectGroup.INTERBANKREGULAR) {
      if ((scope.row.bankType === null || scope.row.bankType === "") && subjectParams.subClassArray.includes(1)) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.bankTypeEorer"));
        return false;
      } else if (
        (scope.row.bankAccount === null || scope.row.bankAccount === "") &&
        subjectParams.subClassArray.includes(2)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.bankAccountError"));
        return false;
      } else if (
        (scope.row.fixedTerm === null || scope.row.fixedTerm === "") &&
        subjectParams.subClassArray.includes(3)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.fixedTermError"));
        return false;
      }
      let checkResult = true;
      //校验录入的数据是否重复
      await tableData.forEach((element: any, index: number) => {
        if (scope.row.randomId !== tableData[index].randomId) {
          if (
            scope.row.subjectTypeGroup === tableData[index].subjectTypeGroup &&
            scope.row.bankAccount === tableData[index].bankAccount &&
            scope.row.fixedTerm === tableData[index].fixedTerm
          ) {
            checkResult = false;
          }
        }
      });
      if (!checkResult) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.inputInfoRepeat"));
        return false;
      }
    } // 校验贴现必输项
    else if (
      scope.row.subjectTypeGroup === subjectGroup.TRANSDIS ||
      scope.row.subjectTypeGroup === subjectGroup.TRANSREDIS
    ) {
      if ((scope.row.billmedia === null || scope.row.billmedia === "") && subjectParams.subClassArray.includes(1)) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.billmediaError"));
        return false;
      } else if (
        (scope.row.draftType === null || scope.row.draftType === "") &&
        subjectParams.subClassArray.includes(2)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.draftTypeError"));
        return false;
      } else if (
        (scope.row.billSource === null || scope.row.billSource === "") &&
        subjectParams.subClassArray.includes(3)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.billSourceError"));
        return false;
      }
      const checkResult = true;
      //校验录入的数据是否重复
      await tableData.forEach((element: any, index: number) => {
        if (scope.row.randomId !== tableData[index].randomId) {
          if (
            scope.row.billmedia === tableData[index].billmedia &&
            scope.row.draftType === tableData[index].draftType &&
            scope.row.billSource === tableData[index].billSource
          ) {
            return false;
          }
        }
      });
      if (!checkResult) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.inputInfoRepeat"));
        return false;
      }
    } // 校验拆借必输项
    else if (scope.row.subjectTypeGroup === subjectGroup.INTERBANKLENDING) {
      if (
        (scope.row.lendingCategory === null || scope.row.lendingCategory === "") &&
        subjectParams.subClassArray.includes(1)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.lendingCategoryError"));
        return false;
      } else if (
        (scope.row.counterpartyClass === null || scope.row.counterpartyClass === "") &&
        subjectParams.subClassArray.includes(2)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.counterpartyClassError"));
        return false;
      }
      const checkResult = true;
      //校验录入的数据是否重复
      await tableData.forEach((element: any, index: number) => {
        if (scope.row.randomId !== tableData[index].randomId) {
          if (
            scope.row.lendingCategory === tableData[index].lendingCategory &&
            scope.row.counterpartyClass === tableData[index].counterpartyClass
          ) {
            return false;
          }
        }
      });
      if (!checkResult) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.inputInfoRepeat"));
        return false;
      }
    } // 校验代开必输项
    else if (scope.row.subjectTypeGroup === subjectGroup.REPLACEBUSINESS) {
      if (scope.row.replaceBusinessType === null || scope.row.replaceBusinessType === "") {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.replaceBusinessTypeError"));
        return false;
      }
      const checkResult = true;
      //校验录入的数据是否重复
      await tableData.forEach((element: any, index: number) => {
        if (scope.row.randomId !== tableData[index].randomId) {
          if (scope.row.replaceBusinessType === tableData[index].replaceBusinessType) {
            return false;
          }
        }
      });
      if (!checkResult) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.inputInfoRepeat"));
        return false;
      }
    } // 校验重空必输项
    else if (scope.row.subjectTypeGroup === subjectGroup.BLANKVOUCHER) {
      if (
        (scope.row.blankVoucherType === null || scope.row.blankVoucherType === "") &&
        subjectParams.subClassArray.includes(1)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.blankVoucherTypeError"));
        return false;
      } else if (
        (scope.row.voucherTypeName === null || scope.row.voucherTypeName === "") &&
        subjectParams.subClassArray.includes(2)
      ) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.voucherTypeNameError"));
        return false;
      }
      const checkResult = true;
      //校验录入的数据是否重复
      await tableData.forEach((element: any, index: number) => {
        if (scope.row.randomId !== tableData[index].randomId) {
          if (
            scope.row.blankVoucherType === tableData[index].blankVoucherType &&
            scope.row.voucherTypeName === tableData[index].voucherTypeName
          ) {
            return false;
          }
        }
      });
      if (!checkResult) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.inputInfoRepeat"));
        return false;
      }
    } // 校验投资必输项
    else if (scope.row.subjectTypeGroup === subjectGroup.INVESTMENT) {
      if (scope.row.productTypeCode === null || scope.row.productTypeCode === "") {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.productTypeCodeError"));
        return false;
      }
      const checkResult = true;
      //校验录入的数据是否重复
      await tableData.forEach((element: any, index: number) => {
        if (scope.row.randomId !== tableData[index].randomId) {
          if (scope.row.productTypeCode === tableData[index].productTypeCode) {
            return false;
          }
        }
      });
      if (!checkResult) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.inputInfoRepeat"));
        return false;
      }
    }
    // 校验科目编号
    if (scope.row.subjectCode === null || scope.row.subjectCode === "") {
      FMessageBox.report(t("accounting.accounting.subjecttypeset.subjectCodeError"));
      return false;
    }
    // 投资业务 判断产品类型是否为空 投资业务时对产品id及名称进行赋值
    if (scope.row.productTypeCode !== null) {
      // 判断是否重新选择了产品类型
      if (productParams.productTypeId > 0 && productParams.productTypeName !== "") {
        scope.row.productTypeId = productParams.productTypeId;
        scope.row.productTypeName = productParams.productTypeName;
      }
    }
    editState[scope.row.randomId] = false;
    // 保存方法调用
    save(url.saveUrl, scope);
  };
  // 删除行
  const deleteTableRow = (scope: any) => {
    //已保存
    if (scope.row.id !== null) {
      let checkDeleteEdit = false;
      tableData.forEach((element: any) => {
        if (editState[element.randomId]) {
          checkDeleteEdit = true;
        }
      });
      if (checkDeleteEdit) {
        FMessageBox.report(t("accounting.accounting.subjecttypeset.deleteCheckEdit"));
        return false;
      }
      const params = {
        ids: [scope.row.id],
        colUuids: [scope.row.colUuid]
      };
      subjectCheckedList.value.splice(0);
      deleteMethod(url.delete, params, scope);
      //未保存
    } else {
      return true;
    }
  };

  // 查询
  const search = (res: any) => {
    if (res.success) {
      tableData.splice(0);
      Object.assign(tableData, res.data);
      // 组装公共参数
      tableData.forEach((element: any, index: number) => {
        tableData[index].randomId = random();
        tableData[index].subjectType = subjectParams.subjectType;
        tableData[index].officeId = subjectParams.officeId;
        tableData[index].currencyId = subjectParams.currencyId;
        tableData[index].subjectTypeGroup = subjectParams.subjectTypeGroup;
      });
    }
  };

  // 保存
  const save = (url: any, scope: any) => {
    httpTool.post(url, scope.row).then((res: any) => {
      if (res.success) {
        FMessageBox.report(
          { type: "success", message: t("accounting.accounting.subjecttypeset.saveSuccess") },
          {
            appendTo: "dialogRef.value",
            callback: () => {
              //关闭当前行编辑状态
              tableGrid.value.closeEdit(scope.row.randomId);
              freshTableData(scope.row);
            }
          }
        );
      } else {
        FMessageBox.report(res.message.description);
      }
    });
  };

  // 删除
  const deleteMethod = (url: any, param: any, scope: any) => {
    httpTool.post(url, param).then((res: any) => {
      if (res.success) {
        FMessageBox.report(
          { type: "success", message: t("accounting.accounting.subjecttypeset.signDeleteSuccess") },
          {
            callback: () => {
              freshTableData(scope.row);
            }
          }
        );
      }
    });
  };

  // 票据介质formatter
  const billMediumFormatter = (row: any) => {
    return billMedium.valueToLabel(row.billmedia);
  };
  // 票据类型formatter
  const billTypeFormatter = (row: any) => {
    return billType.valueToLabel(row.draftType);
  };
  // 票据来源formatter
  const billSourceFormatter = (row: any) => {
    return billSource.valueToLabel(row.billSource);
  };
  // 拆解品种formatter
  const lendingCategoryFormatter = (row: any) => {
    return lendingCategory.valueToLabel(row.lendingCategory);
  };
  // 交易对手分类formatter
  const counterpartyClassFormatter = (row: any) => {
    return counterpartyClass.valueToLabel(row.counterpartyClass);
  };
  // 代开业务子类型formatter
  const replaceBusinessSubTypeFormatter = (row: any) => {
    return replaceBusinessSubType.valueToLabel(row.replaceBusinessType);
  };
  // 重空类型formatter
  const blankVoucherTypeFormatter = (row: any) => {
    return blankVoucherType.valueToLabel(row.blankVoucherType);
  };
  // 产品类型放大镜
  const holdProductTypeCode = (row: any) => {
    productParams.productTypeCode = row.code;
    productParams.productTypeId = row.id;
    productParams.productTypeName = row.name;
  };
  // 下级分类关闭后回调
  const clearSubSelection = () => {
    subjectCheckedList.value.splice(0);
  };
  // 定期 银行账户放大镜
  const handleBankAccountChange = (row: any, scope: any) => {
    scope.bankAccount = row.id;
    scope.bankAccountStr = row.bankAccountNo;
    scope.bankAcctName = row.bankAccountName;
  };
  // 定期 银行类型放大镜
  const handleBankTypeChange = (row: any, scope: any) => {
    scope.bankType = row.code;
    scope.bankTypeStr = row.name;
  };
  // 定期 定期期限放大镜
  const handleFixTermChange = (row: any, scope: any) => {
    scope.fixedTerm = row.id;
    scope.fixedTermStr = row.termShow;
  };

  // 重空 银行类别放大镜
  const blankVoucherBankCheck = (row: any, scope: any) => {
    scope.bankId = row.id;
    scope.bankType = row.code;
    scope.bankTypeStr = row.name;
  };
  // 判断当前列表项是由含有下级分类
  const checkIsSubClassShow = (row: baseInfo) => {
    let result = true;
    if (
      row.subjectTypeGroup === subjectGroup.INTERBANKCURRENT ||
      row.subjectTypeGroup === subjectGroup.TRANSFERCREDITASSETS ||
      row.subjectTypeGroup === subjectGroup.INTERBANKDEPOSITCERTIFICATE
    ) {
      result = false;
    }
    return result;
  };

  const deleteResultConfirm = {
    success: t("accounting.accounting.subjecttypeset.deleteSuccess"),
    fail: t("accounting.accounting.subjecttypeset.deleteFail")
  };

  const allowSort = ["subjectType", "officeName", "currencyName", "subjectTypeGroupName", "subjectCode"];

  const subClassClearSelection = () => {
    subjectCheckedList.value.splice(0);
  };
  return {
    queryForm,
    tableColumns,
    add,
    isChecked,
    selectableAll,
    handleOpen,
    rowId,
    detail,
    queryTable,
    dialogIsSubVisible,
    handleOpenDetail,
    handleSelect,
    generalButtonOption,
    gatherBatchDeleteParams,
    handleSearch,
    handleSuccess,
    clearSelection,
    handleOpenDialog,
    tableGrid,
    tableData,
    addTableRow,
    saveTableRow,
    deleteTableRow,
    isTransIsSubClass,
    isReplaceBusinessSubClass,
    isInterbankLendingSubClass,
    isBlankVoucherSubClass,
    isInvestmentSubClass,
    isInterBankFixedSubClass,
    dialogRef,
    subjectParams,
    billMediumFormatter,
    billTypeFormatter,
    billSourceFormatter,
    lendingCategoryFormatter,
    counterpartyClassFormatter,
    replaceBusinessSubTypeFormatter,
    blankVoucherTypeFormatter,
    holdProductTypeCode,
    handleSelectionChange,
    url,
    isSubCheckedList,
    gatherBatchSubDeleteParams,
    handleSubSuccess,
    tableValidator,
    clearSubSelection,
    handleBankAccountChange,
    blankVoucherBankCheck,
    checkIsSubClassShow,
    allowSort,
    handleBankTypeChange,
    handleFixTermChange,
    deleteResultConfirm,
    subClassClearSelection
  };
};

export default useSubjectTypeSetList;
