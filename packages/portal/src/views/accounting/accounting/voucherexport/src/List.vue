<template>
  <f-query-scene :title="t('accounting.accounting.voucherexport.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :table-columns="tableColumns"
        :url="search"
        border
        :form-data="queryForm"
        show-header
        auto-reset
        auto-init
        :show-collapse="false"
        :show-print="false"
        tile-panel
        @select="handleSelect"
        @select-all="selectableAll"
        :show-count-value="false"
        @clear-selection="clearSelection"
        :count-label="t('accounting.accounting.voucherexport.recording')"
        :count-label-unit="t('accounting.accounting.voucherexport.strip')"
        :show-summation-sum="false"
        :summation-biz-label="t('accounting.accounting.voucherexport.recording')"
        :summation-biz-unit="t('accounting.accounting.voucherexport.strip')"
        query-comp-id="accounting-accounting-voucherexport-query"
        table-comp-id="accounting-accounting-voucherexport-table"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :allow-sort="allowSort"
      >
        <template #operate>
          <f-button type="primary" @click="add">{{ t("accounting.accounting.voucherexport.add") }}</f-button>
        </template>
        <template #query-panel>
          <f-form-item :label="t('accounting.accounting.voucherexport.officeName')" prop="officeIds">
            <f-select
              v-model="queryForm.officeIds"
              value-key="officeId"
              label="officeName"
              :url="queryOffice"
              method="post"
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.voucherexport.currencyName')" prop="currencyIds">
            <f-select
              v-model="queryForm.currencyIds"
              value-key="currencyId"
              label="currencyName"
              :url="queryCurrency"
              method="post"
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.voucherexport.transTypeName')" prop="transTypeIds">
            <f-select
              v-model="queryForm.transTypeIds"
              value-key="key"
              label="value"
              :url="queryTransType"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.voucherexport.subjectCode')" prop="subjectCode">
            <f-input v-model="queryForm.subjectCode" maxlength="30" />
          </f-form-item>
          <!-- 导账日期 -->
          <f-form-item :label="t('accounting.accounting.voucherexport.postDate')" prop="postDate">
            <f-date-picker v-model="queryForm.postDate" />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.voucherexport.postStatus')" prop="postStatusList">
            <f-select
              v-model="queryForm.postStatusList"
              :data="postStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item
            :label="t('accounting.accounting.voucherexport.voucherSummaryStatus')"
            prop="voucherSummaryStatusList"
          >
            <f-select
              v-model="queryForm.voucherSummaryStatusList"
              :data="voucherSummaryStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('accounting.accounting.voucherexport.voucherCode')" prop="voucherCode">
            <f-input v-model="queryForm.voucherCode" maxlength="30" />
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="handleOpen(row)" />
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { search, exportUrl, queryOffice, queryCurrency, queryTransType } from "../url";
import useList from "../hooks/useList";
import OperateButton from "@/components/operate-button/operate-button";
import { useConst } from "@ifs/support";
import { onMounted } from "vue";

const { t } = useI18n();
const voucherSummaryStatus = useConst("accounting.VoucherSummaryStatus");
const postStatusEnum = useConst("accounting.PostStatus");
const postStatus = postStatusEnum.pickConst([
  postStatusEnum.UNPOST,
  postStatusEnum.SENDSUCCESS,
  postStatusEnum.SENDFAIL,
  postStatusEnum.ACCOUNTSUCCESS,
  postStatusEnum.ACCOUNTFAIL
]);

const {
  queryForm,
  add,
  tableColumns,
  selectableAll,
  handleOpen,
  handleSelect,
  generalButtonOption,
  clearSelection,
  queryTable,
  allowSort,
  getOpenDate
} = useList();
onMounted(() => {
  getOpenDate();
});
</script>
