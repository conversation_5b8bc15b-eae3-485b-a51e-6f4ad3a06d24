<template>
  <f-button type="primary" @click="handleAdd">
    {{ t("views.add") }}
  </f-button>
  <f-submit-state
    operate="submit"
    :gather-params="getBatchParams"
    :url="submitBatchUrl"
    :disabled="submitDisabled"
    is-batch
    compatible
    @close="doFreshTable"
  />
  <f-submit-state
    operate="remove"
    type="danger"
    :gather-params="getBatchParams"
    :url="deleteBatchUrl"
    :disabled="removeDisabled"
    is-batch
    compatible
    @close="doFreshTable"
  />
  <!-- <f-submit-state -->
  <!-- operate="cancel" -->
  <!-- :gather-params="getBatchParams" -->
  <!-- :url="revokeBatchUrl" -->
  <!-- :operate-name="t('views.revokeBtch')" -->
  <!-- :confirm-text="t('views.cancelConfirm')" -->
  <!-- :result-confirm="t('views.cancelSuccess')" -->
  <!-- :result-title="t('views.revokeBtch')" -->
  <!-- :disabled="revokeDisabled" -->
  <!-- is-batch -->
  <!-- @close="doFreshTable" -->
  <!-- /> -->
</template>
<script setup lang="ts">
import { computed, reactive } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  table: {
    type: Object,
    required: true
  },
  statusEnum: {
    type: Array,
    required: true
  },
  revokeBatchUrl: {
    type: String,
    required: true
  },
  deleteBatchUrl: {
    type: String,
    required: true
  },
  submitBatchUrl: {
    type: String,
    required: true
  }
});

const state = reactive({
  checkedList: []
});

const doFreshTable = () => {
  props.table.renderTableData();
  state.checkedList.length = 0;
  props.table.clearSelection();
};

const getBatchParams = () => {
  return {
    list: state.checkedList
  };
};

const controlOperateDisabled = (excludeStatus: unknown[], attr: string) => {
  return computed(() => {
    let result = false;
    const enumCodeList = excludeStatus.map(es => {
      return es.code;
    });
    if (state.checkedList.length === 0) {
      result = true;
    }
    if (!result) {
      for (const item of state.checkedList) {
        if (enumCodeList.includes(item[attr])) {
          result = true;
          break;
        }
      }
    }
    return result;
  });
};

const removeDisabled = controlOperateDisabled(props.statusEnum.omitConst(["SAVE", "REFUSE"]), "businessStatus");
const submitDisabled = controlOperateDisabled(props.statusEnum.omitConst(["SAVE", "REFUSE"]), "businessStatus");

const emits = defineEmits(["on-add"]);

const handleAdd = () => {
  emits("on-add");
};

const methods = {
  changeCheckedList(list) {
    state.checkedList = list;
  }
};
defineExpose(methods);
</script>
