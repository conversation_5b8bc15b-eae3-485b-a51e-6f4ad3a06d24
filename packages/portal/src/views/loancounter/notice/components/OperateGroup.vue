<template>
  <OperateButton :options="operateButtonOption(row)" @on-modify="handleModify(row)">
    <template #suffix v-if="row.businessStatus === 'APPROVED'">
      <f-submit-state
        :gather-params="() => row"
        :url="revokeUrl"
        operate="cancel"
        :is-batch="false"
        :operate-name="t('common.operate.cancel')"
        confirm-text=" "
        :icon="DtgCopy"
        link
        :is-show-result-btn-group="false"
        :result-confirm="t('common.operate.cancelSuccess')"
        @close="handleOperateSuccess"
      >
        <template #confirmEdit>
          <f-multi-form-panel :model="cancelModel">
            <f-form-item :label="t('common.operate.cancelReason')" prop="revokeReason">
              <f-input v-model="cancelModel.revokeReason" size="large" maxlength="50" />
            </f-form-item>
          </f-multi-form-panel>
        </template>
      </f-submit-state>
    </template>
  </OperateButton>
</template>
<script setup lang="ts">
import { reactive } from "vue";
import { useI18n } from "vue-i18n";
import { DtgCopy } from "@dtg/frontend-plus-icons";

const { t } = useI18n();

const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  revokeUrl: {
    type: String,
    required: true
  },
  deleteUrl: {
    type: String,
    required: true
  },
  submitUrl: {
    type: String,
    required: true
  }
});

const emits = defineEmits(["on-modify", "operate-success"]);

const operateButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: ["SAVE", "REFUSE"].includes(row.businessStatus)
    },
    {
      type: "submit",
      isShow: ["SAVE", "REFUSE"].includes(row.businessStatus),
      submitComOpt: {
        url: props.submitUrl,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    },
    {
      type: "remove",
      isShow: ["SAVE", "REFUSE"].includes(row.businessStatus),
      submitComOpt: {
        url: props.deleteUrl,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    }
  ];
};

const cancelModel = reactive({
  revokeReason: ""
});

const handleModify = (row: Record<string, any>) => {
  emits("on-modify", row);
};

const handleOperateSuccess = res => {
  emits("operate-success", res.data);
};
</script>
