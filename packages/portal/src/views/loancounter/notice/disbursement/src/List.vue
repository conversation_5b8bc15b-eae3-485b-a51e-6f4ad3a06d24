<template>
  <f-query-scene :title="t('loancounter.notice.disbursement.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTableRef"
        row-key="id"
        query-comp-id="loancounter-notice-disbursement-query-001"
        table-comp-id="loancounter-notice-disbursement-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :form-data="queryForm"
        show-header
        auto-reset
        auto-init
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
        :post-params="postParams"
        :label-width="150"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
      >
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item :label="t('views.biz.officeName')" prop="officeIdList">
            <f-select
              v-model="queryForm.officeIdList"
              :url="getOfficeInfoUrl"
              value-key="officeId"
              label="officeName"
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item :label="t('views.biz.currencyName')" prop="currencyIdList">
            <f-select
              v-model="queryForm.currencyIdList"
              value-key="currencyId"
              label="currencyName"
              :data="globalCurrencyList"
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 贷款业务种类 多选 下拉 -->
          <f-form-item :label="t('loancounter.notice.disbursement.loanType')" prop="loanTypeList">
            <f-select
              v-model="queryForm.loanTypeList"
              :data="loanTypeData"
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 支付通知单号 模糊 文本框 -->
          <f-form-item :label="t('loancounter.notice.disbursement.loanNoticeNo')" prop="receiptClientCode">
            <f-input v-model="queryForm.loanPayBatchCode" />
          </f-form-item>
          <!-- 合同编号 单选 放大镜 -->
          <f-form-item :label="t('loancounter.notice.disbursement.contractCode')" prop="contractCode">
            <f-magnifier-single
              v-model="queryForm.contractCode"
              :title="t('loancounter.notice.disbursement.contractCode')"
              :url="getContractListUrl"
              method="post"
              row-key="contractCode"
              row-label="contractCode"
              input-key="contractCode"
              filterable
            >
              <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.disbursement.contractCode')" />
              <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.disbursement.loanClientName')" />
              <f-magnifier-column
                prop="contractAmount"
                :label="t('loancounter.notice.disbursement.contractAmount')"
                :formatter="{ name: 'amount' }"
              />
              <f-magnifier-column
                prop="contractEndDate"
                :label="t('loancounter.notice.disbursement.contractEndDate')"
              />
            </f-magnifier-single>
          </f-form-item>
          <!-- 付款单位名称 多选 放大镜 -->
          <f-form-item :label="t('loancounter.notice.disbursement.payClientName')" prop="payClientIdList">
            <f-magnifier-multi
              :title="t('loancounter.notice.disbursement.payClientName')"
              :url="getPayClientInfoUrl"
              v-model="queryForm.payClientIdList"
              method="post"
              row-key="clientId"
              selected-key="clientCode"
              selected-label="clientName"
              row-label="clientCode"
              input-key="clientCodeOrName"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.disbursement.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loancounter.notice.disbursement.clientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 支付方式 下拉框 -->
          <f-form-item :label="t('loancounter.notice.disbursement.payMethod')" prop="loanPayMethod">
            <f-select
              v-model="queryForm.loanPayMethod"
              :data="loanPayMethodData"
              clearable
              blank-option
              init-if-blank
              filterable
              collapse-tags
            />
          </f-form-item>
          <!-- 数据来源 下拉框 -->
          <f-form-item :label="t('loancounter.notice.disbursement.dataSource')" prop="dataSource">
            <f-select
              v-model="queryForm.dataSource"
              :data="creditDataSourceData"
              clearable
              blank-option
              init-if-blank
              filterable
              collapse-tags
            />
          </f-form-item>
          <!-- 录入日期 区间 -->
          <f-form-item :label="t('loancounter.notice.disbursement.inputDate')" prop="inputDate">
            <f-lax-range-date-picker v-model="queryForm.inputDate" />
          </f-form-item>
          <!-- 单据状态 多选下拉 -->
          <f-form-item :label="t('loancounter.notice.disbursement.status')" prop="businessStatusList">
            <f-select
              v-model="queryForm.businessStatusList"
              :data="
                businessStatusEnum.pickConst([
                  businessStatusEnum.SAVE,
                  businessStatusEnum.APPROVING,
                  businessStatusEnum.REFUSE,
                  businessStatusEnum.APPROVED,
                  businessStatusEnum.USED,
                  businessStatusEnum.SETTLED,
                  businessStatusEnum.SOME_SETTLE,
                  businessStatusEnum.CANCELLING,
                  businessStatusEnum.CANCEL
                ])
              "
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
        </template>
        <template #operate>
          <f-button type="primary" @click="goAdd">{{ t("views.add") }}</f-button>
          <!-- 批量提交 -->
          <f-submit-state
            :is-batch="true"
            operate="submit"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="batchSubmitUrl"
            :confirm-text="submitMessage"
            :before-trigger="beforeSubmitTrigger"
            :batch-confirm-map="submitResultConfirm"
            @close="row => row.success && handleSearch()"
          />
          <!-- 批量删除 -->
          <f-submit-state
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="batchDeleteUrl"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteResultConfirm"
            @close="row => row.success && handleSearch()"
          />
        </template>
        <template #noticeCode="{ row }">
          <f-button @click="goDetail(row)" link type="primary">{{ row.loanPayBatchCode }}</f-button>
        </template>
        <template #verifier="{ row }">
          <f-input
            v-model="row.verifier"
            maxlength="50"
            :disabled="row.riskCheckResultType !== riskTypeEnum.WARNING || row.riskCheckFlag === yesOrNo.YES"
          />
        </template>
        <template #verifiMethod="{ row }">
          <f-input
            v-model="row.verifiMethod"
            maxlength="50"
            :disabled="row.riskCheckResultType !== riskTypeEnum.WARNING || row.riskCheckFlag === yesOrNo.YES"
          />
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="goModify(row)">
            <template #prefix>
              <f-submit-state
                v-if="row.businessStatus === businessStatusEnum.APPROVED"
                :gather-params="() => row"
                :url="cancelUrl"
                operate="cancel"
                :is-batch="false"
                type="danger"
                :operate-name="t('views.undo')"
                confirm-text=" "
                link
                :is-show-result-btn-group="false"
                :beforeConfirm="handleUndoConfirm"
                @close="handleSearch"
              >
                <template #confirmEdit>
                  <f-multi-form-panel ref="undoFormRef" :model="row">
                    <f-form-item :label="t('views.undoReason')" prop="cancelReason" required>
                      <f-input v-model="row.cancelReason" maxlength="150" />
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
            </template>
          </OperateButton>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detailRef" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useCurrency } from "@/hooks/useCommon";
import { useBatchOperate } from "@/hooks/biz/useBatchOperate";
import { useCommonList } from "@/hooks/biz/useCommonList";
import useList from "../hooks/useList";
import {
  getOfficeInfoUrl,
  listUrl,
  exportUrl,
  batchDeleteUrl,
  batchSubmitUrl,
  deleteUrl,
  listSubmitUrl,
  cancelUrl,
  getContractListUrl,
  getPayClientInfoUrl
} from "../url";
import { KEY } from "../types";
import Detail from "./components/Detail.vue";

const { t } = useI18n();
const yesOrNo = useConst("common.YesOrNo");
const loanTypeDataEnum = useConst("loancounter.LoanType");
const loanTypeData = loanTypeDataEnum.pickConst([
  loanTypeDataEnum.TRUST_LOAN,
  loanTypeDataEnum.CONSIGN_LOAN,
  loanTypeDataEnum.FACTORING
]);
const loanPayMethodData = useConst("counter.LoanPayMethod");
const creditDataSourceData = useConst("credit.CreditDataSource");
const businessStatusEnum = useConst("common.BusinessStatus");
const riskTypeEnum = useConst("loancounter.RiskResultType");
// 币种列表
const { globalCurrencyList } = useCurrency();

const {
  checkedList,
  selectableAll,
  submitMessage,
  isChecked,
  submitResultConfirm,
  deleteResultConfirm,
  beforeDeleteTrigger,
  beforeSubmitTrigger,
  handleSelect,
  clearSelection
} = useBatchOperate();

const gatherBatchParams = () => {
  return checkedList.value;
};

const {
  rowId,
  goAdd,
  goDetail,
  goModify,
  detailRef,
  queryTableRef,
  handleSearch,
  generalButtonOption,
  undoFormRef,
  handleUndoConfirm
} = useCommonList(KEY, {
  submitUrl: listSubmitUrl,
  deleteUrl,
  submitParams: {
    pageListSubmitFlag: yesOrNo.YES
  },
  forceRefreshList: true
});

const { tableColumns, queryForm, allowSort, postParams } = useList();
</script>
