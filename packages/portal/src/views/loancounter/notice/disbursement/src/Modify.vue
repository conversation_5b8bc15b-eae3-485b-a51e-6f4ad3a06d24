<template>
  <f-blank-scene :title="t('loancounter.notice.disbursement.modifyTitle')" :isPoint="false">
    <f-multi-form-panel ref="formRef" :model="dto" :label-width="140" :rules="rules" :column="3">
      <!-- 基本信息 -->
      <f-panel :title="t('loancounter.notice.disbursement.basicInfo')">
        <!-- 机构 -->
        <f-form-item :label="t('loancounter.notice.disbursement.office')" prop="officeId" required>
          <f-select
            v-model="dto.officeId"
            auto-select
            clearable
            value-key="officeId"
            label="officeName"
            :url="getOfficeInfoUrl"
            @change="handleOfficeChange"
            :disabled="modifyDisabledFlag"
          />
        </f-form-item>
        <!-- 币种 -->
        <f-form-item :label="t('loancounter.notice.disbursement.currency')" prop="currencyId" required>
          <f-select
            v-model="dto.currencyId"
            auto-select
            clearable
            value-key="currencyId"
            label="currencyName"
            :data="currencyData"
            @change="handleCurrencyChange"
            :disabled="modifyDisabledFlag"
          />
        </f-form-item>
        <!-- 贷款类型 -->
        <f-form-item :label="t('loancounter.notice.disbursement.loanType')" prop="loanType" required>
          <f-select v-model="dto.loanType" :data="loanTypeData" disabled />
        </f-form-item>
        <!-- 付款单位名称 -->
        <f-form-item :label="t('loancounter.notice.disbursement.payClientName')" prop="payClientId" required>
          <f-magnifier-single
            :title="t('loancounter.notice.disbursement.payClientName')"
            :url="getPayClientInfoUrl"
            v-model="dto.payClientId"
            row-key="clientId"
            row-label="clientName"
            input-key="clientCode"
            auto-init
            :params="{
              currencyId: dto.currencyId,
              officeId: dto.officeId
            }"
            @change="payClientInfoChange"
            @clear="payClientInfoClear"
            :disabled="modifyDisabledFlag"
          >
            <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.disbursement.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('loancounter.notice.disbursement.clientName')" />
          </f-magnifier-single>
        </f-form-item>
        <!-- 合同编号 -->
        <f-form-item :label="t('loancounter.notice.disbursement.contractCode')" prop="contractCode" required>
          <f-magnifier-single
            v-model="dto.contractCode"
            :title="t('loancounter.notice.disbursement.contractCode')"
            :url="getContractListUrl"
            method="post"
            row-key="contractCode"
            row-label="contractCode"
            input-key="contractCode"
            auto-init
            :params="{
              loanTypeList: [
                loanTypeData.TRUST_LOAN,
                loanTypeData.CONSIGN_LOAN,
                loanTypeData.SYNDICATED_LOAN,
                loanTypeData.DISCOUNT,
                loanTypeData.FACTORING,
                loanTypeData.BILL_ACCEPTANCE,
                loanTypeData.OVERDRAW,
                loanTypeData.ACCEPTANCE_ADVANCE,
                loanTypeData.GUARANTEE_ADVANCE
              ]
            }"
            @change="changeContractCode"
            @clear="clearContractCode"
            :disabled="modifyDisabledFlag"
          >
            <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.disbursement.contractCode')" />
            <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.disbursement.loanClientName')" />
            <f-magnifier-column
              prop="contractAmount"
              :label="t('loancounter.notice.disbursement.contractAmount')"
              :formatter="{ name: 'amount' }"
            />
            <f-magnifier-column prop="contractEndDate" :label="t('loancounter.notice.disbursement.contractEndDate')" />
          </f-magnifier-single>
        </f-form-item>
        <!-- 支付方式 -->
        <f-form-item :label="t('loancounter.notice.disbursement.payMethod')" prop="loanPayMethod">
          <f-select
            v-model="dto.loanPayMethod"
            :data="loanPayMethodData"
            :disabled="dto.allEntrustPayFlag === yesOrNoData.YES || modifyDisabledFlag"
          />
        </f-form-item>
      </f-panel>
      <!-- 贷款合同资料 -->
      <f-panel :title="t('loancounter.notice.disbursement.loanContractInfo')">
        <!-- 合同金额 -->
        <f-form-item :label="t('loancounter.notice.disbursement.contractAmount')" prop="contractAmount">
          <f-amount v-model="dto.contractAmount" :symbol="currencySymbol" tooltip disabled />
        </f-form-item>
        <!-- 期限 -->
        <f-form-item :label="t('loancounter.notice.disbursement.contractTerm')" prop="contractTerm">
          <f-number v-model="dto.contractTerm" max="999" :whole-number="true" disabled>
            <template #suffix>
              <span>{{ t("loancounter.notice.disbursement.month") }}</span>
            </template>
          </f-number>
        </f-form-item>
        <!-- 开始日 -->
        <f-form-item :label="t('loancounter.notice.disbursement.contractStartDate')" prop="contractStartDate">
          <f-date-picker v-model="dto.contractStartDate" type="date" disabled />
        </f-form-item>
        <!-- 到期日 -->
        <f-form-item :label="t('loancounter.notice.disbursement.contractEndDate')" prop="contractEndDate">
          <f-date-picker v-model="dto.contractEndDate" type="date" disabled />
        </f-form-item>
        <!-- 执行利率 -->
        <f-form-item :label="t('loancounter.notice.disbursement.ExecuteRate')" prop="execRate">
          <f-number v-model="dto.execRate" is-rate disabled />
        </f-form-item>
        <!-- 是否包含下级单位 -->
        <f-form-item :label="t('loancounter.notice.disbursement.subordinateUnitsFlag')" prop="subordinateUnitsFlag">
          <f-select
            v-model="dto.subordinateUnitsFlag"
            :data="yesOrNoData"
            :disabled="unitsDisabledFlag || modifyDisabledFlag"
            @change="subordinateUnitsChange"
          />
        </f-form-item>
      </f-panel>
      <!-- 放款通知单资料 -->
      <f-panel :title="t('loancounter.notice.disbursement.receiptInfo')">
        <f-form-item :employ="3">
          <f-table
            ref="receiptTable"
            :data="dto.payDetailList"
            style="width: 100%"
            @select="receiptHandleSelectionChange"
            @selection-change="receiptHandleSelectionChange"
            border
          >
            <f-table-column
              type="selection"
              :label="t('loancounter.notice.disbursement.order')"
              :selectable="row => row.loanPayAlreadyAmount <= 0 && !modifyDisabledFlag"
            />
            <!-- 放款通知单号 -->
            <f-table-column
              prop="receiptCode"
              :label="t('loancounter.notice.disbursement.receiptCode')"
              sortable="false"
            />
            <!-- 放款日期 -->
            <f-table-column
              prop="loanDate"
              :label="t('loancounter.notice.disbursement.loanPayDate')"
              sortable="false"
            />
            <!-- 通知单金额 -->
            <f-table-column
              prop="payAmount"
              :label="t('loancounter.notice.disbursement.receiptAmount')"
              sortable="false"
            />
            <!-- 已支付金额 -->
            <f-table-column
              prop="loanPayAlreadyAmount"
              :label="t('loancounter.notice.disbursement.loanPayAlreadyAmount')"
              sortable="false"
            />
            <!-- 可支付金额 -->
            <f-table-column
              prop="loanPayUsableAmount"
              :label="t('loancounter.notice.disbursement.loanPayUsableAmount')"
              sortable="false"
            />
            <!-- 本次支付金额 -->
            <f-table-column
              prop="loanPayAmount"
              :label="t('loancounter.notice.disbursement.loanPayAmount')"
              sortable="false"
            />
          </f-table>
        </f-form-item>
      </f-panel>
      <!-- 收款方信息 -->
      <f-panel :title="t('loancounter.notice.disbursement.receiveInfo')">
        <f-form-item :employ="3">
          <div>
            <f-button type="primary" @click="handleBatchDelete" :disabled="modifyDisabledFlag">
              {{ t(`loancounter.notice.disbursement.batchDelete`) }}
            </f-button>
            <f-import-scene
              :buttonText="t(`loancounter.notice.disbursement.excelImport`)"
              :on-success="handleImportSuccess"
              :url="receiveImportUrl"
              :download-url="downloadUrl"
              :disabled="modifyDisabledFlag"
            />
          </div>
          <f-table-edit
            ref="receiveTable"
            row-key="randomId"
            :data="dto.receiveDetailList"
            style="width: 100%"
            :before-close-edit="handleReceiveRowClose"
            :after-delete="handleAfterDelete"
            @add-row="handleReceiveRowAdd"
            @selection-change="handleSelectionChange"
            border
            :show-operate-delete="!modifyDisabledFlag"
            :show-add-btn="!modifyDisabledFlag"
          >
            <f-table-column type="selection" />
            <!-- 收款方账户类型 -->
            <f-table-column
              prop="recAcctType"
              :label="t('loancounter.notice.disbursement.receiveAccountType')"
              :formatter="row => recAcctTypeData.valueToLabel(row.recAcctType)"
            >
              <template #edit="scope">
                <f-select v-model="scope.row.recAcctType" :data="recAcctTypeData" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 是否对私类型 -->
            <f-table-column
              prop="isPaymentPrivate"
              :label="t('loancounter.notice.disbursement.isPaymentPrivate')"
              :formatter="row => yesOrNoData.valueToLabel(row.isPaymentPrivate)"
            >
              <template #edit="scope">
                <f-select
                  v-if="scope.row.recAcctType === recAcctTypeData.BANK_ACCOUNT"
                  v-model="scope.row.isPaymentPrivate"
                  :data="yesOrNoData"
                  :disabled="modifyDisabledFlag"
                />
                <f-select
                  v-if="scope.row.recAcctType !== recAcctTypeData.BANK_ACCOUNT"
                  v-model="scope.row.isPaymentPrivate"
                  :data="yesOrNoData.pickConst([yesOrNoData.NO])"
                  auto-select
                  disabled
                />
              </template>
            </f-table-column>
            <!-- 支付日期 -->
            <f-table-column prop="loanPayDate" :label="t('loancounter.notice.disbursement.payDate')">
              <template #edit="scope">
                <f-date-picker v-model="scope.row.loanPayDate" type="date" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 支付金额 -->
            <f-table-column prop="loanPayAmount" :label="t('loancounter.notice.disbursement.payAmount')">
              <template #edit="scope">
                <f-amount v-model="scope.row.loanPayAmount" :symbol="currencySymbol" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 收款人账号 -->
            <f-table-column :label="t('loancounter.notice.disbursement.receiveAccountNo')" prop="recAcctNo">
              <template #edit="scope">
                <f-magnifier-single
                  v-if="scope.row.recAcctType !== recAcctTypeData.BANK_ACCOUNT"
                  v-model="scope.row.recAcctNo"
                  :title="t('loancounter.notice.disbursement.receiveAccountNo')"
                  :url="queryInnerAccountInfoUrl"
                  row-key="accountCode"
                  row-label="accountCode"
                  filterable
                  :params="{
                    officeId: dto.officeId,
                    currencyId: dto.currencyId,
                    clientIdList: dto.clientIdList
                  }"
                  :selected-data="{
                    accountCode: scope.row.recAcctNo
                  }"
                  @change="handleInnerAccountChange($event, scope.row)"
                  :disabled="modifyDisabledFlag"
                >
                  <!-- 账户编号 -->
                  <f-magnifier-column prop="accountCode" :label="t('views.biz.acctNo')" filter-input />
                  <!-- 账户名称 -->
                  <f-magnifier-column prop="accountName" :label="t('views.biz.acctName')" filter-input />
                </f-magnifier-single>
                <f-magnifier-single
                  v-if="scope.row.recAcctType === recAcctTypeData.BANK_ACCOUNT"
                  v-model="scope.row.recBankAcctNo"
                  :title="t('loancounter.notice.disbursement.recBankAcctNo')"
                  :url="queryExternalAccountInfoUrl"
                  row-key="acctNo"
                  row-label="acctNo"
                  filterable
                  :auto-init="false"
                  @change="handleExternalAccountChange($event, scope.row)"
                  isManual="true"
                  @query-input="
                    info => {
                      scope.row.recAcctNo = info;
                    }
                  "
                  :selected-data="{
                    acctNo: scope.row.recBankAcctNo
                  }"
                  :disabled="modifyDisabledFlag"
                >
                  <!-- 银行账号 -->
                  <f-magnifier-column prop="acctNo" :label="t('pages.loanCounter.bankAccountNo')" filter-input />
                  <!-- 银行账户名称 -->
                  <f-magnifier-column prop="acctName" :label="t('pages.loanCounter.bankAccountName')" filter-input />
                  <!-- 银行名称 -->
                  <f-magnifier-column prop="bankName" :label="t('views.biz.bankName')" filter-input />
                  <!-- 汇入地（省） -->
                  <f-magnifier-column prop="province" :label="t('views.biz.remittanceProvince')" filter-input />
                  <!-- 汇入地（市） -->
                  <f-magnifier-column prop="city" :label="t('views.biz.remittanceCity')" filter-input />
                </f-magnifier-single>
              </template>
            </f-table-column>
            <!-- 收款人名称 -->
            <f-table-column :label="t('loancounter.notice.disbursement.receiveAccountName')" prop="recAcctName">
              <template #edit="scope">
                <f-input
                  v-model="scope.row.recAcctName"
                  maxlength="50"
                  v-if="scope.row.recAcctType !== recAcctTypeData.BANK_ACCOUNT"
                  disabled
                />
                <f-input
                  v-model="scope.row.recBankAcctName"
                  maxlength="50"
                  @change="handleExternalBankAcctNameChange($event, scope.row)"
                  v-if="scope.row.recAcctType === recAcctTypeData.BANK_ACCOUNT"
                  :disabled="modifyDisabledFlag"
                />
              </template>
            </f-table-column>
            <!-- 汇入地（省） -->
            <f-table-column prop="remitInProvince" :label="t('loancounter.notice.disbursement.remitInProvince')">
              <template #edit="scope">
                <f-input v-model="scope.row.remitInProvince" maxlength="50" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 汇入地（市） -->
            <f-table-column prop="remitInCity" :label="t('loancounter.notice.disbursement.remitInCity')">
              <template #edit="scope">
                <f-input v-model="scope.row.remitInCity" maxlength="50" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 汇入行名称 -->
            <f-table-column prop="remitInBankName" :label="t('loancounter.notice.disbursement.remitInBankName')">
              <template #edit="scope">
                <f-input v-model="scope.row.remitInBankName" maxlength="50" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 汇入行CNAPS号 -->
            <f-table-column prop="recBankCnapsNo" :label="t('loancounter.notice.disbursement.recBankCnapsNo')">
              <template #edit="scope">
                <f-input v-model="scope.row.recBankCnapsNo" maxlength="50" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <!-- 摘要 -->
            <f-table-column prop="abstractInfo" :label="t('loancounter.notice.disbursement.abstractInfo')">
              <template #edit="scope">
                <f-input v-model="scope.row.abstractInfo" maxlength="50" :disabled="modifyDisabledFlag" />
              </template>
            </f-table-column>
            <template #operate="scope">
              <f-button
                v-if="editState[scope.row.randomId]"
                link
                type="primary"
                @click="showAttm(scope.row, editState[scope.row.randomId])"
              >
                {{ t(`loancounter.notice.disbursement.uploadFile`) }}
              </f-button>
              <f-button
                v-if="!editState[scope.row.randomId]"
                link
                type="primary"
                @click="showAttm(scope.row, editState[scope.row.randomId])"
              >
                {{ t(`loancounter.notice.disbursement.seeFile`) }}
              </f-button>
              <OperateButton
                :options="generalButtonOption(scope)"
                @on-edit="beforeOpenEdit(scope)"
                @on-save="saveBasicDataRow(scope)"
                @on-remove="deleteRow(scope)"
              />
            </template>
          </f-table-edit>
        </f-form-item>
      </f-panel>
      <UploadFile v-model="state.showFile" :random-id="rowRandomId" :button-show-flag="rowButtonShowFlag" />
      <RiskInputDetail
        v-model="riskInputShow"
        @save-success-function="saveSuccess"
        :add-dto="dto"
        :receipt-checked-list="receiptCheckedList"
        :button-name="riskButtonName"
      />
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :show-confirm="saveShowConfirmFlag"
          :before-trigger="saveFormValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="saveInfo"
          :url="submitUrl"
          operate="submit"
          :show-confirm="submitShowConfirmFlag"
          :before-trigger="submitFormValidator"
          @close="submitDataSuccess"
        />
        <f-submit-state
          :gather-params="saveInfo"
          type="danger"
          :url="deleteUrl"
          operate="remove"
          @close="deleteDataSuccess"
        />
        <f-button type="info" plain @click.prevent="isSubmit ? goSubmit() : goBack()">
          {{ t("views.linkList") }}
        </f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { onBeforeMount, reactive, ref, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { useConst } from "@ifs/support";
import { useCurrency } from "@/hooks/useCommon";
import { doBack } from "@/utils/wfUtils";
import UploadFile from "./components/UploadFile.vue";
import RiskInputDetail from "./components/RiskInputDetail.vue";
import {
  getOfficeInfoUrl,
  getPayClientInfoUrl,
  getContractListUrl,
  queryExternalAccountInfoUrl,
  queryInnerAccountInfoUrl,
  saveUrl,
  submitUrl,
  downloadUrl,
  receiveImportUrl,
  deleteUrl
} from "../url";
import useDto from "../hooks/useDto";
import { usePage } from "../hooks/usePage";

const props = defineProps({
  id: Number,
  backUrl: String
});

const { t } = useI18n();
const router = useRouter();
const { pageParams } = usePage();

const {
  currencyData,
  rules,
  dto,
  formRef,
  payClientInfoChange,
  payClientInfoClear,
  changeContractCode,
  clearContractCode,
  handleCurrencyChange,
  handleInnerAccountChange,
  handleOfficeChange,
  handleExternalAccountChange,
  handleExternalBankAcctNameChange,
  saveInfo,
  saveSuccess,
  goBack,
  handleBatchDelete,
  handleReceiveRowClose,
  handleReceiveRowAdd,
  unitsDisabledFlag,
  handleSelectionChange,
  receiptHandleSelectionChange,
  receiptTable,
  receiveTable,
  handleAfterDelete,
  subordinateUnitsChange,
  handleImportSuccess,
  getDetail,
  getOpenDate,
  editState,
  generalButtonOption,
  beforeOpenEdit,
  saveBasicDataRow,
  deleteRow,
  riskInputShow,
  saveFormValidator,
  submitFormValidator,
  saveShowConfirmFlag,
  submitShowConfirmFlag,
  receiptCheckedList,
  riskButtonName,
  getCurrencyData,
  modifyDisabledFlag
} = useDto();

const loanTypeDataEnum = useConst("loancounter.LoanType");
const loanTypeData = loanTypeDataEnum.pickConst([
  loanTypeDataEnum.TRUST_LOAN,
  loanTypeDataEnum.CONSIGN_LOAN,
  loanTypeDataEnum.FACTORING
]);
const loanPayMethodData = useConst("counter.LoanPayMethod");
const yesOrNoData = useConst("common.YesOrNo");
const recAcctTypeData = useConst("common.AccountType");

const { currencySymbol } = useCurrency(dto);

//审批历史查询参数
const wfHistoryParams = reactive({
  systemCode: "Z62",
  // 业务执行
  transType: "Z620017",
  agencyId: -1,
  currencyId: -1,
  recordId: -1
});
const isSubmit = ref<boolean>(false);

const wfHistoryRef = shallowRef();

const state = reactive({
  showFile: false
});
const rowRandomId = ref<string>();
const rowButtonShowFlag = ref<boolean>();

const showAttm = (row: any, isShowFlag: boolean) => {
  rowRandomId.value = row.randomId;
  rowButtonShowFlag.value = isShowFlag;
  state.showFile = true;
};
//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};
const submitDataSuccess = (res: any) => {
  if (res?.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      goBack();
    }
  }
};

const deleteDataSuccess = (res: any) => {
  if (res.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      goBack();
    }
  }
};

// 页面初始化
onBeforeMount(async () => {
  getOpenDate();
  if (pageParams) {
    await getDetail(pageParams?.id, "modify");
  } else {
    await getDetail(Number(props?.id), "modify");
    isSubmit.value = true;
  }
  wfHistoryParams.recordId = dto.id;
  wfHistoryParams.agencyId = dto.officeId;
  wfHistoryParams.currencyId = dto.currencyId;
  wfHistoryRef.value?.getData();
  if (dto.officeId) {
    getCurrencyData();
  }
});
</script>
