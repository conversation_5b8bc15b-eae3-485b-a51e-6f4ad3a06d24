<template>
  <f-query-scene :title="t('loancounter.notice.free.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="queryForm"
        :url="listPageUrl"
        :export-url="exportPageUrl"
        :table-columns="tableColumns"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :post-params="postParams"
        :show-collapse="true"
        :show-print="true"
        :show-count-value="false"
        :show-summation-sum="true"
        :export-exclude="['operate']"
        query-comp-id="loancounter-notice-free-query"
        table-comp-id="loancounter-notice-free-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :status-enum="BusinessStatusEnum"
            :submit-batch-url="submitBatchUrl"
            :delete-batch-url="deleteBatchUrl"
            :revoke-batch-url="revokeBatchUrl"
            @on-add="operateHandler.handleAdd"
          />
        </template>
        <!-- 查询面本表单项 -->
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item prop="officeList" :label="t('loancounter.notice.free.officeName')">
            <f-select
              v-model="queryForm.officeList"
              :url="listOfficeUrl"
              value-key="officeId"
              label="officeName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item prop="currencyId" :label="t('loancounter.notice.free.currencyName')">
            <f-select
              v-model="queryForm.currencyList"
              :url="listCurrencyUrl"
              value-key="currencyId"
              label="currencyName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 单据状态 -->
          <f-form-item prop="businessStatusList" :label="t('loancounter.notice.free.businessStatus')">
            <f-select
              v-model="queryForm.businessStatusList"
              :data="BusinessStatusEnum"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 免还申请单号 -->
          <f-form-item prop="businessCode" :label="t('loancounter.notice.free.businessCode')">
            <f-input v-model="queryForm.businessCode" />
          </f-form-item>
          <!-- 免还金额 -->
          <f-form-item prop="freeAmount" :label="t('loancounter.notice.free.freeAmountTotalArray')">
            <f-amount-range v-model="queryForm.freeAmount" max="9999999999999.99" tooltip :precision="2" />
          </f-form-item>
          <!-- 借款单位 -->
          <f-form-item prop="loanClientName" :label="t('loancounter.notice.free.loanClientName')">
            <f-magnifier-single
              :title="t('loancounter.notice.free.loanClientInfo')"
              :url="listLoanClientCodeUrl"
              :params="loanClientQueryParams"
              method="post"
              v-model="queryForm.loanClientName"
              row-key="clientId"
              row-label="clientName"
              input-key="clientCodeOrName"
              auto-init
              selected-key="clientId"
              selected-label="clientName"
            >
              <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.free.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loancounter.notice.free.clientName')" />
            </f-magnifier-single>
          </f-form-item>
          <!-- 委托单位名称 -->
          <f-form-item prop="consignClientName" :label="t('loancounter.notice.free.consignClientName')">
            <f-magnifier-single
              v-model="queryForm.consignClientName"
              :url="listLoanClientCodeUrl"
              :title="t('loancounter.notice.free.loanClientInfo')"
              :params="loanClientQueryParams"
              method="post"
              row-key="clientId"
              row-label="clientName"
              input-key="clientCodeOrName"
              auto-init
              selected-key="clientId"
              selected-label="clientName"
            >
              <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.free.consignClientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loancounter.notice.free.consignClientName')" />
            </f-magnifier-single>
          </f-form-item>
          <!-- 免还类型 -->
          <f-form-item prop="freeTypeList" :label="t('loancounter.notice.free.freeTypeList')">
            <f-select
              v-model="queryForm.freeTypeList"
              :data="freeTypeEnum"
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 合同编号 -->
          <f-form-item prop="contractCode" :label="t('loancounter.notice.free.contractCode')">
            <f-magnifier-single
              v-model="queryForm.contractCode"
              :url="listContractCodeUrl"
              :title="t('loancounter.notice.free.contractCode')"
              :placeholder="t('loancounter.notice.free.contractCodeInput')"
              :params="contractParams"
              method="post"
              row-key="contractCode"
              row-label="contractCode"
              input-key="contractCode"
            >
              <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.free.contractCode')" />
              <f-magnifier-column
                prop="loanType"
                :filter-select="loanTypeEnum.pickConst([loanTypeEnum.CONSIGN_LOAN])"
                :formatter="row => loanTypeEnum.valueToLabel(row.loanType)"
                :label="t('loancounter.notice.free.loanType')"
              />
              <f-magnifier-column prop="productName" :label="t('loancounter.notice.free.productName')" />
              <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.free.loanClientName')" />
              <f-magnifier-column
                prop="contractAmount"
                formatter="amount"
                :label="t('loancounter.notice.free.contractAmount')"
              />
              <f-magnifier-column prop="contractEndDate" :label="t('loancounter.notice.free.contractEndDate')" />
            </f-magnifier-single>
          </f-form-item>
          <!-- 放款通知单号 -->
          <f-form-item prop="receiptCode" :label="t('loancounter.notice.free.receiptId')">
            <f-magnifier-single
              v-model="queryForm.receiptCode"
              :url="listReceiptCodeUrl"
              :title="t('loancounter.notice.free.notePayInfo')"
              :params="receiptCodeQueryParams"
              auto-init
              method="post"
              row-key="receiptCode"
              row-label="receiptCode"
              input-key="receiptCode"
            >
              <f-magnifier-column
                prop="loanType"
                :filter-select="loanTypeEnum.pickConst([loanTypeEnum.CONSIGN_LOAN])"
                :formatter="row => loanTypeEnum.valueToLabel(row.loanType)"
                :label="t('loancounter.notice.free.loanType')"
              />
              <f-magnifier-column prop="productName" :label="t('loancounter.notice.free.productName')" />
              <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.free.contractCode')" />
              <f-magnifier-column prop="receiptCode" :label="t('loancounter.notice.free.notePayCode')" />
              <f-magnifier-column
                prop="receiptAmount"
                formatter="amount"
                :label="t('loancounter.notice.free.payAmount')"
              />
              <f-magnifier-column prop="payDate" :label="t('loancounter.notice.free.payDate')" />
            </f-magnifier-single>
          </f-form-item>
          <!-- 数据来源 -->
          <f-form-item prop="channelList" :label="t('loancounter.notice.free.channelList')">
            <f-select
              v-model="queryForm.channelList"
              :data="ChannelEnum"
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 录入日期 -->
          <f-form-item prop="inputDateArray" :label="t('loancounter.notice.free.inputDateArray')">
            <f-date-picker v-model="queryForm.inputDate" type="daterange" />
          </f-form-item>
        </template>

        <template #link="{ row }">
          <f-button @click="openDetail(row)" link type="primary">
            {{ row.businessCode }}
          </f-button>
        </template>
        <template #businessStatus="{ row }">
          {{ CommonBusinessStatusEnum.find(x => x.code === row.businessStatus)?.label }}
        </template>
        <template #buttons="{ row }">
          <OperateGroup
            :row="row"
            :submit-url="submitUrl"
            :delete-url="deleteUrl"
            :revoke-url="revokeUrl"
            @on-modify="operateHandler.handleModify"
            @operate-success="operateHandler.handleOperateSuccess"
          />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import {
  listPageUrl,
  exportPageUrl,
  submitUrl,
  deleteUrl,
  revokeUrl,
  submitBatchUrl,
  deleteBatchUrl,
  revokeBatchUrl,
  listOfficeUrl,
  listCurrencyUrl,
  listLoanClientCodeUrl,
  listContractCodeUrl,
  listReceiptCodeUrl
} from "../url";
import useEnum from "../hooks/useEnum";
import useQueryForm from "../hooks/list/useQueryForm";
import useTableColumns from "../hooks/list/useTableColumns";
import useListPageOperate from "../hooks/list/useListPageOperate";
import OperateGroup from "../../components/OperateGroup.vue";
import OperateBatchGroup from "../../components/OperateBatchGroup.vue";
import Detail from "./Detail.vue";
import { useListDetail } from "@/hooks/biz";

const { t } = useI18n();

const queryTable = shallowRef();
const operateGroup = shallowRef();

const CommonBusinessStatusEnum = useConst("common.BusinessStatus");
const { loanTypeList, freeTypeEnum, BusinessStatusEnum, loanTypeEnum, ChannelEnum } = useEnum(useConst);
const { queryForm, loanClientQueryParams, postParams } = useQueryForm();
const { tableColumns, columnSort, defaultSort } = useTableColumns(t);
const { operateHandler } = useListPageOperate(queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};

const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const contractParams = {
  loanTypeList
};

const receiptCodeQueryParams = {
  loanType: loanTypeEnum.CONSIGN_LOAN // 只查委托贷款的借据
};

const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};
</script>
