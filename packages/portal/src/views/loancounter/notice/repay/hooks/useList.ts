import { reactive } from "vue";
import { goPage } from "./usePage";

export default function useList(t, table) {
  const state = reactive({
    queryForm: {
      officeId: null,
      officeCode: "",
      officeName: "",
      currencyId: null,
      currencyCode: "",
      currencyName: "",
      loanType: null,
      officeList: [],
      currencyList: [],
      loanClientIdList: [],
      loanClientCodeList: [],
      contractCodeList: [],
      repayTotalAmountArray: [],
      repayDateArray: [],
      inputTimeArray: [],
      businessStatusList: [],
      loanClientId: null,
      loanClientCode: "",
      loanClientName: "",
      contractCode: "",
      businessCode: "",
      repayAmountArray: [],
      listedCompany: null, // 是否上市，暂时不用
      greenCredit: null,
      isMechanism: null // 是否显示机制，暂时不用
    }
  });

  const handler = {
    handleAdd() {
      goPage("add");
    },
    handleModify(row) {
      goPage("modify", { type: "modify", data: row });
    },
    handleOfficeChange(val, row) {
      state.queryForm.officeCode = !val ? "" : row.officeCode;
      state.queryForm.officeName = !val ? "" : row.officeName;
    },
    handleCurrencyChange(val, row) {
      state.queryForm.currencyCode = !val ? "" : row.currencyCode;
      state.queryForm.currencyName = !val ? "" : row.currencyName;
    }
  };
  const methods = {
    reloadTable() {
      table.value.renderTableData();
    }
  };
  return { state, handler, methods };
}
