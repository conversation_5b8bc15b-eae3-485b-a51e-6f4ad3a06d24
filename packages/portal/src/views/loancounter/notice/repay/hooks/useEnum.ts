export default function useEnum(useConst) {
  const loanTypeEnum = useConst("loancounter.LoanType");
  const LoanPayNoticeStatusEnum = useConst("common.BusinessStatus");
  return {
    loanTypeEnum: loanTypeEnum.pickConst([
      loanTypeEnum.TRUST_LOAN,
      loanTypeEnum.CONSIGN_LOAN,
      loanTypeEnum.SYNDICATED_LOAN,
      loanTypeEnum.FACTORING
    ]),
    loanTypeList: [
      loanTypeEnum.TRUST_LOAN,
      loanTypeEnum.CONSIGN_LOAN,
      loanTypeEnum.SYNDICATED_LOAN,
      loanTypeEnum.FACTORING
    ],
    LoanPayNoticeStatusEnum: LoanPayNoticeStatusEnum.pickConst([
      LoanPayNoticeStatusEnum.SAVE,
      LoanPayNoticeStatusEnum.USED,
      LoanPayNoticeStatusEnum.APPROVING,
      LoanPayNoticeStatusEnum.APPROVED,
      LoanPayNoticeStatusEnum.REFUSE,
      LoanPayNoticeStatusEnum.CANCELLING,
      LoanPayNoticeStatusEnum.CANCEL,
      LoanPayNoticeStatusEnum.SETTLE_FAIL,
      LoanPayNoticeStatusEnum.SETTLED
    ])
  };
}
