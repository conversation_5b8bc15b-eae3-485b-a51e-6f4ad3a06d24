<template>
  <f-query-scene :title="t('loancounter.notice.repay.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="state.queryForm"
        :url="listRepayNoticeUrl"
        :export-url="exportRepayNoticeUrl"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        :is-single="false"
        table-type="Record"
        :table-columns="tableColumns"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :show-collapse="false"
        :export-exclude="['operate']"
        query-comp-id="loancounter-notice-repay-query"
        table-comp-id="loancounter-notice-repay-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :status-enum="LoanPayNoticeStatusEnum"
            :submit-batch-url="submitRepayNoticerBatchUrl"
            :delete-batch-url="deleteRepayNoticeBatchUrl"
            :revoke-batch-url="revokeRepayNoticeBatchUrl"
            @on-add="handler.handleAdd"
          />
        </template>

        <!-- 查询面本表单项 -->
        <template #query-panel>
          <f-form-item prop="officeList" :label="t('loancounter.notice.repay.officeName')">
            <f-select
              v-model="state.queryForm.officeList"
              :url="listOfficeUrl"
              value-key="officeId"
              label="officeName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item prop="currencyList" :label="t('loancounter.notice.repay.currencyName')">
            <f-select
              v-model="state.queryForm.currencyList"
              :url="listCurrencyUrl"
              value-key="currencyId"
              label="currencyName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 借款单位 -->
          <f-form-item prop="loanClientCodeList" :label="t('loancounter.notice.repay.loanClientName')">
            <f-magnifier-multi
              :title="t('loancounter.notice.repay.loanClientName')"
              :url="listLoanClientCodeUrl"
              method="post"
              v-model="state.queryForm.loanClientCodeList"
              row-key="clientCode"
              row-label="clientName"
              input-key="clientCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.repay.loanClientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loancounter.notice.repay.loanClientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 合同编号 -->
          <f-form-item prop="contractCodeList" :label="t('loancounter.notice.repay.contractCode')">
            <f-magnifier-multi
              :title="t('loancounter.notice.repay.contractCode')"
              :url="listContractCodeUrl"
              method="post"
              v-model="state.queryForm.contractCodeList"
              row-key="contractCode"
              row-label="contractCode"
              input-key="contractCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.repay.contractCode')" />
              <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.repay.loanClientName')" />
              <f-magnifier-column prop="contractTerm" :label="t('loancounter.notice.repay.contractTerm')" />
              <f-magnifier-column
                prop="contractTotalAmount"
                format
                :label="t('loancounter.notice.repay.contractAmount')"
                formatter="amount"
              />
              <f-magnifier-column prop="contractStartDate" :label="t('loancounter.notice.repay.contractStartDate')" />
              <f-magnifier-column prop="contractEndDate" :label="t('loancounter.notice.repay.contractEndDate')" />
            </f-magnifier-multi>
          </f-form-item>
          <f-form-item prop="businessCode" :label="t('loancounter.notice.repay.businessCode')">
            <f-input v-model="state.queryForm.businessCode" />
          </f-form-item>
          <!-- 还款总额 -->
          <f-form-item prop="repayTotalAmountArray" :label="t('loancounter.notice.repay.repayAmountArray')">
            <f-amount-range
              v-model="state.queryForm.repayTotalAmountArray"
              max="9999999999999.99"
              tooltip
              :precision="2"
            />
          </f-form-item>
          <!-- 还款日期 -->
          <f-form-item prop="repayDateArray" :label="t('loancounter.notice.repay.repayDate')">
            <f-lax-range-date-picker v-model="state.queryForm.repayDateArray" />
          </f-form-item>
          <!-- 录入日期 -->
          <f-form-item prop="inputTimeArray" :label="t('loancounter.notice.repay.inputTime')">
            <f-lax-range-date-picker v-model="state.queryForm.inputTimeArray" />
          </f-form-item>
          <!--状态-->
          <f-form-item prop="businessStatusList" :label="t('loancounter.notice.repay.businessStatus')">
            <f-select
              v-model="state.queryForm.businessStatusList"
              :data="LoanPayNoticeStatusEnum"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
        </template>

        <!-- 表格列 -->
        <template #link="{ row }">
          <f-button type="text" @click="openDetail(row)">
            {{ row.businessCode }}
          </f-button>
        </template>

        <template #loanType="{ row }">
          {{ loanTypeEnum.find(x => x.code === row.loanType)?.label }}
        </template>

        <template #businessType="{ row }">
          {{ LoanBusinessType.find(x => x.code === row.businessType)?.label }}
        </template>

        <template #aheadRepayFlag="{ row }">
          {{ YesOrNoEnum.find(x => x.code === row.aheadRepayFlag)?.label }}
        </template>

        <template #businessStatus="{ row }">
          {{ CommonBusinessStatusEnum.find(x => x.code === row.businessStatus)?.label }}
        </template>

        <template #buttons="{ row }">
          <OperateGroup
            :row="row"
            :submit-url="submitRepayNoticerUrl"
            :delete-url="deleteRepayNoticeUrl"
            :revoke-url="revokeRepayNoticeUrl"
            @on-modify="handler.handleModify"
            @operate-success="methods.reloadTable"
          />
        </template>
      </f-query-grid>
    </template>
    <View ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import View from "./View.vue";
import {
  listRepayNoticeUrl,
  exportRepayNoticeUrl,
  listOfficeUrl,
  listCurrencyUrl,
  listContractCodeUrl,
  submitRepayNoticerUrl,
  revokeRepayNoticeUrl,
  deleteRepayNoticeUrl,
  submitRepayNoticerBatchUrl,
  deleteRepayNoticeBatchUrl,
  revokeRepayNoticeBatchUrl,
  listLoanClientCodeUrl
} from "../url";
import useTableColumns from "../hooks/list/useTableColumns";
import useList from "../hooks/useList";
import useEnum from "../hooks/useEnum";
import { useListDetail } from "@/hooks/biz";
import OperateGroup from "../../components/OperateGroup.vue";
import OperateBatchGroup from "../../components/OperateBatchGroup.vue";

const YesOrNoEnum = useConst("common.YesOrNo");
const CommonBusinessStatusEnum = useConst("common.BusinessStatus");

const LoanBusinessType = useConst("loancounter.LoanBusinessType");
const { loanTypeEnum, LoanPayNoticeStatusEnum } = useEnum(useConst);

const { t } = useI18n();

const queryTable = shallowRef();
const operateGroup = shallowRef();

const { tableColumns, columnSort, defaultSort } = useTableColumns(t, queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};

const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};

const { state, handler, methods } = useList(t, queryTable);
</script>
