<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('loancounter.notice.repay.viewTitle')"
    :isPoint="false"
    destroy-on-close
    @close="closeDrawer"
  >
    <Application ref="appInfo" readonly :id="id" @on-loaded="handleAppLoaded" />
    <f-panel :title="t('loancounter.notice.repay.wfHistoryTitle')">
      <f-wf-history ref="wfhistory" :params="wfParams" :is-through="false" :auto-init="false" />
    </f-panel>
    <template #footer>
      <f-button type="info" plain @click="closeDrawer">{{ t("loancounter.notice.repay.close") }}</f-button>
    </template>
  </f-drawer-scene>
</template>

<script setup lang="ts">
import { shallowRef, reactive, ref } from "vue";
import { useBizDetail } from "@/hooks/biz";
import { useI18n } from "vue-i18n";
import Application from "./Application.vue";

const { t } = useI18n();

const props = defineProps({
  id: {
    type: String
  }
});

const appInfo = shallowRef();
const wfhistory = ref();
const wfParams = reactive({
  systemCode: "Z62",
  transType: "Z620006",
  agencyId: null,
  currencyId: null,
  recordId: null
});

const handleAppLoaded = info => {
  wfParams.agencyId = info.officeId; // 组织ID(机构ID或成员单位ID)
  wfParams.currencyId = info.currencyId; // 币种ID
  wfParams.recordId = info.id; // 业务单据id
  wfhistory.value.getData();
};

const initData = () => {};

const { drawerRef, setVisible, closeDrawer } = useBizDetail(props, initData);
defineExpose({ setVisible });
</script>
