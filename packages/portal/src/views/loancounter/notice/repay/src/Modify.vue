<template>
  <f-blank-scene :title="t('loancounter.notice.repay.modifyTitle')" :isPoint="false">
    <Application ref="app" @on-loaded="handleAppLoaded" :id="props.id" />
    <f-panel :title="t('loancounter.notice.repay.wfHistoryTitle')">
      <f-wf-history ref="wfhistory" :params="wfParams" :is-through="false" :auto-init="false" />
    </f-panel>
    <template #footer>
      <f-submit-state
        v-if="!props.id"
        :gather-params="methods.getAppData"
        :before-trigger="methods.validateApp"
        :url="saveRepayNoticerUrl"
        operate="save"
        @close="handler.handleGoBack"
      />
      <f-submit-state
        :gather-params="methods.getAppData"
        :before-trigger="methods.validateApp"
        :url="submitRepayNoticerUrl"
        operate="submit"
        @close="
          res => {
            props.backUrl ? callBack(res) : handler.handleGoBack();
          }
        "
      />
      <f-button v-if="!props.id" type="info" plain @click.prevent="handler.handleGoBack">
        {{ t("loancounter.notice.repay.goBack") }}
      </f-button>
      <f-button v-if="props.id" type="info" plain @click.prevent="goBack">
        {{ t("loancounter.notice.repay.goBack") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { shallowRef, onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { doBack } from "@/utils/wfUtils";
import { useRouter } from "vue-router";
import useAdd from "../hooks/useAdd";
import Application from "./Application.vue";
import { goPage } from "../hooks/usePage";
import { saveRepayNoticerUrl, submitRepayNoticerUrl } from "../url";

const { t } = useI18n();

const router = useRouter();

const app = shallowRef();

const props = defineProps({ id: { type: Number, default: null }, backUrl: { type: String } });
const goBack = () => {
  doBack(router, String(props.backUrl));
};
const wfhistory = ref();
const wfParams = reactive({
  systemCode: "Z62",
  transType: "Z620006",
  agencyId: null,
  currencyId: null,
  recordId: null
});

const handleAppLoaded = info => {
  wfParams.agencyId = info.officeId; // 组织ID(机构ID或成员单位ID)
  wfParams.currencyId = info.currencyId; // 币种ID
  wfParams.recordId = info.id; // 业务单据id
  wfhistory.value.getData();
};
const callBack = (res: any) => {
  if (res.success) {
    goBack();
  }
};
// 页面初始化
onMounted(async () => {
  if (props.id) {
    await goPage("modify", { type: "modify", data: { id: props.id } });
  }
});
const { handler, methods } = useAdd(t, app);
</script>
