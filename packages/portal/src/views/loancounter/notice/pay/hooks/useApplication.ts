import { reactive, computed, onBeforeMount, ref, nextTick } from "vue";
import { FMessage } from "@dtg/frontend-plus";
import { usePage } from "./usePage";
import { monthBetween } from "@/utils/date";
import httpTool from "@/utils/http";
import currencyAmount from "currency.js";
import {
  getPayNoticeUrl,
  getRecentlyEffectiveDate,
  listLoanAccountUrl,
  getLoanAccountTypeUrl,
  getContractDetailUrl,
  getTrialResult,
  listPayAccountNoUrl,
  openDateUrl
} from "../url";
import { formatDate } from "@/utils/date";
import { useConst } from "@ifs/support";
import useEnum from "@/views/loancounter/notice/pay/hooks/useEnum";

const resetPayNoticeInfo = obj => {
  Object.assign(obj, {
    id: null,
    globalSerial: "",
    businessCode: "",
    businessType: "",
    businessStatus: "",
    loanType: "",
    officeId: null,
    officeCode: "",
    officeName: "",
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    loanClientId: null,
    loanClientCode: "",
    loanClientName: "",
    loanAcctId: null, // 贷款账户相关
    loanAcctNo: "",
    loanAcctName: "",
    consignClientId: null,
    consignClientCode: "",
    consignClientName: "",
    productId: null, // 产品ID
    productCode: "", // 产品编号
    productName: "", // 产品名称
    contractId: null,
    contractCode: "",
    contractAmount: null,
    loanStartDate: "", //贷款开始日期
    loanEndDate: "", //贷款结束日期
    loanTerm: null, // 贷款期限
    greenCredit: "",
    consignCurrentAccountId: null, // 委托方活期户-代偿单位贷款专户相关
    consignCurrentAccountNo: "",
    consignCurrentAccountName: "",
    consignInterestAccountId: null, // 委托方收息账户相关
    consignInterestAccountNo: "",
    consignInterestClientName: "",
    feeAcctNo: "",
    feeAcctName: "",
    feeRulesId: null,
    feeRulesVersion: "",
    feeChargeWay: "",
    feeChargeBasic: "",
    feeChargeRate: "",
    feeAmount: "",
    payDate: "", //放款日期
    recPrincipalAcctId: "", // 收本金账户相关
    recPrincipalAcctNo: "",
    recPrincipalAcctName: "",
    payAmount: "",
    actualPayAmount: "", //实际发放金额
    prepaidInterest: "", //预收利息
    rateRunType: "", // 利率方式
    executeRate: null, // 执行利率
    referenceRateId: "", // 固定利率
    referenceRateName: "",
    floatingRateId: null,
    floatingRateCode: "", //挂牌利率率代码
    floatingRateName: "",
    floatingWay: "", // 浮动方式
    floatingType: "", // 上浮还是下浮
    floatingRatio: 0, // 浮动比例
    pointType: "", // 加基点还是减基点
    pointFloating: 0, //基点加减点数
    floatingRate: null, // 这个字段需要确认-从放大镜中选择的浮动利率
    interestSettTerm: "", // 结息结息周期
    interestSettDateFirst: "", // 首期结息日
    remark: "", // 备注
    fileIds: "",
    fileList: [],
    clnLoanFileList: [], // 贷款申请客户详情
    payPlanId: null, // 合同自动还款还款计划相关
    payPlanCode: "",
    autoRepayMode: "", // 合同自动还款还本息方式 PRINCIPAL: 1、只还本金，末次还本付息 2、每次还本付息
    // 下面这些字段目前后台接口并没有定义
    fixedRate: 0
  });
  return obj;
};
const openDate = ref();
const getOpenDate = () => {
  httpTool.post(openDateUrl).then((res: any) => {
    openDate.value = res.data.onlineDate;
  });
};
const mergeContractToPayNotice = (contract, payNotice) => {
  payNotice.officeId = contract.officeId;
  payNotice.officeCode = contract.officeCode;
  payNotice.officeName = contract.officeName;
  payNotice.currencyId = contract.currencyId;
  payNotice.currencyCode = contract.currencyCode;
  payNotice.currencyName = contract.currencyName;
  payNotice.productId = contract.productId;
  payNotice.productCode = contract.productCode;
  payNotice.productName = contract.productName;
  payNotice.contractId = contract.id;
  payNotice.contractCode = contract.contractCode;
  payNotice.contractAmount = contract.contractAmount;
  payNotice.loanType = contract.loanType;
  payNotice.loanClientId = contract.loanClientId;
  payNotice.loanClientCode = contract.loanClientCode;
  payNotice.loanClientName = contract.loanClientName;
  payNotice.consignClientId = contract.consignClientId;
  payNotice.consignClientCode = contract.consignClientCode;
  payNotice.consignClientName = contract.consignClientName;
  payNotice.interestSettTerm = contract.interestSettFreq;
  payNotice.feeChargeWay = contract.contractPriceFeeDto.feeChargeWay;
  payNotice.feeChargeBasic = contract.contractPriceFeeDto.feeChargeBasic;
  payNotice.feeChargeRate = contract.contractPriceFeeDto.feeChargeRate;
  payNotice.feeAmount = contract.contractPriceFeeDto.feeAmount;
  payNotice.feeChargeFreq = contract.contractPriceFeeDto.feeChargeFreq;
  payNotice.feeAccountId = contract.contractPriceFeeDto.payFeeAcctId;
  payNotice.feeAccountNo = contract.contractPriceFeeDto.payFeeAcctNo;
  payNotice.feeAccountName = contract.contractPriceFeeDto.payFeeAcctName;
  payNotice.firstFeeChargeDate = contract.contractPriceFeeDto.firstFeeChargeDate;
  payNotice.greenCredit = contract.greenCredit;
  payNotice.loanStartDate = contract.contractStartDate;
  payNotice.loanEndDate = contract.contractEndDate;
  payNotice.loanTerm = contract.contractTerm;
};

export default function useApplication(
  t,
  props,
  form,
  emits,
  baseInfo,
  contractInfo,
  contractMethods,
  payPlan,
  AllLoanTypeEnum,
  AccountGroupEnum,
  ChargeBasicsEnum,
  LoanSetRateTypeEnum,
  FloatDirectionEnum,
  FloatPointEnum,
  AutoRepayModeEnum
) {
  const { LoanRateTypeEnum, LoanContractStatusEnum } = useEnum(useConst);

  const state = reactive({
    payNoticeInfo: resetPayNoticeInfo({}),
    recentlyEffectiveDate: "", // 挂牌利率生效日
    loanAccountType: null,
    businessCode: "" // 修改、查看页面在查询合同时，需要传递业务编号，便于核心将该笔单据所预占金额加回来
  });

  const defaulIsBuyAdd = false; // 后面这个应该改成全局配置
  const showIsBuyAdd = computed(() => {
    return state.payNoticeInfo.isBuyAdd === 1 || defaulIsBuyAdd;
  });

  const doResetDefaultData = () => {
    state.payNoticeInfo.feeChargeBasic = ChargeBasicsEnum.CONTRACT_AMOUNT;
    state.payNoticeInfo.rateRunType = LoanSetRateTypeEnum.FLOAT;
    state.payNoticeInfo.floatingType = FloatDirectionEnum.GOUP;
    state.payNoticeInfo.pointType = FloatPointEnum.PLUS;
    state.payNoticeInfo.autoRepayMode = AutoRepayModeEnum.PRINCIPAL;
    if (contractInfo.contractPriceRateDtos && contractInfo.contractPriceRateDtos.length > 0) {
      const normalRateData = contractInfo.contractPriceRateDtos.find(x => x.rateType === "NORMAL");
      if (normalRateData) {
        state.payNoticeInfo.rateRunType = normalRateData.rateRunType;
        state.payNoticeInfo.floatingRateId = normalRateData.boardRateId;
        state.payNoticeInfo.floatingRateCode = normalRateData.boardRateCode;
        state.payNoticeInfo.floatingRate = normalRateData.floatingRate;
        state.payNoticeInfo.floatingType =
          normalRateData.floatingRatio >= 0 ? FloatDirectionEnum.GOUP : FloatDirectionEnum.GODOWN;
        state.payNoticeInfo.floatingRatio = Math.abs(normalRateData.floatingRatio);
        state.payNoticeInfo.pointType = normalRateData.basicPoint >= 0 ? FloatPointEnum.PLUS : FloatPointEnum.MINUS;
        state.payNoticeInfo.pointFloating = Math.abs(normalRateData.basicPoint);
        if (normalRateData.rateRunType === LoanSetRateTypeEnum.FLOAT) {
          doCalRate();
        } else {
          state.payNoticeInfo.executeRate = state.payNoticeInfo.fixedRate = normalRateData.fixedRate;
        }
      }
    }
  };

  const doCalRate = () => {
    let tmp = (state.payNoticeInfo.floatingRate || 0) / 100;
    const proportion =
      state.payNoticeInfo.floatingType === FloatDirectionEnum.GOUP
        ? 100 + state.payNoticeInfo.floatingRatio
        : 100 - state.payNoticeInfo.floatingRatio;
    tmp = tmp * proportion;
    const points =
      state.payNoticeInfo.pointType === FloatPointEnum.PLUS
        ? state.payNoticeInfo.pointFloating
        : 0 - state.payNoticeInfo.pointFloating;
    tmp = tmp + points / 100;
    state.payNoticeInfo.executeRate = tmp.toFixed(6);
  };

  doResetDefaultData();
  const YesOrNoEnum = useConst("common.YesOrNo");
  const loanTypeEnum = useConst("loancounter.LoanType");
  const InterestFreqEnum = useConst("loancounter.LoanInterestFreq");
  const currencyQueryParam = computed(() => {
    return { officeId: state.payNoticeInfo.officeId };
  });

  const loanClientQueryParam = computed(() => {
    return {
      clientclass: 1 // 只查内部账户
    };
  });

  const contractQueryParam = computed(() => {
    return {
      officeId: state.payNoticeInfo.officeId,
      currencyId: state.payNoticeInfo.currencyId,
      loanClientId: state.payNoticeInfo.loanClientId,
      onlineDate: openDate.value,
      loanTypeList:
        state.payNoticeInfo.loanType === ""
          ? [loanTypeEnum.TRUST_LOAN, loanTypeEnum.CONSIGN_LOAN, loanTypeEnum.FACTORING]
          : [state.payNoticeInfo.loanType], // 只查自营、委贷和保理的数据
      contractStatusList: [
        //读取配置项获取是否自动激活  如果自动激活 ；+已审批
        LoanContractStatusEnum.NOT_ACTIVE,
        LoanContractStatusEnum.NOT_EXECUTE, // 未执行
        LoanContractStatusEnum.EXECUTING, // 已执行
        LoanContractStatusEnum.EXTEND // 已展期
      ]
    };
  });

  // 贷款方账户查询参数
  const LoanAccountQueryParams = computed(() => {
    return {
      officeId: state.payNoticeInfo.officeId,
      officeCode: state.payNoticeInfo.officeCode,
      currencyId: state.payNoticeInfo.currencyId,
      currencyCode: state.payNoticeInfo.currencyCode,
      clientId: state.payNoticeInfo.loanClientId,
      accountGroup: AccountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  // 委托方账户查询参数
  const consignAccountQueryParams = computed(() => {
    return {
      officeId: state.payNoticeInfo.officeId,
      officeCode: state.payNoticeInfo.officeCode,
      currencyId: state.payNoticeInfo.currencyId,
      currencyCode: state.payNoticeInfo.currencyCode,
      clientId: state.payNoticeInfo.consignClientId,
      accountGroup: AccountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  const boardRateQueryParams = computed(() => {
    return {
      businessType: "LOAN",
      currencyId: contractInfo.currencyId,
      currencyName: contractInfo.currencyName,
      effectiveDate: state.recentlyEffectiveDate
    };
  });

  const payPlanQueryParams = computed(() => {
    return {
      contractId: contractInfo.contractId,
      contractCode: contractInfo.contractCode,
      planType: "PAY",
      ids: state.payNoticeInfo.payPlanId ? [state.payNoticeInfo.payPlanId] : []
    };
  });

  const handler = {
    handleOfficeChange(val, row) {
      state.payNoticeInfo.officeCode = !val ? "" : row.officeCode;
      state.payNoticeInfo.officeName = !val ? "" : row.officeName;
      state.payNoticeInfo.currencyId = null;
      state.payNoticeInfo.currencyCode = "";
      state.payNoticeInfo.currencyName = "";
      state.payNoticeInfo.loanClientId = null;
      state.payNoticeInfo.loanClientCode = "";
      state.payNoticeInfo.loanClientName = "";
      state.payNoticeInfo.contractId = null;
      state.payNoticeInfo.contractCode = "";
      state.payNoticeInfo.contractName = "";
      state.payNoticeInfo.isBuyAdd = 0;
      state.payNoticeInfo.settleMethod = null;
    },
    handleLoanTypeChange() {
      // 清空下级数据
      state.payNoticeInfo.contractId = null;
      state.payNoticeInfo.contractCode = "";
      state.payNoticeInfo.contractName = "";
      state.payNoticeInfo.isBuyAdd = 0;
      state.payNoticeInfo.settleMethod = null;

      state.payNoticeInfo.contractAmount = 0;
      state.payNoticeInfo.contractBalance = 0;
      state.payNoticeInfo.payedAmount = 0;
      state.payNoticeInfo.unPayedAmount = 0;
      state.payNoticeInfo.contractAvailableAmount = 0;
      state.payNoticeInfo.repayAmount = 0;
      state.payNoticeInfo.contractExecuteRate = null;
      state.payNoticeInfo.contractStartDate = null;
      state.payNoticeInfo.contractEndDate = null;
      state.payNoticeInfo.loanEndDate = null;
      state.payNoticeInfo.contractTerm = null;
      state.payNoticeInfo.loanPurpose = "";

      state.payNoticeInfo.floatingRateId = null;
      state.payNoticeInfo.floatingRateCode = ""; //
      state.payNoticeInfo.floatingRateName = ""; //
      state.payNoticeInfo.rateRunType = null; //利率类型
      state.payNoticeInfo.floatingRate = 0; //浮动利率
      state.payNoticeInfo.floatingWay = null; //浮动方式
      state.payNoticeInfo.floatingRatio = 0; //浮动比例
      state.payNoticeInfo.basicPoint = 0; //基点
      state.payNoticeInfo.fixedRate = 0; //固定利率
      state.payNoticeInfo.executeRate = null; //执行利率
      state.payNoticeInfo.interestSettFreq = null; //结息周期
      state.payNoticeInfo.firstInterestDate = null; //首次结息日
      state.payNoticeInfo.productName = ""; //
      state.payNoticeInfo.loanBusinessType = null; //
      state.payNoticeInfo.loanTerm = null; //
    },
    handleCurrencyChange(val, row) {
      state.payNoticeInfo.currencyCode = !val ? "" : row.currencyCode;
      state.payNoticeInfo.currencyName = !val ? "" : row.currencyName;

      // 清空下级数据
      state.payNoticeInfo.contractId = null;
      state.payNoticeInfo.contractCode = "";
      state.payNoticeInfo.contractName = "";
      state.payNoticeInfo.isBuyAdd = 0;
      state.payNoticeInfo.settleMethod = null;
    },
    handleLoanClientChange(row) {
      state.payNoticeInfo.loanClientCode = !row || !row.clientId ? "" : row.clientCode;
      state.payNoticeInfo.loanClientName = !row || !row.clientId ? "" : row.clientName;
      state.payNoticeInfo.loanClientName = !row || !row.clientId ? "" : row.clientId;
      // 清空下级数据
      state.payNoticeInfo.contractId = null;
      state.payNoticeInfo.contractCode = "";
      state.payNoticeInfo.contractName = "";
      state.payNoticeInfo.isBuyAdd = 0;
      state.payNoticeInfo.settleMethod = null;

      state.payNoticeInfo.contractAmount = 0;
      state.payNoticeInfo.contractBalance = 0;
      state.payNoticeInfo.payedAmount = 0;
      state.payNoticeInfo.unPayedAmount = 0;
      state.payNoticeInfo.contractAvailableAmount = 0;
      state.payNoticeInfo.repayAmount = 0;
      state.payNoticeInfo.contractExecuteRate = null;
      state.payNoticeInfo.contractStartDate = null;
      state.payNoticeInfo.contractEndDate = null;
      state.payNoticeInfo.loanEndDate = null;
      state.payNoticeInfo.contractTerm = null;
      state.payNoticeInfo.loanPurpose = "";

      state.payNoticeInfo.floatingRateId = null;
      state.payNoticeInfo.floatingRateCode = ""; //
      state.payNoticeInfo.floatingRateName = ""; //
      state.payNoticeInfo.rateRunType = null; //利率类型
      state.payNoticeInfo.floatingRate = 0; //浮动利率
      state.payNoticeInfo.floatingWay = null; //浮动方式
      state.payNoticeInfo.floatingRatio = 0; //浮动比例
      state.payNoticeInfo.basicPoint = 0; //基点
      state.payNoticeInfo.fixedRate = 0; //固定利率
      state.payNoticeInfo.executeRate = null; //执行利率
      state.payNoticeInfo.interestSettFreq = null; //结息周期
      state.payNoticeInfo.firstInterestDate = null; //首次结息日
    },

    handleContractChange(row) {
      if (!row || !row.contractId) {
        state.payNoticeInfo.contractCode = "";
        state.payNoticeInfo.contractName = "";
        state.payNoticeInfo.isBuyAdd = 0;
        state.payNoticeInfo.settleMethod = null;

        state.payNoticeInfo.contractAmount = 0;
        state.payNoticeInfo.contractBalance = 0;
        state.payNoticeInfo.payedAmount = 0;
        state.payNoticeInfo.unPayedAmount = 0;
        state.payNoticeInfo.contractAvailableAmount = 0;
        state.payNoticeInfo.repayAmount = 0;
        state.payNoticeInfo.contractExecuteRate = null;
        state.payNoticeInfo.contractStartDate = null;
        state.payNoticeInfo.contractEndDate = null;
        state.payNoticeInfo.loanEndDate = null;
        state.payNoticeInfo.contractTerm = null;
        state.payNoticeInfo.loanPurpose = "";

        state.payNoticeInfo.floatingRateId = null;
        state.payNoticeInfo.floatingRateCode = ""; //
        state.payNoticeInfo.floatingRateName = ""; //
        state.payNoticeInfo.rateRunType = null; //利率类型
        state.payNoticeInfo.floatingRate = 0; //浮动利率
        state.payNoticeInfo.floatingWay = null; //浮动方式
        state.payNoticeInfo.floatingRatio = 0; //浮动比例
        state.payNoticeInfo.basicPoint = 0; //基点
        state.payNoticeInfo.fixedRate = 0; //固定利率
        state.payNoticeInfo.executeRate = null; //执行利率
        state.payNoticeInfo.interestSettFreq = null; //结息周期
        state.payNoticeInfo.firstInterestDate = null; //首次结息日
      } else {
        state.payNoticeInfo.officeId = row.officeId;
        state.payNoticeInfo.officeCode = row.officeCode;
        state.payNoticeInfo.officeName = row.officeName;
        state.payNoticeInfo.currencyId = row.currencyId;
        state.payNoticeInfo.currencyCode = row.currencyCode;
        state.payNoticeInfo.currencyName = row.currencyName;
        state.payNoticeInfo.loanClientId = row.loanClientId;
        state.payNoticeInfo.loanClientCode = row.loanClientCode;
        state.payNoticeInfo.loanClientName = row.loanClientName;
        state.payNoticeInfo.contractId = row.contractId;
        state.payNoticeInfo.contractCode = row.contractCode;
        state.payNoticeInfo.contractName = row.contractName;
        state.payNoticeInfo.isBuyAdd = row.isBuyAdd || 0;
        state.payNoticeInfo.settleMethod = row.settleMethod;

        const paramContractInfo = {
          currencyId: row.contractId,
          contractCode: row.contractCode,
          businessCode: state.payNoticeInfo.businessCode
        };

        httpTool.post(getContractDetailUrl, paramContractInfo).then(res => {
          if (res.success && res.data) {
            // Object.assign(state.payNoticeInfo, res.data);
            state.payNoticeInfo.officeId = res.data.officeId;
            state.payNoticeInfo.officeCode = res.data.officeCode;
            state.payNoticeInfo.officeName = res.data.officeName;
            state.payNoticeInfo.currencyId = res.data.currencyId;
            state.payNoticeInfo.currencyCode = res.data.currencyCode;
            state.payNoticeInfo.currencyName = res.data.currencyName;
            state.payNoticeInfo.loanClientId = res.data.loanClientId;
            state.payNoticeInfo.loanClientCode = res.data.loanClientCode;
            state.payNoticeInfo.loanClientName = res.data.loanClientName;
            state.payNoticeInfo.consignClientId = res.data.consignClientId;
            state.payNoticeInfo.consignClientCode = res.data.consignClientCode;
            state.payNoticeInfo.consignClientName = res.data.consignClientName;
            state.payNoticeInfo.loanType = res.data.loanType;
            state.payNoticeInfo.loanBusinessType = res.data.loanBusinessType;
            state.payNoticeInfo.productId = res.data.productId;
            state.payNoticeInfo.productCode = res.data.productCode;
            state.payNoticeInfo.productName = res.data.productName;

            state.payNoticeInfo.contractAmount = res.data.contractAmount;
            state.payNoticeInfo.contractBalance = res.data.contractBalance;
            state.payNoticeInfo.payedAmount = res.data.payedAmount;
            state.payNoticeInfo.unPayedAmount = res.data.unPayedAmount;
            state.payNoticeInfo.contractAvailableAmount = res.data.contractAvailableAmount;
            state.payNoticeInfo.repayAmount = res.data.repayAmount;
            state.payNoticeInfo.contractExecuteRate = res.data.executeRate;

            state.payNoticeInfo.contractStartDate = res.data.contractStartDate;
            state.payNoticeInfo.contractEndDate = res.data.contractEndDate;
            state.payNoticeInfo.loanEndDate = res.data.contractEndDate;

            state.payNoticeInfo.contractTerm = res.data.contractTerm;
            state.payNoticeInfo.gracePeriod = res.data.gracePeriod;

            // state.payNoticeInfo.industryCategoryFuzzy = res.data.industryCategoryFuzzy;
            // state.payNoticeInfo.loanInvestArea = res.data.loanInvestArea;
            state.payNoticeInfo.revolvingLoanFlag = res.data.revolvingLoanFlag;
            state.payNoticeInfo.allEntrustPayFlag = res.data.allEntrustPayFlag;
            // state.payNoticeInfo.greenCredit = res.data.greenCredit;
            // state.payNoticeInfo.greenCreditName = res.data.greenCreditName;
            // state.payNoticeInfo.technologyLoans = res.data.technologyLoans;
            // state.payNoticeInfo.technologyLoansName = res.data.technologyLoansName;
            state.payNoticeInfo.loanPurpose = res.data.loanPurpose;
            // state.payNoticeInfo.fundsSourceName = res.data.fundsSourceName;
            state.payNoticeInfo.floatingRateId = res.data.rateId;
            state.payNoticeInfo.floatingRateCode = res.data.rateCode; //
            state.payNoticeInfo.floatingRateName = res.data.rateName; //
            state.payNoticeInfo.rateRunType = res.data.rateRunType; //利率类型
            state.payNoticeInfo.floatingRate = res.data.floatingRate; //浮动利率
            state.payNoticeInfo.floatingWay = res.data.floatingWay; //浮动方式
            state.payNoticeInfo.floatingRatio = res.data.floatingRatio; //浮动比例
            state.payNoticeInfo.basicPoint = res.data.basicPoint; //基点
            state.payNoticeInfo.fixedRate = res.data.fixedRate; //固定利率
            state.payNoticeInfo.executeRate = res.data.executeRate; //执行利率
            state.payNoticeInfo.interestSettFreq = res.data.interestSettFreq; //结息周期
            state.payNoticeInfo.firstInterestDate = res.data.firstInterestDate; //首次结息日

            httpTool.post(openDateUrl).then((res: any) => {
              state.payNoticeInfo.payDate = res.data.onlineDate; //放款日期，默认系统开机日
              state.payNoticeInfo.loanStartDate = state.payNoticeInfo.payDate;
              state.payNoticeInfo.loanTerm = monthBetween(
                state.payNoticeInfo.loanStartDate,
                state.payNoticeInfo.loanEndDate
              );
              state.payNoticeInfo.firstFeeChargeDate = state.payNoticeInfo.payDate;
              if (state.payNoticeInfo.repayAmount === 0) {
                const currentDate = state.payNoticeInfo.loanStartDate;
                const monthTime = new Date(currentDate);
                const newTime = monthTime.setMonth(
                  monthTime.getMonth() + (state.payNoticeInfo.loanTerm === null ? 0 : state.payNoticeInfo.loanTerm)
                );
                const monthTimes = new Date(newTime);
                state.payNoticeInfo.loanEndDate = formatDate(monthTimes, "yyyy-MM-dd");
              }
              if (state.payNoticeInfo.interestSettFreq === InterestFreqEnum.MONTH) {
                const payDate = new Date(state.payNoticeInfo.payDate);
                //按季
                if (payDate.getMonth() + 1 < 3) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-03-21";
                } else if (payDate.getMonth() + 1 === 3 && payDate.getDate() <= 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-03-21";
                } else if (payDate.getMonth() + 1 === 3 && payDate.getDate() > 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-06-21";
                } else if (payDate.getMonth() + 1 < 6) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-06-21";
                } else if (payDate.getMonth() + 1 === 6 && payDate.getDate() <= 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-06-21";
                } else if (payDate.getMonth() + 1 === 6 && payDate.getDate() > 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-09-21";
                } else if (payDate.getMonth() + 1 < 9) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-09-21";
                } else if (payDate.getMonth() + 1 === 9 && payDate.getDate() <= 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-09-21";
                } else if (payDate.getMonth() + 1 === 9 && payDate.getDate() > 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-12-21";
                } else if (payDate.getMonth() + 1 < 12) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-12-21";
                } else if (payDate.getMonth() + 1 === 12 && payDate.getDate() <= 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + "-12-21";
                } else if (payDate.getMonth() + 1 === 12 && payDate.getDate() > 21) {
                  state.payNoticeInfo.firstInterestDate = payDate.getFullYear() + 1 + "-03-21";
                }
              }
            });

            // 保理贷款
            state.payNoticeInfo.factoringRecourseFlag = res.data.factoringRecourseFlag; //保理贷款有无追索权
            state.payNoticeInfo.factoringPublicFlag = res.data.factoringPublicFlag; //保理贷款是否公开
            state.payNoticeInfo.factoringRealInterest = res.data.factoringRealInterest; //保理实付利息
            state.payNoticeInfo.factoringRealPayAmount = res.data.factoringRealPayAmount; //保理实际发放金额
            if (state.payNoticeInfo.loanType === AccountGroupEnum.FACTORING) {
              state.payNoticeInfo.payAmount = res.data.contractAvailableAmount;
              state.payNoticeInfo.prepaidInterest = currencyAmount(
                state.payNoticeInfo.payAmount *
                  (state.payNoticeInfo.executeRate / 100 / 360) *
                  state.payNoticeInfo.loanTerm *
                  30,
                { precision: 2 }
              ).value;
              state.payNoticeInfo.actualPayAmount = state.payNoticeInfo.payAmount - state.payNoticeInfo.prepaidInterest;
              //查询贷款专户
              const paramAccount = {
                officeId: state.payNoticeInfo.officeId,
                officeCode: state.payNoticeInfo.officeCode,
                currencyId: state.payNoticeInfo.currencyId,
                currencyCode: state.payNoticeInfo.currencyCode,
                clientId: state.payNoticeInfo.loanClientId,
                includeLoanFlag: YesOrNoEnum.YES,
                loanFlag: YesOrNoEnum.YES,
                accountGroups: [AccountGroupEnum.CURRENT]
              };
              httpTool.post(listPayAccountNoUrl, paramAccount).then(res => {
                if (res.success) {
                  state.payNoticeInfo.recPrincipalAcctId = res.data[0].accountId;
                  state.payNoticeInfo.recPrincipalAcctNo = res.data[0].accountCode;
                  state.payNoticeInfo.recPrincipalAcctName = res.data[0].accountName;
                }
              });
            }
            // 从正常利率中获取下述信息
            if (res.data.contractRateList && res.data.contractRateList.length > 0) {
              res.data.contractRateList.map(item => {
                if (item.rateType === LoanRateTypeEnum.NORMAL) {
                  state.payNoticeInfo.floatingRateId = item.rateId;
                  state.payNoticeInfo.floatingRateCode = item.rateCode; //
                  state.payNoticeInfo.floatingRateName = item.rateName; //
                  state.payNoticeInfo.rateAdjustWay = item.rateAdjustWay; //利率调整方式
                  state.payNoticeInfo.rateAdjustFreq = item.rateAdjustFreq; //利率调整周期
                  state.payNoticeInfo.firstDayAdjustFlag = item.firstDayAdjustFlag; //是否周期第一天调整
                  state.payNoticeInfo.fixedAdjustDate = item.fixedAdjustDate; //固定调整日期
                  state.payNoticeInfo.repeatAnnuallyFlag = item.repeatAnnuallyFlag; //是否按年重复
                }
              });
            }
            // 手续费信息
            if (res.data.contractPriceFeeDto) {
              state.payNoticeInfo.feeChargeWay = res.data.contractPriceFeeDto.feeChargeWay; //手续费收取方式
              state.payNoticeInfo.feeChargeFreq = res.data.contractPriceFeeDto.feeChargeFreq; //手续费收费周期
              state.payNoticeInfo.feeChargeBasic = res.data.contractPriceFeeDto.feeChargeBasic; //计费基础
              state.payNoticeInfo.feeChargeRate = res.data.contractPriceFeeDto.feeChargeRate; //手续费率
              state.payNoticeInfo.feeAmount = res.data.contractPriceFeeDto.feeAmount; //手续费金额
              // state.payNoticeInfo.firstFeeChargeDate = res.data.contractPriceFeeDto.firstFeeChargeDate;//首次收费日
              state.payNoticeInfo.freeChargeFlag = res.data.contractPriceFeeDto.freeChargeFlag; //是否免收手续费
            }
          }
        });

        // 合同改变后，要重新根据合同开始时间获取挂牌利率生效日
        const paramEffectiveDate = {
          currencyId: contractInfo.currencyId,
          interestStartDate: contractInfo.contractStartDate
        };
        httpTool.post(getRecentlyEffectiveDate, paramEffectiveDate).then(res => {
          if (res.success) {
            state.recentlyEffectiveDate = res.data.effectiveDate;
          }
        });
        // // 合同改变后，去查贷款客户的贷款账户
        // const accountGroupMap = {
        //   [AllLoanTypeEnum.TRUST_LOAN]: AccountGroupEnum.SELFLOAN,
        //   [AllLoanTypeEnum.CONSIGN_LOAN]: AccountGroupEnum.CONSIGNLOAN,
        //   [AllLoanTypeEnum.FACTORING]: AccountGroupEnum.FACTORING
        // };
        // const loanAccountParam = {
        //   currencyId: contractInfo.currencyId,
        //   clientId: contractInfo.loanClientId,
        //   accountGroup: accountGroupMap[contractInfo.loanType]
        // };
        // httpTool.post(listLoanAccountUrl, loanAccountParam).then(res => {
        //   if (res.success && res.data.length > 0) {
        //     state.payNoticeInfo.loanAcctId = res.data[0].accountId;
        //     state.payNoticeInfo.loanAcctNo = res.data[0].accountCode;
        //     state.payNoticeInfo.loanAcctName = res.data[0].accountName;
        //   }
        // });
      }
      // emits("contract-change", state.baseInfo);
    },

    handleBaseInfoChange(params) {
      resetPayNoticeInfo(state.payNoticeInfo);
      doResetDefaultData();
      state.recentlyEffectiveDate = "";
      // 合同改变后，回显合同详细信息
      contractMethods
        .renderContractInfo({ ...params, businessCode: state.payNoticeInfo.businessCode })
        .then(contractInfo => {
          baseInfo.value.setBaseInfoFromContractInfo(contractInfo);
          mergeContractToPayNotice(contractInfo, state.payNoticeInfo);
          // 合同改变后，要重新根据合同开始时间获取挂牌利率生效日
          const param = { currencyId: contractInfo.currencyId, interestStartDate: contractInfo.contractStartDate };
          httpTool.post(getRecentlyEffectiveDate, param).then(res => {
            if (res.success) {
              state.recentlyEffectiveDate = res.data.effectiveDate;
            }
          });
          // 合同改变后，去查贷款客户的贷款账户
          const accountGroupMap = {
            [AllLoanTypeEnum.TRUST_LOAN]: AccountGroupEnum.SELFLOAN,
            [AllLoanTypeEnum.CONSIGN_LOAN]: AccountGroupEnum.CONSIGNLOAN,
            [AllLoanTypeEnum.FACTORING]: AccountGroupEnum.FACTORING
          };
          const loanAccountParam = {
            currencyId: contractInfo.currencyId,
            clientId: contractInfo.loanClientId,
            accountGroup: accountGroupMap[contractInfo.loanType]
          };
          httpTool.post(listLoanAccountUrl, loanAccountParam).then(res => {
            if (res.success && res.data.length > 0) {
              state.payNoticeInfo.loanAcctId = res.data[0].accountId;
              state.payNoticeInfo.loanAcctNo = res.data[0].accountCode;
              state.payNoticeInfo.loanAcctName = res.data[0].accountName;
            }
          });
        });
    },

    handleBoardRateCodeChange() {
      methods.calTrialResult();
    },

    handlePayInterestAcctNoChange(row) {
      if (row && row.accountCode) {
        state.payNoticeInfo.payInterestAcctId = row.accountId;
        state.payNoticeInfo.payInterestAcctNo = row.accountCode;
        state.payNoticeInfo.payInterestAcctName = row.accountName;
      } else {
        state.payNoticeInfo.payInterestAcctId = null;
        state.payNoticeInfo.payInterestAcctNo = "";
        state.payNoticeInfo.payInterestAcctName = "";
      }
    },
    handleRepayPrincipalAcctNoChange(row) {
      if (row && row.accountCode) {
        state.payNoticeInfo.repayPrincipalAcctId = row.accountId;
        state.payNoticeInfo.repayPrincipalAcctNo = row.accountCode;
        state.payNoticeInfo.repayPrincipalAcctName = row.accountName;
      } else {
        state.payNoticeInfo.repayPrincipalAcctId = null;
        state.payNoticeInfo.repayPrincipalAcctNo = "";
        state.payNoticeInfo.repayPrincipalAcctName = "";
      }
    },

    handleRecPrincipalAcctNoChange(row) {
      if (row && row.accountCode) {
        state.payNoticeInfo.recPrincipalAcctId = row.accountId;
        state.payNoticeInfo.recPrincipalAcctNo = row.accountCode;
        state.payNoticeInfo.recPrincipalAcctName = row.accountName;
      } else {
        state.payNoticeInfo.recPrincipalAcctId = null;
        state.payNoticeInfo.recPrincipalAcctNo = "";
        state.payNoticeInfo.recPrincipalAcctName = "";
      }
    },
    handlecConsignRecInterestAccountNoChange(row) {
      if (row && row.accountCode) {
        state.payNoticeInfo.consignRecInterestAcctId = row.accountId;
        state.payNoticeInfo.consignRecInterestAcctNo = row.accountCode;
        state.payNoticeInfo.consignRecInterestAcctName = row.clientName;
      } else {
        state.payNoticeInfo.consignRecInterestAcctId = null;
        state.payNoticeInfo.consignRecInterestAcctNo = "";
        state.payNoticeInfo.consignRecInterestAcctName = "";
      }
    },
    handleConsignCurrentAccountNoChange(row) {
      if (row && row.accountCode) {
        state.payNoticeInfo.consignCurrentAcctId = row.accountId;
        state.payNoticeInfo.consignCurrentAcctNo = row.accountCode;
        state.payNoticeInfo.consignCurrentAcctName = row.accountName;
      } else {
        state.payNoticeInfo.consignCurrentAcctId = null;
        state.payNoticeInfo.consignCurrentAcctNo = "";
        state.payNoticeInfo.consignCurrentAcctName = "";
      }
    },
    handlePayFeeAcctNoChange(row) {
      if (row && row.accountCode) {
        state.payNoticeInfo.payFeeAcctId = row.accountId;
        state.payNoticeInfo.payFeeAcctNo = row.accountCode;
        state.payNoticeInfo.payFeeAcctName = row.accountName;
      } else {
        state.payNoticeInfo.payFeeAcctId = null;
        state.payNoticeInfo.payFeeAcctNo = "";
        state.payNoticeInfo.payFeeAcctName = "";
      }
    },

    handleReferenceRateIdChange(val, row) {
      state.payNoticeInfo.referenceRateName = val ? row.label : "";
    },
    handlefixedRateChange() {
      state.payNoticeInfo.executeRate = state.payNoticeInfo.fixedRate;
    },
    handleRateRunTypeChange() {
      if (state.payNoticeInfo.rateRunType === LoanSetRateTypeEnum.FIXED) {
        state.payNoticeInfo.executeRate = state.payNoticeInfo.fixedRate;
      } else {
        methods.doCalRate();
      }
    },
    handlePayPlanChange(row) {
      state.payNoticeInfo.payPlanCode = row ? row.planCode : "";
    }
  };
  const methods = {
    setBaseInfo(baseInfo) {
      state.payNoticeInfo.officeId = baseInfo.officeId;
      state.payNoticeInfo.officeCode = baseInfo.officeCode;
      state.payNoticeInfo.officeName = baseInfo.officeName;
      state.payNoticeInfo.currencyId = baseInfo.currencyId;
      state.payNoticeInfo.currencyCode = baseInfo.currencyCode;
      state.payNoticeInfo.currencyName = baseInfo.currencyName;
      state.payNoticeInfo.loanClientId = baseInfo.loanClientId;
      state.payNoticeInfo.loanClientCode = baseInfo.loanClientCode;
      state.payNoticeInfo.loanClientName = baseInfo.loanClientName;
      state.payNoticeInfo.contractId = baseInfo.contractId;
      state.payNoticeInfo.contractCode = baseInfo.contractCode;
      state.payNoticeInfo.contractName = baseInfo.contractName;
      state.payNoticeInfo.isBuyAdd = baseInfo.isBuyAdd || 0;
      state.payNoticeInfo.settleMethod = baseInfo.settleMethod;
    },
    renderContractInfo(param) {
      return new Promise(resolve => {
        if (!param) {
          param = {
            contractId: contractInfo.contractId, // 合同ID
            contractCode: contractInfo.contractCode // 合同编号
          };
        }
        // resetContractInfo(contractInfo);
        if (param.contractId || param.contractCode) {
          httpTool.post(getContractDetailUrl, param).then(res => {
            if (res.success && res.data) {
              Object.assign(contractInfo, res.data);
              resolve(contractInfo);
            } else {
              resolve(contractInfo);
            }
          });
        } else {
          resolve(contractInfo);
        }
      });
    },
    setBaseInfoFromContractInfo(contractInfo) {
      state.baseInfo.loanType = contractInfo.loanType;
    },
    getContractInfo(contractParams, cb = () => {}) {
      contractMethods
        .renderContractInfo({ ...contractParams, businessCode: state.payNoticeInfo.businessCode })
        .then(contractInfo => {
          baseInfo.value.setBaseInfo(contractInfo);
          const param = { currencyId: contractInfo.currencyId, interestStartDate: contractInfo.contractStartDate };
          httpTool.post(getRecentlyEffectiveDate, param).then(res => {
            if (res.success) {
              state.recentlyEffectiveDate = res.data.effectiveDate;
            }
          });
          cb(contractInfo);
        });
    },
    getPayNoticeInfo(payNoticeParams, cb = () => {}) {
      httpTool.post(getPayNoticeUrl, payNoticeParams).then(res => {
        if (res.success) {
          Object.assign(state.payNoticeInfo, res.data);
          if (state.payNoticeInfo.rateRunType === LoanSetRateTypeEnum.FIXED) {
            state.payNoticeInfo.fixedRate = state.payNoticeInfo.executeRate;
          }
          state.payNoticeInfo.businessCode = res.data.businessCode;
          // if (state.payNoticeInfo.payPlanId) {
          //   state.payNoticeInfo.payPlanId = Number(state.payNoticeInfo.payPlanId);
          // }
          cb();
        }
      });
    },
    doCalRate,
    viewPayPlay() {
      payPlan.value.open({ contractId: contractInfo.contractId, contractCode: contractInfo.contractCode });
    },
    getFormData() {
      state.payNoticeInfo.clnLoanFileList = state.payNoticeInfo.clnLoanFileList.filter(x => !!x.fileId);
      return Object.assign({ floatingRateDate: state.recentlyEffectiveDate }, state.payNoticeInfo);
    },
    updateInfo(info) {
      nextTick(() => {
        state.payNoticeInfo.version = info.version;
        state.payNoticeInfo.loanCounterBusinessType = info.loanCounterBusinessType;
        state.payNoticeInfo.id = info.id;
        state.payNoticeInfo.businessCode = info.businessCode;
        state.payNoticeInfo.globalSerial = info.globalSerial;
      });
    },
    async validateForm(cb = () => {}) {
      if (state.loanAccountType === 3 && !state.payNoticeInfo.loanAcctId) {
        FMessage.error(t("loancounter.notice.pay.needLoanAcct"));
        return false;
      }
      return await form.value.form.validate(cb);
    },
    calTrialResult() {
      state.payNoticeInfo.floatingRate = "";
      if (state.payNoticeInfo.floatingRateCode) {
        const param = {
          boardRateCode: state.payNoticeInfo.floatingRateCode,
          currencyId: contractInfo.currencyId,
          computerDate: formatDate(new Date())
        };
        httpTool.post(getTrialResult, param).then(res => {
          if (res.success) {
            state.payNoticeInfo.floatingRate = res.data.rate;
          }
          doCalRate();
        });
      } else {
        doCalRate();
      }
    }
  };

  onBeforeMount(() => {
    if (props.id) {
      methods.getPayNoticeInfo({ id: props.id }, () => {
        emits("on-loaded", state.payNoticeInfo);
        // methods.getContractInfo({ contractId: data.contractId, contractCode: data.contractCode });
      });
    } else {
      const { pageParams } = usePage();
      if (pageParams?.type === "modify") {
        if (pageParams.data) {
          // 查询合同信息
          state.payNoticeInfo.businessCode = pageParams.data.businessCode;
          // const contractParams = { contractId: pageParams.data.contractId, contractCode: pageParams.data.contractCode };
          // methods.getContractInfo(contractParams);
          // 查询通知单信息
          const payNoticeParams = { id: pageParams.data.id, businessCode: pageParams.data.businessCode };
          methods.getPayNoticeInfo(payNoticeParams, () => {
            emits("on-loaded", state.payNoticeInfo);
          });
        }
      }
    }

    httpTool.get(getLoanAccountTypeUrl).then(res => {
      if (res.success) {
        state.loanAccountType = res.data;
      }
    });
  });

  return {
    state,
    currencyQueryParam,
    loanClientQueryParam,
    contractQueryParam,
    LoanAccountQueryParams,
    consignAccountQueryParams,
    boardRateQueryParams,
    payPlanQueryParams,
    showIsBuyAdd,
    handler,
    getOpenDate,
    methods
  };
}
