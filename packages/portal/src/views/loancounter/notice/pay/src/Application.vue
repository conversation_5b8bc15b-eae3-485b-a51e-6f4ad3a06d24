<template>
  <f-multi-form-panel ref="form" :model="state.payNoticeInfo" :rules="necessaryRules" :column="3">
    <!-- 基础信息 -->
    <f-panel :title="t('loancounter.notice.pay.basicInfo')">
      <!-- 机构 -->
      <f-form-item prop="officeId" :label="t('loancounter.notice.pay.officeName')" :required="true">
        <f-select
          v-model="state.payNoticeInfo.officeId"
          :url="listOfficeUrl"
          :disabled="baseInfoReadonly"
          value-key="officeId"
          label="officeName"
          @change="handler.handleOfficeChange"
        />
      </f-form-item>
      <!-- 币种 -->
      <f-form-item prop="currencyId" :label="t('loancounter.notice.pay.currencyName')" :required="true">
        <f-select
          ref="currency"
          v-model="state.payNoticeInfo.currencyId"
          :url="listCurrencyUrl"
          :disabled="baseInfoReadonly"
          :extra-data="currencyQueryParam"
          value-key="currencyId"
          label="currencyName"
          @change="handler.handleCurrencyChange"
        />
      </f-form-item>
      <!-- 借款单位 -->
      <f-form-item prop="loanClientId" :label="t('loancounter.notice.pay.loanClientName')" :required="true">
        <f-magnifier-single
          v-model="state.payNoticeInfo.loanClientId"
          :url="listLoanClientCodeUrl"
          :disabled="baseInfoReadonly"
          :title="t('loancounter.notice.pay.loanClientCode')"
          :placeholder="t('loancounter.notice.pay.loanClientCodeInput')"
          :params="loanClientQueryParam"
          :auto-init="true"
          method="post"
          row-key="clientId"
          row-label="clientName"
          input-key="clientCode"
          @change="handler.handleLoanClientChange"
        >
          <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.pay.loanClientCode')" />
          <f-magnifier-column prop="clientName" :label="t('loancounter.notice.pay.loanClientName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 贷款业务种类 -->
      <f-form-item prop="loanType" :label="t('loancounter.notice.pay.loanType')" :required="true">
        <f-select
          v-model="state.payNoticeInfo.loanType"
          @change="handler.handleLoanTypeChange"
          :data="loanTypeEnum"
          :disabled="baseInfoReadonly"
        />
      </f-form-item>
      <!-- 贷款业务类型 -->
      <f-form-item v-if="!isFactoringType" prop="loanBusinessType" :label="t('loancounter.notice.pay.loanBizType')">
        <f-scene-view :search="state.payNoticeInfo.loanBusinessType" :data="LoanBusinessCategory" params="value" />
      </f-form-item>
      <!-- 贷款产品 -->
      <f-form-item prop="productName" :label="t('loancounter.notice.pay.loanProductName')">
        <f-input v-model="state.payNoticeInfo.productName" disabled />
      </f-form-item>
      <!-- 委托单位名称 -->
      <f-form-item v-if="isConsignType" prop="consignClientName" :label="t('loancounter.notice.pay.consignClientName')">
        <f-input v-model="state.payNoticeInfo.consignClientName" disabled />
      </f-form-item>
      <!-- 合同编号 -->
      <f-form-item prop="contractId" :label="t('loancounter.notice.pay.contractCode')" :required="true">
        <f-magnifier-single
          v-model="state.payNoticeInfo.contractId"
          :url="listContractCodeUrl"
          :disabled="baseInfoReadonly"
          :title="t('loancounter.notice.pay.contractCode')"
          :placeholder="t('loancounter.notice.pay.contractCodeInput')"
          :params="contractQueryParam"
          auto-init
          method="post"
          row-key="contractId"
          row-label="contractCode"
          input-key="contractCode"
          @change="handler.handleContractChange"
        >
          <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.pay.contractCode')" />
          <f-magnifier-column
            prop="loanType"
            :label="t('loancounter.notice.pay.loanType')"
            :filter-select="loanTypeEnum"
          >
            <template #default="{ row }">
              {{ loanTypeEnum.find(x => x.code === row.loanType)?.label }}
            </template>
          </f-magnifier-column>
          <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.pay.loanClientName')" />
          <!-- 合同金额 -->
          <f-magnifier-column
            prop="contractTotalAmount"
            :label="t('loancounter.notice.pay.contractAmount')"
            formatter="amount"
            width="150px"
          />
          <!-- 贷款产品名称 -->
          <f-magnifier-column prop="productName" :label="t('loancounter.notice.pay.loanProductName')" width="150px" />
          <f-magnifier-column prop="contractStartDate" :label="t('loancounter.notice.pay.loanStartDate')" />
          <f-magnifier-column prop="contractEndDate" :label="t('loancounter.notice.pay.loanEndDate')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 放款申请单编号 -->
      <f-form-item v-if="state.payNoticeInfo.id" prop="businessCode" :label="t('loancounter.notice.pay.businessCode')">
        <f-input v-model="state.payNoticeInfo.businessCode" disabled />
      </f-form-item>
      <!-- 结息方式 -->
      <f-form-item v-if="showIsBuyAdd" prop="settleMethod" :label="t('loancounter.notice.pay.settleMethod')">
        <f-select v-model="state.payNoticeInfo.settleMethod" :data="SettleMethodEnum" disabled />
      </f-form-item>
    </f-panel>

    <!-- 合同基本信息 -->
    <f-panel :title="t('loancounter.notice.pay.loanInfo')">
      <!-- 合同金额 -->
      <f-form-item prop="contractAmount" :label="t('loancounter.notice.pay.contractAmount')">
        <f-amount v-model="state.payNoticeInfo.contractAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 已发放金额 -->
      <f-form-item :label="t('loancounter.notice.pay.inGrantAmount')">
        <f-amount v-model="state.payNoticeInfo.payedAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 已还款金额 -->
      <f-form-item :label="t('loancounter.notice.pay.repaymentAmount')">
        <f-amount v-model="state.payNoticeInfo.repayAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 可发放金额 -->
      <f-form-item prop="payAvailableAmount" :label="t('loancounter.notice.pay.payAvailableAmount')">
        <f-amount v-model="state.payNoticeInfo.contractAvailableAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 合同开始日期 -->
      <f-form-item prop="contractStartDate" :label="t('loancounter.notice.pay.contractStartDate')">
        <f-input v-model="state.payNoticeInfo.contractStartDate" disabled />
      </f-form-item>
      <!-- 合同期限 -->
      <f-form-item prop="contractTerm" :label="t('loancounter.notice.pay.contractTerm')">
        <f-number v-model="state.payNoticeInfo.contractTerm" disabled>
          <template #suffix>
            <span>{{ t("loancounter.notice.pay.month") }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 合同结束日期 -->
      <f-form-item prop="contractEndDate" :label="t('loancounter.notice.pay.contractEndDate')">
        <f-input v-model="state.payNoticeInfo.contractEndDate" disabled />
      </f-form-item>
      <!-- 是否全额受托支付 -->
      <f-form-item
        v-if="!isFactoringType"
        prop="allEntrustPayFlag"
        :label="t('loancounter.notice.pay.fullEntrustedPay')"
      >
        <f-switch
          v-model="state.payNoticeInfo.allEntrustPayFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 合同执行利率 -->
      <f-form-item prop="contractExecuteRate" :label="t('loancounter.notice.pay.contractExecuteRate')">
        <f-number
          v-model="state.payNoticeInfo.contractExecuteRate"
          :precision="4"
          max="99.9999"
          min="0.0000"
          is-rate
          disabled
        />
      </f-form-item>
      <!-- 是否循环贷款 -->
      <f-form-item v-if="!isFactoringType" prop="revolvingLoanFlag" :label="t('loancounter.notice.pay.cycleLoan')">
        <f-switch
          v-model="state.payNoticeInfo.revolvingLoanFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 保理贷款有无追索权 -->
      <f-form-item
        v-if="isFactoringType"
        prop="factoringRecourseFlag"
        :label="t('loancounter.notice.pay.factoringRecourseFlag')"
      >
        <f-switch
          v-model="state.payNoticeInfo.factoringRecourseFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 保理贷款是否公开 -->
      <f-form-item
        v-if="isFactoringType"
        prop="factoringPublicFlag"
        :label="t('loancounter.notice.pay.factoringPublicFlag')"
      >
        <f-switch
          v-model="state.payNoticeInfo.factoringPublicFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 借款用途 -->
      <f-form-item prop="loanPurpose" :label="t('loancounter.notice.pay.usageOfLoan')" :employ="2">
        <f-input v-model="state.payNoticeInfo.loanPurpose" disabled />
      </f-form-item>
    </f-panel>

    <!-- 放款单信息 -->
    <f-panel :title="t('loancounter.notice.pay.payNotice')">
      <!-- 放款日期 -->
      <f-form-item prop="payDate" :label="t('loancounter.notice.pay.payDate')" :required="necessary">
        <f-date-picker
          v-model="state.payNoticeInfo.payDate"
          type="date"
          :disabled="baseInfoReadonlyShow"
          :disabled-date="disabledPayDate"
          @change="handlePayDateChange"
        />
      </f-form-item>
      <!-- 放款金额 -->
      <f-form-item prop="payAmount" :label="t('loancounter.notice.pay.payAmount')" :required="necessary">
        <f-amount
          v-model="state.payNoticeInfo.payAmount"
          maxlength="15"
          :disabled="baseInfoReadonlyShow || isFactoringType"
          :max="state.payNoticeInfo.contractAvailableAmount || 10000"
          @change="handlePayAmountChange"
        />
      </f-form-item>
      <!-- 金额大写 -->
      <f-form-item :label="t('loancounter.notice.pay.payAmountChinese')">
        <f-amount-chinese v-model="state.payNoticeInfo.payAmount" disabled />
      </f-form-item>
      <!-- 贷款开始日期 -->
      <f-form-item :label="t('loancounter.notice.pay.loanStartDate')" :required="necessary" disabled>
        <f-date-picker v-model="state.payNoticeInfo.loanStartDate" type="date" disabled />
      </f-form-item>
      <!-- 贷款期限 -->
      <f-form-item prop="loanTerm" :label="t('loancounter.notice.pay.loanTerm')" :required="necessary" disabled>
        <f-number v-model="state.payNoticeInfo.loanTerm" disabled>
          <template #suffix>
            <span>{{ t("loancounter.notice.pay.month") }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 贷款结束日期 -->
      <f-form-item :label="t('loancounter.notice.pay.loanEndDate')" disabled>
        <f-date-picker v-model="state.payNoticeInfo.loanEndDate" type="date" disabled />
      </f-form-item>
      <!-- 借款人付息账户号 -->
      <f-form-item
        prop="payInterestAcctNo"
        v-if="!isFactoringType"
        :label="t('loancounter.notice.pay.payInterestAcctNo')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.payNoticeInfo.payInterestAcctNo"
          :url="listInterestSettNoUrl"
          :title="t('loancounter.notice.pay.interestSettNo')"
          :placeholder="t('loancounter.notice.pay.interestSettNoInput')"
          :params="LoanAccountQueryParams"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :disabled="baseInfoReadonlyShow"
          @change="handler.handlePayInterestAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('loancounter.notice.pay.interestSettNo')" />
          <f-magnifier-column prop="accountName" :label="t('loancounter.notice.pay.interestSettClientName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人还本账户号 -->
      <f-form-item
        prop="repayPrincipalAcctNo"
        :label="t('loancounter.notice.pay.repayPrincipalAcctNo')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.payNoticeInfo.repayPrincipalAcctNo"
          :url="listInterestSettNoUrl"
          :title="t('loancounter.notice.pay.interestSettNo')"
          :placeholder="t('loancounter.notice.pay.interestSettNoInput')"
          :params="LoanAccountQueryParams"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :disabled="baseInfoReadonlyShow"
          @change="handler.handleRepayPrincipalAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('loancounter.notice.pay.interestSettNo')" />
          <f-magnifier-column prop="clientName" :label="t('loancounter.notice.pay.interestSettClientName')" />
          <f-magnifier-column
            prop="accountGroup"
            :label="t('loancounter.notice.pay.accountGroup')"
            :formatter="{ name: 'const', const: 'basic.AccountGroup' }"
          >
          </f-magnifier-column>
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人贷款专户 -->
      <f-form-item
        prop="recPrincipalAcctNo"
        :label="t('loancounter.notice.pay.recPrincipalAcctNo')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.payNoticeInfo.recPrincipalAcctNo"
          :disabled="isFactoringType || baseInfoReadonlyShow"
          :url="listPayAccountNoUrl"
          :title="t('loancounter.notice.pay.payAccountNo')"
          :placeholder="t('loancounter.notice.pay.payAccountNoInput')"
          :params="{
            officeId: state.payNoticeInfo.officeId,
            officeCode: state.payNoticeInfo.officeCode,
            currencyId: state.payNoticeInfo.currencyId,
            currencyCode: state.payNoticeInfo.currencyCode,
            clientId: state.payNoticeInfo.loanClientId,
            includeLoanFlag: YesOrNoEnum.YES,
            loanFlag: YesOrNoEnum.YES,
            accountGroups: [AccountGroupEnum.CURRENT]
          }"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          @change="handler.handleRecPrincipalAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('loancounter.notice.pay.payAccountNo')" />
          <f-magnifier-column prop="accountName" :label="t('loancounter.notice.pay.payAccountName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 贷款利率类型 -->
      <f-form-item prop="rateRunType" :label="t('loancounter.notice.pay.loanRateType')">
        <f-scene-view :search="state.payNoticeInfo.rateRunType" :data="LoanContractRateRunType" params="value" />
      </f-form-item>
      <!-- 固定利率 -->
      <f-form-item prop="fixedRate" :label="t('loancounter.notice.pay.fixedRate')">
        <f-number v-model="state.payNoticeInfo.fixedRate" :precision="4" min="0.0000" is-rate disabled />
      </f-form-item>
      <!-- 浮动利率 -->
      <f-form-item v-if="!isFactoringType" prop="floatingRate" :label="t('loancounter.notice.pay.floatingRate')">
        <f-number
          v-model="state.payNoticeInfo.floatingRate"
          :precision="6"
          max="99.999999"
          min="0.0000"
          is-rate
          disabled
        />
      </f-form-item>
      <!-- 浮动方式 -->
      <f-form-item v-if="!isFactoringType" prop="floatingWay" :label="t('loancounter.notice.pay.floatingWay')">
        <f-scene-view :search="state.payNoticeInfo.floatingWay" params="value" :data="loanRateFloatType" />
      </f-form-item>
      <!-- 浮动比例 -->
      <f-form-item v-if="!isFactoringType" prop="floatingRatio" :label="t('loancounter.notice.pay.floatingType')">
        <f-number v-model="state.payNoticeInfo.floatingRatio" :precision="4" is-rate disabled />
      </f-form-item>
      <!-- 基点BP -->
      <f-form-item v-if="!isFactoringType" prop="basicPoint" :label="t('loancounter.notice.pay.basePoint')">
        <f-number
          v-model="state.payNoticeInfo.basicPoint"
          :whole-number="true"
          :min="0"
          disabled
          @change="methods.doCalRate"
        />
      </f-form-item>
      <!-- 执行利率 -->
      <f-form-item prop="executeRate" :label="t('loancounter.notice.pay.executeRate')">
        <f-number
          v-model="state.payNoticeInfo.executeRate"
          :precision="6"
          max="99.999999"
          min="0.000000"
          is-rate
          disabled
        />
      </f-form-item>
      <!-- 结息周期 -->
      <f-form-item
        prop="interestSettFreq"
        :label="t('loancounter.notice.pay.interestSettTerm')"
        v-if="state.payNoticeInfo.loanType !== AllLoanTypeEnum.FACTORING"
      >
        <f-select
          v-model="state.payNoticeInfo.interestSettFreq"
          :data="InterestFreqEnum"
          :disabled="baseInfoReadonlyShow"
        />
      </f-form-item>
      <!-- 首次结息日 -->
      <f-form-item
        prop="firstInterestDate"
        :label="t('loancounter.notice.pay.interestSettDateFirst')"
        v-if="!isFactoringType"
        :required="necessary"
      >
        <f-date-picker
          v-model="state.payNoticeInfo.firstInterestDate"
          type="date"
          :disabled-date="disabledFirstInterestDate"
          :disabled="baseInfoReadonlyShow"
        />
      </f-form-item>
      <!-- 利率调整方式 -->
      <f-form-item v-if="!isFactoringType" prop="rateAdjustWay" :label="t('loancounter.notice.pay.rateAdjustWay')">
        <f-scene-view :search="state.payNoticeInfo.rateAdjustWay" :data="LoanRateAdjustWayEnum" params="value" />
      </f-form-item>
      <!-- 利率调整周期 -->
      <f-form-item v-if="!isFactoringType" prop="rateAdjustFreq" :label="t('loancounter.notice.pay.rateAdjustFreq')">
        <f-scene-view :search="state.payNoticeInfo.rateAdjustFreq" :data="LoanRateAdjustFreqEnum" params="value" />
      </f-form-item>
      <!-- 是否周期第一天调整 -->
      <f-form-item
        v-if="!isFactoringType"
        prop="firstDayAdjustFlag"
        :label="t('loancounter.notice.pay.firstDayAdjustFlag')"
      >
        <f-switch
          v-model="state.payNoticeInfo.firstDayAdjustFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 调整日期 -->
      <f-form-item v-if="!isFactoringType" :label="t('loancounter.notice.pay.fixedAdjustDate')" disabled>
        <f-date-picker v-model="state.payNoticeInfo.fixedAdjustDate" type="date" disabled />
      </f-form-item>
      <!-- 是否按年重复 -->
      <f-form-item
        v-if="!isFactoringType"
        prop="repeatAnnuallyFlag"
        :label="t('loancounter.notice.pay.repeatAnnuallyFlag')"
      >
        <f-switch
          v-model="state.payNoticeInfo.repeatAnnuallyFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>

      <!-- 保理实付利息 -->
      <f-form-item
        v-if="isFactoringType"
        prop="prepaidInterest"
        :label="t('loancounter.notice.pay.factoringRealInterest')"
      >
        <f-amount v-model="state.payNoticeInfo.prepaidInterest" maxlength="15" disabled />
      </f-form-item>
      <!-- 保理实际发放金额 -->
      <f-form-item
        v-if="isFactoringType"
        prop="actualPayAmount"
        :label="t('loancounter.notice.pay.factoringRealPayAmount')"
      >
        <f-amount v-model="state.payNoticeInfo.actualPayAmount" maxlength="15" disabled />
      </f-form-item>

      <!-- 委托收息账户 -->
      <f-form-item
        v-if="isConsignType"
        prop="consignRecInterestAcctNo"
        :label="t('loancounter.notice.pay.consignRecInterestAccountNo')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.payNoticeInfo.consignRecInterestAcctNo"
          :url="listRecInterestAccountUrl"
          :title="t('loancounter.notice.pay.recInterestAccountId')"
          :placeholder="t('loancounter.notice.pay.recInterestAccountIdInput')"
          :params="consignAccountQueryParams"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          @change="handler.handlecConsignRecInterestAccountNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('loancounter.notice.pay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('loancounter.notice.pay.accountName')" />
          <f-magnifier-column prop="clientName" :label="t('loancounter.notice.pay.clientName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托活期账户 -->
      <f-form-item
        v-if="isConsignType"
        prop="consignCurrentAcctNo"
        :label="t('loancounter.notice.pay.consignCurrentAccountNo')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.payNoticeInfo.consignCurrentAcctNo"
          :url="listConsignCurrentAccountUrl"
          :title="t('loancounter.notice.pay.consignCurrentAccountId')"
          :placeholder="t('loancounter.notice.pay.consignCurrentAccountIdInput')"
          :params="consignAccountQueryParams"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          @change="handler.handleConsignCurrentAccountNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('loancounter.notice.pay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('loancounter.notice.pay.accountName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 备注 -->
      <f-form-item prop="remark" :label="t('loancounter.notice.pay.remark')" :employ="3">
        <f-textarea
          v-model="state.payNoticeInfo.remark"
          :min-rows="3"
          maxlength="50"
          :disabled="baseInfoReadonlyShow"
        />
      </f-form-item>
    </f-panel>

    <!-- 费用信息 -->
    <f-panel v-if="showFeeCharge" :title="t('loancounter.notice.pay.payFee')">
      <!-- 手续费收取方式 -->
      <f-form-item prop="feeChargeWay" :label="t('loancounter.notice.pay.chargeType')" :required="necessary">
        <f-select
          v-model="state.payNoticeInfo.feeChargeWay"
          @change="feeChargeWayChange"
          :data="
            LoanFeeChargeWayEnum.pickConst([
              LoanFeeChargeWayEnum.AUTO_SINGLE_PAY,
              LoanFeeChargeWayEnum.MANUAL_SINGLE_PAY,
              LoanFeeChargeWayEnum.AUTO_MULTI_PAY,
              LoanFeeChargeWayEnum.MANUAL_MULTI_PAY
            ])
          "
        />
      </f-form-item>
      <!-- 计费基础 -->
      <f-form-item v-if="showNotCharge" prop="feeChargeBasic" :label="t('loancounter.notice.pay.chargeBasics')">
        <f-scene-view :search="state.payNoticeInfo.feeChargeBasic" :data="FeeChargeBasicEnum" params="value" />
      </f-form-item>
      <!-- 手续费收取周期 -->
      <f-form-item v-if="showFeeChargeFreq" prop="feeChargeFreq" :label="t('loancounter.notice.pay.chargeCycle')">
        <f-select v-model="state.payNoticeInfo.feeChargeFreq" :data="CountPeriodTypeEnum" />
      </f-form-item>
      <!-- 付手续费账户号 -->
      <f-form-item
        v-if="showNotCharge"
        prop="payFeeAcctNo"
        :label="t('loancounter.notice.pay.payFeeAcct')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.payNoticeInfo.payFeeAcctNo"
          :url="listConsignCurrentAccountUrl"
          :title="t('loancounter.notice.pay.consignCurrentAccountId')"
          :placeholder="t('loancounter.notice.pay.payFeeAcctInput')"
          :params="consignAccountQueryParams"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          @change="handler.handlePayFeeAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('loancounter.notice.pay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('loancounter.notice.pay.accountName')" />
          <f-magnifier-column
            prop="accountGroup"
            :label="t('loancounter.notice.pay.accountGroup')"
            :formatter="{ name: 'const', const: 'basic.AccountGroup' }"
          >
          </f-magnifier-column>
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托贷款时手续费率 -->
      <f-form-item v-if="showNotCharge" prop="feeChargeRate" :label="t('loancounter.notice.pay.commissionRate')">
        <f-number v-model="state.payNoticeInfo.feeChargeRate" :precision="4" max="99.9999" min="0.0000" is-rate />
      </f-form-item>
      <!-- 委托贷款时手续费金额 -->
      <f-form-item v-if="showNotCharge" prop="feeAmount" :label="t('loancounter.notice.pay.chargeAmount')">
        <f-amount v-model="state.payNoticeInfo.feeAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 是否免收手续费 -->
      <f-form-item prop="freeChargeFlag" :label="t('loancounter.notice.pay.freeChargeFlag')">
        <f-switch
          v-model="state.payNoticeInfo.freeChargeFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
        />
      </f-form-item>
      <!-- 首次收费日 -->
      <f-form-item
        v-if="showNotFreeCharge"
        prop="firstFeeChargeDate"
        :label="t('loancounter.notice.pay.firstChargeDate')"
      >
        <f-date-picker v-model="state.payNoticeInfo.firstFeeChargeDate" type="date" />
      </f-form-item>
    </f-panel>

    <!-- 附件信息 -->
    <f-panel :title="t('loancounter.notice.pay.fileInfo')" v-if="false">
      <!-- 附件 -->
      <f-form-item :label="t('loancounter.notice.pay.file')" :employ="3">
        <f-attm-upload ref="upload" v-model="state.payNoticeInfo.fileList" :disabled="readonly" drag multiple />
      </f-form-item>
    </f-panel>
    <!-- <AttmInfo /> -->
    <f-panel id="attmInfo" :title="t(`loancounter.notice.pay.fileInfo`)">
      <f-form-item label-width="0" :employ="3" prop="clnLoanFileList" :rules="attmRules">
        <f-table
          border
          :data="state.payNoticeInfo.clnLoanFileList"
          style="padding: 10px 0"
          :cell-class-name="tableRequiredClass"
        >
          <f-table-column type="index" :label="t(`loancounter.notice.pay.serialNumber`)" align="center" width="50" />
          <f-table-column
            prop="fileType"
            :label="t(`loancounter.notice.pay.fileClassification`)"
            width="200"
            V
            :formatter="row => loanFileTypeEnum.valueToLabel(row.fileType)"
          />
          <f-table-column prop="fileName" align="center" :label="t(`loancounter.notice.pay.fileName`)" />
          <f-table-column
            prop="fileSize"
            align="center"
            :label="t(`loancounter.notice.pay.fileSize`)"
            width="100"
            formatter="fileSize"
          />
          <f-table-column
            prop="uploadTime"
            align="center"
            :label="t(`loancounter.notice.pay.uploadTime`)"
            width="150"
          />
          <f-table-column
            prop="inputUserName"
            align="center"
            :label="t(`loancounter.notice.pay.uploader`)"
            width="150"
          />
          <f-table-column :label="t(`loancounter.notice.pay.operation`)">
            <template #default="{ row, $index }">
              <div style="display: flex; width: 100%">
                <f-single-upload
                  v-if="showUpload(row)"
                  v-model="row.fileId"
                  :on-success="(res, uploadFile) => fileUpload(res, uploadFile, $index)"
                  :on-error="(err, file) => fileUploadError(err, file, $index)"
                  :on-remove="file => fileRemove(file, $index)"
                />
                <template v-if="row.fileId">
                  <f-button
                    v-if="!baseInfoReadonlyShow"
                    link
                    type="danger"
                    :disabled="false"
                    :icon="Delete"
                    @click="handleDelete(row.fileId)"
                  >
                    {{ t("common.operate.remove") }}
                  </f-button>
                  <f-button link type="primary" :icon="Download" @click="handleDownload(row.fileId)">
                    {{ t(`loancounter.notice.pay.download`) }}
                  </f-button>
                  <f-file-preview-button :fileId="row.fileId" link />
                </template>
              </div>
            </template>
          </f-table-column>
        </f-table>
      </f-form-item>
    </f-panel>
    <PayPlan ref="payPlan" />
  </f-multi-form-panel>
</template>
<script setup lang="ts">
import { shallowRef, computed, reactive, watch, watchEffect, onMounted, h } from "vue";
import { useI18n } from "vue-i18n";
import { FIcon, FMessageBox } from "@dtg/frontend-plus";
import { useConst } from "@ifs/support";
import useContractInfo from "../hooks/add/useContractInfo";
import useApplication from "../hooks/useApplication";
import useAppValidate from "../hooks/useAppValidate";
import { monthBetween, addMonths } from "@/utils/date";
import currencyAmount from "currency.js";
import PayPlan from "./PayPlan.vue";
import httpTool from "@/utils/http";
import { Delete, Download, DtgConfirm } from "@dtg/frontend-plus-icons";
import { downloadAttm } from "@/api/sys";
import { useUserStoreHook } from "@/stores/modules/user";
import {
  listOfficeUrl,
  listCurrencyUrl,
  listContractCodeUrl,
  listLoanClientCodeUrl,
  listInterestSettNoUrl,
  listConsignCurrentAccountUrl,
  listRecInterestAccountUrl,
  queryFee,
  listPayAccountNoUrl
} from "../url";
import useEnum from "../hooks/useEnum";
import { LoanPayFile, LoanPayFileXm, LoanPayFileLg } from "../types";
const { t } = useI18n();

const YesOrNoEnum = useConst("common.YesOrNo");
const AccountGroupEnum = useConst("basic.AccountGroup");
const LoanSetRateTypeEnum = useConst("loancounter.LoanSetRateType");
const FloatDirectionEnum = useConst("loancounter.FloatDirection");
const StaidAdjustRateTypeEnum = useConst("loancounter.FloatPoint");
const loanRateFloatType = useConst("common.LoanRateFloatType");
const AutoRepayModeEnum = useConst("loancounter.LoanAutoRepayMode");
const LoanFeeChargeWayEnum = useConst("loancounter.LoanFeeChargeWay");
const FeeChargeBasicEnum = useConst("loancounter.FeeChargeBasic");
const CountPeriodTypeEnum = useConst("loancounter.CountPeriodType");
const InterestFreqEnum = useConst("loancounter.LoanInterestFreq");
const LoanContractRateRunType = useConst("common.LoanContractRateRunType");
const AllLoanTypeEnum = useConst("loancounter.LoanType");
const loanFileTypeEnum = useConst("common.LoanFileTypeEnum");
const { loanTypeEnum, SettleMethodEnum, LoanBusinessCategory, LoanRateAdjustWayEnum, LoanRateAdjustFreqEnum } =
  useEnum(useConst);

const baseInfo = shallowRef();
const payPlan = shallowRef();

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  }
});

const emits = defineEmits(["on-loaded"]);

const form = shallowRef();

const { contractInfo, contractMethods } = useContractInfo();

const {
  state,
  currencyQueryParam,
  loanClientQueryParam,
  contractQueryParam,
  LoanAccountQueryParams,
  consignAccountQueryParams,
  showIsBuyAdd,
  handler,
  getOpenDate,
  methods
} = useApplication(
  t,
  props,
  form,
  emits,
  baseInfo,
  contractInfo,
  contractMethods,
  payPlan,
  AllLoanTypeEnum,
  AccountGroupEnum,
  FeeChargeBasicEnum,
  LoanSetRateTypeEnum,
  FloatDirectionEnum,
  StaidAdjustRateTypeEnum,
  AutoRepayModeEnum
);

const rules = useAppValidate(t, state, contractInfo);

const baseInfoReadonly = computed(() => props.readonly || state.payNoticeInfo.id);
const baseInfoReadonlyShow = computed(() => props.readonly);
// 是否委托贷款
const isConsignType = computed(() => state.payNoticeInfo.loanType === AllLoanTypeEnum.CONSIGN_LOAN);
// 是否保理
const isFactoringType = computed(() => state.payNoticeInfo.loanType === AllLoanTypeEnum.FACTORING);
const showFeeChargeFreq = computed(
  () =>
    state.payNoticeInfo.feeChargeWay !== LoanFeeChargeWayEnum.AUTO_SINGLE_PAY &&
    state.payNoticeInfo.feeChargeWay !== LoanFeeChargeWayEnum.MANUAL_SINGLE_PAY
);
// 显示手续费相关控件
const showFeeCharge = computed(
  () =>
    (isConsignType.value || isFactoringType.value) &&
    state?.payNoticeInfo.feeChargeBasic === FeeChargeBasicEnum.PAY_FORM_BALANCE
);
// 手续费收取方式不为不计费的条件
const showNotCharge = computed(
  () => showFeeCharge.value && state?.payNoticeInfo.feeChargeBasic !== LoanFeeChargeWayEnum.NOT_CHARGE
);
// 是否免收手续费
const showNotFreeCharge = computed(
  () =>
    state?.payNoticeInfo.freeChargeFlag === YesOrNoEnum.NO &&
    state.payNoticeInfo.feeChargeWay !== LoanFeeChargeWayEnum.AUTO_SINGLE_PAY &&
    state.payNoticeInfo.feeChargeWay !== LoanFeeChargeWayEnum.MANUAL_SINGLE_PAY
);

const disabledPayDate = date => {
  if (!state.payNoticeInfo.contractEndDate) {
    return true;
  }
  return new Date(state.payNoticeInfo.contractEndDate + " 00:00:00") < date;
};

const disabledFirstInterestDate = date => {
  if (!state.payNoticeInfo.payDate) {
    return true;
  }
  if (!state.payNoticeInfo.loanEndDate) {
    return true;
  }
  if (date < new Date(state.payNoticeInfo.payDate + " 00:00:00")) {
    return true;
  }
  if (date > new Date(state.payNoticeInfo.contractEndDate + " 00:00:00")) {
    return true;
  }
  return false;
};

const handlePayDateChange = () => {
  if (state.payNoticeInfo.payedAmount > 0) {
    state.payNoticeInfo.loanStartDate = state.payNoticeInfo.payDate;
    state.payNoticeInfo.loanTerm = monthBetween(state.payNoticeInfo.loanStartDate, state.payNoticeInfo.loanEndDate);
    state.payNoticeInfo.firstFeeChargeDate = state.payNoticeInfo.payDate;
  } else {
    state.payNoticeInfo.loanStartDate = state.payNoticeInfo.payDate;
    state.payNoticeInfo.loanEndDate = addMonths(
      state.payNoticeInfo.loanStartDate,
      Number(state.payNoticeInfo.loanTerm)
    );
    state.payNoticeInfo.firstFeeChargeDate = state.payNoticeInfo.payDate;
  }
};

const necessary = computed(() => !props.readonly);
const necessaryRules = computed(() => (props.readonly ? [] : rules));

defineExpose(methods);
const fileTypeList = computed(() => {
  let result = [];
  if (
    state.payNoticeInfo.loanBusinessType === LoanBusinessCategory.WORKING_CAPITAL_LOAN ||
    state.payNoticeInfo.loanBusinessType === LoanBusinessCategory.FIXED_ASSET_LOAN
  ) {
    //流贷和固贷
    result = LoanPayFileLg;
  } else if (state.payNoticeInfo.loanBusinessType === LoanBusinessCategory.PROJECT_FINANCING) {
    //项目
    result = LoanPayFileXm;
  } else {
    result = LoanPayFile;
  }
  return result;
});
const handleDownload = fileId => {
  downloadAttm(fileId);
};
const handleDelete = fileId => {
  FMessageBox.confirm("删除数据确认", "Warning", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    title: "确认删除",
    buttonSize: "default",
    message: h("div", { style: { display: "flex", alignItems: "center" } }, [
      h(FIcon, { size: 30 }, [h(DtgConfirm)]),
      h("div", { style: { paddingLeft: "10px" } }, "您确定要删除这些数据吗？")
    ])
  }).then(() => {
    const target = state.payNoticeInfo.clnLoanFileList.find(x => x.fileId === fileId);
    target.fileId = "";
    target.fileName = undefined;
    target.fileSize = undefined;
    target.uploadTime = undefined;
    target.inputUserName = undefined;
  });
};
const userStore = useUserStoreHook();
watch(
  () => fileTypeList.value,
  () => {
    const fileLists = fileTypeList.value.map(x => ({ fileType: x.fileType, fileId: "", required: x.required }));
    fileLists.forEach(x => {
      if (!Array.isArray(state.payNoticeInfo.clnLoanFileList)) return;
      const item = state.payNoticeInfo.clnLoanFileList.find(y => y.fileType === x.fileType);
      if (item && item.fileId) {
        Object.assign(x, item);
      }
    });
    state.payNoticeInfo.clnLoanFileList = fileLists;
  },
  {
    immediate: true,
    deep: true
  }
);
const showUpload = (row: any) => {
  if (props.isDetail) {
    return true;
  }
  if (props.readonly) {
    return false;
  }
  if (row.isReplenish || row.fileId) {
    return false;
  }

  return true;
};
const fileUpload = (res, uploadFile, $index) => {
  state.payNoticeInfo.clnLoanFileList[$index].fileId = res.data.id;
  state.payNoticeInfo.clnLoanFileList[$index].fileName = res.data.fileName;
  state.payNoticeInfo.clnLoanFileList[$index].fileSize = res.data.dataSize;
  state.payNoticeInfo.clnLoanFileList[$index].uploadTime = res.data.inputTime;
  state.payNoticeInfo.clnLoanFileList[$index].inputUserName = userStore.loginUserName || userStore.username;
  // console.log("fileUpload", res, uploadFile, $index);
};
const fileUploadError = (err, file) => {
  FMessageBox.report({ type: "error", message: `${file.name}上传失败，${err.message}` });
  // console.log("fileUploadError", err, file, $index);
};
const fileRemove = async (fileId, index) => {
  const { fileType } = state.payNoticeInfo.clnLoanFileList[index];
  state.payNoticeInfo.clnLoanFileList[index] = { fileType, fileId: "" };
};
const tableRequiredClass = ({ row, column }) => {
  if (row.required && column.property === "fileType") {
    return "is-required";
  }
  return "";
};
const attmRules = reactive([
  {
    validator(rule, value, callback) {
      const requiredList = fileTypeList.value.filter(x => x.required).map(x => x.fileType);
      const fileMissList = state.payNoticeInfo.clnLoanFileList.filter(
        x => requiredList.includes(x.fileType) && !x.fileId
      );
      if (fileMissList.length > 0) {
        const fileMissNameList = fileMissList.map(x => loanFileTypeEnum.valueToLabel(x.fileType));
        callback(new Error(`${fileMissNameList.join(", ")}为必填附件，请上传`));
      } else {
        callback();
      }
    }
  }
]);
const handlePayAmountChange = () => {
  state.payNoticeInfo.prepaidInterest = currencyAmount(
    state.payNoticeInfo.payAmount * (state.payNoticeInfo.executeRate / 100 / 360) * state.payNoticeInfo.loanTerm * 30,
    { precision: 2 }
  ).value;
  state.payNoticeInfo.actualPayAmount = state.payNoticeInfo.payAmount - state.payNoticeInfo.prepaidInterest;
};
const requestFeeAmount = async () => {
  let chargeType = "";
  let receiveType = "";

  switch (state.payNoticeInfo.loanType) {
    case AllLoanTypeEnum.CONSIGN_LOAN:
      chargeType = "ENTRUSTED_LOAN";
      break;
    case AllLoanTypeEnum.FACTORING:
      chargeType = "FACTORING";
      break;
    default:
      break;
  }

  switch (state.payNoticeInfo.feeChargeWay) {
    case LoanFeeChargeWayEnum.AUTO_SINGLE_PAY:
      receiveType = "AUTOMATICPAYMENT";
      break;
    case LoanFeeChargeWayEnum.MANUAL_SINGLE_PAY:
      receiveType = "MANUALPAYMENT";
      break;
    case LoanFeeChargeWayEnum.AUTO_MULTI_PAY:
      receiveType = "AUTODIVISION";
      break;
    case LoanFeeChargeWayEnum.MANUAL_MULTI_PAY:
      receiveType = "MANUALDIVISION";
      break;
    default:
      break;
  }

  if (
    !chargeType ||
    !receiveType ||
    !state.payNoticeInfo.officeId ||
    !state.payNoticeInfo.currencyId ||
    !state.payNoticeInfo.loanClientId ||
    !state.payNoticeInfo.feeChargeRate ||
    !state.payNoticeInfo.payAmount
  ) {
    console.warn("手续费计算: 参数不全, 跳过");
    return;
  }

  try {
    const res = await httpTool.post(queryFee, {
      officeId: state.payNoticeInfo.officeId,
      currencyId: state.payNoticeInfo.currencyId,
      clientId: state.payNoticeInfo.loanClientId,
      chargeRate: state.payNoticeInfo.feeChargeRate,
      receiveType,
      chargeType,
      businessAmount: state.payNoticeInfo.payAmount,
      countPeriodType: state.payNoticeInfo.feeChargeFreq,
      startDate: state.payNoticeInfo.payDate,
      loanTerm: state.payNoticeInfo.loanTerm,
      endDate: state.payNoticeInfo.loanEndDate
    });

    if (res && res.success) {
      state.payNoticeInfo.feeAmount = res.data.chargeAmount;
      state.payNoticeInfo.feeChargeRuleId = res.data.chargeRuleId;
      state.payNoticeInfo.feeChargeRuleVersion = res.data.chargeRuleVersion;
      state.payNoticeInfo.feeChargeRuleCode = res.data.chargeRuleCode;
      state.payNoticeInfo.feeChargeRuleName = res.data.chargeRuleName;
    } else {
      console.error("res error", res);
    }
  } catch (e) {
    console.error("err", e);
  }
};
watchEffect(() => {
  if (props.isDetail) {
    return;
  }
  if (state?.payNoticeInfo.feeChargeBasic === FeeChargeBasicEnum.PAY_FORM_BALANCE) {
    requestFeeAmount();
  }
});
// 页面初始化
onMounted(() => {
  getOpenDate();
});
</script>
