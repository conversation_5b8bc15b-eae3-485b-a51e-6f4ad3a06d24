<template>
  <f-query-scene :title="t('loancounter.notice.pay.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="state.queryForm"
        :url="listPayNoticeUrl"
        :export-url="exportPayNoticeUrl"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        :is-single="false"
        table-type="Record"
        :table-columns="tableColumns"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :show-collapse="true"
        :export-exclude="['operate']"
        query-comp-id="loancounter-notice-pay-query"
        table-comp-id="loancounter-notice-pay-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :status-enum="LoanPayNoticeStatusEnum"
            :submit-batch-url="submitPayNoticerBatchUrl"
            :delete-batch-url="deletePayNoticeBatchUrl"
            :revoke-batch-url="revokePayNoticeBatchUrl"
            @on-add="handler.handleAdd"
          />
        </template>

        <!-- 查询面本表单项 -->
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item prop="officeList" :label="t('loancounter.notice.pay.officeName')">
            <f-select
              v-model="state.queryForm.officeList"
              :url="listOfficeUrl"
              value-key="officeId"
              label="officeName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item prop="currencyList" :label="t('loancounter.notice.pay.currencyName')">
            <f-select
              v-model="state.queryForm.currencyList"
              :url="listCurrencyUrl"
              value-key="currencyId"
              label="currencyName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 贷款业务种类 -->
          <f-form-item prop="loanTypeList" :label="t('loancounter.notice.pay.loanType')">
            <f-select
              v-model="state.queryForm.loanTypeList"
              :data="loanTypeEnum"
              value-key="value"
              label="label"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 合同编号 -->
          <f-form-item prop="contractCode" :label="t('loancounter.notice.pay.contractCode')">
            <f-input v-model="state.queryForm.contractCode" />
          </f-form-item>
          <!-- 贷款期限 -->
          <f-form-item prop="loanTermArray" :label="t('loancounter.notice.pay.loanTermArray')">
            <f-amount-range v-model="state.queryForm.loanTermArray" wholeNumber symbol=" " tooltip />
          </f-form-item>
          <!-- 借款客户名称 -->
          <f-form-item prop="loanClientCodeList" :label="t('loancounter.notice.pay.loanClientCodeList')">
            <f-magnifier-multi
              :title="t('loancounter.notice.pay.loanClientCodeList')"
              :url="clientListUrl"
              method="post"
              v-model="state.queryForm.loanClientCodeList"
              row-key="clientCode"
              row-label="clientName"
              input-key="exCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.pay.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loancounter.notice.pay.clientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 委托客户名称 -->
          <f-form-item prop="consignClientCodeList" :label="t('loancounter.notice.pay.consignClientCodeList')">
            <f-magnifier-multi
              :title="t('loancounter.notice.pay.consignClientCodeList')"
              :url="clientListUrl"
              method="post"
              v-model="state.queryForm.consignClientCodeList"
              row-key="clientCode"
              row-label="clientName"
              input-key="exCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.pay.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('loancounter.notice.pay.clientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 放款通知单号 -->
          <f-form-item prop="businessCode" :label="t('loancounter.notice.pay.businessCode')">
            <f-input v-model="state.queryForm.businessCode" />
          </f-form-item>
          <!-- 放款金额 -->
          <f-form-item prop="payAmountArray" :label="t('loancounter.notice.pay.payAmountArray')">
            <f-amount-range v-model="state.queryForm.payAmountArray" max="9999999999999.99" tooltip :precision="2" />
          </f-form-item>
          <!-- 放款日期 -->
          <f-form-item prop="payDateArray" :label="t('loancounter.notice.pay.payDate')">
            <f-date-picker v-model="state.queryForm.payDateArray" type="daterange" />
          </f-form-item>
          <!-- 数据来源 -->
          <f-form-item prop="businessDataSourceList" :label="t('loancounter.notice.pay.businessDataSourceList')">
            <f-select
              v-model="state.queryForm.businessDataSourceList"
              :data="ChannelEnum"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 录入日期 -->
          <f-form-item prop="inputTimeArray" :label="t('loancounter.notice.pay.inputDateArray')">
            <f-date-picker v-model="state.queryForm.inputDateArray" type="daterange" />
          </f-form-item>
          <!-- 单据状态 -->
          <f-form-item prop="businessStatusList" :label="t('loancounter.notice.pay.businessStatus')">
            <f-select
              v-model="state.queryForm.businessStatusList"
              :data="LoanPayNoticeStatusEnum"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
        </template>

        <!-- 表格列 -->
        <template #link="{ row }">
          <f-button type="text" @click="openDetail(row)">
            {{ row.businessCode }}
          </f-button>
        </template>

        <template #loanType="{ row }">
          {{ loanTypeEnum.find(x => x.code === row.loanType)?.label }}
        </template>

        <template #businessType="{ row }">
          {{ LoanBusinessType.find(x => x.code === row.businessType)?.label }}
        </template>

        <template #greenCredit="{ row }">
          {{ YesOrNoEnum.find(x => x.code === row.greenCredit)?.label || row.greenCredit }}
        </template>

        <template #businessStatus="{ row }">
          {{ CommonBusinessStatusEnum.find(x => x.code === row.businessStatus)?.label }}
        </template>

        <template #buttons="{ row }">
          <OperateGroup
            :row="row"
            :submit-url="submitPayNoticerUrl"
            :delete-url="deletePayNoticeUrl"
            :revoke-url="revokePayNoticeUrl"
            @on-modify="handler.handleModify"
            @operate-success="methods.reloadTable"
          />
        </template>
      </f-query-grid>
    </template>
    <View ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import View from "./View.vue";
import {
  listPayNoticeUrl,
  exportPayNoticeUrl,
  listOfficeUrl,
  listCurrencyUrl,
  clientListUrl,
  submitPayNoticerUrl,
  revokePayNoticeUrl,
  deletePayNoticeUrl,
  submitPayNoticerBatchUrl,
  deletePayNoticeBatchUrl,
  revokePayNoticeBatchUrl
} from "../url";
import useTableColumns from "../hooks/list/useTableColumns";
import useList from "../hooks/useList";
import useEnum from "../hooks/useEnum";
import { useListDetail } from "@/hooks/biz";
import OperateGroup from "../../components/OperateGroup.vue";
import OperateBatchGroup from "../../components/OperateBatchGroup.vue";

const { t } = useI18n();

const YesOrNoEnum = useConst("common.YesOrNo");
const ChannelEnum = useConst("loancounter.Channel");
const CommonBusinessStatusEnum = useConst("common.BusinessStatus");

const LoanBusinessType = useConst("loancounter.LoanBusinessType");
const { loanTypeEnum, LoanPayNoticeStatusEnum } = useEnum(useConst);

const queryTable = shallowRef();
const operateGroup = shallowRef();

const { tableColumns, columnSort, defaultSort } = useTableColumns(t, queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};

const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};

const { state, handler, methods } = useList(t, queryTable);
</script>
<!-- <f-form-item prop="loanClientCode" :label="t('loancounter.notice.pay.loanClientCode')"> -->
<!-- <f-input v-model="state.queryForm.loanClientCode" /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="loanClientName" :label="t('loancounter.notice.pay.loanClientName')"> -->
<!-- <f-input v-model="state.queryForm.loanClientName" /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="consignClientCode" :label="t('loancounter.notice.pay.consignClientCode')"> -->
<!-- <f-input v-model="state.queryForm.consignClientCode" /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="consignClientName" :label="t('loancounter.notice.pay.consignClientName')"> -->
<!-- <f-input v-model="state.queryForm.consignClientName" /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="greenCredit" :label="t('loancounter.notice.pay.greenCredit')"> -->
<!-- <f-select v-model="state.queryForm.greenCredit" :data="YesOrNoEnum" /> -->
<!-- </f-form-item> -->

<!-- <f-form-item prop="contractCode" :label="t('loancounter.notice.pay.contractCode')"> -->
<!-- <f-magnifier-single -->
<!-- v-model="state.queryForm.contractCode" -->
<!-- :url="listContractCodeUrl" -->
<!-- :title="t('loancounter.notice.pay.contractCode')" -->
<!-- :placeholder="t('loancounter.notice.pay.contractCodeInput')" -->
<!-- :params="contractParams" -->
<!-- method="post" -->
<!-- row-key="contractCode" -->
<!-- row-label="contractCode" -->
<!-- input-key="contractCode" -->
<!-- &gt; -->
<!-- <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.pay.contractCode')" /> -->
<!-- <f-magnifier-column -->
<!-- prop="loanType" -->
<!-- :label="t('loancounter.notice.pay.loanType')" -->
<!-- :filter-select="loanTypeEnum" -->
<!-- > -->
<!-- <template #default="{ row }"> -->
<!-- {{ loanTypeEnum.find(x => x.code === row.loanType)?.label }} -->
<!-- </template> -->
<!-- </f-magnifier-column> -->
<!-- <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.pay.loanClientName')" /> -->
<!-- <f-magnifier-column prop="contractEndDate" :label="t('loancounter.notice.pay.loanEndDate')" /> -->
<!-- </f-magnifier-single> -->
<!-- </f-form-item> -->
