export default {
  rateAdjustWay: "利率调整方式",
  rateAdjustFreq: "利率调整周期",
  firstDayAdjustFlag: "周期第一天调整",
  fixedAdjustDate: "调整日期",
  repeatAnnuallyFlag: "每年重复",
  freeChargeFlag: "是否免收手续费",
  displayBackContractDateFlag: "是否返显合同日期",
  fixedRate: "固定利率",

  payInterestAcctNo: "借款人付息账户号",
  repayPrincipalAcctNo: "借款人还本账户号",
  recPrincipalAcctNo: "借款人贷款专户",

  consignRecInterestAccountNo: "委托人收息账户号",
  consignCurrentAccountNo: "委托人活期账户号",

  factoringRecourseFlag: "有无追索权",
  factoringPublicFlag: "是否公开",
  factoringRealInterest: "实付利息",
  factoringRealPayAmount: "实际发放金额",

  loanTermArray: "贷款期限",
  businessInfo: "单据信息",
  billCode: "单据号",
  officeInfo: "办理机构",
  loanTypeInfo: "贷款合同",
  compactLoanType: "贷款种类",
  compactLoanBizType: "贷款类型",
  compactLoanTerm: "期限（月）",
  compactLoanStartDate: "开始日",
  compactLoanEndDate: "到期日",
  compactLoanClientCode: "借款客户",
  compactConsignClientCode: "委托客户",
  loanClientCodeList: "借款单位名称",
  consignClientCodeList: "委托单位名称",
  clientCode: "单位编号",
  inputDateArray: "录入日期",
  businessDataSourceList: "数据来源",
  industryCategoryFuzzy: "贷款投向行业",
  loanInvestArea: "贷款投向地区",
  fundsSource: "资金来源",
  listTitle: "放款通知单 - 链接查找",
  businessCode: "放款通知单号",
  businessCodeInput: "请选择放款通知单编号",
  officeName: "机构",
  currencyName: "币种",
  loanType: "贷款业务种类",
  loanBizType: "贷款业务类型",
  loanProduct: "贷款产品",
  loanProductName: "贷款产品名称",
  loanProductNameInput: "贷款产品接口尚未提供",
  businessType: "业务类型",
  contractCode: "合同编号",
  loanClientCode: "借款单位编号",
  loanClientName: "借款单位名称",
  consignClientCode: "委托单位编号",
  consignClientName: "委托单位名称",
  payAmount: "放款金额",
  payAmountInput: "请输入放款金额",
  payAmountMax: "放款金额不能大于可发放金额",
  contractAmount: "合同金额",
  contractExecuteRate: "合同执行利率",
  executeRate: "执行利率",
  greenCredit: "是否绿色贷款",
  greenCreditType: "绿色贷款分类",
  technologyLoans: "是否科技贷款",
  technologyLoansName: "科技贷款分类",
  cycleLoan: "是否循环贷款",
  accountGroup: "账户类型",
  fullEntrustedPay: "是否全额受托支付",
  businessStatus: "单据状态",
  inputUserName: "录入人",
  inputTime: "录入时间",
  payAmountArray: "放款金额",
  listedCompany: "是否上市",
  isMechanism: "是否显示机制",
  add: "新增",
  batchDelete: "批量删除",
  addPayNotice: "新增放款通知单",
  goBack: "链接查找",
  doReturn: "返回",
  payFee: "费用信息",
  addTitle: "放款通知单 - 新增",
  contractAmountTitle: "合同金额",
  payAvailableAmount: "可发放金额",
  contractStartDate: "合同开始日",
  contractEndDate: "合同结束日",
  contractTerm: "期限（月）",
  payAccountNo: "借款人贷款专户",
  payAccountName: "账户名称",
  payAmountTitle: "金额",
  rateRunType: "贷款利率方式",
  loanRateType: "贷款利率类型",
  fixedRateRunType: "固定利率",
  floatingRateRunType: "浮动利率",
  floatingRateId: "挂牌利率",
  floatingRate: "浮动利率",
  floatingWay: "浮动方式",
  floatingType: "浮动比例",
  floatingRatio: "浮动比例",
  basePoint: "基点",
  pointType: "基点浮动方式",
  pointFloating: "基点浮动值",
  referenceRateId: "参考利率",
  interestSettTerm: "结息周期",
  interestSettDateFirst: "首次结息日",
  loanStartDate: "贷款开始日",
  loanEndDate: "贷款结束日",
  loanTerm: "期限（月）",
  loanTermMonth: "贷款期限（月）",
  rejectionReason: "驳回原因",
  revocationReason: "撤销原因",
  remark: "备注",
  contractCodeInput: "请选择合同编号",
  loanClientCodeInput: "请选择借款单位",
  inGrantAmount: "已发放金额",
  unGrantAmount: "未发放金额",
  repaymentAmount: "已还款金额",
  usageOfLoan: "借款用途",
  delegatePayType: "支付方式",
  interestSettNo: "借款人还本账户号",
  interestSettNoInput: "请选择借款人还本账户号",
  interestSettClientName: "借款人还本账户名称",
  payDate: "放款日期",
  autoRepayFlag: "是否自动还款",
  payAccountNoInput: "请选择借款人贷款专户",
  payAmountChinese: "金额大写",
  floatingRateIdInput: "请选择挂牌利率",
  floatingRateCode: "利率编号",
  floatingRateName: "利率名称",
  accountBank: "开户行",
  accountBankInput: "请选择开户行",
  batchSubmit: "批量提交",
  batchRevoke: "批量撤销",
  buyAddFlag: "是否补录",
  yes: "是",
  no: "否",
  settleMethod: "结息方式",
  accountCode: "账户编号",
  accountName: "账户名称",
  consignCurrentAccountId: "委托人活期账户号",
  payFeeAcct: "付手续费账户",
  payFeeAcctInput: "请选择付手续费账户",
  consignCurrentAccountIdInput: "请选择委托人活期账户号",
  recInterestAccountId: "委托人收息账户号",
  recInterestAccountIdInput: "请选择委托人收息账户号",
  entrustInterestName: "委托方收息客户名称",
  payPlanId: "放款计划",
  payPlanIdInput: "请选择放款计划",
  payPlanCode: "放款计划编号",
  payPlanName: "放款计划名称",
  payPlanDate: "计划日期",
  autoRepayMode: "还本息方式",
  chargeType: "手续费收取方式",
  chargeBasics: "计费基础",
  commissionRate: "手续费费率",
  chargeAmount: "手续费金额",
  chargeCycle: "手续费收取周期",
  firstChargeDate: "首次收费日",
  chargeAccountId: "手续费账户号",
  chargeAccountIdInput: "请选择手续费账户",
  chargeAccountName: "手续费账号名称",
  payPlanTitle: "执行计划",
  save: "保存",
  submit: "提交",
  revoke: "撤销",
  delete: "删除",
  close: "关闭",
  viewPayPlay: "查看执行计划",
  deleteConfirm: "确认删除？",
  deleteSuccess: "删除成功！",
  submitConfirm: "确认提交？",
  submitSuccess: "提交成功！",
  revokeConfirm: "确认撤销？",
  revokeSuccess: "撤销成功！",
  operate: "操作",
  payPlayCode: "还款计划编号",
  payPlayName: "还款计划名称",
  modifyTitle: "放款通知单 - 修改",
  viewTitle: "放款通知单 - 查看",
  modify: "修改",
  revokeBtch: "批量撤销",
  approvalTitle: "放款通知单 - 审批",
  planDate: "计划日期",
  planType: "计划类型",
  planAmount: "计划金额",
  loanRepayPlanType: "类型",
  fileInfo: "附件详情",
  file: "附件",
  emptyRate: "利率不能为空",
  emptyLoanStartDate: "贷款开始日期不能为空",
  loanStartDateMin: "贷款开始日期不能早于合同开始日期",
  loanStartDateMax: "贷款开始日期不能晚于合同结束日期",
  emptyLoanEndDate: "贷款结束日期不能为空",
  loanEndDateMin: "贷款结束日期不能早于合同开始日期",
  loanEndDateMax: "贷款结束日期不能晚于合同结束日期",
  emptyPayDate: "放款日期不能为空",
  payDateMin: "放款日期不能早于合同开始日期",
  payDateMax: "放款日期不能晚于合同结束日期",
  emptyFirstInterestSettDate: "首期结息日不能为空",
  firstInterestSettDateMin: "首期结息日不能早于贷款开始日期",
  firstInterestSettDateMax: "首期结息日不能晚于贷款结束日期",
  month: "月",
  day: "天",
  calTrialResult: "测算",
  basicInfo: "基础信息",
  loanInfo: "合同基本信息",
  payNotice: "放款单信息",
  payWay: "放款方式",
  wfHistoryTitle: "审批历史",
  marginRatio: "保证金比例",
  marginAmount: "保证金金额",
  gracePeriod: "宽限期",
  needLoanAcct: "请先开立贷款账户",
  clientName: "单位名称",
  fileClassification: "文件分类名称",
  fileName: "文件名",
  fileSize: "文件大小",
  uploadTime: "上传时间",
  download: "下载",
  uploader: "上传人",
  serialNumber: "序号",
  operation: "操作",
  actualPayAmount: "实际发放金额",
  prepaidInterest: "实付利息"
};
