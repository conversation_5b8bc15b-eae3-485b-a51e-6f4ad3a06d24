<template>
  <f-multi-form-panel ref="form" :model="drawInfo" :rules="necessaryRules" :column="3" :disabled="readonly">
    <!-- 基础信息 -->
    <f-panel :title="t('loancounter.notice.draw.baseInfo')">
      <!-- 机构 -->
      <f-form-item prop="officeId" :label="t('loancounter.notice.draw.officeName')" :required="true">
        <f-select
          v-model="drawInfo.officeId"
          :url="listOfficeUrl"
          :disabled="baseInfoReadonly"
          value-key="officeId"
          label="officeName"
          @change="drawInfoHandler.handleOfficeChange"
        />
      </f-form-item>
      <!-- 币种 -->
      <f-form-item prop="currencyId" :label="t('loancounter.notice.draw.currencyName')" :required="true">
        <f-select
          ref="currency"
          v-model="drawInfo.currencyId"
          :url="listCurrencyUrl"
          :extra-data="baseInfoQuery.currency"
          :disabled="baseInfoReadonly"
          value-key="currencyId"
          label="currencyName"
          @change="drawInfoHandler.handleCurrencyChange"
        />
      </f-form-item>
      <!-- 借款单位             @change="drawInfoHandler.handleLoanClientChange" -->
      <f-form-item prop="loanClientId" :label="t('loancounter.notice.draw.loanClientName')" :required="true">
        <f-magnifier-single
          v-model="drawInfo.loanClientId"
          :url="listLoanClientCodeUrl"
          :title="t('loancounter.notice.draw.loanClientCode')"
          :placeholder="t('loancounter.notice.draw.loanClientCode')"
          :params="loanClientQueryParam"
          :auto-init="true"
          method="post"
          row-key="clientId"
          row-label="clientName"
          input-key="clientCode"
          :selected-data="{
            clientId: drawInfo.loanClientId,
            clientName: drawInfo.loanClientName
          }"
        >
          <f-magnifier-column prop="clientCode" :label="t('loancounter.notice.draw.loanClientCode')" />
          <f-magnifier-column prop="clientName" :label="t('loancounter.notice.draw.loanClientName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 财务公司贷款行角色 -->
      <f-form-item prop="loanBankRole" :label="t('loancounter.notice.draw.loanBankRole')">
        <f-radio-group v-model="drawInfo.financeRole" disabled>
          <!-- 牵头行 -->
          <f-radio label="LEADER_BANK" size="small">{{ t("loancounter.notice.draw.loanBankRoleLeader") }}</f-radio>
          <!-- 参与行 -->
          <f-radio label="ATTEND_BANK" size="small">{{ t("loancounter.notice.draw.loanBankRoleAttend") }}</f-radio>
        </f-radio-group>
      </f-form-item>
      <!-- 合同编号 -->
      <f-form-item prop="contractId" :label="t('loancounter.notice.draw.contractCode')" required>
        <f-magnifier-single
          v-model="drawInfo.contractCode"
          :url="listContractCodeUrl"
          :title="t('loancounter.notice.draw.contractCode')"
          :placeholder="t('loancounter.notice.draw.contractCodeInput')"
          :params="baseInfoQuery.contract"
          :disabled="baseInfoReadonly"
          auto-init
          method="post"
          row-key="contractCode"
          row-label="contractCode"
          input-key="contractCode"
          @change="handler.handleBaseChange"
          :selected-data="{
            contractCode: drawInfo.contractCode
          }"
        >
          <f-magnifier-column prop="contractCode" :label="t('loancounter.notice.draw.contractCode')" />
          <f-magnifier-column prop="loanClientName" :label="t('loancounter.notice.draw.loanClientName')" />
          <f-magnifier-column prop="contractEndDate" :label="t('loancounter.notice.draw.loanEndDate')" />
        </f-magnifier-single>
      </f-form-item>
    </f-panel>

    <f-panel :title="t('loancounter.notice.draw.loanInfo')">
      <!-- 借款总金额 -->
      <f-form-item prop="contractTotalAmount" :label="t('loancounter.notice.draw.contractTotalAmount')">
        <f-amount v-model="drawInfo.contractTotalAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 财务公司承贷金额 -->
      <f-form-item prop="contractAmount" :label="t('loancounter.notice.draw.contractAmount')">
        <f-amount v-model="drawInfo.contractAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 财务公司承贷比例 -->
      <f-form-item prop="financeLoanRatio" :label="t('loancounter.notice.draw.financeLoanRatio')">
        <f-number v-model="drawInfo.financeLoanRatio" :precision="4" max="99.9999" min="0.0000" is-rate disabled />
      </f-form-item>
      <!-- 借款开始日期 -->
      <f-form-item prop="contractStartDate" :label="t('loancounter.notice.draw.contractStartDate')">
        <f-input v-model="drawInfo.contractStartDate" disabled />
      </f-form-item>
      <!-- 贷款期限 -->
      <f-form-item prop="loanTerm" :label="t('loancounter.notice.draw.loanTerm')">
        <f-number v-model="drawInfo.contractTerm" disabled>
          <template #suffix>
            <span>{{ t("loancounter.notice.draw.month") }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 合同结束日期 -->
      <f-form-item prop="contractEndDate" :label="t('loancounter.notice.draw.contractEndDate')">
        <f-input v-model="drawInfo.contractEndDate" disabled />
      </f-form-item>
      <!-- 宽限期 -->
      <f-form-item prop="gracePeriod" :label="t('loancounter.notice.draw.gracePeriod')">
        <f-number v-model="drawInfo.gracePeriod" disabled>
          <template #suffix>
            <span>{{ t("loancounter.notice.draw.day") }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 贷款投向行业 -->
      <f-form-item prop="industryCategoryFuzzy" :label="t('loancounter.notice.draw.industryCategoryFuzzy')">
        <f-input v-model="drawInfo.industryCategoryFuzzy" disabled />
      </f-form-item>
      <!-- 贷款投向地区 -->
      <f-form-item prop="loanInvestArea" :label="t('loancounter.notice.draw.loanInvestArea')">
        <f-input v-model="drawInfo.loanInvestArea" disabled />
      </f-form-item>
      <!-- 是否绿色贷款 -->
      <f-form-item prop="greenCredit" :label="t('loancounter.notice.draw.greenCredit')">
        <f-switch
          v-model="drawInfo.greenCredit"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 绿色贷款分类 -->
      <f-form-item prop="greenCreditName" :label="t('loancounter.notice.draw.greenCreditName')" :employ="2">
        <f-input v-model="drawInfo.greenCreditName" disabled />
      </f-form-item>
      <!-- 是否科技贷款 -->
      <f-form-item prop="technologyLoans" :label="t('loancounter.notice.draw.technologyLoans')">
        <f-switch
          v-model="drawInfo.technologyLoans"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          disabled
        />
      </f-form-item>
      <!-- 科技贷款分类 -->
      <f-form-item prop="technologyLoansName" :label="t('loancounter.notice.draw.technologyLoansName')" :employ="2">
        <f-input v-model="drawInfo.technologyLoansName" disabled />
      </f-form-item>
    </f-panel>

    <!-- 转款信息 -->
    <f-panel :title="t('loancounter.notice.draw.amountGroupInfo')">
      <!-- 可编辑表格 -->
      <f-form-item :employ="3">
        <f-table-edit
          :show-add-btn="false"
          :showOperateDelete="false"
          ref="tableEditRef"
          row-key="_randomId"
          :data="drawInfo.amountGroupList"
          style="width: 100%"
          :before-close-edit="drawInfoHandler.handleRowClose"
        >
          <!-- 分组 -->
          <f-table-column
            prop="groupIndex"
            :label="t('loancounter.notice.draw.groupName')"
            :formatter="{ name: 'const', const: 'loancounter.LoanSyndicateGroup' }"
          >
            <template #edit="scope">
              <span>{{ loanSyndicateGroup.valueToLabel(scope.row.groupIndex) }}</span>
            </template>
          </f-table-column>
          <!-- 期限 -->
          <f-table-column prop="contractTerm" :label="t('loancounter.notice.draw.contractTerm')" />
          <!-- 币种 -->
          <f-table-column prop="currencyName" :label="t('loancounter.notice.draw.currencyName')" />
          <!-- 执行利率 -->
          <f-table-column prop="executeRate" :label="t('loancounter.notice.draw.executeRate')" formatter="rate" />
          <!-- 财务公司承贷金额 -->
          <f-table-column prop="detailAmount" :label="t('loancounter.notice.draw.detailAmount')" formatter="amount" />
          <!-- 可转款金额 -->
          <f-table-column
            prop="availableAmount"
            :label="t('loancounter.notice.draw.availableAmount')"
            formatter="amount"
          />
          <!-- 转款金额 -->
          <f-table-column
            prop="transferAmount"
            :label="t('loancounter.notice.draw.transferAmount')"
            formatter="amount"
            required
          >
            <template #edit="scope">
              <f-amount v-model="scope.row.transferAmount" :symbol="currencySymbol" :negative="false" tooltip />
            </template>
          </f-table-column>
        </f-table-edit>
      </f-form-item>
      <f-form-item prop="transferDate" :label="t('loancounter.notice.draw.transferDate')" required>
        <!-- 转款日期 -->
        <f-date-picker v-model="drawInfo.transferDate" type="date" />
      </f-form-item>
      <!-- 转款总金额 -->
      <f-form-item prop="transferTotalAmount" :label="t('loancounter.notice.draw.drawTotalAmount')">
        <f-amount v-model="drawInfo.transferTotalAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 财务公司银行账户号 -->
      <f-form-item prop="payBankAcctNo" :label="t('loancounter.notice.draw.payBankAcctNo')" required>
        <f-magnifier-single
          v-model="drawInfo.payBankAcctNo"
          :title="t('loancounter.notice.draw.payBankAcctNoInput')"
          :url="getOpenBankInfo"
          method="post"
          row-key="bankAccountCode"
          row-label="bankAccountCode"
          input-key="bankAccountCode"
          :params="drawOpenAccNoParams"
          auto-init
          @change="drawInfoHandler.handlePayBankAcctNoChange"
        >
          <f-magnifier-column prop="bankAccountCode" :label="t('loancounter.notice.draw.drawOpenAccNo')" />
          <f-magnifier-column prop="bankAccountName" :label="t('loancounter.notice.draw.drawOpenAccName')" />
          <f-magnifier-column prop="openBankName" :label="t('loancounter.notice.draw.openBankName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 开户银行账户编号 -->
      <f-form-item prop="agentBankAcctNo" :label="t('loancounter.notice.draw.agentBankAcctNo')" required>
        <f-input v-model="drawInfo.agentBankAcctNo" maxlength="50" />
      </f-form-item>
      <!-- 开户银行账户名称 -->
      <f-form-item prop="agentBankAcctName" :label="t('loancounter.notice.draw.agentBankAcctName')">
        <f-input v-model="drawInfo.agentBankAcctName" />
      </f-form-item>

      <!-- 汇入行CNAPS号 -->
      <f-form-item prop="remitInCnapsNo" :label="t('loancounter.notice.draw.remitInCnapsNo')" required>
        <CnapsInput v-model="drawInfo.remitInCnapsNo" />
      </f-form-item>
      <!-- 汇入地（省） -->
      <f-form-item prop="remitInProvince" :label="t('loancounter.notice.draw.remitInProvince')">
        <f-input v-model="drawInfo.remitInProvince" />
      </f-form-item>
      <!-- 汇入地（市） -->
      <f-form-item prop="remitInCity" :label="t('loancounter.notice.draw.remitInCity')">
        <f-input v-model="drawInfo.remitInCity" />
      </f-form-item>
      <!-- 汇入行名称 -->
      <f-form-item prop="remitInBankName" :label="t('loancounter.notice.draw.remitInBankName')">
        <f-input v-model="drawInfo.remitInBankName" />
      </f-form-item>
      <!-- 备注 -->
      <f-form-item prop="remark" :label="t('loancounter.notice.draw.remark')" :employ="2">
        <f-input v-model="drawInfo.remark" />
      </f-form-item>
    </f-panel>

    <f-panel :title="t('loancounter.notice.draw.fileInfo')">
      <!-- 附件 -->
      <f-form-item :label="t('loancounter.notice.draw.file')" :employ="3">
        <f-attm-upload ref="uploader" v-model="drawInfo.fileList" :is-remove-delete-link="readonly" drag multiple />
      </f-form-item>
    </f-panel>
  </f-multi-form-panel>
</template>
<script setup lang="ts">
import { shallowRef, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useContractInfo from "../hooks/add/useContractInfo";
import useDrawInfo from "../hooks/add/useDrawInfo";
import useDrawInfoRules from "../hooks/add/useDrawInfoRules";
import usePageOperate from "../hooks/add/usePageOperate";
import { getOpenBankInfo } from "@/views/bankplat/common/url";
import { listOfficeUrl, listCurrencyUrl, listContractCodeUrl, listLoanClientCodeUrl } from "../url";

const { t } = useI18n();
const loanSyndicateGroup = useConst("loancounter.LoanSyndicateGroup");
const YesOrNoEnum = useConst("common.YesOrNo");
const AccountGroupEnum = useConst("basic.AccountGroup");
const LoanInterestClassEnum = useConst("counter.InterestClass");
const LoanSetRateTypeEnum = useConst("loancounter.LoanSetRateType");
const FloatDirectionEnum = useConst("loancounter.FloatDirection");
const FloatPointEnum = useConst("loancounter.FloatPoint");
const AllLoanTypeEnum = useConst("loancounter.LoanType");

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  }
});

const emits = defineEmits(["on-loaded"]);
const form = shallowRef();
const uploader = shallowRef<any>();

// const { baseInfo, loanClientQueryParam, baseInfoQuery, baseInfoHandler, baseInfoMethods } = useBaseInfo();
const { contractInfo, contractMethods, tableEditRef } = useContractInfo();
const { drawInfo, drawInfoMethods, drawInfoHandler, loanClientQueryParam, baseInfoQuery } = useDrawInfo(
  LoanSetRateTypeEnum,
  FloatDirectionEnum,
  FloatPointEnum,
  uploader
);
const { handler, methods } = usePageOperate(
  t,
  props,
  emits,
  form,
  contractInfo,
  contractMethods,
  drawInfo,
  drawInfoHandler,
  drawInfoMethods,
  AllLoanTypeEnum,
  AccountGroupEnum,
  LoanInterestClassEnum,
  LoanSetRateTypeEnum,
  FloatDirectionEnum,
  FloatPointEnum,
  uploader
);

const rules = useDrawInfoRules(t, drawInfo, contractInfo);

const drawOpenAccNoParams = computed(() => {
  return {
    currencyId: contractInfo.currencyId,
    officeId: contractInfo.officeId,
    clientId: contractInfo.loanClientId,
    accountGroup: AccountGroupEnum.CURRENT // 查询活期类账户
  };
});

const baseInfoReadonly = computed(() => props.readonly || drawInfo.id);
const necessaryRules = computed(() => (props.readonly ? [] : rules));

defineExpose(methods);
</script>

<!-- &lt;!&ndash; 是否绿色信贷 &ndash;&gt; -->
<!-- <f-form-item prop="greenCredit" :label="t('loancounter.notice.draw.greenCredit')"> -->
<!-- <f-select v-model="contractInfo.greenCredit" :data="YesOrNoEnum" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 借款单位编号 &ndash;&gt; -->
<!-- <f-form-item prop="loanClientCode" :label="t('loancounter.notice.draw.loanClientCode')"> -->
<!-- <f-input v-model="contractInfo.loanClientCode" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 借款单位名称 &ndash;&gt; -->
<!-- <f-form-item prop="loanClientName" :label="t('loancounter.notice.draw.loanClientName')"> -->
<!-- <f-input v-model="contractInfo.loanClientName" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;合同金额&ndash;&gt; -->
<!-- <f-form-item prop="contractAmount" :label="t('loancounter.notice.draw.contractAmount')"> -->
<!-- <f-amount v-model="contractInfo.contractAmount" maxlength="15" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;财司承担份额&ndash;&gt; -->
<!-- <f-form-item prop="financeLoanRatio" :label="t('loancounter.notice.draw.financeLoanRatio')"> -->
<!-- <f-number v-model="contractInfo.financeLoanRatio" :precision="4" max="99.9999" min="0.0000" is-rate disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;合同开始日期&ndash;&gt; -->
<!-- <f-form-item prop="contractStartDate" :label="t('loancounter.notice.draw.contractStartDate')"> -->
<!-- <f-input v-model="contractInfo.contractStartDate" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;合同结束日期&ndash;&gt; -->
<!-- <f-form-item prop="contractEndDate" :label="t('loancounter.notice.draw.contractEndDate')"> -->
<!-- <f-input v-model="contractInfo.contractEndDate" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;贷款期限: 先暂时用合同的&ndash;&gt; -->
<!-- <f-form-item prop="loanTerm" :label="t('loancounter.notice.draw.loanTerm')"> -->
<!-- <f-number v-model="contractInfo.contractTerm" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;合同执行利率&ndash;&gt; -->
<!-- <f-form-item prop="contractExecuteRate" :label="t('loancounter.notice.draw.contractExecuteRate')"> -->
<!-- <f-number v-model="contractInfo.executeRate" :precision="4" max="99.9999" min="0.0000" is-rate disabled /> -->
<!-- </f-form-item> -->

<!-- <f-panel :title="t('loancounter.notice.draw.bankList')"> -->
<!-- <div v-for="(bank, index) in bankList" :key="index"> -->
<!-- &lt;!&ndash; 银行名称 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.bankName')"> -->
<!-- <f-input v-model="bank.bankName" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 开户银行名称 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.openBankName')"> -->
<!-- <f-input v-model="bank.openBankName" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 开户银行账户编号 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.bankAcctNo')"> -->
<!-- <f-input v-model="bank.bankAcctNo" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 开户银行账户名称 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.bankAcctName')"> -->
<!-- <f-input v-model="bank.bankAcctName" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 汇入地（省） &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.remitInProvince')"> -->
<!-- <f-input v-model="bank.remitInProvince" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 汇入地（市） &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.remitInCity')"> -->
<!-- <f-input v-model="bank.remitInCity" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 承贷比例 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.bankLoanRatio')"> -->
<!-- <f-number v-model="bank.bankLoanRatio" :precision="4" max="99.9999" min="0.0000" is-rate disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 承贷金额 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.bankLoanAmount')"> -->
<!-- <f-amount v-model="bank.bankLoanAmount" maxlength="15" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 担保金额 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.assureAmount')"> -->
<!-- <f-amount v-model="bank.assureAmount" maxlength="15" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 信用金额 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.creditAmount')"> -->
<!-- <f-amount v-model="bank.creditAmount" maxlength="15" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 参与银行角色 &ndash;&gt; -->
<!-- <f-form-item :prop="bank.bankName + '-' + index" :label="t('loancounter.notice.draw.attendBankRole')"> -->
<!-- <f-input v-model="bank.attendBankRole" disabled /> -->
<!-- </f-form-item> -->
<!-- </div> -->
<!-- </f-panel> -->
<!--  -->

<!-- <f-panel :title="t('loancounter.notice.draw.drawInfo')"> -->
<!-- &lt;!&ndash; 提款日期: 默认值需要确认 &ndash;&gt; -->
<!-- <f-form-item prop="drawDate" :label="t('loancounter.notice.draw.drawDate')" :required="necessary"> -->
<!-- <f-date-picker v-model="drawInfo.drawDate" type="date" /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;可提款额度&ndash;&gt; -->
<!-- <f-form-item prop="drawUsableAmount" :label="t('loancounter.notice.draw.payAvailableAmount')"> -->
<!-- <f-amount v-model="drawInfo.drawUsableAmount" maxlength="15" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 可提款额度大写 &ndash;&gt; -->
<!-- <f-form-item :label="t('loancounter.notice.draw.payAvailableAmountChinese')"> -->
<!-- <f-amount-chinese v-model="drawInfo.drawUsableAmount" disabled /> -->
<!-- </f-form-item> -->

<!-- &lt;!&ndash; 开户银行账户编号 &ndash;&gt; -->
<!-- <f-form-item prop="drawOpenAccNo" :label="t('loancounter.notice.draw.drawOpenAccNo')"> -->
<!-- <f-magnifier-single -->
<!-- v-model="drawInfo.drawOpenAccNo" -->
<!-- :title="t('loancounter.notice.draw.drawOpenAccNoInput')" -->
<!-- :url="getOpenBankInfo" -->
<!-- method="post" -->
<!-- row-key="bankAccountCode" -->
<!-- row-label="bankAccountCode" -->
<!-- input-key="bankAccountCode" -->
<!-- :params="drawOpenAccNoParams" -->
<!-- auto-init -->
<!-- @change="drawInfoHandler.handleDrawOpenAccNoChange" -->
<!-- > -->
<!-- <f-magnifier-column prop="bankAccountCode" :label="t('loancounter.notice.draw.drawOpenAccNo')" /> -->
<!-- <f-magnifier-column prop="openBankName" :label="t('loancounter.notice.draw.openBankName')" /> -->
<!-- </f-magnifier-single> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 开户银行账户名称 &ndash;&gt; -->
<!-- <f-form-item prop="drawOpenAccName" :label="t('loancounter.notice.draw.drawOpenAccName')"> -->
<!-- <f-input v-model="drawInfo.drawOpenAccName" disabled /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;总提款金额&ndash;&gt; -->
<!-- <f-form-item prop="drawTotalAmount" :label="t('loancounter.notice.draw.drawTotalAmount')"> -->
<!-- <f-amount v-model="drawInfo.drawTotalAmount" maxlength="15" :min="0" /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash; 总提款金额大写 &ndash;&gt; -->
<!-- <f-form-item prop="drawTotalAmountChinese" :label="t('loancounter.notice.draw.drawTotalAmountChinese')"> -->
<!-- <f-amount-chinese v-model="drawInfo.drawTotalAmount" disabled /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="emptyProp1" :employ="2" /> -->

<!-- &lt;!&ndash; 浮动利率方式 &ndash;&gt; -->
<!-- <f-form-item prop="floatingRate" :employ="3"> -->
<!-- <template #label> -->
<!-- <f-radio-group v-model="drawInfo.rateRunType" @change="drawInfoHandler.handleRateRunTypeChange"> -->
<!-- <f-radio label="FLOAT" size="large">{{ t("loancounter.notice.draw.floatRateRunType") }}</f-radio> -->
<!-- </f-radio-group> -->
<!-- </template> -->
<!-- <div style="display: flex; height: 24px; gap: 10px"> -->
<!-- <f-magnifier-single -->
<!-- v-model="drawInfo.floatingRateCode" -->
<!-- :url="listBoardRatUrl" -->
<!-- :title="t('loancounter.notice.draw.floatingRateCode')" -->
<!-- :placeholder="t('loancounter.notice.draw.floatingRateCodeInput')" -->
<!-- :params="boardRateQueryParams" -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- auto-init -->
<!-- method="post" -->
<!-- row-key="code" -->
<!-- row-label="name" -->
<!-- input-key="name" -->
<!-- @change="drawInfoHandler.handleBoardRateCodeChange" -->
<!-- > -->
<!-- <f-magnifier-column prop="code" :label="t('loancounter.notice.draw.floatingRateCode')" /> -->
<!-- <f-magnifier-column prop="name" :label="t('loancounter.notice.draw.floatingRateName')" /> -->
<!-- </f-magnifier-single> -->
<!-- <f-number -->
<!-- v-model="drawInfo.floatingRate" -->
<!-- :precision="6" -->
<!-- max="99.999999" -->
<!-- min="0.0000" -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- is-rate -->
<!-- @change="drawInfoMethods.doCalRate" -->
<!-- > -->
<!-- <template #suffix> -->
<!-- <f-button -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- style="position: relative; right: -7px" -->
<!-- @click="drawInfoMethods.calTrialResult" -->
<!-- > -->
<!-- {{ t("loancounter.notice.draw.calTrialResult") }} -->
<!-- </f-button> -->
<!-- </template> -->
<!-- </f-number> -->
<!-- <f-select -->
<!-- v-model="drawInfo.floatingType" -->
<!-- :data="FloatDirectionEnum" -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalRate" -->
<!-- /> -->
<!-- <div style="word-break: keep-all">{{ t("loancounter.notice.draw.floatingRatio") }}</div> -->
<!-- <f-number -->
<!-- v-model="drawInfo.floatingRatio" -->
<!-- :precision="6" -->
<!-- max="99.999999" -->
<!-- min="0.0000" -->
<!-- is-rate -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalRate" -->
<!-- /> -->
<!-- <f-select -->
<!-- v-model="drawInfo.pointType" -->
<!-- :data="FloatPointEnum" -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalRate" -->
<!-- /> -->
<!-- <div style="word-break: keep-all">{{ t("loancounter.notice.draw.pointFloating") }}</div> -->
<!-- <f-number -->
<!-- v-model="drawInfo.pointFloating" -->
<!-- :precision="4" -->
<!-- min="0.0000" -->
<!-- :disabled="disableFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalRate" -->
<!-- /> -->
<!-- </div> -->
<!-- </f-form-item> -->

<!-- &lt;!&ndash; 固定利率方式 &ndash;&gt; -->
<!-- <f-form-item prop="fixedRate" :employ="3"> -->
<!-- <template #label> -->
<!-- <f-radio-group v-model="drawInfo.rateRunType" @change="drawInfoHandler.handleRateRunTypeChange"> -->
<!-- <f-radio label="FIXED" size="large">{{ t("loancounter.notice.draw.fixedRateRunType") }}</f-radio> -->
<!-- </f-radio-group> -->
<!-- </template> -->
<!-- <div style="display: flex; height: 24px; gap: 10px"> -->
<!-- <f-number -->
<!-- v-model="drawInfo.fixedRate" -->
<!-- :whole-number="false" -->
<!-- :min="0" -->
<!-- :disabled="isFloatRateRunType" -->
<!-- @change="drawInfoHandler.handlefixedRateChange" -->
<!-- /> -->
<!-- <div style="word-break: keep-all">{{ t("loancounter.notice.draw.referenceRate") }}</div> -->
<!-- <f-select -->
<!-- v-model="drawInfo.referenceRateId" -->
<!-- :data="BaseRateTypeEnum" -->
<!-- :disabled="isFloatRateRunType" -->
<!-- @change="drawInfoMethods.handleReferenceRateChange" -->
<!-- /> -->
<!-- </div> -->
<!-- </f-form-item> -->

<!-- <f-form-item prop="executeRate" :label="t('loancounter.notice.draw.executeRate')" :required="necessary"> -->
<!-- <f-number v-model="drawInfo.executeRate" :precision="6" max="99.999999" min="0.000000" is-rate disabled /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="emptyProp2" :employ="2" /> -->

<!-- &lt;!&ndash; 逾期罚息浮动利率方式 &ndash;&gt; -->
<!-- <f-form-item prop="overdueFloatingRate" :employ="3"> -->
<!-- <template #label> -->
<!-- <f-radio-group v-model="drawInfo.overdueRateType" @change="drawInfoHandler.handleOverdueRateRunTypeChange"> -->
<!-- <f-radio label="FLOAT" size="large">{{ t("loancounter.notice.draw.oveFloatRateRunType") }}</f-radio> -->
<!-- </f-radio-group> -->
<!-- </template> -->
<!-- <div style="display: flex; height: 24px; gap: 10px"> -->
<!-- <f-magnifier-single -->
<!-- v-model="drawInfo.overdueFloatingRateCode" -->
<!-- :url="listBoardRatUrl" -->
<!-- :title="t('loancounter.notice.draw.floatingRateCode')" -->
<!-- :placeholder="t('loancounter.notice.draw.floatingRateCodeInput')" -->
<!-- :params="boardRateQueryParams" -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- auto-init -->
<!-- method="post" -->
<!-- row-key="code" -->
<!-- row-label="name" -->
<!-- input-key="name" -->
<!-- @change="drawInfoHandler.handleOverdueBoardRateCodeChange" -->
<!-- > -->
<!-- <f-magnifier-column prop="code" :label="t('loancounter.notice.draw.floatingRateCode')" /> -->
<!-- <f-magnifier-column prop="name" :label="t('loancounter.notice.draw.floatingRateName')" /> -->
<!-- </f-magnifier-single> -->
<!-- <f-number -->
<!-- v-model="drawInfo.overdueFloatingRate" -->
<!-- :precision="6" -->
<!-- max="99.999999" -->
<!-- min="0.0000" -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- is-rate -->
<!-- @change="drawInfoMethods.doCalOverdueRate" -->
<!-- > -->
<!-- <template #suffix> -->
<!-- <f-button -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- style="position: relative; right: -7px" -->
<!-- @click="drawInfoMethods.calOverdueTrialResult" -->
<!-- > -->
<!-- {{ t("loancounter.notice.draw.calTrialResult") }} -->
<!-- </f-button> -->
<!-- </template> -->
<!-- </f-number> -->
<!-- <f-select -->
<!-- v-model="drawInfo.overdueFloatingType" -->
<!-- :data="FloatDirectionEnum" -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalOverdueRate" -->
<!-- /> -->
<!-- <div style="word-break: keep-all">{{ t("loancounter.notice.draw.floatingRatio") }}</div> -->
<!-- <f-number -->
<!-- v-model="drawInfo.overdueFloatingRatio" -->
<!-- :precision="6" -->
<!-- max="99.999999" -->
<!-- min="0.0000" -->
<!-- is-rate -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalOverdueRate" -->
<!-- /> -->
<!-- <f-select -->
<!-- v-model="drawInfo.overduePointType" -->
<!-- :data="FloatPointEnum" -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalOverdueRate" -->
<!-- /> -->
<!-- <div style="word-break: keep-all">{{ t("loancounter.notice.draw.pointFloating") }}</div> -->
<!-- <f-number -->
<!-- v-model="drawInfo.overduePointFloating" -->
<!-- :precision="4" -->
<!-- min="0.0000" -->
<!-- :disabled="disableOverdueFloatRateRunType" -->
<!-- @change="drawInfoMethods.doCalOverdueRate" -->
<!-- /> -->
<!-- </div> -->
<!-- </f-form-item> -->

<!-- <f-form-item prop="fixedRate" :employ="3"> -->
<!-- <template #label> -->
<!-- <f-radio-group v-model="drawInfo.overdueRateType" @change="drawInfoHandler.handleOverdueRateRunTypeChange"> -->
<!-- <f-radio label="FIXED" size="large">{{ t("loancounter.notice.draw.oveFixedRateRunType") }}</f-radio> -->
<!-- </f-radio-group> -->
<!-- </template> -->

<!-- <div style="display: flex; height: 24px; gap: 10px"> -->
<!-- <f-number -->
<!-- v-model="drawInfo.overdueFixedRate" -->
<!-- :precision="6" -->
<!-- max="99.999999" -->
<!-- min="0.0000" -->
<!-- is-rate -->
<!-- :disabled="isOverdueFloatRateRunType" -->
<!-- @change="drawInfoHandler.handleOverdueFixedRateChange" -->
<!-- /> -->
<!-- </div> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="overdueRate" :label="t('loancounter.notice.draw.overdueRate')" :required="necessary"> -->
<!-- <f-number v-model="drawInfo.overdueRate" :precision="6" max="99.999999" min="0.000000" is-rate disabled /> -->
<!-- </f-form-item> -->
<!-- <f-form-item prop="emptyProp3" :employ="2" /> -->

<!-- &lt;!&ndash;提款开始日期&ndash;&gt; -->
<!-- <f-form-item prop="drawStartDate" :label="t('loancounter.notice.draw.drawStartDate')" :required="necessary"> -->
<!-- <f-date-picker v-model="drawInfo.drawStartDate" type="date" /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;提款结束日期&ndash;&gt; -->
<!-- <f-form-item prop="drawEndDate" :label="t('loancounter.notice.draw.drawEndDate')" :required="necessary"> -->
<!-- <f-date-picker v-model="drawInfo.drawEndDate" type="date" /> -->
<!-- </f-form-item> -->
<!-- &lt;!&ndash;贷款期限&ndash;&gt; -->
<!-- <f-form-item prop="loanTerm" :label="t('loancounter.notice.draw.loanTerm')" :required="necessary"> -->
<!-- <f-number v-model="drawInfo.loanTerm" /> -->
<!-- </f-form-item> -->
<!-- </f-panel> -->
