import { useBizEnum } from "./useBizEnum";
import httpTool from "@/utils/http";
import { random } from "@/utils/uuid";
import { SPLITER } from "../types";

/**
 * 转换核心DTO对象为柜面DTO
 * C2C: Core to Counter 核心柜面转换
 * @param dto dto
 * @returns counter dto
 */
export const useParser = (dto: any) => {
  const { floatDirection, loanGuaranteeType, yesOrNo } = useBizEnum();

  const parseRateDtoC2C = (data: any, rateType: string, normalRunType = "") => {
    if (data) {
      const {
        rateId: boardRateId,
        rateCode: boardRateCode,
        rateName: boardRateName,
        floatingWay: loanRateFloatType,
        floatingRatio,
        basicPoint,
        executeRate: execRate,
        rateAdjustWay,
        rateAdjustFreq,
        firstDayAdjustFlag,
        ...rest
      } = data;
      return {
        ...rest,
        boardRateId,
        boardRateCode,
        boardRateName,
        loanRateFloatType,
        floatingRateType: floatingRatio < 0 ? floatDirection.GODOWN : floatDirection.GOUP,
        floatingRatio: Math.abs(floatingRatio),
        basicPointSymbol: basicPoint < 0 ? -1 : 1,
        basicPoint: Math.abs(basicPoint),
        execRate,
        rateAdjustWay,
        rateAdjustFreq,
        firstDayAdjustFlag
      };
    } else {
      return {
        rateType,
        rateRunType: normalRunType
      };
    }
  };

  const parseGuaranteeDtoC2C = (data: Array<any>) => {
    if (Array.isArray(data)) {
      return data.map(x => {
        const guaranteeClientName = x.guaranteeType === loanGuaranteeType.COMFORT ? x.mainBody : x.guaranteeClientName;

        return {
          ...x,
          guaranteeClientName,
          guaranteeRemark: x.remarks,
          creditCode: x.creditCode,
          clientType: x.guarantorType,
          originalContractMargin: yesOrNo.YES
        };
      });
    } else {
      return [];
    }
  };

  const parseSyndicateAmountC2C = (data: Array<any>) => {
    if (Array.isArray(data)) {
      return data.map(x => {
        return {
          ...x,
          groupId: x.groupIndex,
          loanTerm: x.contractTerm,
          amount: x.detailAmount,
          borrowPrincipalAccId: x.recPrincipalAcctId,
          borrowPrincipalAccNo: x.recPrincipalAcctNo,
          borrowPrincipalAccName: x.recPrincipalAcctName,
          payInterestAccId: x.payInterestAcctId,
          payInterestAccNo: x.payInterestAcctNo,
          payInterestAccName: x.payInterestAcctName,
          executeRate: x.executeRate,
          overdueExecRate: x.overdueRate,
          misappropriateExecRate: x.embezzleRate,
          _randomId: random()
        };
      });
    } else {
      return [];
    }
  };
  //保理应收账款信息
  const parseRepayAccountsC2C = (data: Array<any>) => {
    if (Array.isArray(data)) {
      return data.map(x => {
        return {
          ...x,
          contractCode: x.arContractCode,
          invoiceCode: x.invoiceCode,
          buyerName: x.buyerName,
          taxNo: x.taxNo,
          legalDelegate: x.legalDelegate,
          sellerName: x.sellerName,
          accountsAmount: x.invoiceAmount,
          accountsStartDate: x.arContractStartDate,
          accountsEndDate: x.arContractEndDate,
          accountsTerm: x.accountPeriod,
          accountsRemark: x.arRemark
        };
      });
    } else {
      return [];
    }
  };

  // 银团贷款授信列表
  const parseSyndicateCreditC2C = (data: Array<any>, amountData: Array<any>) => {
    if (Array.isArray(data) && Array.isArray(amountData)) {
      return data.map((x, i) => {
        return {
          ...x,
          groupId: x.groupIndex,
          creditCode: x.creditCode,
          creditSeqNo: x.creditSeqNo,
          creditId: Number(x.creditId),
          loanTerm: amountData[i].contractTerm, // 期限从金额数据中的期限组装
          creditClientId: x.creditClientId,
          creditClientCode: x.creditClientCode,
          creditClientName: x.creditClientName,
          _randomId: random()
          //todo 缺少授信额度、已使用额度。可用额度、期限
        };
      });
    } else {
      return [];
    }
  };

  const parseSyndicateBankC2C = (data: Array<any>) => {
    if (Array.isArray(data)) {
      return data.map(x => {
        return {
          ...x,
          groupId: x.groupIndex,
          bankId: x.bankId,
          bankName: x.bankName,
          bankAcctNo: x.bankAcctNo,
          bankAcctName: x.bankAcctName,
          attendBankRole: x.attendBankRole[0],
          _randomId: random()
        };
      });
    } else {
      return [];
    }
  };

  const getFileInfo = () => {
    const loseInfoFiles = dto.clnLoanFileList.filter(x => !!x.fileId && !x.fileName);
    if (loseInfoFiles.length > 0) {
      const ids = loseInfoFiles.map(x => x.fileId);
      httpTool.get("{file}/api/v1/file/ids", { ids }).then(res => {
        if (res.success) {
          res.data.forEach(x => {
            const item = dto.clnLoanFileList.find(y => y.fileId === x.id);
            if (item) {
              item.fileName = x.fileName;
              item.fileSize = x.dataSize;
            }
          });
        }
      });
    }
  };
  const parseDtoC2C = (data: any) => {
    const {
      loanBusinessType,
      contractTerm,
      gracePeriod,
      contractTotalAmount,
      financeLoanRatio,
      contractAmount,
      revolvingLoanFlag,
      allEntrustPayFlag,
      interestSettFreq,
      firstInterestDate,
      overdueRateFlag,
      embezzleRateFlag,
      oweCompoundInstFlag,
      interestCalType,
      payUseLastRateFlag,
      factoringRecourseFlag,
      factoringPublicFlag,
      buyOutFlag,
      tcaContractId,
      tcaContractCode,
      payInterestAcctId,
      payInterestAcctNo,
      payInterestAcctName,
      repayPrincipalAcctId,
      repayPrincipalAcctNo,
      repayPrincipalAcctName,
      overdraftAcctId,
      overdraftAcctNo,
      overdraftAcctName,
      financeRole,
      payBankAcctId,
      payBankAcctNo,
      payBankAcctName,
      payBankId,
      payBankName,
      agentBankAcctNo,
      agentBankAcctName,
      remitInCnapsNo,
      remitInBankName,
      contractRateList,
      contractPriceFeeDto,
      // contractMargin, // TODO
      contractAttachmentDtoList,
      guaranteeDetailList,
      amountGroupList,
      attendBankList,
      contractStartDate,
      contractEndDate,
      //占用借款人授信列表
      creditGroupList,
      //当前利率调整记录id
      currentRateAdjustId,
      //逾期开始日
      overdueDate,
      //合同状态
      contractStatus,
      //是否自动还款
      autoRepayFlag,
      //合同管理人
      managerUserId,
      managerUserNo,
      managerUserName,
      executeRate,
      guaranteeTypeList,
      //贷款专户
      recPrincipalAcctId,
      recPrincipalAcctName,
      recPrincipalAcctNo,
      receivableAccountsList,
      greenCreditName,
      //保理产品类型
      factorProductType,
      industryCategoryId,
      loanInvestAreaId,
      greenCreditId,
      technologyLoansId,
      //保证金
      contractMargin,
      //利率调整后的信息
      rateId,
      rateCode,
      rateName,
      floatingRate,
      //授信序号
      creditSeqNo,
      ...rest
    } = data;

    const contractAttachment = contractAttachmentDtoList || [];
    const clnLoanFileList = contractAttachment.map(y => ({
      fileId: y.fileId,
      fileType: y.fileType,
      fileSize: y.fileSize,
      uploadTime: y.modifyTime,
      inputUserName: y.modifyUserName
    }));
    contractRateList.forEach(item => {
      if (item.rateType === "NORMAL" && item.floatingRate && item.floatingRate > 0) {
        item.rateId = rateId;
        item.rateCode = rateCode;
        item.rateName = rateName;
        item.floatingRate = floatingRate;
      }
    });
    const fileIds = [];
    const normal = parseRateDtoC2C(
      contractRateList.find(x => x.rateType === "NORMAL"),
      "NORMAL"
    );
    const overdue = parseRateDtoC2C(
      contractRateList.find(x => x.rateType === "OVERDUE"),
      "OVERDUE",
      normal.rateRunType
    );
    const punish = parseRateDtoC2C(
      contractRateList.find(x => x.rateType === "PUNISH"),
      "PUNISH",
      normal.rateRunType
    );

    Object.assign(dto, {
      ...rest,
      creditLoanBizType: loanBusinessType,
      loanTerm: contractTerm,
      graceTerm: gracePeriod,
      loanApplySumAmount: contractTotalAmount,
      bankLoanRatio: financeLoanRatio,
      bankLoanAmount: contractAmount,
      cycleLoan: revolvingLoanFlag,
      fullEntrustedPay: allEntrustPayFlag ?? yesOrNo.NO,
      interestSettFreq: interestSettFreq,
      firstSettleDate: firstInterestDate,
      adjustFirstInterestDate: firstInterestDate,
      computeInterestFlag: overdueRateFlag ?? yesOrNo.NO,
      misappropriateFlag: embezzleRateFlag ?? yesOrNo.NO,
      debitCompInterest: oweCompoundInstFlag ?? yesOrNo.NO,
      interestAlgorithm: interestCalType,
      latestRateLpr: payUseLastRateFlag ?? yesOrNo.NO,
      recourseFlag: factoringRecourseFlag ?? yesOrNo.NO,
      publicFlag: factoringPublicFlag ?? yesOrNo.NO,
      additionalRecordFlag: buyOutFlag,
      assetTransferContractId: tcaContractId,
      assetTransferContractCode: tcaContractCode,
      payInterestAccId: payInterestAcctId,
      payInterestAccNo: payInterestAcctNo,
      payInterestAccName: payInterestAcctName,
      borrowPrincipalAccId: repayPrincipalAcctId,
      borrowPrincipalAccNo: repayPrincipalAcctNo,
      borrowPrincipalAccName: repayPrincipalAcctName,
      loanBankRole: financeRole,
      agencyBankAccId: payBankAcctId,
      agencyBankAccNo: payBankAcctNo,
      agencyBankAccName: payBankAcctName,
      agencyOpenBankId: payBankId,
      agencyOpenBankName: payBankName,
      bankAcctNo: agentBankAcctNo,
      bankAcctName: agentBankAcctName,
      remitCnapsNo: remitInCnapsNo,
      remitBankName: remitInBankName,
      //透支账户
      overdrawAccNo: overdraftAcctNo,
      overdrawAccId: overdraftAcctId,
      overdrawAccName: overdraftAcctName,
      //借款开始日
      loanStartDate: contractStartDate,
      //借款结束日
      loanEndDate: contractEndDate,
      //合同开始日
      contractStartDate: contractStartDate,
      //合同结束日
      contractEndDate: contractEndDate,
      // 调整金额，使用合同金额初始化
      contractAdjustAmount: contractAmount,
      //法透金额、借款金额
      loanAmount: contractAmount,
      //合同金额
      contractAmount: contractAmount,
      //合同总金额
      contractTotalAmount: contractTotalAmount,
      //合同执行利率
      executeRate: executeRate,
      //合同正常利率
      normalRate: executeRate,
      //当前利率调整记录id
      currentRateAdjustId: currentRateAdjustId,
      //逾期开始日
      overdueDate: overdueDate,
      //合同状态
      contractStatus: contractStatus,
      //是否自动还款
      autoRepayFlag: autoRepayFlag,
      //合同管理人
      managerUserId: managerUserId,
      managerUserNo: managerUserNo,
      managerUserName: managerUserName,
      //保理应收账款总额
      accountsAmountReceivable: contractTotalAmount,
      // 利率信息
      rateDetailDtoList: [normal, overdue, punish],
      // 手续费相关
      feeAmount: contractPriceFeeDto?.feeAmount,
      feeChargeBasic: contractPriceFeeDto?.feeChargeBasic,
      feeChargeFreq: contractPriceFeeDto?.feeChargeFreq,
      feeChargeRate: contractPriceFeeDto?.feeChargeRate,
      feeChargeRuleCode: contractPriceFeeDto?.feeChargeRuleCode,
      feeChargeRuleId: contractPriceFeeDto?.feeChargeRuleId,
      feeChargeRuleName: contractPriceFeeDto?.feeChargeRuleName,
      feeChargeWay: contractPriceFeeDto?.feeChargeWay,
      firstFeeChargeDate: contractPriceFeeDto?.firstFeeChargeDate,
      freeCharge: contractPriceFeeDto?.freeChargeFlag,
      //保证金相关信息
      //原合同是否存在保证金
      originalContractMargin: contractMargin?.payMarginAcctId !== null ? yesOrNo.YES : yesOrNo.NO,
      payMarginAcctId: contractMargin?.payMarginAcctId,
      payMarginAcctNo: contractMargin?.payMarginAcctNo,
      payMarginAcctName: contractMargin?.payMarginAcctName,
      marginAmount: contractMargin?.marginAmount,
      marginRatio: contractMargin?.marginRatio,
      marginOffsetCreditFlag: contractMargin?.marginOffsetCreditFlag,
      marginProductId: contractMargin?.marginProductId,
      marginProductCode: contractMargin?.marginProductCode,
      marginProductName: contractMargin?.marginProductName,
      marginInterestFlag: contractMargin?.marginInterestFlag,
      marginNegotiatedRate: contractMargin?.marginNegotiatedRate,
      recInterestAcctNo: contractMargin?.marginRecIntAcctNo,
      recInterestAcctName: contractMargin?.marginRecIntAcctName,
      recInterestAcctId: contractMargin?.marginRecIntAcctId,
      marginRemark: contractMargin?.marginRemark,
      // 附件相关
      clnLoanFileList,
      fileIds,
      // 利率主子表字段转换
      rateAdjustWay: normal?.rateAdjustWay,
      rateAdjustFreq: normal?.rateAdjustFreq,
      firstDayAdjustFlag: normal?.firstDayAdjustFlag,
      fixedAdjustDate: normal?.fixedAdjustDate,
      repeatAnnuallyFlag: normal?.repeatAnnuallyFlag,
      // 担保
      gnteeDetailDtoList: parseGuaranteeDtoC2C(guaranteeDetailList),
      // 银团：金额、授信、贷款行
      syndicateAmountDetailDtoList: parseSyndicateAmountC2C(amountGroupList),
      // 授信
      syndicateCreditDetailDtoList: parseSyndicateCreditC2C(creditGroupList, amountGroupList),
      syndicateBankDetailDtoList: parseSyndicateBankC2C(attendBankList),
      guaranteeType: guaranteeTypeList || [],
      //贷款专户
      loanSpecialAccId: recPrincipalAcctId,
      loanSpecialAccName: recPrincipalAcctName,
      loanSpecialAccNo: recPrincipalAcctNo,
      //调整放款单账户
      adjustPayNoteFlag: yesOrNo.YES,
      //应收账款信息
      repayAccountsDetailDtoList: parseRepayAccountsC2C(receivableAccountsList),
      //保理产品类型
      factorProductType: factorProductType,
      //融资利率
      financingRatio: financeLoanRatio,
      greenCreditName: greenCreditName,
      industryCategoryId: industryCategoryId ? industryCategoryId.split(SPLITER) : [],
      loanInvestAreaId: loanInvestAreaId ? loanInvestAreaId.split(SPLITER) : [],
      greenCreditId: greenCreditId ? greenCreditId.split(SPLITER) : [],
      technologyLoansId: technologyLoansId ? technologyLoansId.split(SPLITER) : [],
      //授信序号
      creditSeqNo: creditSeqNo
    });
    getFileInfo();
  };

  return {
    parseDtoC2C
  };
};
