import { nextTick, reactive, shallowRef, computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useModelRange } from "@/hooks/conversion";
import httpTool from "@/utils/http";
import { openDateUrl, reExecuteUrl, cancelUrl } from "../url";
import { useConst } from "@ifs/support";

export const useList = () => {
  const { t } = useI18n();
  const executeResultType = useConst("common.AutoRepayExecuteResult");
  // 表格组件实例
  const queryTableRef = shallowRef();
  // 选中的行
  const checkedList = ref<any[]>([]);
  // 是否可选中
  const isChecked = computed(() => checkedList.value.length > 0);
  // 提交的提示消息
  const submitMessage = ref("");
  // 勾选checkbox
  const handleSelect = (row: []) => {
    checkedList.value = row;
  };
  // 清空
  const clearSelection = () => {
    checkedList.value.splice(0);
  };
  // 批量操作的参数
  const gatherBatchParams = () => {
    return { autoRepayExecuteList: checkedList.value };
  };
  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      fixed: "left",
      selectable(row: any) {
        // 已保存、已拒绝的可选
        return [executeResultType.FAILED].includes(row?.execResult);
      }
    },
    // 单据信息（单据号）
    {
      prop: "repayFormCode",
      label: t("loancounter.query.loan.autoRepay.tableColumns1"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop1"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns1"),
      groupWidth: "250px"
    },
    // 单据信息（执行结果）
    {
      prop: "execResult",
      label: t("loancounter.query.loan.autoRepay.tableColumns2"),
      formatter: { name: "const", const: "common.AutoRepayExecuteResult" },
      compact: "repayFormCode"
    },
    // 单据信息（执行时间）
    {
      prop: "repayAutoDate",
      label: t("loancounter.query.loan.autoRepay.tableColumns3"),
      compact: "repayFormCode"
    },
    // 机构信息（机构）
    {
      prop: "officeName",
      label: t("loancounter.query.loan.autoRepay.tableColumns4"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop2"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns4"),
      groupWidth: "250px",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 机构信息（币种）
    {
      prop: "currencyName",
      label: t("loancounter.query.loan.autoRepay.tableColumns5"),
      compact: "officeName",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 业务信息（贷款业务种类）
    {
      prop: "loanType",
      label: t("loancounter.query.loan.autoRepay.tableColumns6"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop3"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns6"),
      formatter: { name: "const", const: "core.LoanType" },
      groupWidth: "250px"
    },
    // 借款单位（借款单位编号）
    {
      prop: "loanClientCode",
      label: t("loancounter.query.loan.autoRepay.tableColumns7"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop4"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns7"),
      groupWidth: "250px",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 借款单位（借款单位名称）
    {
      prop: "loanClientName",
      label: t("loancounter.query.loan.autoRepay.tableColumns8"),
      compact: "loanClientCode"
    },
    // 还款总额（归还本金）
    {
      prop: "repayAmount",
      label: t("loancounter.query.loan.autoRepay.tableColumns9"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop5"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns9"),
      groupWidth: "250px"
    },
    // 还款总额（归还利息）
    {
      prop: "repayInterest",
      label: t("loancounter.query.loan.autoRepay.tableColumns10"),
      compact: "repayAmount"
    },
    // 还款总额（还款总额）
    {
      prop: "repayTotalAmount",
      label: t("loancounter.query.loan.autoRepay.tableColumns11"),
      compact: "repayAmount"
    },
    // 扣款账户（账户号）
    {
      prop: "payPrincipalAcctNo",
      label: t("loancounter.query.loan.autoRepay.tableColumns12"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop6"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns12"),
      groupWidth: "250px"
    },
    // 扣款账户（账户名称）
    {
      prop: "payPrincipalAcctName",
      label: t("loancounter.query.loan.autoRepay.tableColumns13"),
      compact: "payPrincipalAcctNo"
    },
    // 放款金额（合同余额）
    {
      prop: "contractBalance",
      label: t("loancounter.query.loan.autoRepay.tableColumns14"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop7"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns14"),
      groupWidth: "250px",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 放款金额（放款金额）
    {
      prop: "payAmount",
      label: t("loancounter.query.loan.autoRepay.tableColumns15"),
      compact: "contractBalance",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 放款金额（放款余额）
    {
      prop: "receiptBalance",
      label: t("loancounter.query.loan.autoRepay.tableColumns16"),
      compact: "contractBalance",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 合同信息（合同编号）
    {
      prop: "contractCode",
      label: t("loancounter.query.loan.autoRepay.tableColumns17"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop8"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns17"),
      groupWidth: "250px",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 合同信息（放款通知单号）
    {
      prop: "payFormCode",
      label: t("loancounter.query.loan.autoRepay.tableColumns18"),
      compact: "contractCode",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 合同期限（开始日）
    {
      prop: "payStartDate",
      label: t("loancounter.query.loan.autoRepay.tableColumns19"),
      groupLabel: t("loancounter.query.loan.autoRepay.tableColumnsTop9"),
      compactLabel: t("loancounter.query.loan.autoRepay.tableColumns19"),
      groupWidth: "250px",
      visible: false,
      groupVisible: true,
      compactVisible: true
    },
    // 合同期限（到期日）
    {
      prop: "payEndDate",
      label: t("loancounter.query.loan.autoRepay.tableColumns20"),
      compact: "payStartDate"
    },
    // 任务失败原因
    {
      prop: "execRemark",
      label: t("loancounter.query.loan.autoRepay.tableColumnsTop10"),
      hiddenCompactLabel: true
    },
    // 还款结果
    {
      prop: "repayTransResult",
      label: t("loancounter.query.loan.autoRepay.tableColumnsTop11"),
      formatter: { name: "const", const: "loan.RepayResultType" },
      hiddenCompactLabel: true
    },
    // 操作
    {
      prop: "operate",
      label: t("loancounter.query.loan.autoRepay.tableColumnsTop12"),
      width: "200px",
      fixed: "right",
      slots: { default: "buttons" },
      hiddenCompactLabel: true
    }
  ];
  // 排序的字段
  const allowSort = tableColumns.filter(x => !["loanType", "execResult"].includes(x.prop)).map(x => x.prop);
  // 表格查询对象
  const queryForm = reactive({
    officeIdList: [],
    currencyIdList: [],
    loanTypeList: [],
    loanClientCode: "",
    repayTotalAmount: [],
    contractCode: "",
    payFormCode: "",
    executeDate: [],
    autoRepayExecuteResultList: [],
    repayResultList: []
  });

  const { postParams } = useModelRange(["executeDate", "repayTotalAmount"]);
  const openDate = ref();
  //获取开机日
  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(openDateUrl).then((res: any) => {
        openDate.value = res.data.onlineDate;
        resolve([res.data.onlineDate, res.data.onlineDate]);
        nextTick(() => {
          queryTableRef.value.renderTableData();
        });
      });
    });
  };
  // 列表查询
  const handleSearch = () => {
    queryTableRef.value.renderTableData();
    queryTableRef.value.clearSelection();
  };
  // 重新执行前触发
  const beforeReExecuteTrigger = () => {
    submitMessage.value = t("loancounter.query.loan.autoRepay.beforeReExecuteTrigger", [checkedList.value.length]);
    return true;
  };
  // 撤销前触发
  const beforeCancelTrigger = () => {
    submitMessage.value = t("loancounter.query.loan.autoRepay.beforeCancelTrigger", [checkedList.value.length]);
    return true;
  };
  // 重新执行成功回调
  const reExecuteConfirm = {
    success: t("loancounter.query.loan.autoRepay.reExecuteSuccess"),
    fail: t("loancounter.query.loan.autoRepay.reExecuteFail")
  };
  // 撤销成功回调
  const cancelConfirm = {
    success: t("loancounter.query.loan.autoRepay.cancelSuccess"),
    fail: t("loancounter.query.loan.autoRepay.cancelFail")
  };
  // 列表操作处理
  const generalButtonOption = (row: any) => {
    // 重新执行
    const result: any[] = [
      {
        buttonText: t("loancounter.query.loan.autoRepay.reExecute"),
        isShow: [executeResultType.FAILED].includes(row?.execResult) && row.repayAutoDate === openDate.value,
        submitComOpt: {
          url: reExecuteUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          },
          props: {
            operateName: t("loancounter.query.loan.autoRepay.reExecute"),
            confirmText: t("loancounter.query.loan.autoRepay.reExecuteConfirm"),
            resultConfirm: t("loancounter.query.loan.autoRepay.reExecuteSuccess"),
            resultTitle: t("loancounter.query.loan.autoRepay.reExecuteFail"),
            icon: null,
            type: "primary"
          }
        }
      }
    ];

    // 撤销
    result.push({
      buttonText: t("loancounter.query.loan.autoRepay.cancel"),
      isShow: [executeResultType.FAILED].includes(row?.execResult),
      submitComOpt: {
        url: cancelUrl,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleSearch();
          }
        },
        props: {
          operateName: t("loancounter.query.loan.autoRepay.cancel"),
          confirmText: t("loancounter.query.loan.autoRepay.cancelConfirm"),
          resultConfirm: t("loancounter.query.loan.autoRepay.cancelSuccess"),
          resultTitle: t("loancounter.query.loan.autoRepay.cancelFail"),
          icon: null,
          type: "danger"
        }
      }
    });
    return result;
  };
  return {
    getOpenDate,
    allowSort,
    queryForm,
    tableColumns,
    postParams,
    handleSearch,
    queryTableRef,
    isChecked,
    handleSelect,
    clearSelection,
    gatherBatchParams,
    submitMessage,
    beforeReExecuteTrigger,
    beforeCancelTrigger,
    reExecuteConfirm,
    cancelConfirm,
    generalButtonOption
  };
};

export default useList;
