import { useI18n } from "vue-i18n";

export const useTableColumn = () => {
  const { t } = useI18n();

  //列表显示列配置
  const depositComputerTableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "150px",
      prop: "accountNo",
      label: t("loancore.interestprocess.interestsett.accountNo"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "accountName",
      label: t("loancore.interestprocess.interestsett.accountName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "accountGroup",
      label: t("loancore.interestprocess.interestsett.accountGroup"),
      formatter: { name: "const", const: "counter.AccountGroup" }
    },
    {
      width: "150px",
      prop: "depositNo",
      label: t("loancore.interestprocess.interestsett.depositNo")
    },
    {
      width: "150px",
      prop: "officeName",
      label: t("loancore.interestprocess.interestsett.officeId")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("loancore.interestprocess.interestsett.currencyId")
    },
    {
      width: "150px",
      prop: "startDate",
      label: t("loancore.interestprocess.interestsett.startDate")
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("loancore.interestprocess.interestsett.endDate")
    },
    {
      width: "150px",
      prop: "days",
      label: t("loancore.interestprocess.interestsett.days")
    },
    {
      width: "150px",
      prop: "interestBalance",
      label: t("loancore.interestprocess.interestsett.interestBalance"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "rate",
      label: t("loancore.interestprocess.interestsett.yearRate"),
      formatter: "rate"
    },
    {
      width: "150px",
      prop: "totalInterestReceivable",
      slots: { default: "totalInterestReceivable" },
      label: t("loancore.interestprocess.interestsett.totalInterestReceivable"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "totalInterest",
      slots: { default: "totalInterest" },
      label: t("loancore.interestprocess.interestsett.totalInterest"),
      align: "right"
    },
    {
      width: "150px",
      prop: "ableSettleInterest",
      label: t("loancore.interestprocess.interestsett.ableSettInterest"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "totalAccumAccruedInterest",
      slots: { default: "totalAccumAccruedInterest" },
      label: t("loancore.interestprocess.interestsett.totalAccumAccruedInterest"),
      formatter: "amount",
      align: "right"
    }
  ];

  const depositQueryTableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "150px",
      prop: "accountNo",
      label: t("loancore.interestprocess.interestsett.accountNo"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "accountName",
      label: t("loancore.interestprocess.interestsett.accountName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "accountGroup",
      label: t("loancore.interestprocess.interestsett.accountGroup"),
      formatter: { name: "const", const: "counter.AccountGroup" }
    },
    {
      width: "200px",
      prop: "transactionType",
      label: t("loancore.interestprocess.interestsett.transactionType"),
      formatter: { name: "const", const: "common.TransactionType" }
    },
    {
      width: "200px",
      prop: "transNo",
      label: t("loancore.interestprocess.interestsett.transNo")
    },
    {
      width: "150px",
      prop: "depositNo",
      label: t("loancore.interestprocess.interestsett.depositNo")
    },
    {
      width: "150px",
      prop: "officeName",
      label: t("loancore.interestprocess.interestsett.officeId")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("loancore.interestprocess.interestsett.currencyId")
    },
    {
      width: "150px",
      prop: "startDate",
      label: t("loancore.interestprocess.interestsett.startDate")
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("loancore.interestprocess.interestsett.endDate")
    },
    {
      width: "150px",
      prop: "days",
      label: t("loancore.interestprocess.interestsett.days")
    },
    {
      width: "150px",
      prop: "interestBalance",
      label: t("loancore.interestprocess.interestsett.interestBalance"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "rate",
      label: t("loancore.interestprocess.interestsett.yearRate"),
      formatter: "rate"
    },
    {
      width: "150px",
      prop: "receivableTotalInterest",
      label: t("loancore.interestprocess.interestsett.totalInterestReceivable"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "totalInterest",
      label: t("loancore.interestprocess.interestsett.recTotalInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      prop: "operate",
      width: "220px",
      label: t("loancore.interestprocess.interestsett.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];

  const loanComputerTableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "150px",
      prop: "payInterestAcctNo", // 付息账户号
      label: t("loancore.interestprocess.interestsett.payInterestAcctNo"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "payInterestAcctName", // 付息账户名称
      label: t("loancore.interestprocess.interestsett.payInterestAcctName"),
      showOverflowTooltip: true
    },
    // {
    //   width: "150px",
    //   prop: "loanAcctNo", // 账户号
    //   label: t("loancore.interestprocess.interestsett.accountNo"),
    //   showOverflowTooltip: true
    // },
    // {
    //   width: "150px",
    //   prop: "loanAcctName", // 账户名称
    //   label: t("loancore.interestprocess.interestsett.accountName"),
    //   showOverflowTooltip: true
    // },
    {
      width: "150px",
      prop: "loanType", // 贷款业务种类
      label: t("loancore.interestprocess.interestsett.loanBusinessType"),
      formatter: { name: "const", const: "core.LoanType" }
    },
    {
      width: "150px",
      prop: "contractCode", // 合同号
      label: t("loancore.interestprocess.interestsett.contractNo")
    },
    {
      width: "150px",
      prop: "payFormCode", // 放款通知单号
      label: t("loancore.interestprocess.interestsett.payFormNo")
    },
    {
      width: "150px",
      prop: "oppBranchName", // 开户行
      label: t("loancore.interestprocess.interestsett.openBank")
    },
    {
      width: "150px",
      prop: "officeName", //机构
      label: t("loancore.interestprocess.interestsett.officeId")
    },
    {
      width: "150px",
      prop: "currencyName", // 币种
      label: t("loancore.interestprocess.interestsett.currencyId")
    },
    {
      width: "150px",
      prop: "calculateStartDate", // 开始日期
      label: t("loancore.interestprocess.interestsett.startDate")
    },
    {
      width: "150px",
      prop: "calculateEndDate", // 结束日期
      label: t("loancore.interestprocess.interestsett.endDate")
    },
    {
      width: "150px",
      prop: "calculateDays", // 天数
      label: t("loancore.interestprocess.interestsett.days")
    },
    {
      width: "150px",
      prop: "receiptBalance", // 通知单余额
      label: t("loancore.interestprocess.interestsett.receiptBalance"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "calculateNormalRate", // 年利率
      label: t("loancore.interestprocess.interestsett.yearRate"),
      formatter: "rate"
    },
    {
      width: "150px",
      prop: "receivableInterest", // 本次利息
      label: t("loancore.interestprocess.interestsett.totalInterestReceivable"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "oweInterest", // 历史欠息
      label: t("loancore.interestprocess.interestsett.hisOweInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "compoundInterest", // 复利
      label: t("loancore.interestprocess.interestsett.compoundInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "totalInterest", // 利息合计
      slots: { default: "totalInterest" },
      label: t("loancore.interestprocess.interestsett.totalInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "freedInterest", // 已免还利息
      label: t("loancore.interestprocess.interestsett.freedInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "realAmount", //本次可结利息
      label: t("loancore.interestprocess.interestsett.ableSettInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "accrualTotalInterest", // 累计已计提利息
      label: t("loancore.interestprocess.interestsett.totalAccumAccruedInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "accountingTransNo", // 入账信息
      slots: { default: "accountingTransNo" },
      label: t("loancore.interestprocess.interestsett.accountingTransNo")
    }
    // {
    //   width: "150px",
    //   prop: "totalInterestBalance", // 利息积数
    //   label: t("loancore.interestprocess.interestsett.interestBalance"),
    //   formatter: "amount",
    //   headerAlign: "right"
    // },
  ];

  const loanQueryTableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "200px",
      prop: "insNoticeCode", //计提通知单
      label: t("loancore.interestprocess.interestsett.insNoticeCode")
    },
    {
      width: "150px",
      prop: "interestDate", //结息/冲销日期
      label: t("loancore.interestprocess.interestsett.predrawDate")
    },
    {
      width: "150px",
      prop: "interestOperateType", // 结息类型
      label: t("loancore.interestprocess.interestsett.interestSettType"),
      formatter: { name: "const", const: "core.LoanInterestOperateType" }
    },
    {
      width: "150px",
      prop: "loanType", // 贷款业务种类
      label: t("loancore.interestprocess.interestsett.loanBusinessType"),
      formatter: { name: "const", const: "core.LoanType" }
    },
    {
      width: "150px",
      prop: "payInterestAcctNo", // 付息账户名称
      label: t("loancore.interestprocess.interestsett.payInterestAcctNo"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "payInterestAcctName", // 付息账户名称
      label: t("loancore.interestprocess.interestsett.payInterestAcctName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "oppBranchName", // 开户行
      label: t("loancore.interestprocess.interestsett.openBank")
    },
    {
      width: "150px",
      prop: "contractCode", // 合同号
      label: t("loancore.interestprocess.interestsett.contractNo")
    },
    {
      width: "150px",
      prop: "receiptCode", // 放款通知单号
      label: t("loancore.interestprocess.interestsett.payFormNo")
    },
    {
      width: "150px",
      prop: "officeName", //机构
      label: t("loancore.interestprocess.interestsett.officeId")
    },
    {
      width: "150px",
      prop: "currencyName", // 币种
      label: t("loancore.interestprocess.interestsett.currencyId")
    },
    {
      width: "150px",
      prop: "calInterestStartDate", // 开始日期
      label: t("loancore.interestprocess.interestsett.startDate")
    },
    {
      width: "150px",
      prop: "calInterestEndDate", // 结束日期
      label: t("loancore.interestprocess.interestsett.endDate")
    },
    {
      width: "150px",
      prop: "days", // 天数
      label: t("loancore.interestprocess.interestsett.days")
    },
    {
      width: "150px",
      prop: "interestBalance", // 通知单余额
      label: t("loancore.interestprocess.interestsett.receiptBalance"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "rate", // 年利率
      label: t("loancore.interestprocess.interestsett.yearRate"),
      formatter: "rate"
    },
    {
      width: "150px",
      prop: "receivableInterest", // 本次利息
      label: t("loancore.interestprocess.interestsett.totalInterestReceivable"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "oweInterest", // 历史欠息
      label: t("loancore.interestprocess.interestsett.hisOweInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "compoundInterest", // 复利
      label: t("loancore.interestprocess.interestsett.compoundInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "totalInterest", // 利息合计
      slots: { default: "totalInterest" },
      label: t("loancore.interestprocess.interestsett.totalInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "freedInterest", // 已免还利息
      label: t("loancore.interestprocess.interestsett.freedInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "noticeOperate", // 执行时间
      label: t("loancore.interestprocess.interestsett.noticeOperate")
    },
    {
      width: "150px",
      prop: "receivableInterest", //本次可结利息
      label: t("loancore.interestprocess.interestsett.ableSettInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "realReceivableInterest", //实收利息
      label: t("loancore.interestprocess.interestsett.realReceivableInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "accrualTotalInterest", // 累计已计提利息
      label: t("loancore.interestprocess.interestsett.totalAccumAccruedInterest"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "150px",
      prop: "inputTime", // 执行时间
      label: t("loancore.interestprocess.interestsett.executeTime")
    },
    {
      prop: "operate",
      width: "220px",
      label: t("loancore.interestprocess.interestsett.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];

  return { depositComputerTableColumns, depositQueryTableColumns, loanComputerTableColumns, loanQueryTableColumns };
};

export default useTableColumn;
