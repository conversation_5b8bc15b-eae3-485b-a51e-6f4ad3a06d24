import { nextTick, reactive, ref, shallowRef, watch } from "vue";
import useForm from "./useForm";
import httpTool from "@/utils/http";
import { getOne } from "../url";
import { random } from "@/utils/uuid";
import { useI18n } from "vue-i18n";
import { printDom } from "@/utils/print";

export const useDetail = (props?: Record<string, any>) => {
  const {
    form,
    formData,
    interbankCreditType,
    currencyType,
    formatterCategory,
    creditControlType,
    creditUseType,
    creditStyleType,
    ibcCreditType,
    creditTermEnum,
    creditUsingTypeEnum
  } = useForm();
  const { t } = useI18n();
  // 抽屉模板
  const drawerRef = shallowRef();

  const usageTable = shallowRef();

  const visible = ref<boolean>(false);

  const pageName = ref<string>("detail");

  // 结转数据
  const carryoverGrid = shallowRef();
  const carryoverList = reactive<any[]>([]);

  const setTrueToVisible = () => {
    visible.value = true;
  };
  const setFalseToVisible = () => {
    visible.value = false;
  };

  const usageQueryParams = reactive({
    infoId: "",
    originalId: ""
  });

  const loadData = async id => {
    const loadSuccess = await httpTool.post(getOne, { id: id }, { noLoading: true, ignoreGlobalErrorTip: false });
    if (loadSuccess.success) {
      Object.assign(formData, loadSuccess.data);
      if (loadSuccess.data.carryoverDetailDtoList && loadSuccess.data.carryoverDetailDtoList.length > 0) {
        loadSuccess.data.carryoverDetailDtoList.forEach(element => {
          const carrClientData = {
            counterpartyId: element.counterpartyId,
            counterpartyCode: element.counterpartyCode,
            counterpartyName: element.counterpartyName,
            originCreditCode: element.originCreditCode,
            originCreditCategory: element.originCreditCategory,
            originCreditMode: element.originCreditMode,
            originCreditType: element.originCreditType,
            originCurrencyType: element.originCurrencyType,
            originAmountCurrencyId: element.originAmountCurrencyId,
            originAmountCurrencyCode: element.originAmountCurrencyCode,
            originAmountCurrencyName: element.originAmountCurrencyName,
            originCreditSeqNo: element.originCreditSeqNo,
            originCreditVarietyId: element.originCreditVarietyId,
            originCreditVarietyCode: element.originCreditVarietyCode,
            originCreditVarietyName: element.originCreditVarietyName,
            originEffectiveDate: element.originEffectiveDate,
            originEndDate: element.originEndDate,
            originCreditTerm: element.originCreditTerm,
            originCreditVersion: element.originCreditVersion,
            originCarryoverAmount: element.originCarryoverAmount,
            targetCarryoverAmount: element.targetCarryoverAmount,
            targetCreditCode: element.targetCreditCode,
            targetCreditVersion: element.targetCreditVersion,
            targetCreditSeqNo: element.targetCreditSeqNo,
            targetCreditVarietyId: element.targetCreditVarietyId,
            targetCreditVarietyCode: element.targetCreditVarietyCode,
            targetCreditVarietyName: element.targetCreditVarietyName,
            _randomId: random()
          };
          carryoverList.push(carrClientData);
        });
      }
      nextTick(() => {
        drawerRef.value.openDrawer();
        usageTable.value.renderTableData();
      });
    }
  };

  watch([() => visible.value, () => props?.row, () => props?.page], nVal => {
    if (nVal[0]) {
      pageName.value = props?.page;
      usageQueryParams.infoId = props?.row.id;
      usageQueryParams.originalId = props?.row.originalId;
      loadData(props?.row.id);
    } else {
      usageQueryParams.infoId = "";
      usageQueryParams.originalId = "";
      drawerRef.value.closeDrawer();
    }
  });

  const allowSort = [
    "occupyCode",
    "categoryName",
    "transCurrency",
    "occupancyAmount",
    "releaseAmount",
    "counterpartyName"
  ];

  const detailAllowSort = ["usageType", "occupyCode", "transCurrencyName", "transAmount"];

  const carryoverDetailColumns = [
    { prop: "counterpartyCode", width: "50px", label: t("interbankcredit.query.usageQuery.counterpartyCode") },
    { prop: "counterpartyName", width: "50px", label: t("interbankcredit.query.usageQuery.counterpartyName") },
    { prop: "originCreditCode", width: "50px", label: t("interbankcredit.query.usageQuery.originCreditNo") },
    {
      prop: "originCreditCategory",
      formatter: { name: "const", const: "interbankcredit.InterbankCreditType" },
      label: t("interbankcredit.query.usageQuery.creditCategoryType")
    },
    {
      prop: "originCreditType",
      formatter: { name: "const", const: "interbankcredit.IBCCreditType" },
      label: t("interbankcredit.query.usageQuery.creditType")
    },
    {
      prop: "originCreditVarietyName",
      label: t("interbankcredit.query.usageQuery.creditCategory")
    },
    {
      prop: "originCreditTerm",
      formatter: { name: "const", const: "interbankcredit.IBCCreditTerm" },
      label: t("interbankcredit.query.usageQuery.creditTerm")
    },
    {
      prop: "originCarryoverAmount",
      align: "right",
      formatter: "amount",
      label: t("interbankcredit.query.usageQuery.notCarryoverAmount")
    },
    {
      prop: "targetCreditSeqNo",
      label: t("interbankcredit.query.usageQuery.targetToSeqNo")
    },
    {
      prop: "targetCreditVarietyName",
      label: t("interbankcredit.query.usageQuery.targetCreditVariety")
    },
    {
      prop: "targetCarryoverAmount",
      align: "right",
      formatter: "amount",
      label: t("interbankcredit.query.usageQuery.targetCarryoverAmount")
    }
  ];

  //公共处理信息
  const state = reactive({
    //结转数据
    carryoverInfo: {
      showDialog: false,
      carryoverDetailColumns: carryoverDetailColumns
    }
  });
  const printNode = ref();
  const handlePrint = () => {
    printDom(printNode.value);
  };
  return {
    drawerRef,
    setTrueToVisible,
    setFalseToVisible,
    form,
    formData,
    interbankCreditType,
    currencyType,
    formatterCategory,
    creditControlType,
    creditUseType,
    creditStyleType,
    ibcCreditType,
    pageName,
    allowSort,
    detailAllowSort,
    usageTable,
    usageQueryParams,
    creditTermEnum,
    creditUsingTypeEnum,
    state,
    carryoverGrid,
    carryoverList,
    carryoverDetailColumns,
    handlePrint,
    printNode
  };
};

export default useDetail;
