<template>
  <f-query-scene :title="t('interbankcredit.business.creditAdd.linkTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="listPage"
        border
        :selectable-all="false"
        :form-data="queryForm"
        :allow-sort="allowSort"
        show-header
        :params="queryForm"
        :show-collapse="false"
        auto-reset
        :show-print="false"
        auto-init
        :export-exclude="['operate']"
        :export-url="listExport"
        :post-params="postParams"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="handClearSelection"
        query-comp-id="interbankcredit-business-creditAdd-query-001"
        table-comp-id="interbankcredit-business-creditAdd-table-001"
        :show-summation-sum="false"
        :summation-biz-label="t('interbankcredit.business.creditAdd.record')"
        :summation-biz-unit="t('interbankcredit.business.creditAdd.recordUnit')"
        :show-count-value="false"
        :count-label="t('interbankcredit.business.creditAdd.record')"
        :count-label-unit="t('interbankcredit.business.creditAdd.recordUnit')"
        tableType="Record"
      >
        <template #operate>
          <f-button type="primary" @click="linkToAdd">{{ t("interbankcredit.business.creditAdd.btnAdd") }}</f-button>
          <f-submit-state
            :is-batch="true"
            operate="submit"
            type="primary"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="batchSubmit"
            @close="handleSearch"
            compatible
            :confirm-text="submitMessage"
            :before-trigger="beforeSubmitTrigger"
            :batch-confirm-map="submitResultConfim"
          />
          <f-submit-state
            operate="remove"
            type="danger"
            :is-batch="true"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="batchRemove"
            @close="handleSearch"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteResultConfim"
          />
          <f-button type="primary" @click="linkToImport">{{
            t("interbankcredit.business.creditAdd.btnImport")
          }}</f-button>
        </template>
        <template #query-panel>
          <f-form-item :label="t('interbankcredit.business.creditAdd.office')" prop="officeCodes">
            <f-select
              v-model="queryForm.officeCodes"
              value-key="officeCode"
              label="officeName"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :url="getOfficeInfo"
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('interbankcredit.business.creditAdd.totalAmountCurrency')" prop="currencyCodes">
            <f-select
              v-model="queryForm.currencyCodes"
              value-key="currencyCode"
              label="currencyName"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :url="getCurrencyInfo"
              clearable
              method="post"
            />
          </f-form-item>
          <f-form-item :label="t('interbankcredit.business.creditAdd.creditCategoryType')" prop="businessTypes">
            <f-select
              v-model="queryForm.businessTypes"
              filterable
              :data="interbankCreditType"
              blank-option
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankcredit.business.creditAdd.creditType')" prop="creditTypes">
            <f-select
              v-model="queryForm.creditTypes"
              filterable
              :data="ibcCreditType"
              blank-option
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankcredit.business.creditAdd.creditCode')" prop="code">
            <f-magnifier-single
              :title="t('interbankcredit.business.creditAdd.creditCodeMagnifier')"
              :url="listPage"
              method="post"
              v-model="queryForm.code"
              row-key="code"
              row-label="code"
              auto-init
            >
              <f-magnifier-column prop="code" :label="t('interbankcredit.business.creditAdd.code')" />
            </f-magnifier-single>
          </f-form-item>
          <!-- 开始日期 -->
          <f-form-item :label="t('interbankcredit.business.creditAdd.startDate')" prop="effectiveDate">
            <f-date-picker v-model="queryForm.effectiveDateEq" type="date" />
          </f-form-item>
          <!-- 结束日期 -->
          <f-form-item :label="t('interbankcredit.business.creditAdd.endDate')" prop="endDate">
            <f-date-picker v-model="queryForm.endDateEq" type="date" />
          </f-form-item>
          <f-form-item :label="t('interbankcredit.business.creditAdd.counterpartyName')" prop="counterpartyName">
            <f-magnifier-multi
              :title="t('interbankcredit.business.creditAdd.counterpartyMagnifier')"
              :url="counterPartyList"
              method="post"
              v-model="queryForm.counterpartyCodes"
              row-key="clientCode"
              row-label="clientName"
              input-key="clientCode"
            >
              <f-magnifier-column prop="clientCode" :label="t('interbankcredit.business.creditAdd.counterpartyCode')" />
              <f-magnifier-column prop="clientName" :label="t('interbankcredit.business.creditAdd.counterpartyName')" />
            </f-magnifier-multi>
          </f-form-item>
          <f-form-item :label="t('interbankcredit.business.creditAdd.applyStatus')" prop="businessStatuses">
            <f-select
              v-model="queryForm.businessStatuses"
              filterable
              :data="businessStatus"
              blank-option
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
        </template>
        <template #detail="{ row }">
          <f-button link type="primary" @click="viewDetail(row)">{{ row.code }}</f-button>
        </template>
        <template #operates="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="toModify(row)">
            <!-- 撤销按钮(不显示) -->
            <template #suffix v-if="row.businessStatus === businessStatus.APPROVED">
              <f-submit-state
                :gather-params="gatherCancelParams(row)"
                :url="revoke"
                operate="cancel"
                :is-batch="false"
                :operate-name="t('interbankcredit.business.creditAdd.btnCancel')"
                :icon="DtgCopy"
                link
                :is-show-result-btn-group="false"
                confirm-text=" "
                :result-confirm="t('interbankcredit.business.creditAdd.cancelSuccess')"
                @close="handleSearch"
                :before-trigger="() => revokeValidator(row)"
                :resultTitle="t('interbankcredit.business.creditAdd.btnCancel')"
                :beforeConfirm="beforeConfirm"
              >
                <template #confirmEdit>
                  <f-multi-form-panel ref="confirmForm" :model="cancelDto">
                    <f-form-item
                      :label="t('interbankcredit.business.creditAdd.revokeReason')"
                      prop="cancelReason"
                      :required="true"
                    >
                      <f-input v-model="cancelDto.cancelReason" maxlength="150" />
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
            </template>
          </OperateButton>
        </template>
      </f-query-grid>
      <!-- 批量导入对话框 -->
      <f-dialog
        v-model="dialogVisible"
        :title="t('interbankcredit.business.creditAdd.btnImport')"
        width="80%"
        append-to-body
        :before-close="handleClose"
        destroy-on-close="true"
      >
        <template #default>
          <FImportTable :template-url="downLoad" :import-url="importUrl" :table-columns="importResultColumns">
            <template #fail-desc="scope">
              <div class="stat-card">
                <div class="stat-line has-border">
                  <span>{{ t("interbankcredit.business.creditAdd.importNumber") }}</span>
                  <span style="color: #d8853e">{{ scope?.res?.data?.failCount }}</span>
                </div>
                <div class="stat-line">
                  <span>{{ t("interbankcredit.business.creditAdd.importAmount") }}</span>
                  <span style="color: #d8853e">{{ format(scope?.res?.data?.failAmount) }}</span>
                </div>
              </div>
            </template>
            <template #success-desc="scope">
              <div class="stat-card">
                <div class="stat-line has-border">
                  <span>{{ t("interbankcredit.business.creditAdd.importNumber") }}</span>
                  <span style="color: #2fa758">{{ scope?.res?.data?.successCount }}</span>
                </div>
                <div class="stat-line">
                  <span>{{ t("interbankcredit.business.creditAdd.importAmount") }}</span>
                  <span style="color: #2fa758">{{ format(scope?.res?.data?.successAmount) }}</span>
                </div>
              </div>
            </template>
            <template #total-desc="scope">
              <div class="stat-card">
                <div class="stat-line has-border">
                  <span>{{ t("interbankcredit.business.creditAdd.importNumber") }}</span>
                  <span style="color: #3077b8">{{ scope?.res?.data?.submitCount }}</span>
                </div>
                <div class="stat-line">
                  <span>{{ t("interbankcredit.business.creditAdd.importAmount") }}</span>
                  <span style="color: #3077b8">{{ format(scope?.res?.data?.submitAmount) }}</span>
                </div>
              </div>
            </template>
          </FImportTable>
        </template>
      </f-dialog>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>

<style scoped>
.stat-card {
  width: 100%;
}

.stat-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.has-border {
  border-bottom: 1px solid var(--el-border-color-light); /* 使用Element Plus的现有变量 */
}
</style>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { format } from "@/utils/currency";
import {
  batchRemove,
  listPage,
  listExport,
  getOfficeInfo,
  getCurrencyInfo,
  counterPartyList,
  batchSubmit,
  revoke,
  downLoad,
  importUrl
} from "../url";
import useList from "../hooks/useList";
import { goPage } from "../hooks/usePage";
import Detail from "./components/Detail.vue";
import { nextTick, ref, shallowRef } from "vue";
import type { IBCInfoDto } from "../types";
import { DtgCopy } from "@dtg/frontend-plus-icons";

const { t } = useI18n();

const linkToAdd = () => {
  goPage("add");
};

const toModify = row => {
  goPage("modify", { id: row.id });
};

const rowId = ref<number>();
const detail = shallowRef();

const viewDetail = (row: IBCInfoDto) => {
  rowId.value = row.id as number;
  nextTick(() => {
    detail.value.setTrueToVisible();
  });
};

const {
  tableColumns,
  queryForm,
  queryTable,
  allowSort,
  handleSearch,
  handleSelect,
  isChecked,
  generalButtonOption,
  handClearSelection,
  postParams,
  businessStatus,
  interbankCreditType,
  ibcCreditType,
  gatherBatchParams,
  gatherCancelParams,
  cancelDto,
  handleClose,
  dialogVisible,
  linkToImport,
  importResultColumns,
  revokeValidator,
  confirmForm,
  beforeConfirm,
  beforeDeleteTrigger,
  deleteResultConfim,
  submitMessage,
  beforeSubmitTrigger,
  submitResultConfim
} = useList();
</script>
