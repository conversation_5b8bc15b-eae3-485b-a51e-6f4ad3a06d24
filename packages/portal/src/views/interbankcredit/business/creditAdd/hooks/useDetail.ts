import { nextTick, ref, shallowRef, watch } from "vue";
import useForm from "./useForm";
import httpTool from "@/utils/http";
import { getOne } from "../url";
import { random } from "@/utils/uuid";

export const useDetail = (constDirectory?: Record<string, any>, props?: Record<string, any>) => {
  const {
    form,
    formData,
    interbankCreditType,
    currencyType,
    systemCodeEnum,
    formatterCategory,
    creditControlType,
    creditUseType,
    creditStyleType,
    ibcBusinessType,
    ibcCreditType,
    creditTermEnum,
    state,
    carryoverGrid,
    carryoverList
  } = useForm();
  // 抽屉模板
  const drawerRef = shallowRef();
  const visible = ref<boolean>(false);
  const setTrueToVisible = () => {
    visible.value = true;
  };
  const setFalseToVisible = () => {
    visible.value = false;
  };

  const loadData = async id => {
    const loadSuccess = await httpTool.post(getOne, { id: id }, { noLoading: true, ignoreGlobalErrorTip: false });
    if (loadSuccess.success) {
      Object.assign(formData, loadSuccess.data);
      carryoverList.splice(0);
      if (loadSuccess.data.carryoverDetailDtoList && loadSuccess.data.carryoverDetailDtoList.length > 0) {
        loadSuccess.data.carryoverDetailDtoList.forEach(element => {
          const carrClientData = {
            counterpartyId: element.counterpartyId,
            counterpartyCode: element.counterpartyCode,
            counterpartyName: element.counterpartyName,
            originCreditCode: element.originCreditCode,
            originCreditCategory: element.originCreditCategory,
            originCreditMode: element.originCreditMode,
            originCreditType: element.originCreditType,
            originCurrencyType: element.originCurrencyType,
            originAmountCurrencyId: element.originAmountCurrencyId,
            originAmountCurrencyCode: element.originAmountCurrencyCode,
            originAmountCurrencyName: element.originAmountCurrencyName,
            originCreditSeqNo: element.originCreditSeqNo,
            originCreditVarietyId: element.originCreditVarietyId,
            originCreditVarietyCode: element.originCreditVarietyCode,
            originCreditVarietyName: element.originCreditVarietyName,
            originEffectiveDate: element.originEffectiveDate,
            originEndDate: element.originEndDate,
            originCreditTerm: element.originCreditTerm,
            originCreditVersion: element.originCreditVersion,
            originCarryoverAmount: element.originCarryoverAmount,
            targetCarryoverAmount: element.targetCarryoverAmount,
            targetCreditCode: element.targetCreditCode,
            targetCreditVersion: element.targetCreditVersion,
            targetCreditSeqNo: element.targetCreditSeqNo,
            _randomId: random()
          };
          carryoverList.push(carrClientData);
        });
      }
      nextTick(() => {
        drawerRef.value.openDrawer();
      });
    }
  };

  watch([() => visible.value, () => props?.id], nVal => {
    if (nVal[0]) {
      loadData(props?.id);
    } else {
      drawerRef.value.closeDrawer();
    }
  });

  return {
    drawerRef,
    setTrueToVisible,
    setFalseToVisible,
    form,
    formData,
    interbankCreditType,
    currencyType,
    systemCodeEnum,
    formatterCategory,
    creditControlType,
    creditUseType,
    creditStyleType,
    ibcBusinessType,
    ibcCreditType,
    creditTermEnum,
    state,
    carryoverGrid,
    carryoverList
  };
};

export default useDetail;
