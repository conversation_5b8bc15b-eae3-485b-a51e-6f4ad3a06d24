import type { IBCInfoDto, FileDto, IBCDetailInfoDto } from "../types";
import { computed, nextTick, reactive, ref, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { getOne, getCurrencyInfo, openDateUrl } from "../url";
import { goPage } from "./usePage";
import { FMessageBox } from "@dtg/frontend-plus";
import { random } from "@/utils/uuid";
import { useI18n } from "vue-i18n";
import { useCarryover, useCarryoverColumns } from "./useCarryover";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useForm = () => {
  const { defaultOfficeId, defaultOfficeCode, defaultOfficeName } = storeToRefs(useUserStoreHook());
  const { t } = useI18n();

  const form = shallowRef();

  // 创建upload 组件引用
  const upload = shallowRef();

  const currencyRef = ref();

  //上传附件返回的数组信息
  const fileInfos = ref<FileDto[]>([]);

  const interbankCreditType = useConst("interbankcredit.InterbankCreditType");

  const ibcCreditType = useConst("interbankcredit.IBCCreditType");

  const currencyType = useConst("credit.CurrencyType");

  const creditControlType = useConst("credit.CreditControlType");

  const creditUseType = useConst("credit.CreditUseType");

  const systemCodeEnum = useConst("interbankcredit.SystemCode");

  const creditStyleType = useConst("interbankcredit.IBCCreditStyleType");

  const ibcBusinessType = useConst("interbankcredit.IbcBusinessType");

  const yesOrNo = useConst("counter.YesOrNo");

  const creditTermEnum = useConst("interbankcredit.IBCCreditTerm");

  // 同业授信数据对象
  const formData = reactive<IBCInfoDto>({
    id: null,
    lastVersionId: null,
    officeId: defaultOfficeId.value,
    officeCode: defaultOfficeCode.value,
    officeName: defaultOfficeName.value,
    businessType: "",
    code: "",
    creditMode: creditStyleType.SINGLE_LEGAL_CREDIT,
    currencyType: currencyType.ONE_CURRENCY,
    totalAmountCurrencyId: null,
    totalAmountCurrencyCode: "",
    totalAmountCurrencyName: "",
    counterpartyTypeId: null,
    counterpartyTypeCode: "",
    counterpartyTypeName: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    firstReviewId: null,
    firstReviewCode: "",
    counterpartyTopBankId: null,
    counterpartyBankId: null,
    creditGrade: "",
    score: null,
    gradeEffectiveDate: "",
    gradeEndDate: "",
    creditType: ibcCreditType.COMPOSITE_CREDIT,
    totalAmount: null,
    originalId: null,
    originalCode: null,
    unReleaseAmount: null,
    effectiveDate: "",
    endDate: "",
    periodMonth: null,
    periodDay: null,
    operationType: "",
    changeType: "",
    adjustRemark: "",
    lrmPassDate: "",
    lrmCount: "",
    carryForwardId: "",
    lrmFileIds: [],
    fileIds: [],
    remark: "",
    dataVersion: null,
    businessStatus: "",
    detailList: [],
    dataStatus: "",
    inputUserName: "",
    modifyUserName: "",
    inputUserId: null,
    modifyUserId: null,
    inputTime: "",
    modifyTime: "",
    version: null,
    carriedAmount: null,
    creditTerm: creditTermEnum.TWELVE_MONTH
  });

  // 是否进入修改状态，用于控制表单元素是否可编辑
  const isModifyFlag = computed(() => !!formData.id);

  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      Object.assign(formData, res.data);
      if (res.data.carryoverDetailDtoList && res.data.carryoverDetailDtoList.length > 0) {
        res.data.carryoverDetailDtoList.forEach(element => {
          const carrClientData = {
            counterpartyId: element.counterpartyId,
            counterpartyCode: element.counterpartyCode,
            counterpartyName: element.counterpartyName,
            originCreditCode: element.originCreditCode,
            originCreditCategory: element.originCreditCategory,
            originCreditMode: element.originCreditMode,
            originCreditType: element.originCreditType,
            originCurrencyType: element.originCurrencyType,
            originAmountCurrencyId: element.originAmountCurrencyId,
            originAmountCurrencyCode: element.originAmountCurrencyCode,
            originAmountCurrencyName: element.originAmountCurrencyName,
            originCreditSeqNo: element.originCreditSeqNo,
            originCreditVarietyId: element.originCreditVarietyId,
            originCreditVarietyCode: element.originCreditVarietyCode,
            originCreditVarietyName: element.originCreditVarietyName,
            originEffectiveDate: element.originEffectiveDate,
            originEndDate: element.originEndDate,
            originCreditTerm: element.originCreditTerm,
            originCreditVersion: element.originCreditVersion,
            originCarryoverAmount: element.originCarryoverAmount,
            targetCarryoverAmount: element.targetCarryoverAmount,
            targetCreditCode: element.targetCreditCode,
            targetCreditVersion: element.targetCreditVersion,
            targetCreditSeqNo: element.targetCreditSeqNo,
            targetCreditVarietyId: element.targetCreditVarietyId,
            targetCreditVarietyCode: element.targetCreditVarietyCode,
            targetCreditVarietyName: element.targetCreditVarietyName,
            _randomId: random()
          };
          carryoverList.push(carrClientData);
        });
      }
    }
  };

  const linkToList = () => {
    goPage("list");
  };

  const handleSaveSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  const handleSubmitSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  const handleFormValidator = async () => {
    const result = await form.value.form.validate((valid: any) => valid);
    if (!result) {
      return false;
    }
    if (!detailList || detailList.length === 0) {
      FMessageBox.report({ type: "error", message: t("interbankcredit.business.creditAdd.detailListNotNull") });
      return false;
    }
    if (formData.effectiveDate >= formData.endDate) {
      FMessageBox.report({ type: "error", message: t("interbankcredit.business.creditAdd.endDateError") });
      return false;
    }
    // 统计总授信金额
    let totalAmount = 0;
    detailList.forEach(item => {
      totalAmount += item.creditAmount;
    });
    if (!formData.totalAmount || totalAmount > formData.totalAmount) {
      FMessageBox.report({ type: "error", message: t("interbankcredit.business.creditAdd.totalAmountError") });
      return false;
    }

    if (
      formData.carriedAmount !== "" &&
      formData.carriedAmount !== null &&
      formData.carriedAmount > 0 &&
      formData.totalAmount < formData.carriedAmount
    ) {
      FMessageBox.report(t("interbankcredit.business.creditAdd.creditQuotaTip"));
      return false;
    }
    return result;
  };

  const gatherRemoveInfo = () => {
    return formData;
  };

  const handleRemoveSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  // 机构下拉框
  const officeChange = (value: any, row: any) => {
    if (row) {
      formData.officeId = row.officeId;
      formData.officeCode = row.officeCode;
      formData.officeName = row.officeName;
      currencyParams.officeId = row.officeId;
    } else {
      formData.officeId = "";
      formData.officeCode = "";
      formData.officeName = "";
      currencyParams.officeId = null;
    }
    currencyRef.value.initRemoteData();
    counterpartyMagnifierConfirm(null);
    clearDetailTable();
    if (formData.currencyType !== currencyType.MANY_CURRENCY) {
      totalAmountCurrencyChange(null, null);
    }
  };

  const currencyTypeChange = (value: any) => {
    if (value === currencyType.MANY_CURRENCY && formData.totalAmountCurrencyId !== 1) {
      initTotalAmountCurrency();
    }
  };

  const initTotalAmountCurrency = () => {
    httpTool.post(getCurrencyInfo, { id: 1 }).then((res: any) => {
      if (res.success && res.data.length > 0) {
        formData.totalAmountCurrencyId = res.data[0].currencyId;
        formData.totalAmountCurrencyCode = res.data[0].currencyCode;
        formData.totalAmountCurrencyName = res.data[0].currencyName;
      }
    });
  };

  // 币种下拉框
  const totalAmountCurrencyChange = (value: any, row: any) => {
    if (row) {
      formData.totalAmountCurrencyId = row.currencyId;
      formData.totalAmountCurrencyCode = row.currencyCode;
      formData.totalAmountCurrencyName = row.currencyName;
    } else {
      formData.totalAmountCurrencyId = "";
      formData.totalAmountCurrencyCode = "";
      formData.totalAmountCurrencyName = "";
    }
  };
  const creditModeChange = () => {
    counterpartyMagnifierConfirm(null);
  };

  // 币种查询入参
  const currencyParams = reactive({
    officeId: null
  });

  const totalAmountChange = (value: number) => {
    // 统计总授信金额
    let totalAmount = 0;
    detailList.forEach(item => {
      totalAmount += item.creditAmount;
    });
    if (!value || totalAmount > value) {
      FMessageBox.report(t("interbankcredit.business.creditAdd.totalAmountError"));
    }
  };

  // 交易对手类型改变事件
  const counterpartyTypeChange = (value: any, row: any) => {
    if (row) {
      formData.counterpartyTypeId = row.key;
      formData.counterpartyTypeCode = row.code;
      formData.counterpartyTypeName = row.value;
    } else {
      formData.counterpartyTypeId = "";
      formData.counterpartyTypeCode = "";
      formData.counterpartyTypeName = "";
    }
    counterpartyMagnifierConfirm(null);
  };
  // 交易对手放大镜确认事件
  const counterpartyMagnifierConfirm = (row: any) => {
    if (row) {
      formData.counterpartyId = row.id;
      formData.counterpartyCode = row.clientCode;
      formData.counterpartyName = row.clientName;
      if (row.openBankId > 0) {
        formData.counterpartyBankId = row.openBankId;
      } else {
        formData.counterpartyBankId = null;
      }
    } else {
      formData.counterpartyId = null;
      formData.counterpartyCode = "";
      formData.counterpartyName = "";
      formData.counterpartyBankId = null;
    }
  };

  // 信用等级评定放大镜确认事件
  const firstReviewMagnifierConfirm = (row: any) => {
    if (row) {
      formData.firstReviewId = row.id;
      formData.firstReviewCode = row.businessCode;
      formData.creditGrade = row.approvedLevel;
      formData.score = row.initialScore;
      formData.gradeEffectiveDate = row.ratingEffectiveDate;
      formData.gradeEndDate = row.ratingExpirationDate;
    } else {
      formData.firstReviewId = "";
      formData.firstReviewCode = "";
    }
  };

  /**
   * 组装保存参数
   */
  const gatherSaveInfo = () => {
    if (detailList.length > 0) {
      formData.detailList = detailList;
    }
    fileInfos.value.splice(0);
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      formData.fileIds = fileInfos.value.map((item: FileDto) => item.id);
    }
    return formData;
  };

  // 构造审批时参数
  const postApprovalInfo = (params: any) => {
    let isApprovalPass = false;
    let transition = "";
    if (params.ifinanceWorkFlowDto.agreeChoose) {
      isApprovalPass = true;
      transition = params.ifinanceWorkFlowDto.agreeChoose;
    } else {
      transition = params.ifinanceWorkFlowDto.refuseChoose;
    }
    return {
      taskId: params.ifinanceWorkFlowDto.taskId,
      approvalPass: isApprovalPass,
      approvalContent: params.ifinanceWorkFlowDto.idea,
      approveMode: params.ifinanceWorkFlowDto.approveMode,
      transition: transition
    };
  };

  const businessTypeChange = (value: any) => {
    // 清除信用评级信息
    formData.firstReviewCode = "";
    formData.creditGrade = "";
    formData.creditType = "";
    formData.score = null;
    formData.gradeEffectiveDate = "";
    formData.gradeEndDate = "";
    if (value === interbankCreditType.INTERBANK_TO_FINANCE) {
      formData.creditMode = creditStyleType.SINGLE_LEGAL_CREDIT;
    }
    clearDetailTable();
  };

  const creditTypeChange = () => {
    clearDetailTable();
  };

  // 清空授信明细
  const clearDetailTable = () => {
    // 关闭编辑
    detailList.forEach(item => {
      detailTable.value.closeEdit(item._randomId);
    });
    detailList.splice(0);
    editNotState.value = {};
    rowIndex = 0;
    formData.totalAmount = null;
    carryoverList.splice(0);
    notCarryoverList.splice(0);
  };

  //授信明细可编辑表格
  const detailTable = shallowRef();

  const detailList = reactive<IBCDetailInfoDto[]>([]);

  const editNotState = reactive<any>({});
  const generalDetailButtonOption = (scope: any) => {
    return [
      {
        type: "edit",
        buttonText: t("interbankcredit.business.creditAdd.btnModify"),
        isShow: !editNotState[scope.row._randomId],
        emitName: "on-edit",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "close",
        buttonText: t("interbankcredit.business.creditAdd.btnSave"),
        isShow: editNotState[scope.row._randomId],
        emitName: "on-close",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "remove",
        buttonText: t("interbankcredit.business.creditAdd.btnRemove"),
        isShow: true,
        emitName: "on-remove",
        originalProps: {
          type: "danger",
          icon: null
        }
      }
    ];
  };

  let rowIndex = 0;
  // 新增详情
  const addDetailRow = () => {
    detailList.push({
      id: null,
      categoryIds: [],
      infoId: null,
      usageMode: creditUseType.DISPOSABLE,
      controlMode: creditControlType.STRONG,
      creditAmount: null,
      creditBalance: null,
      remark: "",
      creditSeqNo: "",
      categoryNames: "",
      _randomId: random()
    });
    detailTable.value.openEdit(detailList[detailList.length - 1]._randomId);
    editNotState[detailList[detailList.length - 1]._randomId] = true;
    rowIndex++;
  };

  //点击编辑
  const openDetailEdit = (scope: any) => {
    detailTable.value.openEdit(scope.row._randomId);
    editNotState[scope.row._randomId] = true;
    // 关闭其他编辑
    detailList.forEach(item => {
      if (item._randomId !== scope.row._randomId) {
        detailTable.value.closeEdit(item._randomId);
        editNotState[item._randomId] = false;
      }
    });
  };
  // 关闭详情编辑
  const closeDetailEdit = (scope: any) => {
    if (scope.row.categoryIds === null || scope.row.categoryIds === undefined || scope.row.categoryIds.length <= 0) {
      FMessageBox.report(t("interbankcredit.business.creditAdd.categoryNotNull"));
      return false;
    }
    if (scope.row.controlMode === null || scope.row.controlMode === undefined || scope.row.controlMode === "") {
      FMessageBox.report(t("interbankcredit.business.creditAdd.controlModeNotNull"));
      return false;
    }
    if (scope.row.usageMode === null || scope.row.usageMode === undefined || scope.row.usageMode === "") {
      FMessageBox.report(t("interbankcredit.business.creditAdd.usageModeNotNull"));
      return false;
    }
    if (scope.row.creditAmount === null || scope.row.creditAmount === undefined) {
      FMessageBox.report(t("interbankcredit.business.creditAdd.creditAmountNotNull"));
      return false;
    }
    if (scope.row.creditAmount === 0) {
      FMessageBox.report(t("interbankcredit.business.creditAdd.creditAmountNotZero"));
      return false;
    }
    // 统计总授信金额
    let totalAmount = 0;
    detailList.forEach(item => {
      totalAmount += item.creditAmount;
    });
    formData.totalAmount = totalAmount;
    // if (!formData.totalAmount || totalAmount > formData.totalAmount) {
    //   formData.totalAmount = totalAmount;
    // }
    if (!scope.row.creditSeqNo) {
      scope.row.creditSeqNo = rowIndex.toString().padStart(2, "0");
    }
    detailTable.value.closeEdit(scope.row._randomId);
    editNotState[scope.row._randomId] = false;
  };

  // 删除详情
  const deleteDetail = (scope: any) => {
    detailTable.value.closeEdit(scope.row._randomId);
    detailList.splice(scope.$index, 1);
    editNotState[scope.row._randomId] = false;
    // 重新为 creditSeqNo 赋值
    detailList.forEach((item, index) => {
      item.creditSeqNo = (index + 1).toString().padStart(2, "0");
    });
    rowIndex--;
  };

  const categoryChange = (values: number[], rows: any[], index: number) => {
    detailList[index].categoryDtos = [];
    if (values && values.length > 0) {
      rows.forEach(item => {
        const category = {
          categoryId: item.id,
          categoryCode: item.code,
          categoryName: item.name
        };
        detailList[index].categoryDtos.push(category);
        detailList[index].categoryNames = detailList[index].categoryDtos.map(dto => dto.categoryName).join(",");
      });
    }
  };

  const unitaryCategoryChange = (value: any, row: any, index: number) => {
    detailList[index].categoryDtos = [];
    if (row) {
      const category = {
        categoryId: row.id,
        categoryCode: row.code,
        categoryName: row.name
      };
      detailList[index].categoryDtos.push(category);
      detailList[index].categoryNames = row.name;
    }
  };

  const formatterCategory = (row: any) => {
    if (row.categoryDtos) {
      return row.categoryDtos.map((item: any) => item.categoryName).join(",");
    }
    return "";
  };

  const beforeAddDetail = (row, data) => {
    if (formData.creditType === ibcCreditType.TEMPORARY_CREDIT && data.length >= 1) {
      FMessageBox.report(t("interbankcredit.business.creditAdd.detailExtraLong"));
      return false;
    }
    return true;
  };

  const initEffectiveDate = callback => {
    httpTool.post(openDateUrl).then((res: any) => {
      formData.effectiveDate = res.data.onlineDate;
      callback();
    });
  };

  // 获取明细信息
  const loadFormData = (id: number) => {
    return httpTool.post(getOne, { id: id }).then((res: any) => {
      if (res.success) {
        Object.assign(formData, res.data);
        rowIndex = formData.detailList.length;
        nextTick(() => {
          upload.value?.init(res.data?.fileIds || []);
        });
        if (formData.detailList) {
          formData.detailList.forEach(item => {
            const categoryIds = item.categoryDtos.map((item: any) => item.categoryId);
            let categoryId;
            if (formData.creditType === ibcCreditType.TEMPORARY_CREDIT) {
              categoryId = categoryIds[0];
            } else {
              categoryId = categoryIds;
            }
            const detail = {
              id: item.id,
              creditSeqNo: item.creditSeqNo,
              categoryDtos: item.categoryDtos,
              categoryIds: categoryId,
              infoId: item.infoId,
              usageMode: item.usageMode,
              controlMode: item.controlMode,
              creditAmount: item.creditAmount,
              creditBalance: item.creditBalance,
              remark: item.remark,
              version: item.version,
              _randomId: random()
            };
            editNotState[detail._randomId] = false;
            detailList.push(detail);
          });
        }
        if (res.data.carryoverDetailDtoList && res.data.carryoverDetailDtoList.length > 0) {
          res.data.carryoverDetailDtoList.forEach(element => {
            const carrClientData = {
              counterpartyId: element.counterpartyId,
              counterpartyCode: element.counterpartyCode,
              counterpartyName: element.counterpartyName,
              originCreditCode: element.originCreditCode,
              originCreditCategory: element.originCreditCategory,
              originCreditMode: element.originCreditMode,
              originCreditType: element.originCreditType,
              originCurrencyType: element.originCurrencyType,
              originAmountCurrencyId: element.originAmountCurrencyId,
              originAmountCurrencyCode: element.originAmountCurrencyCode,
              originAmountCurrencyName: element.originAmountCurrencyName,
              originCreditSeqNo: element.originCreditSeqNo,
              originCreditVarietyId: element.originCreditVarietyId,
              originCreditVarietyCode: element.originCreditVarietyCode,
              originCreditVarietyName: element.originCreditVarietyName,
              originEffectiveDate: element.originEffectiveDate,
              originEndDate: element.originEndDate,
              originCreditTerm: element.originCreditTerm,
              originCreditVersion: element.originCreditVersion,
              originCarryoverAmount: element.originCarryoverAmount,
              targetCarryoverAmount: element.targetCarryoverAmount,
              targetCreditCode: element.targetCreditCode,
              targetCreditVersion: element.targetCreditVersion,
              targetCreditSeqNo: element.targetCreditSeqNo,
              targetCreditVarietyId: element.targetCreditVarietyId,
              targetCreditVarietyCode: element.targetCreditVarietyCode,
              targetCreditVarietyName: element.targetCreditVarietyName,
              usageId: element.usageId,
              _randomId: random()
            };
            carryoverList.push(carrClientData);
          });
        }
      }
    });
  };

  const { notCarryoverDetailColumns, carryoverDetailColumns } = useCarryoverColumns();

  //公共处理信息
  const state = reactive({
    //结转数据
    carryoverInfo: {
      showDialog: false,
      notCarryoverDetailColumns: notCarryoverDetailColumns,
      carryoverDetailColumns: carryoverDetailColumns
    },
    refSeqNoElement: null,
    nextSeqNo: 0
  });

  const {
    //明细结转列表
    carryoverGrid,
    carryoverList,
    generalButtonOption,
    deleteCarryoverButton,
    //授信明细未结转
    notCarryoverGrid,
    notCarryoverList,
    generalNotCarryoverButtonOption,
    openCarryoverEditButton,
    carryoverButton,
    closeCarryoverEditButton,
    //明细操作
    openCarryover,
    //结转校验
    validateDeleteDetail,
    creditSeqNoConfirm,
    creditVarietyData
  } = useCarryover(formData, state, detailList);

  return {
    form,
    formData,
    linkToList,
    gatherSaveInfo,
    handleSaveSuccess,
    handleFormValidator,
    loadFormData,
    handleRemoveSuccess,
    gatherRemoveInfo,
    officeChange,
    interbankCreditType,
    ibcCreditType,
    detailList,
    fileInfos,
    currencyType,
    currencyTypeChange,
    totalAmountCurrencyChange,
    counterpartyTypeChange,
    counterpartyMagnifierConfirm,
    firstReviewMagnifierConfirm,
    upload,
    detailTable,
    creditControlType,
    creditUseType,
    generalDetailButtonOption,
    deleteDetail,
    systemCodeEnum,
    postApprovalInfo,
    businessTypeChange,
    creditStyleType,
    addDetailRow,
    openDetailEdit,
    closeDetailEdit,
    categoryChange,
    formatterCategory,
    beforeAddDetail,
    creditTypeChange,
    initEffectiveDate,
    handleSubmitSuccess,
    ibcBusinessType,
    initTotalAmountCurrency,
    yesOrNo,
    creditModeChange,
    totalAmountChange,
    currencyParams,
    currencyRef,
    creditTermEnum,
    unitaryCategoryChange,
    isModifyFlag,
    saveSuccess,
    state,
    //明细结转列表
    carryoverGrid,
    carryoverList,
    generalButtonOption,
    deleteCarryoverButton,
    //授信明细未结转
    notCarryoverGrid,
    notCarryoverList,
    generalNotCarryoverButtonOption,
    openCarryoverEditButton,
    carryoverButton,
    closeCarryoverEditButton,
    //明细操作
    openCarryover,
    //结转校验
    validateDeleteDetail,
    creditSeqNoConfirm,
    creditVarietyData
  };
};

export default useForm;
