import { computed, reactive, ref, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { FMessageBox } from "@dtg/frontend-plus";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { random } from "@/utils/uuid";
import { getCurrencyInfo, openDateUrl } from "@/views/loan/paynotice/url";
import { getOne } from "../url";
import { goPage } from "./usePage";
import { useCarryover, useCarryoverColumns } from "./useCarryover";
import type { FileDto, IBCDetailInfoDto, IBCInfoDto } from "../types";

export const useForm = () => {
  const { t } = useI18n();

  const form = shallowRef();

  // 创建upload 组件引用
  const upload = shallowRef();

  //上传附件返回的数组信息
  const fileInfos = ref<FileDto[]>([]);

  const interbankCreditType = useConst("interbankcredit.InterbankCreditType");

  const ibcCreditType = useConst("interbankcredit.IBCCreditType");

  const currencyType = useConst("credit.CurrencyType");

  const creditControlType = useConst("credit.CreditControlType");

  const creditUseType = useConst("credit.CreditUseType");

  const systemCodeEnum = useConst("interbankcredit.SystemCode");

  const ibcOperationSubType = useConst("interbankcredit.IBCOperationSubType");

  const creditStyleType = useConst("interbankcredit.IBCCreditStyleType");

  const ibcChangeType = useConst("interbankcredit.IBCChangeType");

  const ibcBusinessType = useConst("interbankcredit.IbcBusinessType");

  /** 授信期限 */
  const creditTermEnum = useConst("interbankcredit.IBCCreditTerm");

  // 同业授信数据对象
  const formData = reactive<IBCInfoDto>({
    id: null,
    lastVersionId: null,
    officeId: null,
    officeCode: "",
    officeName: "",
    businessType: "",
    code: "",
    creditMode: "",
    currencyType: "",
    totalAmountCurrencyId: null,
    totalAmountCurrencyCode: "",
    totalAmountCurrencyName: "",
    counterpartyTypeId: null,
    counterpartyTypeCode: "",
    counterpartyTypeName: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    firstReviewId: null,
    firstReviewCode: "",
    creditGrade: "",
    score: null,
    gradeEffectiveDate: "",
    gradeEndDate: "",
    creditType: "",
    totalAmount: null,
    originalId: null,
    originalCode: "",
    unReleaseAmount: null,
    effectiveDate: "",
    endDate: "",
    periodMonth: null,
    periodDay: null,
    operationType: "",
    changeType: ibcChangeType.AMOUNT_ADJUSTMENT,
    adjustRemark: "",
    lrmPassDate: "",
    lrmCount: "",
    carryForwardId: "",
    lrmFileIds: [],
    fileIds: [],
    remark: "",
    dataVersion: null,
    businessStatus: "",
    detailList: [],
    dataStatus: "",
    inputUserName: "",
    modifyUserName: "",
    inputUserId: null,
    modifyUserId: null,
    inputTime: "",
    modifyTime: "",
    version: null,
    carriedAmount: null,
    creditTerm: ""
  });

  const initFormData = reactive<IBCInfoDto>({
    id: null,
    lastVersionId: null,
    officeId: null,
    officeCode: "",
    officeName: "",
    businessType: "",
    code: "",
    creditMode: "",
    currencyType: "",
    totalAmountCurrencyId: null,
    totalAmountCurrencyCode: "",
    totalAmountCurrencyName: "",
    counterpartyTypeId: null,
    counterpartyTypeCode: "",
    counterpartyTypeName: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    firstReviewId: null,
    firstReviewCode: "",
    creditGrade: "",
    score: null,
    gradeEffectiveDate: "",
    gradeEndDate: "",
    creditType: "",
    totalAmount: null,
    originalId: null,
    originalCode: "",
    unReleaseAmount: null,
    effectiveDate: "",
    endDate: "",
    periodMonth: null,
    periodDay: null,
    operationType: "",
    changeType: ibcChangeType.AMOUNT_ADJUSTMENT,
    adjustRemark: "",
    lrmPassDate: "",
    lrmCount: "",
    carryForwardId: "",
    lrmFileIds: [],
    fileIds: [],
    remark: "",
    dataVersion: null,
    businessStatus: "",
    detailList: [],
    dataStatus: "",
    inputUserName: "",
    modifyUserName: "",
    inputUserId: null,
    modifyUserId: null,
    inputTime: "",
    modifyTime: "",
    version: null
  });

  const linkToList = () => {
    goPage("list");
  };

  const handleSaveSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  const handleSubmitSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  const handleFormValidator = async () => {
    const result = await form.value.form.validate((valid: any) => valid);
    if (!result) {
      return false;
    }
    if (!detailList || detailList.length === 0) {
      FMessageBox.report({ type: "error", message: t("interbankcredit.business.creditChange.detailListNotNull") });
      return false;
    }
    if (formData.effectiveDate >= formData.endDate) {
      FMessageBox.report({ type: "error", message: t("interbankcredit.business.creditAdd.endDateError") });
      return false;
    }
    // 统计总授信金额
    let totalAmount = 0;
    detailList.forEach(item => {
      totalAmount += item.creditAmount;
    });
    if (!formData.totalAmount || totalAmount > formData.totalAmount) {
      FMessageBox.report({ type: "error", message: t("interbankcredit.business.creditChange.totalAmountError") });
      return false;
    }
    if (
      formData.carriedAmount !== "" &&
      formData.carriedAmount !== null &&
      formData.carriedAmount > 0 &&
      formData.totalAmount < formData.carriedAmount
    ) {
      FMessageBox.report(t("interbankcredit.business.creditChange.creditQuotaTip"));
      return false;
    }
    return result;
  };

  const gatherRemoveInfo = () => {
    return formData;
  };

  const handleRemoveSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  // 机构下拉框
  const officeChange = (value: any, row: any) => {
    if (row) {
      formData.officeId = row.officeId;
      formData.officeCode = row.officeCode;
      formData.officeName = row.officeName;
    } else {
      formData.officeId = "";
      formData.officeCode = "";
      formData.officeName = "";
    }
    formData.value = { ...initFormData };
    fileInfos.value.splice(0);
    clearDetailTable();
  };

  const currencyTypeChange = (value: any) => {
    if (value === currencyType.MANY_CURRENCY && formData.totalAmountCurrencyId !== 1) {
      httpTool.post(getCurrencyInfo, { id: 1 }).then((res: any) => {
        if (res.success && res.data.length > 0) {
          formData.totalAmountCurrencyId = res.data[0].currencyId;
          formData.totalAmountCurrencyCode = res.data[0].currencyCode;
          formData.totalAmountCurrencyName = res.data[0].currencyName;
        }
      });
    }
  };

  // 币种下拉框
  const totalAmountCurrencyChange = (value: any, row: any) => {
    if (row) {
      formData.totalAmountCurrencyId = row.currencyId;
      formData.totalAmountCurrencyCode = row.currencyCode;
      formData.totalAmountCurrencyName = row.currencyName;
    } else {
      formData.totalAmountCurrencyId = "";
      formData.totalAmountCurrencyCode = "";
      formData.totalAmountCurrencyName = "";
    }
  };

  // 交易对手类型改变事件
  const counterpartyTypeChange = (value: any, row: any) => {
    if (row) {
      formData.counterpartyTypeId = row.key;
      formData.counterpartyTypeCode = row.code;
      formData.counterpartyTypeName = row.value;
    } else {
      formData.counterpartyTypeId = "";
      formData.counterpartyTypeCode = "";
      formData.counterpartyTypeName = "";
    }
    counterpartyMagnifierConfirm(null);
  };
  // 交易对手放大镜确认事件
  const counterpartyMagnifierConfirm = (row: any) => {
    if (row) {
      formData.counterpartyId = row.clientId;
      formData.counterpartyCode = row.clientCode;
      formData.counterpartyName = row.clientName;
    } else {
      formData.counterpartyId = "";
      formData.counterpartyCode = "";
      formData.counterpartyName = "";
    }
  };

  // 信用等级评定放大镜确认事件
  const firstReviewMagnifierConfirm = (row: any) => {
    if (row) {
      formData.firstReviewId = row.id;
      formData.firstReviewCode = row.businessCode;
      formData.creditGrade = row.approvedLevel;
      formData.score = row.initialScore;
      formData.gradeEffectiveDate = row.ratingEffectiveDate;
      formData.gradeEndDate = row.ratingExpirationDate;
    } else {
      formData.firstReviewId = "";
      formData.firstReviewCode = "";
    }
  };

  /**
   * 组装保存参数
   */
  const gatherSaveInfo = () => {
    if (detailList.length > 0) {
      formData.detailList = detailList;
    }
    fileInfos.value.splice(0);
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      formData.fileIds = fileInfos.value.map((item: FileDto) => item.id);
    }
    return formData;
  };

  // 构造审批时参数
  const postApprovalInfo = (params: any) => {
    let isApprovalPass = false;
    let transition = "";
    if (params.ifinanceWorkFlowDto.agreeChoose) {
      isApprovalPass = true;
      transition = params.ifinanceWorkFlowDto.agreeChoose;
    } else {
      transition = params.ifinanceWorkFlowDto.refuseChoose;
    }
    return {
      taskId: params.ifinanceWorkFlowDto.taskId,
      approvalPass: isApprovalPass,
      approvalContent: params.ifinanceWorkFlowDto.idea,
      approveMode: params.ifinanceWorkFlowDto.approveMode,
      transition: transition
    };
  };

  const businessTypeChange = (value: any) => {
    // 清除信用评级信息
    formData.firstReviewCode = "";
    formData.creditGrade = "";
    formData.creditType = "";
    formData.score = null;
    formData.gradeEffectiveDate = "";
    formData.gradeEndDate = "";
    if (value === interbankCreditType.INTERBANK_TO_FINANCE) {
      formData.creditMode = creditStyleType.SINGLE_LEGAL_CREDIT;
    }
  };

  const creditTypeChange = () => {
    // 关闭编辑
    detailList.forEach(item => {
      detailTable.value.closeEdit(item._randomId);
    });
    detailList.splice(0);
    editNotState.value = {};
  };

  //授信明细可编辑表格
  const detailTable = shallowRef();

  const detailList = reactive<IBCDetailInfoDto[]>([]);

  const oldDetailList = reactive<IBCDetailInfoDto[]>([]);

  const editNotState = reactive<any>({});
  const generalDetailButtonOption = (scope: any) => {
    return [
      {
        type: "edit",
        buttonText: t("interbankcredit.business.creditChange.btnModify"),
        isShow: !editNotState[scope.row._randomId],
        emitName: "on-edit",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "close",
        buttonText: t("interbankcredit.business.creditChange.btnSave"),
        isShow: editNotState[scope.row._randomId],
        emitName: "on-close",
        originalProps: {
          type: "primary",
          icon: null
        }
      },
      {
        type: "remove",
        buttonText: t("interbankcredit.business.creditChange.btnRemove"),
        isShow: true,
        emitName: "on-remove",
        originalProps: {
          type: "danger",
          icon: null
        }
      }
    ];
  };

  let rowIndex = 0;

  // 新增详情
  const addDetailRow = () => {
    if (formData.creditType === ibcCreditType.TEMPORARY_CREDIT) {
      detailList.push({
        id: null,
        categoryIds: [],
        infoId: null,
        usageMode: "",
        controlMode: "",
        creditAmount: null,
        creditBalance: null,
        remark: "",
        creditSeqNo: "",
        _randomId: random()
      });
    } else {
      detailList.push({
        id: null,
        categoryIds: "",
        infoId: null,
        usageMode: "",
        controlMode: "",
        creditAmount: null,
        creditBalance: null,
        remark: "",
        creditSeqNo: "",
        _randomId: random()
      });
    }

    detailTable.value.openEdit(detailList[detailList.length - 1]._randomId);
    editNotState[detailList[detailList.length - 1]._randomId] = true;
    rowIndex++;
  };

  //点击编辑
  const openDetailEdit = (scope: any) => {
    // 当授信类型为“临时授信”，变更类型为“增加授信品种时”，原有的授信详情不可编辑
    if (
      formData.creditType === ibcCreditType.TEMPORARY_CREDIT &&
      formData.changeType === ibcChangeType.ADD_CATEGORY &&
      scope.row.id
    ) {
      FMessageBox.report(t("interbankcredit.business.creditChange.specialDetailNotModify"));
      return false;
    }
    if (formData.creditType === ibcCreditType.TEMPORARY_CREDIT) {
      if (scope.row.categoryIds[0] !== undefined && scope.row.categoryIds[0] !== null) {
        scope.row.categoryIds = scope.row.categoryIds[0];
      }
    }
    detailTable.value.openEdit(scope.row._randomId);
    editNotState[scope.row._randomId] = true;
    // 关闭其他编辑
    detailList.forEach(item => {
      if (item._randomId !== scope.row._randomId) {
        detailTable.value.closeEdit(item._randomId);
        editNotState[item._randomId] = false;
      }
    });
  };
  // 关闭详情编辑
  const closeDetailEdit = (scope: any) => {
    if (scope.row.categoryIds === null || scope.row.categoryIds === undefined || scope.row.categoryIds.length <= 0) {
      FMessageBox.report(t("interbankcredit.business.creditChange.categoryNotNull"));
      return false;
    }
    if (scope.row.controlMode === null || scope.row.controlMode === undefined || scope.row.controlMode === "") {
      FMessageBox.report(t("interbankcredit.business.creditChange.controlModeNotNull"));
      return false;
    }
    if (scope.row.usageMode === null || scope.row.usageMode === undefined || scope.row.usageMode === "") {
      FMessageBox.report(t("interbankcredit.business.creditChange.usageModeNotNull"));
      return false;
    }
    if (scope.row.creditAmount === null || scope.row.creditAmount === undefined) {
      FMessageBox.report(t("interbankcredit.business.creditChange.creditAmountNotNull"));
      return false;
    }
    if (scope.row.creditAmount === 0) {
      FMessageBox.report(t("interbankcredit.business.creditChange.creditAmountNotZero"));
      return false;
    }
    // 统计总授信金额
    let totalAmount = 0;
    detailList.forEach(item => {
      totalAmount += item.creditAmount;
    });
    if (!formData.totalAmount || totalAmount > formData.totalAmount) {
      formData.totalAmount = totalAmount;
    }
    if (!scope.row.creditSeqNo) {
      scope.row.creditSeqNo = rowIndex.toString().padStart(2, "0");
    }
    detailTable.value.closeEdit(scope.row._randomId);
    editNotState[scope.row._randomId] = false;
  };

  // 删除详情
  const deleteDetail = (scope: any) => {
    detailTable.value.closeEdit(scope.row._randomId);
    detailList.splice(scope.$index, 1);
    editNotState[scope.row._randomId] = false;
    // 重新为 creditSeqNo 赋值
    detailList.forEach((item, index) => {
      item.creditSeqNo = (index + 1).toString().padStart(2, "0");
    });
    rowIndex--;
  };

  const categoryChange = (values: number[], rows: any[], index: number) => {
    detailList[index].categoryDtos = [];
    if (values && values.length > 0) {
      rows.forEach(item => {
        const category = {
          categoryId: item.id,
          categoryCode: item.code,
          categoryName: item.name
        };
        detailList[index].categoryDtos.push(category);
        detailList[index].categoryNames = detailList[index].categoryDtos.map(dto => dto.categoryName).join(",");
      });
    }
  };

  const formatterCategory = (row: any) => {
    if (row.categoryDtos) {
      return row.categoryDtos.map((item: any) => item.categoryName).join(",");
    }
    return "";
  };

  const beforeAddDetail = (row, data) => {
    if (formData.creditType === ibcCreditType.TEMPORARY_CREDIT && data.length >= 1) {
      FMessageBox.report(t("interbankcredit.business.creditChange.detailExtraLong"));
      return false;
    }
    if (formData.changeType !== ibcChangeType.ADD_CATEGORY) {
      FMessageBox.report(t("interbankcredit.business.creditChange.changeTypeNotAddCategory"));
      return false;
    }
    return true;
  };

  const initEffectiveDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      formData.effectiveDate = res.data.onlineDate;
    });
  };

  // 获取明细信息
  const loadFormData = async (id: number) => {
    const res = await httpTool.post(getOne, { id: id });
    if (res.success) {
      Object.assign(formData, res.data);
      rowIndex = formData.detailList.length;
      if (formData.detailList) {
        formData.detailList.forEach(item => {
          const categoryIds = item.categoryDtos.map((item: any) => item.categoryId);
          let categoryId;
          if (formData.creditType === ibcCreditType.TEMPORARY_CREDIT) {
            categoryId = categoryIds[0];
          } else {
            categoryId = categoryIds;
          }
          const detail = {
            id: item.id,
            oldId: item.id,
            creditSeqNo: item.creditSeqNo,
            categoryDtos: item.categoryDtos,
            categoryIds: categoryId,
            infoId: item.infoId,
            usageMode: item.usageMode,
            controlMode: item.controlMode,
            creditAmount: item.creditAmount,
            creditBalance: item.creditBalance,
            unReleaseAmount: item.unReleaseAmount,
            remark: item.remark,
            version: item.version,
            _randomId: random()
          };
          editNotState[detail._randomId] = false;
          detailList.push(detail);
        });
        Object.assign(oldDetailList, detailList);
      }
      if (res.data.carryoverDetailDtoList && res.data.carryoverDetailDtoList.length > 0) {
        res.data.carryoverDetailDtoList.forEach(element => {
          const carrClientData = {
            counterpartyId: element.counterpartyId,
            counterpartyCode: element.counterpartyCode,
            counterpartyName: element.counterpartyName,
            originCreditCode: element.originCreditCode,
            originCreditCategory: element.originCreditCategory,
            originCreditMode: element.originCreditMode,
            originCreditType: element.originCreditType,
            originCurrencyType: element.originCurrencyType,
            originAmountCurrencyId: element.originAmountCurrencyId,
            originAmountCurrencyCode: element.originAmountCurrencyCode,
            originAmountCurrencyName: element.originAmountCurrencyName,
            originCreditSeqNo: element.originCreditSeqNo,
            originCreditVarietyId: element.originCreditVarietyId,
            originCreditVarietyCode: element.originCreditVarietyCode,
            originCreditVarietyName: element.originCreditVarietyName,
            originEffectiveDate: element.originEffectiveDate,
            originEndDate: element.originEndDate,
            originCreditTerm: element.originCreditTerm,
            originCreditVersion: element.originCreditVersion,
            originCarryoverAmount: element.originCarryoverAmount,
            targetCarryoverAmount: element.targetCarryoverAmount,
            targetCreditCode: element.targetCreditCode,
            targetCreditVersion: element.targetCreditVersion,
            targetCreditSeqNo: element.targetCreditSeqNo,
            targetCreditVarietyId: element.targetCreditVarietyId,
            targetCreditVarietyCode: element.targetCreditVarietyCode,
            targetCreditVarietyName: element.targetCreditVarietyName,
            usageId: element.usageId,
            _randomId: random()
          };
          carryoverList.push(carrClientData);
        });
      }
    }
  };

  // 变更类型改变事件
  const changeTypeChange = () => {
    if (detailList.length > 0) {
      FMessageBox.report(t("interbankcredit.business.creditChange.detailChengMsg"));
      Object.assign(detailList, oldDetailList);
      // 关闭其他编辑
      detailList.forEach(item => {
        detailTable.value.closeEdit(item._randomId);
        editNotState[item._randomId] = false;
      });
    }
  };

  // 同业授信编号确认事件
  const codeConfirm = (row: IBCInfoDto) => {
    if (row) {
      getDetail(row.id, "add");
    }
  };

  // 获取同业授信明细
  const getDetail = (id: number, page: string) => {
    return httpTool.post(getOne, { id: id }).then((res: any) => {
      if (res.success) {
        Object.assign(formData, res.data);
        if (page === "add") {
          formData.lastVersionId = id;
          formData.id = null;
          formData.version = null;
          formData.inputUserId = null;
          formData.inputUserName = null;
          formData.inputTime = null;
          formData.changeType = ibcChangeType.AMOUNT_ADJUSTMENT;
        }
        if (formData.detailList) {
          rowIndex = formData.detailList.length;
          formData.detailList.forEach(item => {
            const categoryIds = item.categoryDtos.map((item: any) => item.categoryId);
            item.categoryDtos.forEach(citem => {
              citem.id = page === "add" ? null : citem.id;
              citem.version = page === "add" ? null : citem.version;
            });
            const detail = {
              id: page === "add" ? null : item.id,
              oldId: item.id,
              creditSeqNo: item.creditSeqNo,
              categoryDtos: item.categoryDtos,
              categoryIds: categoryIds,
              infoId: item.infoId,
              usageMode: item.usageMode,
              controlMode: item.controlMode,
              creditAmount: item.creditAmount,
              creditBalance: item.creditBalance,
              unReleaseAmount: item.unReleaseAmount,
              remark: item.remark,
              version: page === "add" ? null : item.version,
              _randomId: random()
            };
            editNotState[detail._randomId] = false;
            detailList.push(detail);
          });
          Object.assign(oldDetailList, detailList);
        }
        if (res.data.carryoverDetailDtoList && res.data.carryoverDetailDtoList.length > 0) {
          res.data.carryoverDetailDtoList.forEach(element => {
            const carrClientData = {
              counterpartyId: element.counterpartyId,
              counterpartyCode: element.counterpartyCode,
              counterpartyName: element.counterpartyName,
              originCreditCode: element.originCreditCode,
              originCreditCategory: element.originCreditCategory,
              originCreditMode: element.originCreditMode,
              originCreditType: element.originCreditType,
              originCurrencyType: element.originCurrencyType,
              originAmountCurrencyId: element.originAmountCurrencyId,
              originAmountCurrencyCode: element.originAmountCurrencyCode,
              originAmountCurrencyName: element.originAmountCurrencyName,
              originCreditSeqNo: element.originCreditSeqNo,
              originCreditVarietyId: element.originCreditVarietyId,
              originCreditVarietyCode: element.originCreditVarietyCode,
              originCreditVarietyName: element.originCreditVarietyName,
              originEffectiveDate: element.originEffectiveDate,
              originEndDate: element.originEndDate,
              originCreditTerm: element.originCreditTerm,
              originCreditVersion: element.originCreditVersion,
              originCarryoverAmount: element.originCarryoverAmount,
              targetCarryoverAmount: element.targetCarryoverAmount,
              targetCreditCode: element.targetCreditCode,
              targetCreditVersion: element.targetCreditVersion,
              targetCreditSeqNo: element.targetCreditSeqNo,
              targetCreditVarietyId: element.targetCreditVarietyId,
              targetCreditVarietyCode: element.targetCreditVarietyCode,
              targetCreditVarietyName: element.targetCreditVarietyName,
              _randomId: random()
            };
            detailCarryoverList.push(carrClientData);
          });
        }
      }
    });
  };

  const creditAmountChange = (value: number, index: number) => {
    detailList[index].creditBalance = value;
  };

  const totalAmountChange = (value: number) => {
    // 统计总授信金额
    let totalAmount = 0;
    detailList.forEach(item => {
      totalAmount += item.creditAmount;
    });
    if (!value || totalAmount > value) {
      FMessageBox.report(t("interbankcredit.business.creditChange.totalAmountError"));
    }
  };

  // 清空授信明细
  const clearDetailTable = () => {
    // 关闭编辑
    detailList.forEach(item => {
      detailTable.value.closeEdit(item._randomId);
    });
    detailList.splice(0);
    editNotState.value = {};
    rowIndex = 0;
  };

  // 是否进入修改状态，用于控制表单元素是否可编辑
  const isModifyFlag = computed(() => !!formData.id);
  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      Object.assign(formData, res.data);
      if (res.data.carryoverDetailDtoList && res.data.carryoverDetailDtoList.length > 0) {
        res.data.carryoverDetailDtoList.forEach(element => {
          const carrClientData = {
            counterpartyId: element.counterpartyId,
            counterpartyCode: element.counterpartyCode,
            counterpartyName: element.counterpartyName,
            originCreditCode: element.originCreditCode,
            originCreditCategory: element.originCreditCategory,
            originCreditMode: element.originCreditMode,
            originCreditType: element.originCreditType,
            originCurrencyType: element.originCurrencyType,
            originAmountCurrencyId: element.originAmountCurrencyId,
            originAmountCurrencyCode: element.originAmountCurrencyCode,
            originAmountCurrencyName: element.originAmountCurrencyName,
            originCreditSeqNo: element.originCreditSeqNo,
            originCreditVarietyId: element.originCreditVarietyId,
            originCreditVarietyCode: element.originCreditVarietyCode,
            originCreditVarietyName: element.originCreditVarietyName,
            originEffectiveDate: element.originEffectiveDate,
            originEndDate: element.originEndDate,
            originCreditTerm: element.originCreditTerm,
            originCreditVersion: element.originCreditVersion,
            originCarryoverAmount: element.originCarryoverAmount,
            targetCarryoverAmount: element.targetCarryoverAmount,
            targetCreditCode: element.targetCreditCode,
            targetCreditVersion: element.targetCreditVersion,
            targetCreditSeqNo: element.targetCreditSeqNo,
            targetCreditVarietyId: element.targetCreditVarietyId,
            targetCreditVarietyCode: element.targetCreditVarietyCode,
            targetCreditVarietyName: element.targetCreditVarietyName,
            _randomId: random()
          };
          carryoverList.push(carrClientData);
        });
      }
    }
  };

  const unitaryCategoryChange = (value: any, row: any, index: number) => {
    detailList[index].categoryDtos = [];
    if (row) {
      const category = {
        categoryId: row.id,
        categoryCode: row.code,
        categoryName: row.name
      };
      detailList[index].categoryDtos.push(category);
      detailList[index].categoryNames = row.name;
    }
  };

  const { notCarryoverDetailColumns, carryoverDetailColumns } = useCarryoverColumns();

  //公共处理信息
  const state = reactive({
    //结转数据
    carryoverInfo: {
      showDialog: false,
      notCarryoverDetailColumns: notCarryoverDetailColumns,
      carryoverDetailColumns: carryoverDetailColumns
    },
    refSeqNoElement: null,
    nextSeqNo: 0
  });

  const {
    //明细结转列表
    carryoverGrid,
    carryoverList,
    generalButtonOption,
    deleteCarryoverButton,
    //授信明细未结转
    notCarryoverGrid,
    notCarryoverList,
    generalNotCarryoverButtonOption,
    openCarryoverEditButton,
    carryoverButton,
    closeCarryoverEditButton,
    //明细操作
    openCarryover,
    //结转校验
    validateDeleteDetail,
    detailCarryoverList,
    creditSeqNoConfirm,
    creditVarietyData
  } = useCarryover(formData, state, detailList);

  return {
    form,
    formData,
    linkToList,
    gatherSaveInfo,
    handleSaveSuccess,
    handleFormValidator,
    loadFormData,
    handleRemoveSuccess,
    gatherRemoveInfo,
    officeChange,
    interbankCreditType,
    ibcCreditType,
    detailList,
    fileInfos,
    currencyType,
    currencyTypeChange,
    totalAmountCurrencyChange,
    counterpartyTypeChange,
    counterpartyMagnifierConfirm,
    firstReviewMagnifierConfirm,
    upload,
    detailTable,
    creditControlType,
    creditUseType,
    generalDetailButtonOption,
    deleteDetail,
    systemCodeEnum,
    ibcOperationSubType,
    postApprovalInfo,
    businessTypeChange,
    creditStyleType,
    addDetailRow,
    openDetailEdit,
    closeDetailEdit,
    categoryChange,
    formatterCategory,
    beforeAddDetail,
    creditTypeChange,
    initEffectiveDate,
    handleSubmitSuccess,
    ibcChangeType,
    changeTypeChange,
    codeConfirm,
    ibcBusinessType,
    creditAmountChange,
    totalAmountChange,
    creditTermEnum,
    isModifyFlag,
    saveSuccess,
    unitaryCategoryChange,
    state,
    //明细结转列表
    carryoverGrid,
    carryoverList,
    generalButtonOption,
    deleteCarryoverButton,
    //授信明细未结转
    notCarryoverGrid,
    notCarryoverList,
    generalNotCarryoverButtonOption,
    openCarryoverEditButton,
    carryoverButton,
    closeCarryoverEditButton,
    //明细操作
    openCarryover,
    //结转校验
    validateDeleteDetail,
    creditSeqNoConfirm,
    creditVarietyData
  };
};

export default useForm;
