<template>
  <f-query-scene :title="t('interbankdeposit.business.handle.agreedefault.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="interbankdeposit-business-handle-agreedefault-query-001"
        table-comp-id="interbankdeposit-business-handle-agreedefault-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :form-data="queryFrom"
        show-header
        auto-reset
        auto-init
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('interbankdeposit.business.handle.agreedefault.record')"
        :count-label-unit="t('interbankdeposit.business.handle.agreedefault.recordUnit')"
        :summation-biz-label="t('interbankdeposit.business.handle.agreedefault.record')"
        :summation-biz-unit="t('interbankdeposit.business.handle.agreedefault.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
      >
        <template #operate>
          <f-submit-state
            :is-batch="true"
            :disabled="!isChecked"
            :gather-params="gatherBatchSumitParams"
            :url="batchSubmitUrl"
            @close="handleSearch"
            :operate-name="t('interbankdeposit.business.handle.agreedefault.batchSubmit')"
            :confirm-text="submitMessage"
            :before-trigger="beforeSubmitTrigger"
            :batch-confirm-map="submitRusultConfirm"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('interbankdeposit.business.handle.agreedefault.businessCode')" prop="businessCode">
            <f-input v-model="queryFrom.businessCode" />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.handle.agreedefault.businessStatus')" prop="businessStatus">
            <f-select
              v-model="queryFrom.businessStatus"
              :data="
                businessStatus.pickConst([
                  businessStatus.PENDING_HANDLE,
                  businessStatus.SAVE,
                  businessStatus.APPROVING,
                  businessStatus.REFUSE,
                  businessStatus.APPROVED,
                  businessStatus.CANCELLING,
                  businessStatus.CANCEL
                ])
              "
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item
            :label="t('interbankdeposit.business.handle.agreedefault.depositBankAcctNo')"
            prop="depositBankAcctIds"
          >
            <f-magnifier-multi
              :title="
                t('interbankdeposit.business.handle.agreedefault.depositBankAcctNo') +
                t('interbankdeposit.business.handle.agreedefault.magnifier')
              "
              :url="queryBankAccount"
              method="post"
              v-model="queryFrom.depositBankAcctIds"
              row-key="id"
              row-label="bankAccountNo"
              input-key="condition"
              selected-key="bankAccountNo"
              selected-label="bankAccountName"
              auto-init
              :params="{
                // 0为活期账户类型
                accountType: 0,
                // 1为财务公司
                ownerType: 1
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column
                prop="bankAccountNo"
                :label="t('interbankdeposit.business.handle.agreedefault.bankAcctNo')"
              />
              <f-magnifier-column
                prop="bankAccountName"
                :label="t('interbankdeposit.business.handle.agreedefault.bankAcctName')"
              />
              <f-magnifier-column
                prop="bankName"
                :label="t('interbankdeposit.business.handle.agreedefault.bankName')"
              />
            </f-magnifier-multi>
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-handle="handleRow(row)" @on-modify="changeRow(row)">
            <!--撤销-->
            <template #suffix v-if="row.businessStatus === businessStatus.APPROVED">
              <f-submit-state
                :gather-params="
                  () => {
                    return row;
                  }
                "
                :url="cancelUrl"
                operate="cancel"
                :is-batch="false"
                :operate-name="t('interbankdeposit.business.handle.agreedefault.cancel')"
                confirm-text=" "
                link
                :icon="DtgCopy"
                :is-show-result-btn-group="false"
                :result-confirm="t('interbankdeposit.business.handle.agreedefault.cancelSuccess')"
                :result-title="t('interbankdeposit.business.handle.agreedefault.cancel')"
                @close="handleSearch"
                :beforeConfirm="() => formValidator(row)"
              >
                <template #confirmEdit>
                  <f-multi-form-panel ref="cancelForm" :model="row">
                    <f-form-item
                      :label="t('interbankdeposit.business.handle.agreedefault.cancelReason')"
                      prop="refuseReason"
                      required
                    >
                      <f-input v-model="row.refuseReason" />
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
            </template>
          </OperateButton>
        </template>
        <template #businessCode="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.businessCode }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import OperateButton from "@/components/operate-button/operate-button";
import useList from "../hooks/useList";
import Detail from "./components/Detail.vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { DtgCopy } from "@dtg/frontend-plus-icons";
import { useEntrace } from "@/hooks/useEntrace.ts";
import { queryBankAccount, listUrl, exportUrl, batchSubmitUrl, cancelUrl } from "../url";
const { t } = useI18n();
const businessStatus = useConst("common.BusinessStatus");
const {
  tableColumns,
  selectableAll,
  handleSelect,
  isChecked,
  gatherBatchSumitParams,
  generalButtonOption,
  handleRow,
  changeRow,
  handleOpen,
  rowId,
  detail,
  queryFrom,
  handleSearch,
  queryTable,
  submitMessage,
  beforeSubmitTrigger,
  submitRusultConfirm,
  clearSelection,
  allowSort,
  cancelForm,
  formValidator
} = useList();
const { onEnterPage } = useEntrace();

onEnterPage(urlParams => {
  queryFrom.businessStatus = [];
  queryFrom.businessStatus = [urlParams.businessStatus];
});
</script>
