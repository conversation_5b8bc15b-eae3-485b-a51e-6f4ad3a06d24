<template>
  <f-blank-scene :title="t('interbankdeposit.business.agreeopenapply.modifyTitle')">
    <f-multi-form-panel ref="form" :model="applyDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.agreeopenapply.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.businessCode')" prop="businessCode">
          <f-input v-model="applyDto.businessCode" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.office')" prop="officeId">
          <f-input v-model="applyDto.officeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.currency')" prop="currencyId">
          <f-input v-model="applyDto.currencyName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.applyDate')" prop="applyDate">
          <f-date-picker v-model="applyDto.applyDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.rate')" prop="rate" required>
          <f-number v-model="applyDto.rate" negative :max="100" :min="0" tooltip is-rate />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.amount')" prop="amount" required>
          <f-amount
            v-model="applyDto.amount"
            tooltip
            :negative="false"
            :max="*************.99"
            :symbol="currencySymbol"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.term')" prop="termShow" required>
          <f-select
            v-model="applyDto.termShow"
            value-key="termShow"
            label="termShow"
            :url="termSetListUrl"
            method="post"
            :extra-data="termParams"
            @change="termChange"
            ref="termRef"
            auto-select
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.startDate')" prop="startDate" required>
          <f-date-picker v-model="applyDto.startDate" type="date" @change="startDateChange" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.endDate')" prop="endDate" required>
          <f-date-picker v-model="applyDto.endDate" type="date" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.defaultRate')" prop="defaultRate" required>
          <f-number v-model="applyDto.defaultRate" negative :max="100" :min="0" tooltip is-rate />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.exemptDays')" prop="exemptDays" required>
          <f-number v-model="applyDto.exemptDays" negative :whole-number="true" tooltip />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.floatAmount')" prop="floatAmount" required>
          <f-amount-range v-model="applyDto.floatAmount" :max="*************.99" :symbol="currencySymbol" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.countDays')" prop="countDays" required>
          <f-select v-model="applyDto.countDays" :data="countDays" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.rateType')" prop="rateType">
          <f-select v-model="applyDto.rateType" :data="interBankDepositRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.baseRateType')" prop="baseRateType">
          <f-select v-model="applyDto.baseRateType" :data="interBankDepositBaseRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.interestType')" prop="interestType">
          <f-select v-model="applyDto.interestType" :data="interBankDepositInterestType" />
        </f-form-item>
        <f-form-item />
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.remark')" prop="remark" :employ="2">
          <f-textarea v-model="applyDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.agreeopenapply.bankAcctInfo')">
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.depositBank')" prop="bankTypeId" required>
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.agreeopenapply.depositBank') +
              t('interbankdeposit.business.agreeopenapply.magnifier')
            "
            :url="queryOpenBank"
            method="post"
            v-model="applyDto.bankTypeId"
            row-key="id"
            row-label="name"
            input-key="name"
            auto-init
            :params="{
              isTopBank: 1
            }"
            @change="depositBankChange"
          >
            <f-magnifier-column
              prop="referenceCode"
              :label="t('interbankdeposit.business.agreeopenapply.openBankCode')"
            />
            <f-magnifier-column prop="name" :label="t('interbankdeposit.business.agreeopenapply.openBankName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.agreeopenapply.depositBankAcctNo')"
          prop="depositBankAcctId"
          required
        >
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.agreeopenapply.depositBankAcctNo') +
              t('interbankdeposit.business.agreeopenapply.magnifier')
            "
            :url="queryBankAccount"
            method="post"
            v-model="applyDto.depositBankAcctId"
            row-key="id"
            row-label="bankAccountNo"
            input-key="condition"
            auto-init
            :params="{
              currencyId: applyDto.currencyId,
              officeId: applyDto.officeId,
              // 0为活期账户类型
              accountType: 0,
              // 1为财务公司
              ownerType: 1,
              bankTypeCode: applyDto.bankTypeCode
            }"
            @change="depositBankAccountChange"
          >
            <f-magnifier-column
              prop="bankAccountNo"
              :label="t('interbankdeposit.business.agreeopenapply.bankAcctNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('interbankdeposit.business.agreeopenapply.bankAcctName')"
            />
            <f-magnifier-column prop="bankName" :label="t('interbankdeposit.business.agreeopenapply.bankName')" />
            <f-magnifier-column
              prop="balance"
              :label="t('interbankdeposit.business.agreeopenapply.depositAcctBalance')"
              formatter="amount"
              :filterInput="false"
            />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.agreeopenapply.depositBankAcctName')"
          prop="depositBankAcctName"
        >
          <f-input v-model="applyDto.depositBankAcctName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.depositBankName')" prop="depositBankName">
          <f-input v-model="applyDto.depositBankName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.agreeopenapply.depositAcctBalance')"
          prop="depositAcctBalance"
        >
          <f-amount v-model="applyDto.depositAcctBalance" tooltip :negative="false" disabled :symbol="currencySymbol" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.depositRate')" prop="depositRate">
          <f-number v-model="applyDto.depositRate" negative :precision="4" tooltip is-rate disabled />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.agreeopenapply.fileinfo')">
        <f-form-item :label="t('interbankdeposit.business.agreeopenapply.file')" :employ="3">
          <f-attm-upload ref="upload" v-model="applyDto.fileIdArr" drag multiple />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :before-trigger="formValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="submitInfo"
          :url="submitUrl"
          operate="submit"
          :before-trigger="formValidator"
          @close="callBack"
        />
        <f-submit-state :gather-params="removeInfo" type="danger" :url="deleteUrl" operate="remove" @close="callBack" />
        <f-button v-if="!isSubmit" type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.agreeopenapply.back")
        }}</f-button>
        <f-button v-if="isSubmit" type="info" plain @click.prevent="goSubmit">{{
          t("interbankdeposit.business.agreeopenapply.back1")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { termSetListUrl, queryOpenBank, queryBankAccount, saveUrl, submitUrl, deleteUrl } from "../url";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { usePage } from "../hooks/usePage";
import useModify from "../hooks/useModify";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
const { t } = useI18n();
const countDays = useConst("common.CountDays");
const interBankDepositRateType = useConst("interbankdeposit.InterBankDepositRateType");
const interBankDepositBaseRateType = useConst("interbankdeposit.InterBankDepositBaseRateType");
const interBankDepositInterestType = useConst("interbankdeposit.InterBankDepositInterestType");
const router = useRouter();
const {
  applyDto,
  rules,
  termRef,
  termParams,
  termChange,
  startDateChange,
  depositBankChange,
  depositBankAccountChange,
  formValidator,
  form,
  saveInfo,
  submitInfo,
  removeInfo,
  saveSuccess,
  goBack,
  modifyCreate,
  upload,
  currencySymbol
} = useModify();
const { pageParams } = usePage();
//定义父组件传参, 参数不唯一，根据⻚面需要参数动态添加
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
// 页面初始化
onMounted(() => {
  if (pageParams) {
    modifyCreate(pageParams?.id);
  } else {
    modifyCreate(Number(props?.id));
    isSubmit.value = true;
  }
});
//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};
const callBack = (res: any) => {
  if (res.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      goBack();
    }
  }
};
</script>
