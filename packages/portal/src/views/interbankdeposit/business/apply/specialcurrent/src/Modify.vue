<template>
  <f-blank-scene :title="t('interbankdeposit.business.specialcurrentapply.modifyTitle')">
    <f-multi-form-panel ref="form" :model="applyDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.specialcurrentapply.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.businessCode')" prop="businessCode">
          <f-input v-model="applyDto.businessCode" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.office')" prop="officeName">
          <f-input v-model="applyDto.officeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.currency')" prop="currencyName">
          <f-input v-model="applyDto.currencyName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.applyDate')" prop="applyDate">
          <f-date-picker v-model="applyDto.applyDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.bankAcctNo')" prop="bankAcctNo">
          <f-input v-model="applyDto.bankAcctNo" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.bankAcctName')" prop="bankAcctName">
          <f-input v-model="applyDto.bankAcctName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.bankName')" prop="bankName">
          <f-input v-model="applyDto.bankName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.term')" prop="termShow" required>
          <f-select
            v-model="applyDto.termShow"
            value-key="termShow"
            label="termShow"
            :url="termSetListUrl"
            method="post"
            :extra-data="termParams"
            @change="termChange"
            ref="termRef"
            auto-select
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.startDate')" prop="startDate" required>
          <f-date-picker v-model="applyDto.startDate" type="date" @change="startDateChange" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.endDate')" prop="endDate" required>
          <f-date-picker v-model="applyDto.endDate" type="date" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.countDays')" prop="countDays" required>
          <f-select v-model="applyDto.countDays" :data="countDays" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.specialcurrentapply.rateBalance')"
          prop="rateBalance"
          required
        >
          <f-select v-model="applyDto.rateBalance" :data="interBankDepositRateBalance" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.rateType')" prop="rateType">
          <f-select v-model="applyDto.rateType" :data="interBankDepositRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.baseRateType')" prop="baseRateType">
          <f-select v-model="applyDto.baseRateType" :data="interBankDepositBaseRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.interestType')" prop="interestType">
          <f-select v-model="applyDto.interestType" :data="interBankDepositInterestType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.remark')" prop="remark" :employ="2">
          <f-textarea v-model="applyDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.specialcurrentapply.rateInfoSet')">
        <f-table-edit
          ref="directorGrid"
          row-key="_randomId"
          :data="directorTableData.data"
          border
          @add-row="addRow"
          :before-close-edit="saveDirectorRow"
        >
          <f-table-column
            type="index"
            :label="t('interbankdeposit.business.specialcurrentapply.order')"
            align="center"
          />
          <f-table-column
            prop="geAmount"
            :label="t('interbankdeposit.business.specialcurrentapply.geAmount')"
            align="right"
          >
            <template #edit="scope">
              <f-amount
                v-model="scope.row.geAmount"
                tooltip
                :negative="false"
                :max="*************.99"
                :symbol="currencySymbol"
              />
            </template>
            <template #default="scope">
              <div style="text-align: right; color: #ff9e00">
                {{ format(scope.row.geAmount, { separator: ",", precision: 2, symbol: "" }) }}
              </div>
            </template>
          </f-table-column>
          <f-table-column
            prop="ltAmount"
            :label="t('interbankdeposit.business.specialcurrentapply.ltAmount')"
            align="right"
          >
            <template #edit="scope">
              <f-amount
                v-model="scope.row.ltAmount"
                :negative="false"
                tooltip
                :max="*************.99"
                :symbol="currencySymbol"
              />
            </template>
            <template #default="scope">
              <div style="text-align: right; color: #ff9e00">
                {{ format(scope.row.ltAmount, { separator: ",", precision: 2, symbol: "" }) }}
              </div>
            </template>
          </f-table-column>
          <f-table-column
            prop="rate"
            :label="t('interbankdeposit.business.specialcurrentapply.rate') + '%'"
            align="right"
          >
            <template #edit="scope">
              <f-number v-model="scope.row.rate" :min="0" :max="100" is-rate />
            </template>
            <template #default="scope">
              <div style="text-align: right; color: #ff9e00">
                {{ format(scope.row.rate, { separator: ",", precision: 6, symbol: "" }) }}
              </div>
            </template>
          </f-table-column>
        </f-table-edit>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :before-trigger="formValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="submitInfo"
          :url="submitUrl"
          operate="submit"
          :before-trigger="formValidator"
          @close="callBack"
        />
        <f-submit-state :gather-params="removeInfo" type="danger" :url="deleteUrl" operate="remove" @close="callBack" />
        <f-button v-if="!isSubmit" type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.specialcurrentapply.back")
        }}</f-button>
        <f-button v-if="isSubmit" type="info" plain @click.prevent="goSubmit">{{
          t("interbankdeposit.business.specialcurrentapply.back1")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { termSetListUrl, saveUrl, submitUrl, deleteUrl } from "../url";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { usePage } from "../hooks/usePage";
import useModify from "../hooks/useModify";
import { format } from "@/utils/currency";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
const { t } = useI18n();
const router = useRouter();
const countDays = useConst("common.CountDays");
const interBankDepositRateBalance = useConst("interbankdeposit.InterBankDepositRateBalance");
const interBankDepositRateType = useConst("interbankdeposit.InterBankDepositRateType");
const interBankDepositBaseRateType = useConst("interbankdeposit.InterBankDepositBaseRateType");
const interBankDepositInterestType = useConst("interbankdeposit.InterBankDepositInterestType");
const {
  applyDto,
  directorTableData,
  rules,
  termRef,
  termParams,
  termChange,
  startDateChange,
  formValidator,
  form,
  saveInfo,
  submitInfo,
  removeInfo,
  saveSuccess,
  goBack,
  modifyCreate,
  currencySymbol,
  directorGrid,
  addRow,
  saveDirectorRow
} = useModify();
const { pageParams } = usePage();
//定义父组件传参, 参数不唯一，根据⻚面需要参数动态添加
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
// 页面初始化
onMounted(() => {
  if (pageParams) {
    modifyCreate(pageParams?.id);
  } else {
    modifyCreate(Number(props?.id));
    isSubmit.value = true;
  }
});
//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};
const callBack = (res: any) => {
  if (res.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      goBack();
    }
  }
};
</script>
