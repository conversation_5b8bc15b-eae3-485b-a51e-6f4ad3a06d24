<template>
  <f-blank-scene :title="t('interbankdeposit.business.specialcurrentapply.addTitle')">
    <f-multi-form-panel ref="form" :model="applyDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.specialcurrentapply.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.office')" prop="officeId" required>
          <f-select
            v-model="applyDto.officeId"
            value-key="officeId"
            label="officeName"
            :url="officeListUrl"
            method="post"
            @change="officeChange"
            auto-select
            :disabled="applyDto.checkStatus === checkStatus.SAVE"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.currency')" prop="currencyId" required>
          <f-select
            v-model="applyDto.currencyId"
            value-key="currencyId"
            label="currencyName"
            :url="currencyListUrl"
            method="post"
            :extra-data="currencyParams"
            @change="currencyChange"
            ref="currencyRef"
            auto-select
            :disabled="applyDto.checkStatus === checkStatus.SAVE"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.applyDate')" prop="applyDate">
          <f-date-picker v-model="applyDto.applyDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.bankAcctNo')" prop="bankAcctId" required>
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.specialcurrentapply.bankAcctNo') +
              t('interbankdeposit.business.specialcurrentapply.magnifier')
            "
            :url="queryBankAccount"
            method="post"
            v-model="applyDto.bankAcctId"
            row-key="id"
            row-label="bankAccountNo"
            input-key="bankAccountNo"
            auto-init
            :params="{
              currencyId: applyDto.currencyId,
              officeId: applyDto.officeId,
              // 0为活期账户类型
              accountType: 0,
              // 1为财务公司
              ownerType: 1
            }"
            @change="bankAccountChange"
          >
            <f-magnifier-column
              prop="bankAccountNo"
              :label="t('interbankdeposit.business.specialcurrentapply.bankAcctNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('interbankdeposit.business.specialcurrentapply.bankAcctName')"
            />
            <f-magnifier-column prop="bankName" :label="t('interbankdeposit.business.specialcurrentapply.bankName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.bankAcctName')" prop="bankAcctName">
          <f-input v-model="applyDto.bankAcctName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.bankName')" prop="bankName">
          <f-input v-model="applyDto.bankName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.term')" prop="termShow" required>
          <f-select
            v-model="applyDto.termShow"
            value-key="termShow"
            label="termShow"
            :url="termSetListUrl"
            method="post"
            :extra-data="termParams"
            @change="termChange"
            ref="termRef"
            auto-select
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.startDate')" prop="startDate" required>
          <f-date-picker v-model="applyDto.startDate" type="date" @change="startDateChange" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.endDate')" prop="endDate" required>
          <f-date-picker v-model="applyDto.endDate" type="date" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.countDays')" prop="countDays" required>
          <f-select v-model="applyDto.countDays" :data="countDays" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.specialcurrentapply.rateBalance')"
          prop="rateBalance"
          required
        >
          <f-select v-model="applyDto.rateBalance" :data="interBankDepositRateBalance" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.rateType')" prop="rateType">
          <f-select v-model="applyDto.rateType" :data="interBankDepositRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.baseRateType')" prop="baseRateType">
          <f-select v-model="applyDto.baseRateType" :data="interBankDepositBaseRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.interestType')" prop="interestType">
          <f-select v-model="applyDto.interestType" :data="interBankDepositInterestType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.specialcurrentapply.remark')" prop="remark" :employ="2">
          <f-textarea v-model="applyDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.specialcurrentapply.rateInfoSet')">
        <f-table-edit
          ref="directorGrid"
          row-key="_randomId"
          :data="directorTableData.data"
          border
          @add-row="addRow"
          :before-close-edit="saveDirectorRow"
        >
          <f-table-column
            type="index"
            :label="t('interbankdeposit.business.specialcurrentapply.order')"
            align="center"
          />
          <f-table-column
            prop="geAmount"
            :label="t('interbankdeposit.business.specialcurrentapply.geAmount')"
            align="right"
          >
            <template #edit="scope">
              <f-amount
                v-model="scope.row.geAmount"
                tooltip
                :negative="false"
                :max="*************.99"
                :symbol="currencySymbol"
              />
            </template>
            <template #default="scope">
              <div style="text-align: right; color: #ff9e00">
                {{ format(scope.row.geAmount, { separator: ",", precision: 2, symbol: "" }) }}
              </div>
            </template>
          </f-table-column>
          <f-table-column
            prop="ltAmount"
            :label="t('interbankdeposit.business.specialcurrentapply.ltAmount')"
            align="right"
          >
            <template #edit="scope">
              <f-amount
                v-model="scope.row.ltAmount"
                :negative="false"
                tooltip
                :max="*************.99"
                :symbol="currencySymbol"
              />
            </template>
            <template #default="scope">
              <div style="text-align: right; color: #ff9e00">
                {{ format(scope.row.ltAmount, { separator: ",", precision: 2, symbol: "" }) }}
              </div>
            </template>
          </f-table-column>
          <f-table-column
            prop="rate"
            :label="t('interbankdeposit.business.specialcurrentapply.rate') + '%'"
            align="right"
          >
            <template #edit="scope">
              <f-number v-model="scope.row.rate" :min="0" :max="100" is-rate />
            </template>
            <template #default="scope">
              <div style="text-align: right; color: #ff9e00">
                {{ format(scope.row.rate, { separator: ",", precision: 6, symbol: "" }) }}
              </div>
            </template>
          </f-table-column>
        </f-table-edit>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :before-trigger="formValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="submitInfo"
          :url="submitUrl"
          operate="submit"
          :before-trigger="formValidator"
          @close="submitSuccess"
        />
        <f-button type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.specialcurrentapply.back")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { officeListUrl, currencyListUrl, termSetListUrl, queryBankAccount, saveUrl, submitUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useAdd from "../hooks/useAdd";
import { onMounted } from "vue";
import { format } from "@/utils/currency";
const { t } = useI18n();
const checkStatus = useConst("interbankdeposit.CheckStatus");
const countDays = useConst("common.CountDays");
const interBankDepositRateBalance = useConst("interbankdeposit.InterBankDepositRateBalance");
const interBankDepositRateType = useConst("interbankdeposit.InterBankDepositRateType");
const interBankDepositBaseRateType = useConst("interbankdeposit.InterBankDepositBaseRateType");
const interBankDepositInterestType = useConst("interbankdeposit.InterBankDepositInterestType");
const {
  applyDto,
  directorTableData,
  rules,
  officeChange,
  currencyRef,
  currencyParams,
  currencyChange,
  termRef,
  termParams,
  termChange,
  startDateChange,
  bankAccountChange,
  formValidator,
  form,
  saveInfo,
  submitInfo,
  saveSuccess,
  submitSuccess,
  goBack,
  getOpenDate,
  currencySymbol,
  directorGrid,
  addRow,
  saveDirectorRow
} = useAdd();
onMounted(() => {
  getOpenDate();
});
</script>
