<template>
  <f-blank-scene :title="t('interbankdeposit.business.fixedopenapply.addTitle')">
    <f-multi-form-panel ref="form" :model="applyDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.fixedopenapply.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.office')" prop="officeId" required>
          <f-select
            v-model="applyDto.officeId"
            value-key="officeId"
            label="officeName"
            :url="officeListUrl"
            method="post"
            @change="officeChange"
            auto-select
            :disabled="applyDto.checkStatus === checkStatus.SAVE"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.currency')" prop="currencyId" required>
          <f-select
            v-model="applyDto.currencyId"
            value-key="currencyId"
            label="currencyName"
            :url="currencyListUrl"
            method="post"
            :extra-data="currencyParams"
            @change="currencyChange"
            ref="currencyRef"
            auto-select
            :disabled="applyDto.checkStatus === checkStatus.SAVE"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.applyDate')" prop="applyDate">
          <f-date-picker v-model="applyDto.applyDate" type="date" disabled />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenapply.quotenfo')">
        <f-form-item :employ="3">
          <div style="display: flex">
            <f-form-item :label="t('interbankdeposit.business.fixedopenapply.quotedCode')" prop="quotedCodes" required>
              <f-magnifier-multi
                :title="
                  t('interbankdeposit.business.fixedopenapply.quotedCode') +
                  t('interbankdeposit.business.fixedopenapply.magnifier')
                "
                :url="queryQuoteInfoUrl"
                method="post"
                v-model="applyDto.quotedCodes"
                row-key="applyCode"
                query-key="applyCodeList"
                row-label="applyCode"
                input-key="applyCode"
                selected-key="applyCode"
                selected-label="applyCode"
                auto-init
                :collapse-tags-tooltip="true"
                :params="{
                  businessTypeList: [quotedRateSetBusinessTypeEnum.FIXED_DEPOSIT]
                }"
                @change="quotedchange"
              >
                <f-magnifier-column
                  prop="applyCode"
                  :label="t('interbankdeposit.business.fixedopenapply.quotedCode')"
                />
                <f-magnifier-column
                  prop="quotedType"
                  :label="t('interbankdeposit.business.fixedopenapply.quotedType')"
                  :formatter="{ name: 'const', const: 'interbankprice.QuotedType' }"
                />
                <f-magnifier-column
                  prop="counterpartyName"
                  :label="t('interbankdeposit.business.fixedopenapply.counterpartyName')"
                />
                <f-magnifier-column
                  prop="quotedDate"
                  :label="t('interbankdeposit.business.fixedopenapply.quotedDate')"
                />
                <f-magnifier-column
                  prop="inquiryAmount"
                  :label="t('interbankdeposit.business.fixedopenapply.inquiryAmount')"
                  formatter="amount"
                />
                <f-magnifier-column prop="term" :label="t('interbankdeposit.business.fixedopenapply.term')" />
                <f-magnifier-column
                  prop="rate"
                  :label="t('interbankdeposit.business.fixedopenapply.quotedRate') + '%'"
                  formatter="rate"
                />
                <f-magnifier-column prop="remark" :label="t('interbankdeposit.business.fixedopenapply.remark')" />
              </f-magnifier-multi>
            </f-form-item>
          </div>
        </f-form-item>
        <f-query-table
          ref="queryQuotenTable"
          row-key="id"
          border
          :tableData="quoteInfoList"
          show-header
          :auto-init="false"
          :show-query-panel="false"
          :pagination="false"
          :show-export="false"
          :show-print="false"
          :show-layout="false"
          :count-label="t('interbankdeposit.business.fixedopenapply.record')"
          :count-label-unit="t('interbankdeposit.business.fixedopenapply.recordUnit')"
          :summation-biz-label="t('interbankdeposit.business.fixedopenapply.record')"
          :summation-biz-unit="t('interbankdeposit.business.fixedopenapply.recordUnit')"
          :show-count-value="false"
          :show-summation-sum="false"
        >
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.quotedCode')" prop="applyCode" />
          <f-table-column
            :label="t('interbankdeposit.business.fixedopenapply.quotedType')"
            prop="quotedType"
            :formatter="row => quotedTypeEnum.valueToLabel(row.quotedType)"
          />
          <f-table-column
            :label="t('interbankdeposit.business.fixedopenapply.counterpartyName')"
            prop="counterpartyName"
          />
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.quotedDate')" prop="quotedDate" />

          <f-table-column
            :label="t('interbankdeposit.business.fixedopenapply.inquiryAmount')"
            prop="inquiryAmount"
            formatter="amount"
          />
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.term')" prop="term" />
          <f-table-column
            :label="t('interbankdeposit.business.fixedopenapply.quotedRate') + '%'"
            prop="rate"
            formatter="rate"
          />
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.remark')" prop="remark" />
        </f-query-table>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenapply.bankAccountInfo')">
        <f-form-item :employ="3">
          <div style="display: flex">
            <f-form-item :label="t('interbankdeposit.business.fixedopenapply.bankAcctNo')" prop="bankAccountIds">
              <f-magnifier-multi
                :title="
                  t('interbankdeposit.business.fixedopenapply.bankAcctNo') +
                  t('interbankdeposit.business.fixedopenapply.magnifier')
                "
                :url="getCommonAccount"
                method="post"
                v-model="applyDto.bankAccountIds"
                row-key="id"
                row-label="bankAccountCode"
                input-key="codeOrName"
                selected-key="bankAccountCode"
                selected-label="bankAccountName"
                auto-init
                :collapse-tags-tooltip="true"
                :params="{
                  officeId: applyDto.officeId,
                  currencyId: applyDto.currencyId
                }"
              >
                <f-magnifier-column
                  prop="bankAccountCode"
                  :label="t('interbankdeposit.business.fixedopenapply.bankAcctNo')"
                />
                <f-magnifier-column
                  prop="bankAccountName"
                  :label="t('interbankdeposit.business.fixedopenapply.bankAcctName')"
                />
                <f-magnifier-column
                  prop="openBankName"
                  :label="t('interbankdeposit.business.fixedopenapply.bankName')"
                />
              </f-magnifier-multi>
            </f-form-item>
            <div style="text-align: right">
              <f-button type="primary" @click.prevent="queryBankAcctInfo">{{
                t("interbankdeposit.business.fixedopenapply.query")
              }}</f-button>
            </div>
          </div>
        </f-form-item>
        <f-query-table
          ref="queryBankAccountTable"
          row-key="id"
          border
          :tableData="bankAcctInfoList"
          show-header
          :auto-init="false"
          :show-query-panel="false"
          :pagination="true"
          :show-export="false"
          :show-print="false"
          :show-layout="false"
          :count-label="t('interbankdeposit.business.fixedopenapply.record')"
          :count-label-unit="t('interbankdeposit.business.fixedopenapply.recordUnit')"
          :summation-biz-label="t('interbankdeposit.business.fixedopenapply.record')"
          :summation-biz-unit="t('interbankdeposit.business.fixedopenapply.recordUnit')"
          :show-summation-sum="false"
        >
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.bankAcctNo')" prop="bankAcctNo" />
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.bankAcctName')" prop="bankAcctName" />
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.bankName')" prop="bankName" />
          <f-table-column
            :label="t('interbankdeposit.business.fixedopenapply.balance')"
            prop="balance"
            formatter="amount"
          />
          <f-table-column
            :label="t('interbankdeposit.business.fixedopenapply.rate') + '%'"
            prop="rate"
            :formatter="{ name: 'rate', allowEmpty: true }"
          />
          <f-table-column :label="t('interbankdeposit.business.fixedopenapply.remark')" prop="remark" />
        </f-query-table>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenapply.applyInfo')">
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.counterpartyName')"
          prop="counterpartyId"
          required
        >
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.fixedopenapply.counterpartyName') +
              t('interbankdeposit.business.fixedopenapply.magnifier')
            "
            :url="counterPartyList"
            method="post"
            v-model="applyDto.counterpartyId"
            row-key="id"
            query-key="clientId"
            row-label="clientName"
            input-key="codeOrName"
            auto-init
            :params="{
              officeId: applyDto.officeId,
              clientClass: 3
            }"
            @change="counterpartyChange"
          >
            <f-magnifier-column
              prop="clientCode"
              :label="t('interbankdeposit.business.fixedopenapply.counterpartyCode')"
            />
            <f-magnifier-column
              prop="clientName"
              :label="t('interbankdeposit.business.fixedopenapply.counterpartyName')"
            />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.counterpartyType')"
          prop="counterpartyTypeName"
        >
          <f-input v-model="applyDto.counterpartyTypeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.term')" prop="termShow" required>
          <f-select
            v-model="applyDto.termShow"
            value-key="termShow"
            label="termShow"
            :url="termSetListUrl"
            method="post"
            :extra-data="termParams"
            @change="termChange"
            ref="termRef"
            auto-select
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.startDate')" prop="startDate" required>
          <f-date-picker v-model="applyDto.startDate" type="date" @change="startDateChange" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.endDate')" prop="endDate" required>
          <f-date-picker v-model="applyDto.endDate" type="date" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.amount')" prop="amount" required>
          <f-amount
            v-model="applyDto.amount"
            tooltip
            :negative="false"
            :max="*************.99"
            :symbol="currencySymbol"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.upper')">
          <f-amount-chinese v-model="applyDto.amount" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.fixedRate')" prop="rate" required>
          <f-number v-model="applyDto.rate" negative :max="100" :min="0" tooltip is-rate />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.advanceRate')" prop="advanceRate" required>
          <f-number v-model="applyDto.advanceRate" negative :max="100" :min="0" tooltip is-rate />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.countDays')" prop="countDays" required>
          <f-select v-model="applyDto.countDays" :data="countDays" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.drawType')" prop="drawType" required>
          <f-select v-model="applyDto.drawType" :data="interBankDepositDrawType" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.supplyFundFlag')"
          prop="supplyFundFlag"
          required
        >
          <f-switch
            v-model="applyDto.supplyFundFlag"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
            @change="supplyFundFlagChange"
          />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.payBankAcctNo')"
          prop="payBankAcctId"
          required
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.fixedopenapply.payBankAcctNo') +
              t('interbankdeposit.business.fixedopenapply.magnifier')
            "
            :url="getCommonAccount"
            method="post"
            v-model="applyDto.payBankAcctId"
            row-key="id"
            row-label="bankAccountCode"
            input-key="codeOrName"
            auto-init
            :params="{
              currencyId: applyDto.currencyId,
              officeId: applyDto.officeId
            }"
            @change="payBankAccountChange"
          >
            <f-magnifier-column
              prop="bankAccountCode"
              :label="t('interbankdeposit.business.fixedopenapply.bankAcctNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('interbankdeposit.business.fixedopenapply.bankAcctName')"
            />
            <f-magnifier-column prop="openBankName" :label="t('interbankdeposit.business.fixedopenapply.bankName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.payBankAcctName')"
          prop="payBankAcctName"
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-input v-model="applyDto.payBankAcctName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.payBankName')"
          prop="payBankName"
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-input v-model="applyDto.payBankName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.depositBankAcctNo')"
          prop="depositBankAcctId"
          required
        >
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.fixedopenapply.depositBankAcctNo') +
              t('interbankdeposit.business.fixedopenapply.magnifier')
            "
            :url="queryBankAccount"
            method="post"
            v-model="applyDto.depositBankAcctId"
            row-key="id"
            row-label="bankAccountNo"
            input-key="bankAccountNo"
            auto-init
            :params="{
              currencyId: applyDto.currencyId,
              officeId: applyDto.officeId,
              // 0为活期账户类型
              accountType: 0,
              // 1为财务公司
              ownerType: 1
            }"
            @change="depositBankAccountChange"
          >
            <f-magnifier-column
              prop="bankAccountNo"
              :label="t('interbankdeposit.business.fixedopenapply.bankAcctNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('interbankdeposit.business.fixedopenapply.bankAcctName')"
            />
            <f-magnifier-column prop="bankName" :label="t('interbankdeposit.business.fixedopenapply.bankName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.depositBankAcctName')"
          prop="depositBankAcctName"
        >
          <f-input v-model="applyDto.depositBankAcctName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.depositBankName')" prop="depositBankName">
          <f-input v-model="applyDto.depositBankName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.depositFixBankAcctNo')"
          prop="depositFixBankAcctNo"
          required
        >
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.fixedopenapply.depositFixBankAcctNo') +
              t('interbankdeposit.business.fixedopenapply.magnifier')
            "
            :url="queryBankAccount"
            method="post"
            v-model="applyDto.depositFixBankAcctNo"
            row-key="bankAccountNo"
            row-label="bankAccountNo"
            input-key="bankAccountNo"
            :auto-init="false"
            :selected-data="{
              bankAccountNo: applyDto.depositFixBankAcctNo
            }"
            :params="{
              currencyId: applyDto.currencyId,
              officeId: applyDto.officeId,
              accountType: 1,
              ownerType: 1
            }"
            filterable
            :maxlength="32"
            @query-input="depositFixBankAccountQueryInput"
            @change="depositFixBankAccountChange"
          >
            <f-magnifier-column
              prop="bankAccountNo"
              :label="t('interbankdeposit.business.fixedopenapply.bankAcctNo')"
            />
            <f-magnifier-column
              prop="bankAccountName"
              :label="t('interbankdeposit.business.fixedopenapply.bankAcctName')"
            />
            <f-magnifier-column prop="bankName" :label="t('interbankdeposit.business.fixedopenapply.bankName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.depositFixBankAcctName')"
          prop="depositFixBankAcctName"
          required
        >
          <f-input v-model="applyDto.depositFixBankAcctName" :maxlength="50" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.depositFixBankName')"
          prop="depositFixBankName"
          required
        >
          <f-input v-model="applyDto.depositFixBankName" :maxlength="50" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.paymentAmount')"
          prop="paymentAmount"
          required
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-amount v-model="applyDto.paymentAmount" tooltip :negative="false" :symbol="currencySymbol" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.remitInProvince')"
          prop="remitInProvince"
          required
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-input v-model="applyDto.remitInProvince" maxlength="32" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.remitInCity')"
          prop="remitInCity"
          required
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-input v-model="applyDto.remitInCity" maxlength="32" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.bankCnapsNo')"
          prop="bankCnapsNo"
          required
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <CnapsInput v-model="applyDto.bankCnapsNo" @confirm="onConfirm" maxlength="32" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.bankOrgNo')"
          prop="bankOrgNo"
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-input v-model="applyDto.bankOrgNo" maxlength="32" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenapply.bankExchangeNo')"
          prop="bankExchangeNo"
          v-if="applyDto.supplyFundFlag === yesOrNo.YES"
        >
          <f-input v-model="applyDto.bankExchangeNo" maxlength="32" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.rateType')" prop="rateType">
          <f-select v-model="applyDto.rateType" :data="interBankDepositRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.baseRateType')" prop="baseRateType">
          <f-select v-model="applyDto.baseRateType" :data="interBankDepositBaseRateType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.interestType')" prop="interestType">
          <f-select v-model="applyDto.interestType" :data="interBankDepositInterestType" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.remark')" prop="remark" :employ="2">
          <f-textarea v-model="applyDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenapply.fileinfo')">
        <f-form-item :label="t('interbankdeposit.business.fixedopenapply.file')" :employ="3">
          <f-attm-upload ref="upload" drag multiple />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :before-trigger="formValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="submitInfo"
          :url="submitUrl"
          operate="submit"
          :before-trigger="formValidator"
          @close="submitSuccess"
        />
        <f-button type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.fixedopenapply.back")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import {
  officeListUrl,
  currencyListUrl,
  queryQuoteInfoUrl,
  getCommonAccount,
  counterPartyList,
  termSetListUrl,
  queryBankAccount,
  saveUrl,
  submitUrl
} from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useAdd from "../hooks/useAdd";
import { onMounted } from "vue";
const { t } = useI18n();
const onConfirm = model => {
  applyDto.remitInProvince = model.sAreaProvince;
  applyDto.remitInCity = model.sAreaCity;
};
const yesOrNo = useConst("interbankdeposit.YesOrNo");
const checkStatus = useConst("interbankdeposit.CheckStatus");
const quotedTypeEnum = useConst("interbankprice.QuotedType");
const quotedRateSetBusinessTypeEnum = useConst("interbankprice.QuotedRateSetBusinessType");
const countDays = useConst("common.CountDays");
const interBankDepositDrawType = useConst("interbankdeposit.InterBankDepositDrawType");
const interBankDepositRateType = useConst("interbankdeposit.InterBankDepositRateType");
const interBankDepositBaseRateType = useConst("interbankdeposit.InterBankDepositBaseRateType");
const interBankDepositInterestType = useConst("interbankdeposit.InterBankDepositInterestType");
const {
  queryQuotenTable,
  queryBankAccountTable,
  applyDto,
  rules,
  officeChange,
  currencyRef,
  currencyParams,
  currencyChange,
  quotedchange,
  queryBankAcctInfo,
  counterpartyChange,
  termRef,
  termParams,
  termChange,
  startDateChange,
  supplyFundFlagChange,
  payBankAccountChange,
  depositBankAccountChange,
  depositFixBankAccountChange,
  depositFixBankAccountQueryInput,
  formValidator,
  form,
  saveInfo,
  submitInfo,
  saveSuccess,
  submitSuccess,
  goBack,
  getOpenDate,
  upload,
  quoteInfoList,
  bankAcctInfoList,
  currencySymbol
} = useAdd();
onMounted(() => {
  getOpenDate();
});
</script>
