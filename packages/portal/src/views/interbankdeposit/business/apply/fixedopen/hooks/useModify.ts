import httpTool from "@/utils/http";
import type { ApplyDto, fileIdList } from "../types";
import { goPage } from "../hooks/usePage";
import { reactive, shallowRef, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { findByIdUrl, openDateUrl, queryBankAcctInfoUrl, getBankInfoById } from "../url";
import { formatDate } from "@/utils/date";
import { FMessageBox } from "@dtg/frontend-plus";
import { useCurrency } from "@/hooks/useCommon";
export const useModify = () => {
  const { t } = useI18n();
  const yesOrNo = useConst("interbankdeposit.YesOrNo");
  const termType = useConst("interbankdeposit.TermType");
  const interBankDepositType = useConst("interbankdeposit.InterBankDepositType");
  const checkStatus = useConst("interbankdeposit.CheckStatus");
  const form = ref();
  const queryQuotenTable = shallowRef();
  const queryBankAccountTable = shallowRef();
  const termRef = ref();
  const upload = ref();
  const termParams = reactive({
    currencyIds: [],
    depositTypes: [interBankDepositType.FIXED_DEPOSIT]
  });
  const applyDto = reactive<ApplyDto>({
    id: null,
    businessCode: "",
    officeId: null,
    officeCode: "",
    officeName: "",
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    applyDate: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    counterpartyTypeId: null,
    counterpartyTypeName: "",
    term: null,
    termType: null,
    termShow: "",
    startDate: "",
    endDate: "",
    amount: null,
    rate: null,
    advanceRate: null,
    countDays: "",
    drawType: "",
    supplyFundFlag: "",
    payBankAcctId: null,
    payBankAcctNo: "",
    payBankAcctName: "",
    payBankId: null,
    payBankCode: "",
    payBankName: "",
    payBankTypeId: null,
    payBankTypeCode: "",
    payBankTypeName: "",
    depositBankAcctId: null,
    depositBankAcctNo: "",
    depositBankAcctName: "",
    depositBankId: null,
    depositBankCode: "",
    depositBankName: "",
    depositBankTypeId: null,
    depositBankTypeCode: "",
    depositBankTypeName: "",
    paymentAmount: null,
    remitInProvince: "",
    remitInCity: "",
    bankCnapsNo: "",
    bankOrgNo: "",
    bankExchangeNo: "",
    depositFixBankAcctId: null,
    depositFixBankAcctNo: "",
    depositFixBankAcctName: "",
    depositFixBankId: null,
    depositFixBankCode: "",
    depositFixBankName: "",
    depositFixBankTypeId: null,
    depositFixBankTypeCode: "",
    depositFixBankTypeName: "",
    rateType: "",
    baseRateType: "",
    interestType: "",
    remark: "",
    checkStatus: "",
    fileIds: "",
    fileIdArr: [],
    version: null,
    quotedCodes: [],
    bankAccountIds: [],
    quoteInfo: [],
    bankAcctInfo: [],
    creditFlag: ""
  });
  const { currencySymbol } = useCurrency(applyDto);
  const quoteInfoList = reactive({
    data: []
  });
  const bankAcctInfoList = reactive({
    data: [],
    total: 0
  });
  // 校验规则
  const rules = reactive({});
  //change事件
  const quotedDateChange = () => {
    applyDto.quoteInfo.length = 0;
    quoteInfoList.data.length = 0;
    if (row) {
      applyDto.quoteInfo.push(...row);
      quoteInfoList.data.push(...row);
    }
  };
  const queryBankAcctInfo = () => {
    applyDto.bankAcctInfo.length = 0;
    bankAcctInfoList.data.length = 0;
    bankAcctInfoList.total = 0;
    httpTool
      .post(queryBankAcctInfoUrl, {
        bankAcctIds: applyDto.bankAccountIds,
        applyDate: applyDto.applyDate
      })
      .then((res: any) => {
        if (res.success && res.data !== null) {
          applyDto.bankAcctInfo.push(...res.data);
          bankAcctInfoList.data.push(...res.data);
          bankAcctInfoList.total = res.data.length;
        }
      });
  };
  const counterpartyChange = (row: any) => {
    if (row) {
      applyDto.counterpartyCode = row.clientCode;
      applyDto.counterpartyName = row.clientName;
      applyDto.counterpartyTypeId = row.counterpartyTypeId;
      applyDto.counterpartyTypeName = row.counterpartyTypeName;
    } else {
      applyDto.counterpartyId = null;
      applyDto.counterpartyCode = "";
      applyDto.counterpartyName = "";
      applyDto.counterpartyTypeId = null;
      applyDto.counterpartyTypeName = "";
    }
  };
  const termChange = (value: string, info: any) => {
    applyDto.term = info.term;
    applyDto.termType = info.termType;
    applyDto.termShow = info.termShow;
    if (applyDto.startDate !== null && applyDto.startDate.length > 0) {
      //根据期限计算结束日期
      const currentTime = new Date(applyDto.startDate);
      if (applyDto.termType === termType.DAY) {
        const newTime = currentTime.setDate(currentTime.getDate() + applyDto.term);
        applyDto.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
      }
      if (applyDto.termType === termType.MONTH) {
        const newTime = currentTime.setMonth(currentTime.getMonth() + applyDto.term);
        applyDto.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
      }
      if (applyDto.termType === termType.YEAR) {
        const newTime = currentTime.setFullYearh(currentTime.getFullYear() + applyDto.term);
        applyDto.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
      }
    }
  };
  const startDateChange = (value: string) => {
    if (value && applyDto.term !== null && applyDto.term > 0) {
      //根据期限计算结束日期
      const currentTime = new Date(value);
      if (applyDto.termType === termType.DAY) {
        const newTime = currentTime.setDate(currentTime.getDate() + applyDto.term);
        applyDto.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
      }
      if (applyDto.termType === termType.MONTH) {
        const newTime = currentTime.setMonth(currentTime.getMonth() + applyDto.term);
        applyDto.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
      }
      if (applyDto.termType === termType.YEAR) {
        const newTime = currentTime.setFullYearh(currentTime.getFullYear() + applyDto.term);
        applyDto.endDate = formatDate(new Date(newTime), "yyyy-MM-dd");
      }
    }
  };
  const supplyFundFlagChange = () => {
    if (applyDto.supplyFundFlag === yesOrNo.NO) {
      applyDto.payBankAcctId = null;
      applyDto.payBankAcctNo = "";
      applyDto.payBankAcctName = "";
      applyDto.payBankId = null;
      applyDto.payBankCode = "";
      applyDto.payBankName = "";
      applyDto.payBankTypeId = null;
      applyDto.payBankTypeCode = "";
      applyDto.payBankTypeName = "";
      applyDto.paymentAmount = null;
      applyDto.remitInProvince = "";
      applyDto.remitInCity = "";
      applyDto.bankCnapsNo = "";
      applyDto.bankOrgNo = "";
      applyDto.bankExchangeNo = "";
    } else {
      if (applyDto.depositBankAcctId) {
        httpTool
          .post(getBankInfoById, { openBankId: applyDto.depositBankId }, { ignoreGlobalErrorTip: false })
          .then((res: any) => {
            if (res.success) {
              applyDto.remitInProvince = res.data.province;
              applyDto.remitInCity = res.data.city;
              applyDto.bankCnapsNo = res.data.CNAPSCode;
            }
          });
      }
    }
  };
  const payBankAccountChange = (row: any) => {
    if (row) {
      applyDto.payBankAcctNo = row.bankAccountCode;
      applyDto.payBankAcctName = row.bankAccountName;
      applyDto.payBankId = row.openBankId;
      applyDto.payBankCode = row.openBankCode;
      applyDto.payBankName = row.openBankName;
      applyDto.payBankTypeId = row.bankId;
      applyDto.payBankTypeCode = row.bankCode;
      applyDto.payBankTypeName = row.bankName;
    } else {
      applyDto.payBankAcctId = null;
      applyDto.payBankAcctNo = "";
      applyDto.payBankAcctName = "";
      applyDto.payBankId = null;
      applyDto.payBankCode = "";
      applyDto.payBankName = "";
      applyDto.payBankTypeId = null;
      applyDto.payBankTypeCode = "";
      applyDto.payBankTypeName = "";
    }
  };
  const depositBankAccountChange = (row: any) => {
    if (row) {
      applyDto.depositBankAcctNo = row.bankAccountNo;
      applyDto.depositBankAcctName = row.bankAccountName;
      applyDto.depositBankId = row.bankId;
      applyDto.depositBankCode = row.bankCode;
      applyDto.depositBankName = row.bankName;
      applyDto.depositBankTypeId = row.bankTypeId;
      applyDto.depositBankTypeCode = row.bankTypeCode;
      applyDto.depositBankTypeName = row.bankTypeName;
      if (applyDto.supplyFundFlag === yesOrNo.YES) {
        httpTool
          .post(getBankInfoById, { openBankId: applyDto.depositBankId }, { ignoreGlobalErrorTip: false })
          .then((res: any) => {
            if (res.success) {
              applyDto.remitInProvince = res.data.province;
              applyDto.remitInCity = res.data.city;
              applyDto.bankCnapsNo = res.data.CNAPSCode;
            }
          });
      }
    } else {
      applyDto.depositBankAcctId = null;
      applyDto.depositBankAcctNo = "";
      applyDto.depositBankAcctName = "";
      applyDto.depositBankId = null;
      applyDto.depositBankCode = "";
      applyDto.depositBankName = "";
      applyDto.depositBankTypeId = null;
      applyDto.depositBankTypeCode = "";
      applyDto.depositBankTypeName = "";
    }
  };
  const depositFixBankAccountChange = (row: any) => {
    if (row) {
      applyDto.depositFixBankAcctId = row.id;
      applyDto.depositFixBankAcctNo = row.bankAccountNo;
      applyDto.depositFixBankAcctName = row.bankAccountName;
      applyDto.depositFixBankId = row.bankId;
      applyDto.depositFixBankCode = row.bankCode;
      applyDto.depositFixBankName = row.bankName;
      applyDto.depositFixBankTypeId = row.bankTypeId;
      applyDto.depositFixBankTypeCode = row.bankTypeCode;
      applyDto.depositFixBankTypeName = row.bankTypeName;
    } else {
      applyDto.depositFixBankAcctId = null;
      applyDto.depositFixBankAcctNo = "";
      applyDto.depositFixBankAcctName = "";
      applyDto.depositFixBankId = null;
      applyDto.depositFixBankCode = "";
      applyDto.depositFixBankName = "";
      applyDto.depositFixBankTypeId = null;
      applyDto.depositFixBankTypeCode = "";
      applyDto.depositFixBankTypeName = "";
    }
  };
  const depositFixBankAccountQueryInput = (info: any) => {
    applyDto.depositFixBankAcctNo = info;
  };
  // 初始化方法
  const modifyCreate = (id: number) => {
    return httpTool.post(findByIdUrl, { id }, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        Object.assign(applyDto, res.data);
        termParams.currencyIds.push(res.data.currencyId);
        if (termRef.value !== undefined) {
          termRef.value.initRemoteData();
        }
        if (res.data.fileIds !== null) {
          applyDto.fileIdArr.push(...res.data.fileIds.split(","));
        }
        if (applyDto.checkStatus === checkStatus.SAVE) {
          httpTool.post(openDateUrl, {}, { ignoreGlobalErrorTip: false }).then((res1: any) => {
            if (res1.success) {
              applyDto.applyDate = res1.data.onlineDate;
            }
          });
        }
        quoteInfoList.data.push(...applyDto.quoteInfo);
        if (applyDto.bankAccountIds) {
          httpTool
            .post(queryBankAcctInfoUrl, {
              bankAcctIds: applyDto.bankAccountIds,
              applyDate: applyDto.applyDate
            })
            .then((res: any) => {
              if (res.success && res.data !== null) {
                applyDto.bankAcctInfo.push(...res.data);
                bankAcctInfoList.data.push(...res.data);
                bankAcctInfoList.total = res.data.length;
              }
            });
        }
      }
    });
  };
  //上传附件返回的数组信息
  const fileInfos = ref<fileIdList[]>([]);
  // 保存
  const saveInfo = () => {
    applyDto.fileIds = "";
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      applyDto.fileIds = fileInfos.value.map((item: fileIdList) => item.id).join(",");
    }
    return applyDto;
  };
  // 提交
  const submitInfo = () => {
    applyDto.fileIds = "";
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      applyDto.fileIds = fileInfos.value.map((item: fileIdList) => item.id).join(",");
    }
    return applyDto;
  };
  // 删除
  const removeInfo = () => {
    return applyDto;
  };
  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      applyDto.id = res.data.id;
      applyDto.businessCode = res.data.businessCode;
      applyDto.checkStatus = res.data.checkStatus;
      applyDto.version = res.data.version;
    }
  };
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate();
    if (!result) {
      return false;
    }
    if (applyDto.amount === 0) {
      FMessageBox.report(t("interbankdeposit.business.fixedopenapply.error2"));
      return false;
    }
    if (applyDto.supplyFundFlag === yesOrNo.YES && applyDto.payBankAcctId === applyDto.depositBankAcctId) {
      FMessageBox.report(t("interbankdeposit.business.fixedopenapply.error1"));
      return false;
    }
    if (applyDto.startDate > applyDto.endDate) {
      FMessageBox.report(t("interbankdeposit.business.fixedopenapply.error3"));
      return false;
    }
    return result;
  };
  // 构造审批时参数
  const postApprovalInfo = (params: any) => {
    let isApprovalPass = false;
    let transition = "";
    if (params.ifinanceWorkFlowDto.agreeChoose) {
      isApprovalPass = true;
      transition = params.ifinanceWorkFlowDto.agreeChoose;
    } else {
      transition = params.ifinanceWorkFlowDto.refuseChoose;
    }
    return {
      taskId: params.ifinanceWorkFlowDto.taskId,
      approvalPass: isApprovalPass,
      approvalContent: params.ifinanceWorkFlowDto.idea,
      approveMode: params.ifinanceWorkFlowDto.approveMode,
      transition: transition
    };
  };
  return {
    queryQuotenTable,
    queryBankAccountTable,
    applyDto,
    rules,
    quotedDateChange,
    queryBankAcctInfo,
    counterpartyChange,
    termRef,
    termParams,
    termChange,
    startDateChange,
    supplyFundFlagChange,
    payBankAccountChange,
    depositBankAccountChange,
    depositFixBankAccountChange,
    depositFixBankAccountQueryInput,
    formValidator,
    form,
    goBack,
    saveInfo,
    submitInfo,
    removeInfo,
    modifyCreate,
    saveSuccess,
    postApprovalInfo,
    upload,
    quoteInfoList,
    bankAcctInfoList,
    currencySymbol
  };
};
export default useModify;
