<template>
  <f-blank-scene :title="t('interbankdeposit.business.fixeddrawapply.addTitle')">
    <f-multi-form-panel ref="form" :model="applyDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.depositNo')" prop="depositNo" required>
          <f-magnifier-single
            :title="
              t('interbankdeposit.business.fixeddrawapply.depositNo') +
              t('interbankdeposit.business.fixeddrawapply.magnifier')
            "
            :url="depositNoMagnifierUrl"
            method="post"
            v-model="applyDto.depositNo"
            row-key="depositNo"
            row-label="depositNo"
            input-key="depositNo"
            auto-init
            @change="depositNoChange"
            :disabled="applyDto.checkStatus === checkStatus.SAVE"
          >
            <f-magnifier-column prop="depositNo" :label="t('interbankdeposit.business.fixeddrawapply.depositNo')" />
            <f-magnifier-column
              prop="openAmount"
              :label="t('interbankdeposit.business.fixeddrawapply.amount')"
              formatter="amount"
            />
            <f-magnifier-column
              prop="balance"
              :label="t('interbankdeposit.business.fixeddrawapply.drawBalance')"
              formatter="amount"
            />
            <f-magnifier-column
              prop="rate"
              :label="t('interbankdeposit.business.fixeddrawapply.depositRate') + '(%)'"
              formatter="rate"
            />
            <f-magnifier-column
              prop="recBankTypeName"
              :label="t('interbankdeposit.business.fixeddrawapply.depositBank')"
            />
            <f-magnifier-column prop="startDate" :label="t('interbankdeposit.business.fixeddrawapply.startDate')" />
            <f-magnifier-column prop="endDate" :label="t('interbankdeposit.business.fixeddrawapply.endDate')" />
            <f-magnifier-column prop="depositTermShow" :label="t('interbankdeposit.business.fixeddrawapply.term')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.office')" prop="officeName">
          <f-input v-model="applyDto.officeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.currency')" prop="currencyName">
          <f-input v-model="applyDto.currencyName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.applyDate')" prop="applyDate">
          <f-date-picker v-model="applyDto.applyDate" type="date" disabled />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.depositInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.counterpartyName')" prop="counterpartyName">
          <f-input v-model="applyDto.counterpartyName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixeddrawapply.counterpartyType')"
          prop="counterpartyTypeName"
        >
          <f-input v-model="applyDto.counterpartyTypeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.term')" prop="termShow">
          <f-input v-model="applyDto.termShow" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.startDate')" prop="startDate">
          <f-date-picker v-model="applyDto.startDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.endDate')" prop="endDate">
          <f-date-picker v-model="applyDto.endDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.amount')" prop="amount">
          <f-amount v-model="applyDto.amount" tooltip :negative="false" disabled :symbol="currencySymbol" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.upper')">
          <f-amount-chinese v-model="applyDto.amount" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.fixedRate')" prop="rate">
          <f-number v-model="applyDto.rate" negative tooltip is-rate disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.advanceRate')" prop="advanceRate">
          <f-number v-model="applyDto.advanceRate" negative tooltip is-rate disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawType')" prop="drawType">
          <f-scene-view :search="applyDto.drawType" :data="interBankDepositDrawType" params="value" label="label" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.drawInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawDate')" prop="drawDate" required>
          <f-date-picker v-model="applyDto.drawDate" type="date" :disabled-date="disabledDate" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawAmount')" prop="drawAmount" required>
          <f-amount
            v-model="applyDto.drawAmount"
            tooltip
            :negative="false"
            :max="*************.99"
            :symbol="currencySymbol"
            @change="drawAmountChange"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.upper')">
          <f-amount-chinese v-model="applyDto.drawAmount" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawBalance')" prop="drawBalance">
          <f-amount v-model="applyDto.drawBalance" tooltip :negative="false" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.depositBalance')" prop="depositBalance">
          <f-amount v-model="applyDto.depositBalance" tooltip :negative="false" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.remark')" prop="remark" :employ="2">
          <f-textarea v-model="applyDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.fileinfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.file')" :employ="3">
          <f-attm-upload ref="upload" drag multiple />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :before-trigger="formValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="submitInfo"
          :url="submitUrl"
          operate="submit"
          :before-trigger="formValidator"
          @close="submitSuccess"
        />
        <f-button type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.fixeddrawapply.back")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { depositNoMagnifierUrl, saveUrl, submitUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useAdd from "../hooks/useAdd";
import { onMounted } from "vue";
const { t } = useI18n();
const checkStatus = useConst("interbankdeposit.CheckStatus");
const interBankDepositDrawType = useConst("interbankdeposit.InterBankDepositDrawType");
const {
  applyDto,
  rules,
  formValidator,
  form,
  depositNoChange,
  drawAmountChange,
  saveInfo,
  submitInfo,
  saveSuccess,
  submitSuccess,
  goBack,
  getOpenDate,
  upload,
  currencySymbol,
  disabledDate
} = useAdd();
onMounted(() => {
  getOpenDate();
});
</script>
