import httpTool from "@/utils/http";
import type { ApplyDto, fileIdList } from "../types";
import { goPage } from "../hooks/usePage";
import { reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { openDateUrl } from "../url";
import { FMessageBox } from "@dtg/frontend-plus";
import { useCurrency } from "@/hooks/useCommon";
import { formatDate } from "@/utils/date";
export const useAdd = () => {
  const { t } = useI18n();
  const interBankDepositDrawType = useConst("interbankdeposit.InterBankDepositDrawType");
  const applyDto = reactive<ApplyDto>({
    id: null,
    businessCode: "",
    depositNo: "",
    officeId: null,
    officeCode: "",
    officeName: "",
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    applyDate: "",
    depositBankAcctId: null,
    depositBankAcctNo: "",
    depositBankAcctName: "",
    depositBankId: null,
    depositBankCode: "",
    depositBankName: "",
    depositBankTypeId: null,
    depositBankTypeCode: "",
    depositBankTypeName: "",
    depositFixBankAcctId: null,
    depositFixBankAcctNo: "",
    depositFixBankAcctName: "",
    depositFixBankId: null,
    depositFixBankCode: "",
    depositFixBankName: "",
    depositFixBankTypeId: null,
    depositFixBankTypeCode: "",
    depositFixBankTypeName: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    counterpartyTypeId: null,
    counterpartyTypeName: "",
    term: null,
    termType: "",
    termShow: "",
    startDate: "",
    endDate: "",
    amount: null,
    rate: null,
    advanceRate: null,
    countDays: "",
    drawType: "",
    drawDate: "",
    drawAmount: null,
    drawBalance: null,
    depositBalance: null,
    remark: "",
    checkStatus: "",
    fileIds: "",
    fileIdArr: [],
    version: null,
    creditFlag: ""
  });
  const { currencySymbol } = useCurrency(applyDto);
  // 校验规则
  const rules = reactive({});
  const form = ref();
  const upload = ref();
  //change事件
  const depositNoChange = (row: any) => {
    if (row) {
      applyDto.officeId = row.officeId;
      applyDto.officeCode = row.officeCode;
      applyDto.officeName = row.officeName;
      applyDto.currencyId = row.currencyId;
      applyDto.currencyCode = row.currencyCode;
      applyDto.currencyName = row.currencyName;
      applyDto.depositBankAcctId = row.recBankAcctId;
      applyDto.depositBankAcctNo = row.recBankAcctNo;
      applyDto.depositBankAcctName = row.recBankAcctName;
      applyDto.depositBankId = row.recOpenBankId;
      applyDto.depositBankCode = row.recOpenBankCode;
      applyDto.depositBankName = row.recOpenBankName;
      applyDto.depositBankTypeId = row.recBankTypeId;
      applyDto.depositBankTypeCode = row.recBankTypeCode;
      applyDto.depositBankTypeName = row.recBankTypeName;
      applyDto.depositFixBankAcctId = row.bankAcctId;
      applyDto.depositFixBankAcctNo = row.bankAcctNo;
      applyDto.depositFixBankAcctName = row.bankAcctName;
      applyDto.depositFixBankId = row.openBankId;
      applyDto.depositFixBankCode = row.openBankCode;
      applyDto.depositFixBankName = row.openBankName;
      applyDto.depositFixBankTypeId = row.bankTypeId;
      applyDto.depositFixBankTypeCode = row.bankTypeCode;
      applyDto.depositFixBankTypeName = row.bankTypeName;
      applyDto.counterpartyId = row.counterpartyId;
      applyDto.counterpartyCode = row.counterpartyCode;
      applyDto.counterpartyName = row.counterpartyName;
      applyDto.counterpartyTypeId = row.counterpartyTypeId;
      applyDto.counterpartyTypeName = row.counterpartyTypeName;
      applyDto.term = row.depositTerm;
      applyDto.termType = row.depositTermType;
      applyDto.termShow = row.depositTermShow;
      applyDto.startDate = row.startDate;
      applyDto.endDate = row.endDate;
      applyDto.amount = row.openAmount;
      applyDto.rate = row.rate;
      applyDto.advanceRate = row.advanceRate;
      applyDto.countDays = row.countDays;
      applyDto.drawType = row.drawType;
      applyDto.drawBalance = row.balance;
      applyDto.depositBalance = null;
      applyDto.creditFlag = row.creditFlag;
    } else {
      applyDto.depositNo = "";
      applyDto.officeId = null;
      applyDto.officeCode = "";
      applyDto.officeName = "";
      applyDto.currencyId = null;
      applyDto.currencyCode = "";
      applyDto.currencyName = "";
      applyDto.depositBankAcctId = null;
      applyDto.depositBankAcctNo = "";
      applyDto.depositBankAcctName = "";
      applyDto.depositBankId = null;
      applyDto.depositBankCode = "";
      applyDto.depositBankName = "";
      applyDto.depositBankTypeId = null;
      applyDto.depositBankTypeCode = "";
      applyDto.depositBankTypeName = "";
      applyDto.depositFixBankAcctId = null;
      applyDto.depositFixBankAcctNo = "";
      applyDto.depositFixBankAcctName = "";
      applyDto.depositFixBankId = null;
      applyDto.depositFixBankCode = "";
      applyDto.depositFixBankName = "";
      applyDto.depositFixBankTypeId = null;
      applyDto.depositFixBankTypeCode = "";
      applyDto.depositFixBankTypeName = "";
      applyDto.counterpartyId = null;
      applyDto.counterpartyCode = "";
      applyDto.counterpartyName = "";
      applyDto.counterpartyTypeId = null;
      applyDto.counterpartyTypeName = "";
      applyDto.term = null;
      applyDto.termType = "";
      applyDto.termShow = "";
      applyDto.startDate = "";
      applyDto.endDate = "";
      applyDto.amount = null;
      applyDto.rate = null;
      applyDto.advanceRate = null;
      applyDto.countDays = "";
      applyDto.drawType = "";
      applyDto.drawBalance = null;
      applyDto.depositBalance = null;
      applyDto.creditFlag = "";
    }
  };
  const drawAmountChange = (value: any) => {
    applyDto.depositBalance = Number((applyDto.drawBalance - value).toFixed(2));
  };
  //上传附件返回的数组信息
  const fileInfos = ref<fileIdList[]>([]);
  // 保存
  const saveInfo = () => {
    applyDto.fileIds = "";
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      applyDto.fileIds = fileInfos.value.map((item: fileIdList) => item.id).join(",");
    }
    return applyDto;
  };
  // 提交
  const submitInfo = () => {
    applyDto.fileIds = "";
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      applyDto.fileIds = fileInfos.value.map((item: fileIdList) => item.id).join(",");
    }
    return applyDto;
  };
  // 保存成功
  const saveSuccess = (res: any) => {
    if (res.success) {
      applyDto.id = res.data.id;
      applyDto.businessCode = res.data.businessCode;
      applyDto.checkStatus = res.data.checkStatus;
      applyDto.version = res.data.version;
    }
  };
  // 提交成功
  const submitSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };

  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate();
    if (!result) {
      return false;
    }
    if (applyDto.drawAmount === 0) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawapply.error2"));
      return false;
    }
    if (applyDto.drawDate < applyDto.applyDate) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawapply.error3"));
      return false;
    }
    if (applyDto.drawDate > applyDto.endDate) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawapply.error4"));
      return false;
    }
    if (applyDto.drawDate === applyDto.endDate) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawapply.error6"));
      return false;
    }
    if (applyDto.drawAmount > applyDto.drawBalance) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawapply.error5"));
      return false;
    }
    if (applyDto.drawType === interBankDepositDrawType.ADVANCE_DRAW && applyDto.depositBalance === 0) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawapply.error7"));
      return false;
    }
    return result;
  };
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl, {}, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        applyDto.applyDate = res.data.onlineDate;
        applyDto.drawDate = res.data.onlineDate;
      }
    });
  };

  // 禁用开机日之前的日期
  const disabledDate = (date: Date) => {
    if (!applyDto.applyDate) return false;
    const dateStr = formatDate(date);
    return dateStr < applyDto.applyDate;
  };

  return {
    applyDto,
    rules,
    formValidator,
    form,
    depositNoChange,
    drawAmountChange,
    saveInfo,
    submitInfo,
    saveSuccess,
    submitSuccess,
    goBack,
    getOpenDate,
    upload,
    currencySymbol,
    disabledDate
  };
};
export default useAdd;
