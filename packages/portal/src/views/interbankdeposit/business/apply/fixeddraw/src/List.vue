<template>
  <f-query-scene :title="t('interbankdeposit.business.fixeddrawapply.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="interbankdeposit-business-fixeddrawapply-query-001"
        table-comp-id="interbankdeposit-business-fixeddrawapply-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :form-data="queryFrom"
        show-header
        auto-reset
        :post-params="postParams"
        auto-init
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('interbankdeposit.business.fixeddrawapply.record')"
        :count-label-unit="t('interbankdeposit.business.fixeddrawapply.recordUnit')"
        :summation-biz-label="t('interbankdeposit.business.fixeddrawapply.record')"
        :summation-biz-unit="t('interbankdeposit.business.fixeddrawapply.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
      >
        <template #operate>
          <f-button type="primary" @click="add">{{ t("interbankdeposit.business.fixeddrawapply.add") }}</f-button>
          <f-submit-state
            :is-batch="true"
            :disabled="!isChecked"
            :gather-params="gatherBatchSumitParams"
            :url="batchSubmitUrl"
            @close="handleSearch"
            :operate-name="t('interbankdeposit.business.fixeddrawapply.batchSubmit')"
            :confirm-text="submitMessage"
            :before-trigger="beforeSubmitTrigger"
            :batch-confirm-map="submitRusultConfirm"
          />
          <f-submit-state
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="gatherBatchDeleteParams"
            :url="batchDeleteUrl"
            @close="handleSearch"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteRusultConfirm"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.office')" prop="officeIds">
            <f-select
              v-model="queryFrom.officeIds"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.currency')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              value-key="currencyId"
              label="currencyName"
              :url="currencyListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.businessCode')" prop="businessCode">
            <f-input v-model="queryFrom.businessCode" />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.depositNo')" prop="depositNo">
            <f-input v-model="queryFrom.depositNo" />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.status')" prop="checkStatus">
            <f-select
              v-model="queryFrom.checkStatus"
              :data="checkStatusEnum"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawAmount')" prop="drawAmount">
            <f-amount-range
              v-model="queryFrom.drawAmount"
              tooltip
              value-of-string
              :precision="2"
              symbol=" "
              :max="*************.99"
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.applyDate')" prop="applyDate">
            <f-lax-range-date-picker
              v-model="queryFrom.applyDate"
              :startDisabledDate="excuteDateStartControl(queryFrom, 'applyDate')"
              :endDisabledDate="excuteDateEndControl(queryFrom, 'applyDate')"
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.term')" prop="termShows">
            <f-select
              v-model="queryFrom.termShows"
              value-key="termShow"
              label="termShow"
              :url="termSetListUrl"
              :extra-data="{
                depositTypes: [interBankDepositType.FIXED_DEPOSIT],
                queryHisDataFlag: yesOrNo.YES
              }"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item
            :label="t('interbankdeposit.business.fixeddrawapply.depositOpenBank')"
            prop="depositBankTypeCodes"
          >
            <f-magnifier-multi
              :title="
                t('interbankdeposit.business.fixeddrawapply.depositOpenBank') +
                t('interbankdeposit.business.fixeddrawapply.magnifier')
              "
              :url="queryOpenBank"
              method="post"
              v-model="queryFrom.depositBankTypeCodes"
              row-key="referenceCode"
              row-label="name"
              input-key="codeOrName"
              selected-key="referenceCode"
              selected-label="name"
              auto-init
              :params="{
                isTopBank: 1
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column
                prop="referenceCode"
                :label="t('interbankdeposit.business.fixeddrawapply.openBankCode')"
              />
              <f-magnifier-column prop="name" :label="t('interbankdeposit.business.fixeddrawapply.openBankName')" />
            </f-magnifier-multi>
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="changeRow(row)">
            <!--撤销-->
            <template #suffix v-if="row.checkStatus === checkStatus.APPROVED">
              <f-submit-state
                :gather-params="
                  () => {
                    return row;
                  }
                "
                :url="cancelUrl"
                operate="cancel"
                :is-batch="false"
                :operate-name="t('interbankdeposit.business.fixeddrawapply.cancel')"
                confirm-text=" "
                link
                :icon="DtgCopy"
                :is-show-result-btn-group="false"
                :result-confirm="t('interbankdeposit.business.fixeddrawapply.cancelSuccess')"
                :result-title="t('interbankdeposit.business.fixeddrawapply.cancel')"
                @close="handleSearch"
                :beforeConfirm="beforeCancelConfirm"
              >
                <template #confirmEdit>
                  <f-multi-form-panel :model="row" ref="cancelFormRef">
                    <f-form-item
                      :label="t('interbankdeposit.business.fixeddrawapply.cancelReason')"
                      prop="refuseReason"
                      required
                    >
                      <f-input v-model="row.refuseReason" maxlength="150" />
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
              <f-process-tracking-dialog
                :params="{
                  recordId: row.id,
                  systemCode: 'Z46',
                  transType: 'Z460013',
                  agencyId: row.officeId,
                  currencyId: row.currencyId,
                  globalSerialNo: row.globalSerialNo
                }"
              />
            </template>
          </OperateButton>
        </template>
        <template #businessCode="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.businessCode }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import OperateButton from "@/components/operate-button/operate-button";
import useList from "../hooks/useList";
import Detail from "./components/Detail.vue";
import { useI18n } from "vue-i18n";
import { onMounted } from "vue";
import { useConst } from "@ifs/support";
import { useCommon } from "@/hooks/useCommon";
import { DtgCopy } from "@dtg/frontend-plus-icons";
import {
  officeListUrl,
  currencyListUrl,
  termSetListUrl,
  queryOpenBank,
  listUrl,
  exportUrl,
  batchSubmitUrl,
  batchDeleteUrl,
  cancelUrl
} from "../url";
const { excuteDateStartControl, excuteDateEndControl } = useCommon();
const { t } = useI18n();
const checkStatus = useConst("interbankdeposit.CheckStatus");
const checkStatusEnum = checkStatus.omitConst([checkStatus.DELETE]);
const interBankDepositType = useConst("interbankdeposit.InterBankDepositType");
const yesOrNo = useConst("interbankdeposit.YesOrNo");
const {
  tableColumns,
  selectableAll,
  handleSelect,
  add,
  isChecked,
  gatherBatchSumitParams,
  gatherBatchDeleteParams,
  generalButtonOption,
  changeRow,
  handleOpen,
  rowId,
  detail,
  queryFrom,
  handleSearch,
  queryTable,
  submitMessage,
  beforeSubmitTrigger,
  submitRusultConfirm,
  beforeDeleteTrigger,
  deleteRusultConfirm,
  clearSelection,
  allowSort,
  postParams,
  getOpenDate,
  cancelFormRef,
  beforeCancelConfirm
} = useList();
onMounted(() => {
  getOpenDate();
});
</script>
