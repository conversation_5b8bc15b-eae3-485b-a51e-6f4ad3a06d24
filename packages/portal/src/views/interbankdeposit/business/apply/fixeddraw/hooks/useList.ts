import httpTool from "@/utils/http";
import { reactive, shallowRef, ref, computed, nextTick } from "vue";
import type { ApplyDto } from "../types";
import { useI18n } from "vue-i18n";
import { goPage } from "./usePage";
import { openDateUrl, submitUrl, deleteUrl } from "../url";
import { useModelRange } from "@/hooks/conversion";
import { useConst } from "@ifs/support";

export const useList = () => {
  const { t } = useI18n();
  const cancelFormRef = shallowRef();
  const checkStatus = useConst("interbankdeposit.CheckStatus");
  const yesOrNo = useConst("interbankdeposit.YesOrNo");
  const rowId = ref<number>();
  const detail = shallowRef();
  const systemDate = ref<string>();
  //获取列表数据
  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      selectable(row: ApplyDto) {
        return [checkStatus.SAVE, checkStatus.REFUSE].includes(row?.checkStatus);
      },
      fixed: "left"
    },
    {
      width: "150px",
      prop: "businessCode",
      slots: { default: "businessCode" },
      label: t("interbankdeposit.business.fixeddrawapply.businessCode"),
      fixed: "left"
    },
    {
      width: "150px",
      prop: "depositNo",
      label: t("interbankdeposit.business.fixeddrawapply.depositNo")
    },
    {
      width: "150px",
      prop: "officeName",
      label: t("interbankdeposit.business.fixeddrawapply.office")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("interbankdeposit.business.fixeddrawapply.currency")
    },
    {
      width: "150px",
      prop: "applyDate",
      label: t("interbankdeposit.business.fixeddrawapply.applyDate")
    },
    {
      width: "200px",
      prop: "depositFixBankName",
      label: t("interbankdeposit.business.fixeddrawapply.depositBank")
    },
    {
      width: "150px",
      prop: "termShow",
      label: t("interbankdeposit.business.fixeddrawapply.term")
    },
    {
      width: "150px",
      prop: "amount",
      label: t("interbankdeposit.business.fixeddrawapply.amount"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "rate",
      label: t("interbankdeposit.business.fixeddrawapply.fixedRate") + "(%)",
      formatter: "rate",
      align: "right"
    },
    {
      width: "150px",
      prop: "startDate",
      label: t("interbankdeposit.business.fixeddrawapply.startDate")
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("interbankdeposit.business.fixeddrawapply.endDate")
    },
    {
      width: "150px",
      prop: "drawAmount",
      label: t("interbankdeposit.business.fixeddrawapply.drawAmount"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "advanceRate",
      label: t("interbankdeposit.business.fixeddrawapply.advanceRate") + "(%)",
      formatter: "rate",
      align: "right"
    },
    {
      width: "150px",
      prop: "drawDate",
      label: t("interbankdeposit.business.fixeddrawapply.drawDate")
    },
    {
      width: "150px",
      prop: "depositBalance",
      label: t("interbankdeposit.business.fixeddrawapply.depositBalance"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "checkStatus",
      label: t("interbankdeposit.business.fixeddrawapply.status"),
      formatter: { name: "const", const: "interbankdeposit.CheckStatus" }
    },
    {
      width: "150px",
      prop: "inputTime",
      label: t("interbankdeposit.business.fixeddrawapply.inputTime")
    },
    {
      width: "150px",
      prop: "failReason",
      label: t("interbankdeposit.business.fixeddrawapply.failReason")
    },
    {
      width: "220px",
      prop: "operate",
      label: t("interbankdeposit.business.fixeddrawapply.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];
  const allowSort = [
    "businessCode",
    "depositNo",
    "officeName",
    "currencyName",
    "applyDate",
    "depositFixBankName",
    "termShow",
    "amount",
    "rate",
    "startDate",
    "endDate",
    "drawAmount",
    "advanceRate",
    "drawDate",
    "depositBalance",
    "inputTime",
    "failReason"
  ];
  // 表格查询对象
  const queryFrom = reactive({
    // 机构
    officeIds: [],
    // 币种
    currencyIds: [],
    // 单据号
    businessCode: "",
    // 存单号
    depositNo: "",
    // 单据状态
    checkStatus: [],
    // 支取金额
    drawAmount: [],
    // 申请日期
    applyDate: [],
    // 期限
    termShows: [],
    // 存款银行开户大行集合
    depositBankTypeCodes: []
  });
  // 表格模板
  const queryTable = shallowRef();
  // 已选列表
  const checkedList = ref<ApplyDto[]>([]);
  // 抽屉模板
  const drawerRef = shallowRef();
  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length > 0);
  // 控制全选checkbox
  const selectableAll = (rows: ApplyDto[]) => {
    return !rows;
  };
  // 打开抽屉
  const handleOpen = (row: ApplyDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  // 点击修改
  const changeRow = (row: ApplyDto) => {
    goPage("modify", { id: row.id });
  };
  // 新增跳转
  const add = () => {
    goPage("add");
  };
  // 链接查找跳转
  const changeList = () => {
    goPage("list");
  };
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  // 批量提交的参数
  const gatherBatchSumitParams = () => {
    checkedList.value.forEach(row => {
      row.listOperateFlag = yesOrNo.YES;
    });
    return { listDto: checkedList.value };
  };
  // 批量删除的参数
  const gatherBatchDeleteParams = () => {
    return { listDto: checkedList.value };
  };
  // 勾选checkbox
  const handleSelect = (row: ApplyDto[]) => {
    checkedList.value = row;
  };
  // 列表操作处理
  const generalButtonOption = (row: ApplyDto) => {
    return [
      {
        type: "modify",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus)
      },
      {
        type: "submit",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: submitUrl,
          gatherParams: () => {
            row.listOperateFlag = yesOrNo.YES;
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      },
      {
        type: "remove",
        isShow: [checkStatus.SAVE, checkStatus.REFUSE].includes(row.checkStatus),
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ];
  };
  const submitMessage = ref("");
  const beforeSubmitTrigger = () => {
    submitMessage.value = t("interbankdeposit.business.fixeddrawapply.submitTip", [checkedList.value.length]);
    return true;
  };
  const submitRusultConfirm = {
    success: t("interbankdeposit.business.fixeddrawapply.submitSuccess"),
    fail: t("interbankdeposit.business.fixeddrawapply.submitFail")
  };
  const beforeDeleteTrigger = () => {
    submitMessage.value = t("interbankdeposit.business.fixeddrawapply.deleteTip", [checkedList.value.length]);
    return true;
  };
  const deleteRusultConfirm = {
    success: t("interbankdeposit.business.fixeddrawapply.deleteSuccess"),
    fail: t("interbankdeposit.business.fixeddrawapply.deleteFail")
  };
  const clearSelection = () => {
    checkedList.value.splice(0);
  };
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      systemDate.value = res.data.onlineDate;
    });
  };
  const beforeCancelConfirm = async () => {
    let result = true;
    result = await cancelFormRef.value.form.validate();
    if (!result) {
      return false;
    }
    return result;
  };
  const { postParams } = useModelRange(["drawAmount", "applyDate"]);
  return {
    tableColumns,
    queryFrom,
    queryTable,
    checkedList,
    drawerRef,
    isChecked,
    handleOpen,
    changeRow,
    add,
    changeList,
    handleSelect,
    handleSearch,
    selectableAll,
    rowId,
    detail,
    gatherBatchSumitParams,
    gatherBatchDeleteParams,
    generalButtonOption,
    submitMessage,
    beforeSubmitTrigger,
    submitRusultConfirm,
    beforeDeleteTrigger,
    deleteRusultConfirm,
    clearSelection,
    allowSort,
    postParams,
    getOpenDate,
    cancelFormRef,
    beforeCancelConfirm
  };
};
export default useList;
