<template>
  <f-blank-scene :title="t('interbankdeposit.business.fixeddrawapply.modifyTitle')">
    <f-multi-form-panel ref="form" :model="applyDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.depositNo')" prop="depositNo">
          <f-input v-model="applyDto.depositNo" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.office')" prop="officeName">
          <f-input v-model="applyDto.officeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.currency')" prop="currencyName">
          <f-input v-model="applyDto.currencyName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.applyDate')" prop="applyDate">
          <f-date-picker v-model="applyDto.applyDate" type="date" disabled />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.depositInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.counterpartyName')" prop="counterpartyName">
          <f-input v-model="applyDto.counterpartyName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixeddrawapply.counterpartyType')"
          prop="counterpartyTypeName"
        >
          <f-input v-model="applyDto.counterpartyTypeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.term')" prop="termShow">
          <f-input v-model="applyDto.termShow" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.startDate')" prop="startDate">
          <f-date-picker v-model="applyDto.startDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.endDate')" prop="endDate">
          <f-date-picker v-model="applyDto.endDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.amount')" prop="amount">
          <f-amount v-model="applyDto.amount" tooltip :negative="false" disabled :symbol="currencySymbol" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.upper')">
          <f-amount-chinese v-model="applyDto.amount" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.fixedRate')" prop="rate">
          <f-number v-model="applyDto.rate" negative tooltip is-rate disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.advanceRate')" prop="advanceRate">
          <f-number v-model="applyDto.advanceRate" negative tooltip is-rate disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawType')" prop="drawType">
          <f-scene-view :search="applyDto.drawType" :data="interBankDepositDrawType" params="value" label="label" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.drawInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawDate')" prop="drawDate" required>
          <f-date-picker v-model="applyDto.drawDate" type="date" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawAmount')" prop="drawAmount" required>
          <f-amount
            v-model="applyDto.drawAmount"
            tooltip
            :negative="false"
            :max="*************.99"
            :symbol="currencySymbol"
            @change="drawAmountChange"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.upper')">
          <f-amount-chinese v-model="applyDto.drawAmount" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.drawBalance')" prop="drawBalance">
          <f-amount v-model="applyDto.drawBalance" tooltip :negative="false" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.depositBalance')" prop="depositBalance">
          <f-amount v-model="applyDto.depositBalance" tooltip :negative="false" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.remark')" prop="remark" :employ="2">
          <f-textarea v-model="applyDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixeddrawapply.fileinfo')">
        <f-form-item :label="t('interbankdeposit.business.fixeddrawapply.file')" :employ="3">
          <f-attm-upload ref="upload" v-model="applyDto.fileIdArr" drag multiple />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="saveInfo"
          :url="saveUrl"
          operate="save"
          :before-trigger="formValidator"
          @close="saveSuccess"
        />
        <f-submit-state
          :gather-params="submitInfo"
          :url="submitUrl"
          operate="submit"
          :before-trigger="formValidator"
          @close="callBack"
        />
        <f-submit-state :gather-params="removeInfo" type="danger" :url="deleteUrl" operate="remove" @close="callBack" />
        <f-button v-if="!isSubmit" type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.fixeddrawapply.back")
        }}</f-button>
        <f-button v-if="isSubmit" type="info" plain @click.prevent="goSubmit">{{
          t("interbankdeposit.business.fixeddrawapply.back1")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { saveUrl, submitUrl, deleteUrl } from "../url";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { usePage } from "../hooks/usePage";
import useModify from "../hooks/useModify";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
const { t } = useI18n();
const router = useRouter();
const interBankDepositDrawType = useConst("interbankdeposit.InterBankDepositDrawType");
const {
  applyDto,
  rules,
  drawAmountChange,
  formValidator,
  form,
  saveInfo,
  submitInfo,
  removeInfo,
  saveSuccess,
  goBack,
  modifyCreate,
  upload,
  currencySymbol
} = useModify();
const { pageParams } = usePage();
//定义父组件传参, 参数不唯一，根据⻚面需要参数动态添加
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
// 页面初始化
onMounted(() => {
  if (pageParams) {
    modifyCreate(pageParams?.id);
  } else {
    modifyCreate(Number(props?.id));
    isSubmit.value = true;
  }
});
//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};
const callBack = (res: any) => {
  if (res.success) {
    if (isSubmit.value) {
      doBack(router, String(props.backUrl));
    } else {
      //返回列表页面
      goBack();
    }
  }
};
</script>
