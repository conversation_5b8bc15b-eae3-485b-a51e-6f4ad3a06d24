<template>
  <f-query-scene :title="t('interbankdeposit.business.fixeddrawregister.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="interbankdeposit-business-fixeddrawregister-query-001"
        table-comp-id="interbankdeposit-business-fixeddrawregister-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :form-data="queryFrom"
        show-header
        auto-reset
        :post-params="postParams"
        auto-init
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('interbankdeposit.business.fixeddrawregister.record')"
        :count-label-unit="t('interbankdeposit.business.fixeddrawregister.recordUnit')"
        :summation-biz-label="t('interbankdeposit.business.fixeddrawregister.record')"
        :summation-biz-unit="t('interbankdeposit.business.fixeddrawregister.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
      >
        <template #query-panel>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.office')" prop="officeIds">
            <f-select
              v-model="queryFrom.officeIds"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.currency')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              value-key="currencyId"
              label="currencyName"
              :url="currencyListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.businessCode')" prop="businessCode">
            <f-input v-model="queryFrom.businessCode" />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.depositNo')" prop="depositNo">
            <f-input v-model="queryFrom.depositNo" />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.businessStatus')" prop="businessStatus">
            <f-select
              v-model="queryFrom.businessStatus"
              :data="businessStatus.pickConst([businessStatus.PENDING_REGISTER, businessStatus.REGISTERED])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.drawAmount')" prop="drawAmount">
            <f-amount-range
              v-model="queryFrom.drawAmount"
              tooltip
              value-of-string
              :precision="2"
              symbol=" "
              :max="*************.99"
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.registerDate')" prop="registerDate">
            <f-lax-range-date-picker
              v-model="queryFrom.registerDate"
              :startDisabledDate="excuteDateStartControl(queryFrom, 'registerDate')"
              :endDisabledDate="excuteDateEndControl(queryFrom, 'registerDate')"
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixeddrawregister.term')" prop="termShows">
            <f-select
              v-model="queryFrom.termShows"
              value-key="termShow"
              label="termShow"
              :url="termSetListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :extra-data="{ depositTypes: ['FIXED_DEPOSIT'] }"
            />
          </f-form-item>
          <f-form-item
            :label="t('interbankdeposit.business.fixeddrawregister.depositBank')"
            prop="depositBankTypeCodes"
          >
            <f-magnifier-multi
              :title="
                t('interbankdeposit.business.fixeddrawregister.depositBank') +
                t('interbankdeposit.business.fixeddrawregister.magnifier')
              "
              :url="queryOpenBank"
              method="post"
              v-model="queryFrom.depositBankTypeCodes"
              row-key="referenceCode"
              row-label="name"
              input-key="codeOrName"
              selected-key="referenceCode"
              selected-label="name"
              auto-init
              :params="{
                isTopBank: 1
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column
                prop="referenceCode"
                :label="t('interbankdeposit.business.fixeddrawregister.openBankCode')"
              />
              <f-magnifier-column prop="name" :label="t('interbankdeposit.business.fixeddrawregister.openBankName')" />
            </f-magnifier-multi>
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-register="changeRow(row)" />
        </template>
        <template #businessCode="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.businessCode }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import OperateButton from "@/components/operate-button/operate-button";
import useList from "../hooks/useList";
import Detail from "./components/Detail.vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useEntrace } from "@/hooks/useEntrace.ts";
import { onMounted } from "vue";
import { useCommon } from "@/hooks/useCommon";
import { officeListUrl, currencyListUrl, termSetListUrl, queryOpenBank, listUrl, exportUrl } from "../url";
const { excuteDateStartControl, excuteDateEndControl } = useCommon();
const { t } = useI18n();
const businessStatus = useConst("common.BusinessStatus");
const {
  tableColumns,
  generalButtonOption,
  changeRow,
  handleOpen,
  rowId,
  detail,
  queryFrom,
  queryTable,
  allowSort,
  postParams,
  getOpenDate
} = useList();
onMounted(() => {
  getOpenDate();
});
const { onEnterPage } = useEntrace();
onEnterPage(urlParams => {
  queryFrom.businessStatus = [];
  queryFrom.businessStatus = [urlParams.businessStatus];
});
</script>
