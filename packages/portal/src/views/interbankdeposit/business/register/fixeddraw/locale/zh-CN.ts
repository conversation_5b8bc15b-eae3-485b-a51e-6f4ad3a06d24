export default {
  title: "定期支取登记-链接查找",
  addTitle: "定期支取登记",
  detailTitle: "定期支取登记-查看",
  back: "链接查找",
  operate: "操作",
  close: "关闭",
  register: "登记",
  cancelRegister: "取消登记",
  cancel: "取消",
  print: "打印",
  recordUnit: "条",
  record: "记录",
  baseInfo: "基础信息",
  businessCode: "单据号",
  depositNo: "存单号",
  registerDate: "登记日期",
  office: "机构",
  currency: "币种",
  depositAccountInfo: "存款账户信息",
  depositBankAcctNo: "存款银行活期账号",
  depositBankAcctName: "存款银行活期账户名称",
  depositBankName: "存款银行活期账户开户行",
  depositFixBankAcctNo: "存款银行定期账号",
  depositFixBankAcctName: "存款银行定期账户名称",
  depositFixBankName: "存款银行定期账户开户行",
  depositInfo: "存款信息",
  amount: "金额",
  upper: "金额大写",
  fixedRate: "定期利率",
  advanceRate: "提前支取利率",
  term: "期限",
  startDate: "开始日期",
  endDate: "到期日期",
  drawInfo: "支取信息",
  drawDate: "支取日期",
  drawAmount: "支取金额",
  drawRate: "支取利率",
  drawBalance: "可支取金额",
  depositBalance: "存单余额",
  calInterest: "匡算利息",
  paidInterest: "实收利息",
  recBankAcctNo: "收款银行账号",
  recBankAcctName: "收款账户名称",
  recBankName: "收款账户开户行",
  magnifier: "放大镜",
  bankAcctNo: "银行账号",
  bankAcctName: "银行账户名称",
  bankName: "银行账户开户行",
  bankInsRelInfo: "关联入账信息",
  oppAcctNo: "对方账号",
  oppAcctName: "对方账户名称",
  transAmount: "交易金额",
  transDate: "交易日期",
  fileinfo: "附件信息",
  file: "附件",
  depositBank: "存款银行",
  drawType: "支取类型",
  inputTime: "录入时间",
  businessStatus: "单据状态",
  transNo: "交易号",
  openBankCode: "开户行编号",
  openBankName: "开户行名称",
  counterpartyCode: "交易对手编号",
  counterpartyName: "交易对手名称",
  counterpartyType: "交易对手类型",
  registerMessage: "是否登记",
  registerSuccess: "登记成功",
  message: "关联交易明细的交易金额之和不等于利息金额与支取金额之和，请确认是否继续",
  makeSure: "继续",
  cancelRegisterMessage: "是否取消登记",
  cancelRegisterSuccess: "取消登记成功",
  error1: "登记日期只能早于等于开机日",
  error2: "支取日期不可晚于开机日",
  error3: "登记日期不可早于开始日期"
};
