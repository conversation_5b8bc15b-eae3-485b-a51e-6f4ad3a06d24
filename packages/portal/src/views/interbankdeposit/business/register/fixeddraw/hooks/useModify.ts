import httpTool from "@/utils/http";
import type { RegisterDto, fileIdList } from "../types";
import { goPage } from "../hooks/usePage";
import { reactive, ref, h } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { findByIdUrl, openDateUrl, calculateInterestUrl } from "../url";
import { FIcon, FMessageBox } from "@dtg/frontend-plus";
import { DtgConfirm } from "@dtg/frontend-plus-icons";
import { formatDate } from "@/utils/date";
import { useCurrency } from "@/hooks/useCommon";
import { add } from "@/utils/currency";
export const useModify = () => {
  const { t } = useI18n();
  const checkStatus = useConst("interbankdeposit.CheckStatus");
  const form = ref();
  const upload = ref();
  const registerDto = reactive<RegisterDto>({
    id: null,
    businessCode: "",
    depositNo: "",
    registerDate: "",
    officeId: null,
    officeCode: "",
    officeName: "",
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    depositBankAcctId: null,
    depositBankAcctNo: "",
    depositBankAcctName: "",
    depositBankId: null,
    depositBankCode: "",
    depositBankName: "",
    depositBankTypeId: null,
    depositBankTypeCode: "",
    depositBankTypeName: "",
    depositFixBankAcctId: null,
    depositFixBankAcctNo: "",
    depositFixBankAcctName: "",
    depositFixBankId: null,
    depositFixBankCode: "",
    depositFixBankName: "",
    depositFixBankTypeId: null,
    depositFixBankTypeCode: "",
    depositFixBankTypeName: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    counterpartyTypeId: null,
    counterpartyTypeName: "",
    term: null,
    termType: "",
    termShow: "",
    startDate: "",
    endDate: "",
    amount: null,
    rate: null,
    advanceRate: null,
    drawType: "",
    drawDate: "",
    drawRate: null,
    drawAmount: null,
    drawBalance: null,
    depositBalance: null,
    calInterest: null,
    calInterestStartDate: "",
    calInterestEndDate: "",
    paidInterest: null,
    recBankAcctId: null,
    recBankAcctNo: "",
    recBankAcctName: "",
    recBankId: null,
    recBankName: "",
    recBankTypeId: null,
    recBankTypeCode: "",
    recBankTypeName: "",
    fileIds: "",
    fileIdArr: [],
    checkStatus: "",
    version: null,
    bankInsRelList: [],
    executeDate: "",
    creditFlag: ""
  });
  const { currencySymbol } = useCurrency(registerDto);
  // 匡算利息
  const calculateInterest = () => {
    if (
      registerDto.drawDate !== null &&
      registerDto.drawDate.length > 0 &&
      registerDto.drawRate !== null &&
      registerDto.drawRate >= 0 &&
      registerDto.drawAmount !== null &&
      registerDto.drawAmount > 0
    ) {
      if (registerDto.drawDate === registerDto.startDate) {
        registerDto.calInterest = 0;
        registerDto.calInterestStartDate = registerDto.startDate;
        registerDto.calInterestEndDate = registerDto.drawDate;
        registerDto.paidInterest = 0;
      } else {
        httpTool
          .post(calculateInterestUrl, {
            bankAcctId: registerDto.depositFixBankAcctId,
            depositNo: registerDto.depositNo,
            calculateDate: registerDto.drawDate,
            calculateRate: registerDto.drawRate,
            calculateAmount: registerDto.drawAmount
          })
          .then((res: any) => {
            registerDto.calInterest = res.data.totalInterest;
            registerDto.paidInterest = res.data.totalInterest;
            registerDto.calInterestStartDate = res.data.calculateStartDate;
            registerDto.calInterestEndDate = res.data.calculateEndDate;
          });
      }
    }
  };
  // change
  const drawDateChange = () => {
    registerDto.bankInsRelList.length = 0;
    registerDto.bankInsRelIds.length = 0;
    calculateInterest();
  };
  const recBankAcctChange = (row: any) => {
    if (row) {
      registerDto.recBankAcctNo = row.bankAccountNo;
      registerDto.recBankAcctName = row.bankAccountName;
      registerDto.recBankId = row.bankId;
      registerDto.recBankName = row.bankName;
    } else {
      registerDto.recBankAcctId = null;
      registerDto.recBankAcctNo = "";
      registerDto.recBankAcctName = "";
      registerDto.recBankId = null;
      registerDto.recBankName = "";
    }
    registerDto.bankInsRelList.length = 0;
    registerDto.bankInsRelIds.length = 0;
  };
  const bankInsRelChange = (rows: any) => {
    registerDto.bankInsRelList.length = 0;
    const bankInsRelList: Array<any> = [];
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      bankInsRelList.push({
        bankInstructionId: row.id,
        acctId: row.accountId,
        acctNo: row.accountNo,
        acctName: row.accountName,
        bankName: row.bankTypeName,
        oppAcctId: row.oppAcctId,
        oppAcctNo: row.oppAccountNo,
        oppAcctName: row.oppAccountName,
        oppBankName: row.oppBranchName,
        transAmount: row.amount,
        transDate: formatDate(new Date(row.transactionTime)),
        bankInstructionUuid: row.uuid
      });
    }
    registerDto.bankInsRelList.push(...bankInsRelList);
  };
  // 初始化方法
  const modifyCreate = (id: number) => {
    return httpTool.post(findByIdUrl, { id }, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        Object.assign(registerDto, res.data);
        if (res.data.fileIds !== null) {
          registerDto.fileIdArr.push(...res.data.fileIds.split(","));
        }
        if (registerDto.checkStatus === checkStatus.SAVE) {
          httpTool.post(openDateUrl, {}, { ignoreGlobalErrorTip: false }).then((res1: any) => {
            if (res1.success) {
              registerDto.executeDate = res1.data.onlineDate;
            }
          });
        }
      }
    });
  };
  //上传附件返回的数组信息
  const fileInfos = ref<fileIdList[]>([]);
  // 登记
  const registerInfo = () => {
    registerDto.fileIds = "";
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      registerDto.fileIds = fileInfos.value.map((item: fileIdList) => item.id).join(",");
    }
    return registerDto;
  };
  // 登记成功
  const registerSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate();
    if (!result) {
      return false;
    }
    if (registerDto.registerDate > registerDto.executeDate) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawregister.error1"));
      return false;
    }
    if (registerDto.registerDate < registerDto.startDate) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawregister.error3"));
      return false;
    }
    if (registerDto.drawDate > registerDto.executeDate) {
      FMessageBox.report(t("interbankdeposit.business.fixeddrawregister.error2"));
      return false;
    }
    if (registerDto.bankInsRelList.length > 0) {
      let bankInsRelAmount = 0;
      registerDto.bankInsRelList.forEach(element => {
        bankInsRelAmount = add(bankInsRelAmount, element.transAmount);
      });
      if (bankInsRelAmount !== add(registerDto.drawAmount, registerDto.paidInterest)) {
        await FMessageBox.confirm(t("interbankdeposit.business.fixeddrawregister.message"), "Warning", {
          confirmButtonText: t("interbankdeposit.business.fixeddrawregister.makeSure"),
          cancelButtonText: t("interbankdeposit.business.fixeddrawregister.cancel"),
          title: "",
          customClass: "tree-card-message",
          buttonSize: "default",
          message: h("div", { class: "el-tree-card__messagebox" }, [
            h(FIcon, { size: 50 }, [h(DtgConfirm)]),
            h("span", null, t("interbankdeposit.business.fixeddrawregister.message"))
          ])
        });
        return true;
      }
    }
    return result;
  };
  // 构造审批时参数
  const postApprovalInfo = (params: any) => {
    let isApprovalPass = false;
    let transition = "";
    if (params.ifinanceWorkFlowDto.agreeChoose) {
      isApprovalPass = true;
      transition = params.ifinanceWorkFlowDto.agreeChoose;
    } else {
      transition = params.ifinanceWorkFlowDto.refuseChoose;
    }
    return {
      taskId: params.ifinanceWorkFlowDto.taskId,
      approvalPass: isApprovalPass,
      approvalContent: params.ifinanceWorkFlowDto.idea,
      approveMode: params.ifinanceWorkFlowDto.approveMode,
      transition: transition
    };
  };
  return {
    registerDto,
    drawDateChange,
    calculateInterest,
    recBankAcctChange,
    bankInsRelChange,
    formValidator,
    form,
    goBack,
    registerInfo,
    modifyCreate,
    registerSuccess,
    postApprovalInfo,
    upload,
    currencySymbol
  };
};
export default useModify;
