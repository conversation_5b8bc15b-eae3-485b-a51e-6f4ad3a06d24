import httpTool from "@/utils/http";
import { reactive, shallowRef, ref, nextTick } from "vue";
import type { InterBankDepositRegisterDto } from "../types";
import { useI18n } from "vue-i18n";
import { goPage } from "./usePage";
import { DtgEdit } from "@dtg/frontend-plus-icons";
import { useModelRange } from "@/hooks/conversion";
import { useConst } from "@ifs/support";
import { cancelRegisterUrl, openDateUrl } from "../url";

export const useList = () => {
  const { t } = useI18n();
  const businessStatus = useConst("common.BusinessStatus");
  const rowId = ref<number>();
  const detail = shallowRef();
  const openDate = ref<string>();
  //获取列表数据
  // 表格配置
  const tableColumns = [
    {
      width: "150px",
      prop: "businessCode",
      slots: { default: "businessCode" },
      label: t("interbankdeposit.business.fixedopenregister.businessCode"),
      fixed: "left"
    },
    {
      width: "150px",
      prop: "depositNo",
      label: t("interbankdeposit.business.fixedopenregister.depositNo")
    },
    {
      width: "150px",
      prop: "officeName",
      label: t("interbankdeposit.business.fixedopenregister.office")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("interbankdeposit.business.fixedopenregister.currency")
    },
    {
      width: "150px",
      prop: "termShow",
      label: t("interbankdeposit.business.fixedopenregister.term")
    },
    {
      width: "150px",
      prop: "amount",
      label: t("interbankdeposit.business.fixedopenregister.amount"),
      formatter: "amount",
      align: "right"
    },
    {
      width: "150px",
      prop: "startDate",
      label: t("interbankdeposit.business.fixedopenregister.startDate")
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("interbankdeposit.business.fixedopenregister.dueDate")
    },
    {
      width: "200px",
      prop: "counterpartyName",
      label: t("interbankdeposit.business.fixedopenregister.counterpartyName")
    },
    {
      width: "150px",
      prop: "rate",
      label: t("interbankdeposit.business.fixedopenregister.fixedRate") + "(%)",
      formatter: "rate",
      align: "right"
    },
    {
      width: "150px",
      prop: "registerDate",
      label: t("interbankdeposit.business.fixedopenregister.registerDate")
    },
    {
      width: "150px",
      prop: "businessStatus",
      label: t("interbankdeposit.business.fixedopenregister.businessStatus"),
      formatter: { name: "const", const: "common.BusinessStatus" }
    },
    {
      width: "150px",
      prop: "depositStatus",
      label: t("interbankdeposit.business.fixedopenregister.depositStatus"),
      formatter: { name: "const", const: "interbankdeposit.InterBankDepositStatus" }
    },
    {
      width: "150px",
      prop: "inputTime",
      label: t("interbankdeposit.business.fixedopenregister.inputTime")
    },
    {
      width: "220px",
      prop: "operate",
      label: t("interbankdeposit.business.fixedopenregister.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];
  const allowSort = [
    "businessCode",
    "depositNo",
    "officeName",
    "currencyName",
    "termShow",
    "amount",
    "startDate",
    "endDate",
    "counterpartyName",
    "rate",
    "registerDate",
    "inputTime"
  ];
  // 表格查询对象
  const queryFrom = reactive({
    // 单据号
    businessCode: "",
    // 机构
    officeIds: [],
    // 币种
    currencyIds: [],
    // 单据状态
    businessStatus: [],
    // 存单状态
    depositStatus: [],
    // 金额
    amount: [],
    // 存款银行活期账户
    depositBankAcctIds: [],
    // 期限
    termShows: [],
    // 交易对手
    counterpartyId: null
  });
  // 表格模板
  const queryTable = shallowRef();
  // 打开抽屉
  const handleOpen = (row: InterBankDepositRegisterDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  // 点击修改
  const changeRow = (row: InterBankDepositRegisterDto) => {
    goPage("modify", { id: row.id });
  };
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
  };
  // 列表操作处理
  const generalButtonOption = (row: InterBankDepositRegisterDto) => {
    return [
      {
        type: "register",
        originalProps: {
          icon: DtgEdit,
          type: "primary"
        },
        buttonText: t("interbankdeposit.business.fixedopenregister.register"),
        isShow: row.businessStatus === businessStatus.PENDING_REGISTER,
        emitName: "on-register"
      },
      {
        type: "cancelRegister",
        buttonText: t("interbankdeposit.business.fixedopenregister.cancelRegister"),
        isShow: row.businessStatus === businessStatus.REGISTERED && row.registerDate === openDate.value,
        submitComOpt: {
          url: cancelRegisterUrl,
          gatherParams: () => {
            return row;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          },
          props: {
            resultTitle: t("interbankdeposit.business.fixedopenregister.cancelRegister"),
            confirmText: t("interbankdeposit.business.fixedopenregister.cancelRegisterMessage"),
            operateName: t("interbankdeposit.business.fixedopenregister.cancelRegister"),
            resultConfirm: t("interbankdeposit.business.fixedopenregister.cancelRegisterSuccess"),
            icon: DtgEdit,
            type: "primary"
          }
        }
      }
    ];
  };
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl, {}, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        openDate.value = res.data.onlineDate;
      }
    });
  };
  const { postParams } = useModelRange(["amount"]);
  return {
    tableColumns,
    generalButtonOption,
    changeRow,
    handleOpen,
    rowId,
    detail,
    queryFrom,
    handleSearch,
    queryTable,
    allowSort,
    postParams,
    getOpenDate
  };
};
export default useList;
