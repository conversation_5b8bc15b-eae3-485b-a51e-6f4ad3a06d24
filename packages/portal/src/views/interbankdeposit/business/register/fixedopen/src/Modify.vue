<template>
  <f-blank-scene :title="t('interbankdeposit.business.fixedopenregister.addTitle')">
    <f-multi-form-panel ref="form" :model="registerDto" :rules="rules" :column="3">
      <f-panel :title="t('interbankdeposit.business.fixedopenregister.baseInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.businessCode')" prop="businessCode">
          <f-input v-model="registerDto.businessCode" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.office')" prop="officeName">
          <f-input v-model="registerDto.officeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.currency')" prop="currencyName">
          <f-input v-model="registerDto.currencyName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.applyBusinessCode')"
          prop="applyBusinessCode"
        >
          <f-input v-model="registerDto.applyBusinessCode" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.depositNo')" prop="depositNo" required>
          <f-input v-model="registerDto.depositNo" maxlength="30" />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.registerDate')"
          prop="registerDate"
          required
        >
          <f-date-picker v-model="registerDto.registerDate" type="date" :disabled-date="afterDisabled" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenregister.depositAccountInfo')">
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.depositBankAcctNo')"
          prop="depositBankAcctNo"
        >
          <f-input v-model="registerDto.depositBankAcctNo" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.depositBankAcctName')"
          prop="depositBankAcctName"
        >
          <f-input v-model="registerDto.depositBankAcctName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.depositBankName')" prop="depositBankName">
          <f-input v-model="registerDto.depositBankName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.depositFixBankAcctNo')"
          prop="depositFixBankAcctNo"
        >
          <f-input v-model="registerDto.depositFixBankAcctNo" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.depositFixBankAcctName')"
          prop="depositFixBankAcctName"
        >
          <f-input v-model="registerDto.depositFixBankAcctName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.depositFixBankName')"
          prop="depositFixBankName"
        >
          <f-input v-model="registerDto.depositFixBankName" disabled />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenregister.depositInfo')">
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.counterpartyName')" prop="counterpartyName">
          <f-input v-model="registerDto.counterpartyName" disabled />
        </f-form-item>
        <f-form-item
          :label="t('interbankdeposit.business.fixedopenregister.counterpartyType')"
          prop="counterpartyTypeName"
        >
          <f-input v-model="registerDto.counterpartyTypeName" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.term')" prop="termShow">
          <f-input v-model="registerDto.termShow" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.startDate')" prop="startDate">
          <f-date-picker v-model="registerDto.startDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.endDate')" prop="endDate">
          <f-date-picker v-model="registerDto.endDate" type="date" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.amount')" prop="amount">
          <f-amount v-model="registerDto.amount" tooltip :negative="false" :symbol="currencySymbol" disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.upper')">
          <f-amount-chinese v-model="registerDto.amount" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.fixedRate')" prop="rate">
          <f-number v-model="registerDto.rate" negative tooltip is-rate disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.advanceRate')" prop="advanceRate">
          <f-number v-model="registerDto.advanceRate" negative tooltip is-rate disabled />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.countDays')" prop="countDays">
          <f-scene-view :search="registerDto.countDays" :data="countDays" params="value" label="label" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.drawType')" prop="drawType">
          <f-scene-view :search="registerDto.drawType" :data="interBankDepositDrawType" params="value" label="label" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.supplyFundFlag')" prop="supplyFundFlag">
          <f-switch
            v-model="registerDto.supplyFundFlag"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
            disabled
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.rateType')" prop="rateType">
          <f-scene-view :search="registerDto.rateType" :data="interBankDepositRateType" params="value" label="label" />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.baseRateType')" prop="baseRateType">
          <f-scene-view
            :search="registerDto.baseRateType"
            :data="interBankDepositBaseRateType"
            params="value"
            label="label"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.interestType')" prop="interestType">
          <f-scene-view
            :search="registerDto.interestType"
            :data="interBankDepositInterestType"
            params="value"
            label="label"
          />
        </f-form-item>
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.remark')" prop="remark" :employ="2">
          <f-textarea v-model="registerDto.remark" :min-rows="3" maxlength="300" />
        </f-form-item>
      </f-panel>
      <f-panel :title="t('interbankdeposit.business.fixedopenregister.fileinfo')">
        <f-form-item :label="t('interbankdeposit.business.fixedopenregister.file')" :employ="3">
          <f-attm-upload ref="upload" v-model="registerDto.fileIdArr" drag multiple />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <div>
        <f-submit-state
          :gather-params="registerInfo"
          :url="registerUrl"
          operate="register"
          :operate-name="t('interbankdeposit.business.fixedopenregister.register')"
          :confirm-text="t('interbankdeposit.business.fixedopenregister.registerMessage')"
          :result-confirm="t('interbankdeposit.business.fixedopenregister.registerSuccess')"
          :before-trigger="formValidator"
          @close="registerSuccess"
        />
        <f-button type="info" plain @click.prevent="goBack">{{
          t("interbankdeposit.business.fixedopenregister.back")
        }}</f-button>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { registerUrl } from "../url";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { usePage } from "../hooks/usePage";
import useModify from "../hooks/useModify";
const { t } = useI18n();
const yesOrNo = useConst("interbankdeposit.YesOrNo");
const countDays = useConst("common.CountDays");
const interBankDepositDrawType = useConst("interbankdeposit.InterBankDepositDrawType");
const interBankDepositRateType = useConst("interbankdeposit.InterBankDepositRateType");
const interBankDepositBaseRateType = useConst("interbankdeposit.InterBankDepositBaseRateType");
const interBankDepositInterestType = useConst("interbankdeposit.InterBankDepositInterestType");
const {
  registerDto,
  rules,
  formValidator,
  form,
  registerInfo,
  registerSuccess,
  goBack,
  modifyCreate,
  upload,
  currencySymbol,
  afterDisabled
} = useModify();
const { pageParams } = usePage();
onMounted(() => {
  modifyCreate(pageParams?.id);
});
</script>
