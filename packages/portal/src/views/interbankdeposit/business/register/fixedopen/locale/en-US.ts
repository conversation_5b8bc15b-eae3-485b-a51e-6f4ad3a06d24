export default {
  title: "定期开立登记-链接查找",
  addTitle: "定期开立登记",
  detailTitle: "定期开立登记-查看",
  back: "链接查找",
  operate: "操作",
  close: "关闭",
  register: "登记",
  cancelRegister: "取消登记",
  print: "打印",
  recordUnit: "条",
  record: "记录",
  baseInfo: "基础信息",
  businessCode: "单据号",
  office: "机构",
  currency: "币种",
  applyBusinessCode: "申请单据号",
  depositNo: "存单号",
  registerDate: "登记日期",
  payAccountInfo: "付款账户信息",
  payBankAcctNo: "付款银行账号",
  payBankAcctName: "付款银行账户名称",
  payBankName: "付款银行账户开户行",
  depositAccountInfo: "存款账户信息",
  depositBankAcctNo: "存款银行活期账号",
  depositBankAcctName: "存款银行活期账户名称",
  depositBankName: "存款银行活期账户开户行",
  depositFixBankAcctNo: "存款银行定期账号",
  depositFixBankAcctName: "存款银行定期账户名称",
  depositFixBankName: "存款银行定期账户开户行",
  depositInfo: "存款信息",
  amount: "金额",
  upper: "金额大写",
  fixedRate: "定期利率",
  advanceRate: "提前支取利率",
  term: "期限",
  startDate: "开始日期",
  endDate: "结束日期",
  dueDate: "到期日期",
  counterpartyCode: "交易对手编号",
  counterpartyName: "交易对手名称",
  counterpartyType: "交易对手类型",
  countDays: "日利率计算天数",
  drawType: "支取方式",
  supplyFundFlag: "是否补充资金",
  rateType: "利率类型",
  baseRateType: "定价基准类型",
  interestType: "计息方式",
  remark: "备注",
  fileinfo: "附件信息",
  file: "附件",
  magnifier: "放大镜",
  bankAcctNo: "银行账号",
  bankAcctName: "银行账户名称",
  bankName: "银行账户开户行",
  businessStatus: "单据状态",
  depositStatus: "存单状态",
  failReason: "驳回/失败原因",
  inputTime: "录入时间",
  transNo: "交易号",
  registerMessage: "是否登记",
  registerSuccess: "登记成功",
  cancelRegisterMessage: "是否取消登记",
  cancelRegisterSuccess: "取消登记成功",
  message: "补充资金还未支付成功，是否确认登记",
  makeSure: "继续",
  cancellation: "取消",
  error1: "登记日期只能早于等于开机日"
};
