import httpTool from "@/utils/http";
import { useI18n } from "vue-i18n";
import { paymentApplyUrl } from "../url";
import type { RegisterDto, fileIdList } from "../types";
import { goPage } from "../hooks/usePage";
import { reactive, ref, h } from "vue";
import { useConst } from "@ifs/support";
import { findByIdUrl } from "../url";
import { FIcon, FMessageBox } from "@dtg/frontend-plus";
import { DtgConfirm } from "@dtg/frontend-plus-icons";
import { useCurrency } from "@/hooks/useCommon";
import { useOpenDate } from "@/hooks";
import { formatDate } from "@/utils/date";
export const useModify = () => {
  const { t } = useI18n();
  const { openDate } = useOpenDate();
  const yesOrNo = useConst("interbankdeposit.YesOrNo");
  const checkStatus = useConst("interbankdeposit.CheckStatus");
  const form = ref();
  const upload = ref();

  const registerDto = reactive<RegisterDto>({
    id: null,
    businessCode: "",
    applyBusinessCode: "",
    applyDate: "",
    depositNo: "",
    registerDate: "",
    officeId: null,
    officeCode: "",
    officeName: "",
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    payBankAcctId: null,
    payBankAcctNo: "",
    payBankAcctName: "",
    payBankId: null,
    payBankCode: "",
    payBankName: "",
    payBankTypeId: null,
    payBankTypeCode: "",
    payBankTypeName: "",
    depositBankAcctId: null,
    depositBankAcctNo: "",
    depositBankAcctName: "",
    depositBankId: null,
    depositBankCode: "",
    depositBankName: "",
    depositBankTypeId: null,
    depositBankTypeCode: "",
    depositBankTypeName: "",
    depositFixBankAcctId: null,
    depositFixBankAcctNo: "",
    depositFixBankAcctName: "",
    depositFixBankId: null,
    depositFixBankCode: "",
    depositFixBankName: "",
    depositFixBankTypeId: null,
    depositFixBankTypeCode: "",
    depositFixBankTypeName: "",
    counterpartyId: null,
    counterpartyCode: "",
    counterpartyName: "",
    counterpartyTypeId: null,
    counterpartyTypeName: "",
    term: null,
    termType: "",
    termShow: "",
    startDate: "",
    endDate: "",
    amount: null,
    rate: null,
    advanceRate: null,
    countDays: "",
    drawType: "",
    supplyFundFlag: "",
    rateType: "",
    baseRateType: "",
    interestType: "",
    remark: "",
    fileIds: "",
    fileIdArr: [],
    checkStatus: "",
    businessStatus: "",
    version: null,
    executeDate: "",
    creditFlag: ""
  });
  const { currencySymbol } = useCurrency(registerDto);
  // 校验规则
  const rules = reactive({});
  // 初始化方法
  const modifyCreate = (id: number) => {
    httpTool.post(findByIdUrl, { id }, { ignoreGlobalErrorTip: false }).then((res: any) => {
      if (res.success) {
        Object.assign(registerDto, res.data);
        if (res.data.fileIds !== null) {
          registerDto.fileIdArr.push(...res.data.fileIds.split(","));
        }
        if (registerDto.checkStatus === checkStatus.SAVE) {
          registerDto.executeDate = openDate.value;
        }
      }
    });
  };
  //上传附件返回的数组信息
  const fileInfos = ref<fileIdList[]>([]);
  // 登记
  const registerInfo = () => {
    registerDto.fileIds = "";
    fileInfos.value.push(...upload.value.fileData);
    if (fileInfos.value.length > 0) {
      registerDto.fileIds = fileInfos.value.map((item: fileIdList) => item.id).join(",");
    }
    return registerDto;
  };
  // 登记成功
  const registerSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  // 点击保存/提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form.value.form.validate();
    if (!result) {
      return false;
    }
    if (registerDto.registerDate > registerDto.executeDate) {
      FMessageBox.report(t("interbankdeposit.business.fixedopenregister.error1"));
      return false;
    }
    if (registerDto.supplyFundFlag === yesOrNo.YES) {
      const res = await httpTool.post(
        paymentApplyUrl,
        { frontBusinessType: "PAYMENT_FIXED_OPEN", frontApplyCode: registerDto.applyBusinessCode },
        { ignoreGlobalErrorTip: false }
      );
      if (res.success) {
        if (res.paymentStatus !== "EXECUTION_SUCCESS") {
          await FMessageBox.confirm(t("interbankdeposit.business.fixedopenregister.message"), "Warning", {
            confirmButtonText: t("interbankdeposit.business.fixedopenregister.makeSure"),
            cancelButtonText: t("interbankdeposit.business.fixedopenregister.cancellation"),
            title: "",
            customClass: "tree-card-message",
            buttonSize: "default",
            message: h("div", { class: "el-tree-card__messagebox" }, [
              h(FIcon, { size: 50 }, [h(DtgConfirm)]),
              h("span", null, t("interbankdeposit.business.fixedopenregister.message"))
            ])
          });
          return true;
        }
      }
    }
    return result;
  };

  const afterDisabled = (time: Date) => {
    if (openDate.value) {
      return new Date(formatDate(time)).getTime() > new Date(openDate.value).getTime();
    }
    return false;
  };

  return {
    registerDto,
    rules,
    formValidator,
    form,
    registerInfo,
    registerSuccess,
    goBack,
    modifyCreate,
    upload,
    currencySymbol,
    afterDisabled
  };
};
export default useModify;
