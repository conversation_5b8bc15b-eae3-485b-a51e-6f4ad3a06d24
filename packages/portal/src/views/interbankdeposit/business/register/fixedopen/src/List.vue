<template>
  <f-query-scene :title="t('interbankdeposit.business.fixedopenregister.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="interbankdeposit-business-fixedopenregister-query-001"
        table-comp-id="interbankdeposit-business-fixedopenregister-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :form-data="queryFrom"
        show-header
        auto-reset
        :post-params="postParams"
        auto-init
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('interbankdeposit.business.fixedopenregister.record')"
        :count-label-unit="t('interbankdeposit.business.fixedopenregister.recordUnit')"
        :summation-biz-label="t('interbankdeposit.business.fixedopenregister.record')"
        :summation-biz-unit="t('interbankdeposit.business.fixedopenregister.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
      >
        <template #query-panel>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.businessCode')" prop="businessCode">
            <f-input v-model="queryFrom.businessCode" />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.office')" prop="officeIds">
            <f-select
              v-model="queryFrom.officeIds"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.currency')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              value-key="currencyId"
              label="currencyName"
              :url="currencyListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.businessStatus')" prop="businessStatus">
            <f-select
              v-model="queryFrom.businessStatus"
              :data="businessStatus.pickConst([businessStatus.PENDING_REGISTER, businessStatus.REGISTERED])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.depositStatus')" prop="depositStatus">
            <f-select
              v-model="queryFrom.depositStatus"
              :data="depositStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.amount')" prop="amount">
            <f-amount-range
              v-model="queryFrom.amount"
              tooltip
              value-of-string
              :precision="2"
              symbol=" "
              :max="*************.99"
            />
          </f-form-item>
          <f-form-item
            :label="t('interbankdeposit.business.fixedopenregister.depositBankAcctNo')"
            prop="depositBankAcctIds"
          >
            <f-magnifier-multi
              :title="
                t('interbankdeposit.business.fixedopenregister.depositBankAcctNo') +
                t('interbankdeposit.business.fixedopenregister.magnifier')
              "
              :url="queryBankAccount"
              method="post"
              v-model="queryFrom.depositBankAcctIds"
              row-key="id"
              row-label="bankAccountNo"
              input-key="bankAccountNo"
              selected-key="bankAccountNo"
              selected-label="bankAccountNo"
              auto-init
              :params="{
                ownerType: 1,
                accountType: 0
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column
                prop="bankAccountNo"
                :label="t('interbankdeposit.business.fixedopenregister.bankAcctNo')"
              />
              <f-magnifier-column
                prop="bankAccountName"
                :label="t('interbankdeposit.business.fixedopenregister.bankAcctName')"
              />
              <f-magnifier-column prop="bankName" :label="t('interbankdeposit.business.fixedopenregister.bankName')" />
            </f-magnifier-multi>
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.term')" prop="termShows">
            <f-select
              v-model="queryFrom.termShows"
              value-key="termShow"
              label="termShow"
              :url="termSetListUrl"
              :extra-data="{
                depositTypes: [interBankDepositType.FIXED_DEPOSIT],
                queryHisDataFlag: yesOrNo.YES
              }"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('interbankdeposit.business.fixedopenregister.counterpartyName')" prop="counterpartyId">
            <f-magnifier-single
              :title="
                t('interbankdeposit.business.fixedopenregister.counterpartyName') +
                t('interbankdeposit.business.fixedopenregister.magnifier')
              "
              :url="counterPartyList"
              method="post"
              v-model="queryFrom.counterpartyId"
              row-key="id"
              query-key="clientId"
              row-label="clientName"
              input-key="codeOrName"
              auto-init
              :params="{
                clientClass: 3
              }"
            >
              <f-magnifier-column
                prop="clientCode"
                :label="t('interbankdeposit.business.fixedopenregister.counterpartyCode')"
              />
              <f-magnifier-column
                prop="clientName"
                :label="t('interbankdeposit.business.fixedopenregister.counterpartyName')"
              />
            </f-magnifier-single>
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-register="changeRow(row)" />
        </template>
        <template #businessCode="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.businessCode }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import OperateButton from "@/components/operate-button/operate-button";
import useList from "../hooks/useList";
import Detail from "./components/Detail.vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useEntrace } from "@/hooks/useEntrace.ts";
import { onMounted } from "vue";
import {
  officeListUrl,
  currencyListUrl,
  termSetListUrl,
  queryBankAccount,
  counterPartyList,
  listUrl,
  exportUrl
} from "../url";
const { t } = useI18n();
const businessStatus = useConst("common.BusinessStatus");
const depositStatus = useConst("interbankdeposit.InterBankDepositStatus");
const interBankDepositType = useConst("interbankdeposit.InterBankDepositType");
const yesOrNo = useConst("interbankdeposit.YesOrNo");
const {
  tableColumns,
  generalButtonOption,
  changeRow,
  handleOpen,
  rowId,
  detail,
  queryFrom,
  queryTable,
  allowSort,
  postParams,
  getOpenDate
} = useList();
onMounted(() => {
  getOpenDate();
});
const { onEnterPage } = useEntrace();
onEnterPage(urlParams => {
  queryFrom.businessStatus = [];
  queryFrom.businessStatus = [urlParams.businessStatus];
});
</script>
