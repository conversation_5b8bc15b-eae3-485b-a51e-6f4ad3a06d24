<template>
  <f-query-scene :title="t('interbankdeposit.business.accruleinterest.predrawInterestTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="subAccountId"
        query-comp-id="interbankdeposit-business-accruleinterest-query-001"
        table-comp-id="interbankdeposit-business-accruleinterest-table-001"
        :table-columns="state.column"
        :url="state.queryUrl"
        border
        :select-all="selectableAll"
        :form-data="queryFrom"
        :show-header="true"
        :show-print="false"
        auto-reset
        :auto-init="false"
        :export-url="state.exportUrl"
        :export-exclude="['operate']"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="handClearSelection"
        :countLabel="t('interbankdeposit.business.accruleinterest.record')"
        :countLabelUnit="t('interbankdeposit.business.accruleinterest.recordUnit')"
        :summation-biz-label="t('interbankdeposit.business.accruleinterest.record')"
        :summation-biz-unit="t('interbankdeposit.business.accruleinterest.recordUnit')"
        :allowSort="allowSort"
        :show-count-value="false"
        :show-summation-sum="false"
        tableType="Record"
        @query-table="queryOperate"
        @on-loaded="handleOnLoaded"
      >
        <template #extend-btns>
          <div>
            <f-button type="primary" @click="computerOperate()">
              {{ t("interbankdeposit.business.accruleinterest.computer") }}
            </f-button>
          </div>
        </template>
        <template #operate>
          <!--全额结息-->
          <f-submit-state
            v-if="buttonShow && allButtonShow"
            :is-batch="true"
            :disabled="!isSubmitChecked"
            :gather-params="gatherBatchParams"
            :url="predrawSaveUrl"
            @close="handleSearch"
            :operate-name="t('interbankdeposit.business.accruleinterest.batchSettle')"
            :confirm-text="submitMessage"
            :before-trigger="beforeSettleTrigger"
            :batch-confirm-map="settleResultConfirm"
            :need-extend-columns="batchOperateColumn"
            @submit-success="operateAfter"
            :is-show-result-btn-group="true"
            :is-custom-button="true"
          >
            <template #custom-button>
              <f-button type="primary" @click="errorExport()" :disabled="!exportFailButton">
                {{ t("interbankdeposit.business.accruleinterest.exportFailReason") }}
              </f-button>
            </template>
          </f-submit-state>
          <!--冲销结息-->
          <f-submit-state
            v-if="!buttonShow && allButtonShow"
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="predrawSaveUrl"
            @close="handleSearch"
            :operate-name="t('interbankdeposit.business.accruleinterest.writeOffSettle')"
            :confirm-text="submitMessage"
            :before-trigger="beforeWriteOffSettleTrigger"
            :batch-confirm-map="writeOffSettleResultConfirm"
            :need-extend-columns="batchOperateColumn"
            @submit-success="operateAfter"
            :is-show-result-btn-group="true"
            :is-custom-button="true"
          >
            <template #custom-button>
              <f-button type="primary" @click="errorExport()" :disabled="!exportFailButton">
                {{ t("interbankdeposit.business.accruleinterest.exportFailReason") }}
              </f-button>
            </template>
          </f-submit-state>
          <!--批量删除-->
          <f-submit-state
            v-if="batchDeleteShow"
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isDeleteChecked"
            :gather-params="gatherWritOffBatchParams"
            :url="batchPredrawDeleteUrl"
            @close="handleSearch"
            :operate-name="t('interbankdeposit.business.accruleinterest.batchDelete')"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteResultConfirm"
            :need-extend-columns="batchOperateColumn"
            @submit-success="operateAfter"
            :is-show-result-btn-group="true"
            :is-custom-button="true"
          >
            <template #custom-button>
              <f-button type="primary" @click="errorExport()" :disabled="!exportFailButton">
                {{ t("interbankdeposit.business.accruleinterest.exportFailReason") }}
              </f-button>
            </template>
          </f-submit-state>
        </template>
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.officeId')"
            prop="officeId"
            :required="true"
            ref="officeIdRef"
          >
            <f-select
              v-model="queryFrom.officeId"
              :url="getOfficeInfo"
              value-key="officeId"
              label="officeName"
              method="post"
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.currencyId')"
            prop="currencyId"
            :required="true"
            ref="currencyIdRef"
          >
            <f-select
              v-model="queryFrom.currencyId"
              :url="getCurrencyInfo"
              value-key="currencyId"
              label="currencyName"
              method="post"
            />
          </f-form-item>
          <!-- 业务类型 -->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.businessType')"
            prop="businessTypes"
            :required="true"
            ref="businessTypeRef"
          >
            <f-select
              v-model="queryFrom.businessTypes"
              :data="interbankInterestType"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--交易类型-->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.transactionType')"
            prop="operateType"
            :required="true"
            ref="operateTypeRef"
          >
            <f-select
              v-model="queryFrom.operateType"
              :data="interestOptSource.pickConst([interestOptSource.ACCRUAL, interestOptSource.WRITE_OFF])"
            />
          </f-form-item>
          <!--计提日-->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.accrualDate')"
            prop="accrualDate"
            :required="true"
            ref="interestDateRef"
          >
            <f-date-picker v-model="queryFrom.accrualDate" type="date" :disabled-date="preDateDisabledDate" />
          </f-form-item>
          <!--执行日-->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.executeDate')"
            prop="executeDate"
            :required="true"
            ref="executeDateRef"
          >
            <f-date-picker v-model="queryFrom.executeDate" type="date" disabled />
          </f-form-item>
          <!--开户银行-->
          <f-form-item :label="t('interbankdeposit.business.accruleinterest.bankName')" prop="openBankIds">
            <f-magnifier-multi
              :title="t('interbankdeposit.business.accruleinterest.openBankMagnifier')"
              :url="queryBankSetting"
              method="post"
              v-model="queryFrom.openBankIds"
              row-key="id"
              row-label="name"
              input-key="codeOrName"
              auto-init
              selected-key="code"
              selected-label="name"
            >
              <f-magnifier-column prop="code" :label="t('interbankdeposit.business.accruleinterest.bankCode')" />
              <f-magnifier-column prop="name" :label="t('interbankdeposit.business.accruleinterest.bankName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--银行账户号-->
          <f-form-item
            :label="t('interbankdeposit.business.accruleinterest.interbankBankAccountNo')"
            prop="bankAcctIds"
          >
            <f-magnifier-multi
              :title="t('interbankdeposit.business.accruleinterest.interbankBankAccountNoMagnifier')"
              :url="queryBankAccount"
              method="post"
              v-model="queryFrom.bankAcctIds"
              row-key="id"
              row-label="bankAccountNo"
              input-key="bankAccountNo"
              auto-init
              selected-key="bankAccountNo"
              selected-label="bankAccountName"
              :params="{
                ownerType: 1
              }"
            >
              <f-magnifier-column
                prop="bankAccountNo"
                :label="t('interbankdeposit.business.accruleinterest.bankAccountNo')"
              />
              <f-magnifier-column
                prop="bankAccountName"
                :label="t('interbankdeposit.business.accruleinterest.bankAccountName')"
              />
              <f-magnifier-column
                prop="bankName"
                :label="t('interbankdeposit.business.accruleinterest.openBankName')"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--存单号-->
          <f-form-item :label="t('interbankdeposit.business.accruleinterest.depositNo')" prop="subAccountIds">
            <f-magnifier-multi
              :title="t('interbankdeposit.business.accruleinterest.depositNoMagnifier')"
              :url="queryFixedDeposit"
              method="post"
              v-model="queryFrom.subAccountIds"
              row-key="id"
              row-label="depositNo"
              input-key="codeOrName"
              selected-key="bankAcctNo"
              selected-label="depositNo"
              :params="{
                businessType: interbankInterestType.FIXED_INTEREST
              }"
            >
              <f-magnifier-column
                prop="bankAcctNo"
                :label="t('interbankdeposit.business.accruleinterest.fixedBankAcctNo')"
              />
              <f-magnifier-column
                prop="bankAcctName"
                :label="t('interbankdeposit.business.accruleinterest.fixedBankAcctName')"
              />
              <f-magnifier-column
                prop="openBankName"
                :label="t('interbankdeposit.business.accruleinterest.fixedOpenBankName')"
              />
              <f-magnifier-column prop="depositNo" :label="t('interbankdeposit.business.accruleinterest.depositNo')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--约期协议号-->
          <f-form-item :label="t('interbankdeposit.business.accruleinterest.agreementCode')" prop="agreementSubAcctIds">
            <f-magnifier-multi
              :title="t('interbankdeposit.business.accruleinterest.agreementCodeMagnifier')"
              :url="queryFixedDeposit"
              method="post"
              v-model="queryFrom.agreementSubAcctIds"
              row-key="id"
              row-label="agreementCode"
              input-key="codeOrName"
              selected-key="agreementCode"
              selected-label="bankAcctNo"
              :params="{
                businessType: interbankInterestType.AGREE_INTEREST
              }"
            >
              <f-magnifier-column
                prop="bankAcctNo"
                :label="t('interbankdeposit.business.accruleinterest.agreementBankAcctNo')"
                :showOverflowTooltip="true"
              />
              <f-magnifier-column
                prop="bankAcctName"
                :label="t('interbankdeposit.business.accruleinterest.agreementBankAcctName')"
                :showOverflowTooltip="true"
              />
              <f-magnifier-column
                prop="openBankName"
                :label="t('interbankdeposit.business.accruleinterest.agreementOpenBankName')"
                :showOverflowTooltip="true"
              />
              <f-magnifier-column
                prop="agreementCode"
                :label="t('interbankdeposit.business.accruleinterest.agreementCode')"
                :showOverflowTooltip="true"
              />
              <f-magnifier-column
                prop="startDate"
                :label="t('interbankdeposit.business.accruleinterest.agreementStartDate')"
                :showOverflowTooltip="true"
              />
              <f-magnifier-column
                prop="endDate"
                :label="t('interbankdeposit.business.accruleinterest.agreementEndDate')"
                :showOverflowTooltip="true"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--是否直连-->
          <f-form-item :label="t('interbankdeposit.business.accruleinterest.directLinkFlag')" prop="directLinkFlag">
            <f-select
              v-model="queryFrom.directLinkFlag"
              :data="yesOrNo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--显示机制交易-->
          <f-form-item :label="t('interbankdeposit.business.accruleinterest.mechanismsFlag')" prop="mechanismsFlag">
            <f-select
              v-model="queryFrom.mechanismsFlag"
              :data="yesOrNo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--滤空-->
          <f-form-item :label="t('interbankdeposit.business.accruleinterest.filterFlag')" prop="filterFlag">
            <f-switch v-model="queryFrom.filterFlag" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" />
          </f-form-item>
          <f-form-item label=" " prop="desc" :employ="2">
            <span style="font-size: 14px">
              {{ t("interbankdeposit.business.accruleinterest.queryTip") }}
            </span>
          </f-form-item>
        </template>
        <template #totalAccruedInterest="{ row }">
          <f-button
            @click="handleOpen(row)"
            link
            type="primary"
            v-if="queryFrom.operateType === interestOptSource.ACCRUAL"
          >
            {{ format(row.interest) }}
          </f-button>
          <div v-else style="text-align: right; color: #ff9e00">
            {{ format(row.interest, { separator: ",", precision: 2, symbol: "" }) }}
          </div>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" />
        </template>
        <template #accrualOrWriteOffDate="{ row }">
          {{
            row.writeOffDate === null || row.writeOffDate === undefined || row.writeOffDate === ""
              ? row.accrualDate
              : row.writeOffDate
          }}
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :businessId="businessId" :segment-detail-table-data="segmentDetailTableData.data" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { useI18n } from "vue-i18n";
import Detail from "./components/SegmentDetail.vue";
import {
  getOfficeInfo,
  getCurrencyInfo,
  queryBankAccount,
  queryBankSetting,
  predrawSaveUrl,
  batchPredrawDeleteUrl,
  queryFixedDeposit
} from "../url";
import { useConst } from "@ifs/support";
import OperateButton from "@/components/operate-button/operate-button";
import { onMounted } from "vue";
import { format } from "@/utils/currency";

const { t } = useI18n();

//业务类型
const interbankInterestType = useConst("interbankdeposit.InterbankInterestBusinessType");
//是否
const yesOrNo = useConst("counter.YesOrNo");
//操作类型
const interestOptSource = useConst("interbankdeposit.InterestOperateType");

const {
  queryTable,
  state,
  selectableAll,
  queryFrom,
  handleSelect,
  isChecked,
  isSubmitChecked,
  gatherBatchParams,
  gatherWritOffBatchParams,
  handleSearch,
  generalButtonOption,
  handleOpen,
  segmentDetailTableData,
  businessId,
  detail,
  handClearSelection,
  submitMessage,
  beforeWriteOffSettleTrigger,
  beforeSettleTrigger,
  writeOffSettleResultConfirm,
  settleResultConfirm,
  allowSort,
  getOpenDate,
  computerOperate,
  buttonShow,
  queryOperate,
  allButtonShow,
  officeIdRef,
  currencyIdRef,
  businessTypeRef,
  interestDateRef,
  executeDateRef,
  operateTypeRef,
  batchDeleteShow,
  beforeDeleteTrigger,
  deleteResultConfirm,
  batchOperateColumn,
  operateAfter,
  preDateDisabledDate,
  isDeleteChecked,
  exportFailButton,
  errorExport,
  handleOnLoaded
} = useList();

onMounted(() => {
  getOpenDate();
});
</script>
