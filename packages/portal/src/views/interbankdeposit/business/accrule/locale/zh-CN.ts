export default {
  predrawInterestTitle: "同业利息计提",
  segmentInterestTile: "分段明细",
  predraw: "计提",
  writeOff: "冲销计提",
  query: "查询",
  queryList: "链接查找",
  delete: "删除",
  officeId: "机构",
  currencyId: "币种",
  doClose: "关闭",
  interestDate: "起息日",
  executeDate: "执行日",
  startDate: "开始日期",
  endDate: "结束日期",
  days: "天数",
  validatePredrawDate: "计提日期不能大于开机日+1天",
  errorInfo1: "跑批过程中，不能进行利息计提/结息",
  batchPredrawSuccess: "计提成功",
  batchPredrawFail: "计提失败",
  deleteSuccess: "删除成功",
  deleteFail: "删除失败",
  validateInterestOptSource: "请选择计提交易类型！",
  errorInfo2: "执行日必须为当前开机日！",
  recordUnit: "条",
  record: "记录",
  isFilter: "滤空",
  computer: "计算",
  operate: "操作",
  batchSettle: "计提",
  writeOffSettle: "冲销计提",
  batchSettleTip: "共勾选{0}条数据，是否全部计提？",
  settleSuccess: "计提成功",
  settleFail: "计提失败",
  settleWriteOffTip: "共勾选{0}条数据，是否全部冲销？",
  writeOffSuccess: "冲销成功",
  writeOffFail: "冲销失败",
  batchDeleteTip: "共勾选{0}条数据，是否全部删除？",
  batchDelete: "批量删除",
  accruedInterest: "计提利息",
  totleAccruedInterest: "累计计提利息",
  accruedOrWriteInterest: "计提/冲销利息",
  errorAlert1: "计算的执行日必须为当前开机日",
  businessType: "业务类型",
  bankAccountNo: "银行账户号",
  bankAccountName: "银行账户名称",
  bankAccountNoMarginifer: "银行账户号放大镜",
  bankCode: "开户银行编号",
  openBankMagnifier: "开户银行放大镜",
  bankName: "开户银行",
  depositNo: "定期存单号",
  depositNoMagnifier: "定期存单号放大镜",
  accrualDate: "计提日",
  isFilterEndDeposit: "是否过滤到期存单",
  directLinkFlag: "是否直连",
  transactionType: "交易类型",
  bankAcctNo: "存放同业账号",
  bankAcctName: "存放同业账号名称",
  bankDepositNo: "同业定期存单号",
  accountType: "业务类型",
  interestRate: "利率(%)",
  interest: "利息",
  totalInterest: "计提利息/累计已计提利息",
  transNo: "交易号",
  interestBalance: "计息余额",
  failReason: "失败原因",

  interbankBankAccountNo: "存放同业银行账号",
  interbankBankAccountNoMagnifier: "存放同业银行账号放大镜",
  openBankName: "开户行",
  fixedBankAcctNo: "定期账户账号",
  fixedBankAcctName: "定期账户名称",
  fixedOpenBankName: "定期账户开户行",
  mechanismsFlag: "显示机制交易",
  filterFlag: "滤空",
  exportFailReason: "导出失败原因",
  totalInterestBalance: "利息积数",
  interestType: "利息类型",
  agreementCode: "约期协议号",
  agreementCodeMagnifier: "约期协议号放大镜",
  agreementStartDate: "协议开始日",
  agreementEndDate: "协议结束日",
  writeOffDate: "计提/冲销日",
  accountTypeName: "账户类型",
  inputUserName: "录入人",
  agreementBankAcctNo: "约期账户账号",
  agreementBankAcctName: "约期账户名称",
  agreementOpenBankName: "约期账户开户行",
  queryTip: "存放同业定期到期后按照活期利率算息。（支取未支取均按照此规则计算利息）"
};
