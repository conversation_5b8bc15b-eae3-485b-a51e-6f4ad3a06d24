import { useI18n } from "vue-i18n";
import { reactive, shallowRef, ref, nextTick } from "vue";
import { useModelRange } from "@/hooks/conversion";
export const useList = () => {
  const { t } = useI18n();
  const rowId = ref<number>();
  const detail = shallowRef();
  // 表格配置
  const tableColumns = [
    {
      width: "200px",
      prop: "depositNo",
      slots: { default: "depositNo" },
      label: t("interbankdeposit.query.fixedquery.depositNo"),
      fixed: "left",
      isLayoutColumn: false
    },
    {
      width: "200px",
      prop: "openBusinessCode",
      label: t("interbankdeposit.query.fixedquery.openBusinessCode")
    },
    {
      width: "200px",
      prop: "officeName",
      label: t("interbankdeposit.query.fixedquery.office")
    },
    {
      width: "200px",
      prop: "currencyName",
      label: t("interbankdeposit.query.fixedquery.currency")
    },
    {
      width: "200px",
      prop: "counterpartyName",
      label: t("interbankdeposit.query.fixedquery.counterpartyName")
    },
    {
      width: "200px",
      prop: "amount",
      label: t("interbankdeposit.query.fixedquery.amount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "paidInterest",
      label: t("interbankdeposit.query.fixedquery.paidInterest"),
      formatter: { name: "amount", allowEmpty: true },
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "drawAmount",
      label: t("interbankdeposit.query.fixedquery.drawAmount"),
      formatter: { name: "amount", allowEmpty: true },
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "unDrawAmount",
      label: t("interbankdeposit.query.fixedquery.unDrawAmount"),
      formatter: { name: "amount", allowEmpty: true },
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "termShow",
      label: t("interbankdeposit.query.fixedquery.term")
    },
    {
      width: "200px",
      prop: "startDate",
      label: t("interbankdeposit.query.fixedquery.startDate")
    },
    {
      width: "200px",
      prop: "endDate",
      label: t("interbankdeposit.query.fixedquery.endDate")
    },
    {
      width: "200px",
      prop: "rate",
      label: t("interbankdeposit.query.fixedquery.rate") + "(%)",
      formatter: "rate",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "openRegisterDate",
      label: t("interbankdeposit.query.fixedquery.openRegisterDate")
    },
    {
      width: "200px",
      prop: "openBusinessStatus",
      label: t("interbankdeposit.query.fixedquery.openBusinessStatus"),
      formatter: { name: "const", const: "common.BusinessStatus" }
    },
    {
      width: "200px",
      prop: "drawRegisterDate",
      label: t("interbankdeposit.query.fixedquery.drawRegisterDate")
    },
    {
      width: "150px",
      prop: "depositStatus",
      label: t("interbankdeposit.query.fixedquery.depositStatus"),
      formatter: { name: "const", const: "interbankdeposit.InterBankDepositStatus" }
    }
  ];
  const allowSort = [
    "depositNo",
    "openBusinessCode",
    "officeName",
    "currencyName",
    "counterpartyName",
    "amount",
    "paidInterest",
    "drawAmount",
    "unDrawAmount",
    "termShow",
    "startDate",
    "endDate",
    "rate",
    "openRegisterDate",
    "drawRegisterDate"
  ];
  // 表格查询对象
  const queryFrom = reactive({
    //机构集合
    officeIds: [],
    //币种集合
    currencyIds: [],
    //存款银行
    depositBankTypeCodes: [],
    // 存单号
    depositNo: "",
    // 开立登记单据号
    openBusinessCode: "",
    // 开立登记日期
    openRegisterDate: [],
    // 支取登记日期
    drawRegisterDate: [],
    // 存单状态
    depositStatus: [],
    //金额
    amount: [],
    //期限
    termShows: [],
    //交易对手名称
    counterpartyId: null
  });
  // 表格模板
  const queryTable = shallowRef();
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
  };
  // 打开抽屉
  const handleOpen = (row: any) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  const { postParams } = useModelRange(["openRegisterDate", "drawRegisterDate", "amount"]);
  return {
    tableColumns,
    queryFrom,
    handleSearch,
    queryTable,
    postParams,
    allowSort,
    rowId,
    detail,
    handleOpen
  };
};
export default useList;
