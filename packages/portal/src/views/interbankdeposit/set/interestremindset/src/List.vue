<template>
  <f-query-scene :title="t('interbankdeposit.set.interestremindset.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="interbankdeposit-set-remindset-query-001"
        table-comp-id="interbankdeposit-set-remindset-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :form-data="queryFrom"
        show-header
        auto-reset
        auto-init
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
        :export-exclude="['code', 'operate']"
        :export-url="exportUrl"
        :count-label="t('interbankdeposit.set.interestremindset.record')"
        :count-label-unit="t('interbankdeposit.set.interestremindset.recordUnit')"
        :summation-biz-label="t('interbankdeposit.set.interestremindset.record')"
        :summation-biz-unit="t('interbankdeposit.set.interestremindset.recordUnit')"
        tile-panel
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
      >
        <template #operate>
          <f-button type="primary" @click="add">{{ t("interbankdeposit.set.interestremindset.add") }}</f-button>
          <f-submit-state
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="gatherBatchDeleteParams"
            :url="batchDeleteUrl"
            @close="handleSearch"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteResultConfirm"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('interbankdeposit.set.interestremindset.office')" prop="officeIds">
            <!--机构-->
            <f-select
              v-model="queryFrom.officeIds"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--币种-->
          <f-form-item :label="t('interbankdeposit.set.interestremindset.currency')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              value-key="currencyId"
              label="currencyName"
              :url="currencyListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--提醒类型-->
          <f-form-item
            :label="t('interbankdeposit.set.interestremindset.remindBusinessType')"
            prop="remindBusinessTypes"
          >
            <f-select
              v-model="queryFrom.remindBusinessTypes"
              :data="interbankInterestOperateType"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--到期是否提醒-->
          <f-form-item :label="t('interbankdeposit.set.interestremindset.remindFlagList')" prop="remindFlags">
            <f-select
              v-model="queryFrom.remindFlags"
              :data="yesOrNo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--提醒周期-->
          <f-form-item :label="t('interbankdeposit.set.interestremindset.remindType')" prop="remindTypes">
            <f-select
              v-model="queryFrom.remindTypes"
              :data="interbankInterestRemindType"
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!--提醒日期-->
          <f-form-item :label="t('interbankdeposit.set.interestremindset.remindDate')" prop="remindDates">
            <f-select v-model="queryFrom.remindDates" filterable multiple collapse-tags select-all>
              <f-option v-for="item in options" :key="item.key" :label="item.label" :value="item.key" />
            </f-select>
          </f-form-item>
        </template>
        <template #remind-type="{ row }">
          {{ row.remindFlag === yesOrNo.NO ? "" : remindType.valueToLabel(row.remindType) }}
        </template>
        <template #advance-day="{ row }">
          {{ row.remindFlag === yesOrNo.NO ? "" : row.advanceDay }}
        </template>
        <template #remind-day="{ row }">
          {{ row.remindFlag === yesOrNo.NO ? "" : row.remindDay }}
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="changeRow(row)" />
        </template>
        <template #code="{ row, $index }">
          <f-button @click="handleOpen(row)" link type="primary">{{ $index + 1 }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import Detail from "./components/Detail.vue";
import { useI18n } from "vue-i18n";
import { officeListUrl, currencyListUrl, listUrl, exportUrl, batchDeleteUrl } from "../url";
import OperateButton from "@/components/operate-button/operate-button";
import { useConst } from "@ifs/support";
import { useInterestRemindSet } from "../hooks/useInterestRemindSet";

const { t } = useI18n();

//提醒类型
const interbankInterestRemindType = useConst("interbankdeposit.InterbankInterestRemindType");
//是否
const yesOrNo = useConst("interbankdeposit.YesOrNo");
//利息操作类型
const interbankInterestOperateType = useConst("interbankdeposit.InterbankInterestOperateType");
const remindType = useConst("interbankdeposit.InterbankInterestRemindType");
const {
  tableColumns,
  selectableAll,
  handleSelect,
  add,
  generalButtonOption,
  changeRow,
  handleOpen,
  rowId,
  detail,
  queryFrom,
  queryTable,
  clearSelection,
  isChecked,
  gatherBatchDeleteParams,
  handleSearch,
  submitMessage,
  beforeDeleteTrigger,
  deleteResultConfirm,
  allowSort
} = useList();

const { buildDateSelect } = useInterestRemindSet();

const options = [];
buildDateSelect(options);
</script>
