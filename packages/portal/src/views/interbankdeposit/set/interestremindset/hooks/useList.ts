import { reactive, shallowRef, ref, computed, nextTick } from "vue";
import type { InterestRemindSetDto } from "../types";
import { useI18n } from "vue-i18n";
import { goPage } from "./usePage";
import { deleteUrl } from "../url";

export const useList = () => {
  const { t } = useI18n();

  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "150px",
      prop: "code",
      slots: { default: "code" },
      label: t("interbankdeposit.set.interestremindset.number")
    },
    {
      width: "150px",
      prop: "officeName",
      label: t("interbankdeposit.set.interestremindset.office")
    },
    {
      width: "150px",
      prop: "currencyName",
      label: t("interbankdeposit.set.interestremindset.currency")
    },
    {
      width: "150px",
      prop: "remindBusinessType",
      label: t("interbankdeposit.set.interestremindset.remindBusinessType"),
      formatter: { name: "const", const: "interbankdeposit.InterbankInterestOperateType" }
    },
    {
      width: "150px",
      prop: "remindFlag",
      label: t("interbankdeposit.set.interestremindset.remindFlagList"),
      formatter: { name: "const", const: "interbankdeposit.YesOrNo" }
    },
    {
      width: "150px",
      prop: "remindType",
      label: t("interbankdeposit.set.interestremindset.remindType"),
      slots: { default: "remind-type" },
      formatter: { name: "const", const: "interbankdeposit.InterbankInterestRemindType" }
    },
    {
      width: "150px",
      prop: "advanceDay",
      slots: { default: "advance-day" },
      label: t("interbankdeposit.set.interestremindset.remindDate")
    },
    {
      width: "150px",
      prop: "remindDay",
      slots: { default: "remind-day" },
      label: t("interbankdeposit.set.interestremindset.remindDays")
    },
    {
      width: "150px",
      prop: "modifyUserName",
      label: t("interbankdeposit.set.interestremindset.modifyUserName")
    },
    {
      width: "150px",
      prop: "modifyTime",
      label: t("interbankdeposit.set.interestremindset.modifyTime")
    },
    {
      width: "220px",
      prop: "operate",
      label: t("interbankdeposit.set.interestremindset.operate"),
      slots: { default: "buttons" },
      fixed: "right"
    }
  ];

  const allowSort = [
    "officeName",
    "currencyName",
    "remindBusinessType",
    "remindFlag",
    "remindType",
    "advanceDay",
    "remindDay",
    "modifyUserName",
    "modifyTime"
  ];

  // 表格查询对象
  const queryFrom = reactive({
    // 机构
    officeIds: [],
    // 币种
    currencyIds: [],
    //提醒类型
    remindBusinessTypes: [],
    //到期是否提醒
    remindFlags: [],
    //提醒周期
    remindTypes: [],
    //提醒日期
    remindDates: []
  });

  // 表格模板
  const queryTable = shallowRef();

  // 已选列表
  const checkedList = ref<InterestRemindSetDto[]>([]);

  // 抽屉模板
  const drawerRef = shallowRef();

  // 是否选中checkbox
  const isChecked = computed(() => checkedList.value.length > 0);

  // 控制全选checkbox
  const selectableAll = (rows: InterestRemindSetDto[]) => {
    return !rows;
  };

  const rowId = ref<number>();

  const detail = shallowRef();

  // 打开抽屉
  const handleOpen = (row: InterestRemindSetDto) => {
    rowId.value = row.id as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };

  // 点击修改
  const changeRow = (row: InterestRemindSetDto) => {
    goPage("modify", { id: row.id });
  };

  // 新增跳转
  const add = () => {
    goPage("add");
  };

  // 链接查找跳转
  const changeList = () => {
    goPage("list");
  };

  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
    checkedList.value.splice(0);
  };

  // 批量删除的参数
  const gatherBatchDeleteParams = () => {
    return { list: checkedList.value };
  };

  // 勾选checkbox
  const handleSelect = (row: InterestRemindSetDto[]) => {
    checkedList.value = row;
  };

  // 列表操作处理
  const generalButtonOption = (row: any) => {
    return [
      {
        type: "modify",
        isShow: true
      },
      {
        type: "remove",
        isShow: true,
        submitComOpt: {
          url: deleteUrl,
          gatherParams: () => {
            return row;
          },
          close: () => {
            handleSearch();
          }
        }
      }
    ];
  };

  const submitMessage = ref("");

  const beforeDeleteTrigger = () => {
    submitMessage.value = t("interbankdeposit.set.interestremindset.deleteTip", [checkedList.value.length]);
    return true;
  };

  const deleteResultConfirm = {
    success: t("interbankdeposit.set.interestremindset.deleteSuccess"),
    fail: t("interbankdeposit.set.interestremindset.deleteFail")
  };

  return {
    tableColumns,
    queryFrom,
    queryTable,
    checkedList,
    drawerRef,
    isChecked,
    handleOpen,
    changeRow,
    add,
    changeList,
    handleSelect,
    handleSearch,
    selectableAll,
    rowId,
    detail,
    gatherBatchDeleteParams,
    generalButtonOption,
    allowSort,
    submitMessage,
    beforeDeleteTrigger,
    deleteResultConfirm
  };
};
export default useList;
