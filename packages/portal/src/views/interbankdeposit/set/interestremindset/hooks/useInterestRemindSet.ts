import type { InterestRemindSetDto } from "../types";
import { reactive, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { FMessageBox } from "@dtg/frontend-plus";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useInterestRemindSet = () => {
  const { t } = useI18n();

  const {
    defaultOfficeId,
    defaultOfficeCode,
    defaultOfficeName,
    defaultCurrencyId,
    defaultCurrencyCode,
    defaultCurrencyName
  } = storeToRefs(useUserStoreHook());

  const yesOrNo = useConst("interbankdeposit.YesOrNo");
  //利息操作类型
  const interbankInterestOperateType = useConst("interbankdeposit.InterbankInterestOperateType");
  //提醒类型
  const interbankInterestRemindType = useConst("interbankdeposit.InterbankInterestRemindType");

  const interestRemindSetDto = reactive<InterestRemindSetDto>({
    id: null,
    version: null,
    officeId: defaultOfficeId.value,
    officeCode: defaultOfficeCode.value,
    officeName: defaultOfficeName.value,
    currencyId: defaultCurrencyId.value,
    currencyCode: defaultCurrencyCode.value,
    currencyName: defaultCurrencyName.value,
    remindBusinessType: interbankInterestOperateType.ACCRUAL, //提醒业务类型
    remindFlag: yesOrNo.YES, //是否提醒
    remindType: null, //提醒类型
    remindDate: null, //提醒日期
    advanceDay: null, //提前提醒天数
    remindDay: null //提醒天数
  });

  const officeChange = (value: any, info: any) => {
    interestRemindSetDto.officeCode = info.officeCode;
    interestRemindSetDto.officeName = info.officeName;
  };

  const currencyChange = (value: any, info: any) => {
    interestRemindSetDto.currencyCode = info.currencyCode;
    interestRemindSetDto.currencyName = info.currencyName;
  };

  const buildDateSelect = (options: any) => {
    for (let index = 0; index < 31; index++) {
      options.push({ key: index + 1, label: String(index + 1) });
    }
  };

  const form = shallowRef();

  const formValidator = async () => {
    const result = await form.value.form.validate();
    if (!result) {
      return false;
    }
    if (interestRemindSetDto.remindFlag === yesOrNo.YES) {
      if (interestRemindSetDto.remindType === undefined || interestRemindSetDto.remindType === null) {
        FMessageBox.report(t("interbankdeposit.set.interestremindset.remindDateTypePlaceHold"));
        return false;
      } else {
        if (interestRemindSetDto.remindType !== interbankInterestRemindType.DAY) {
          if (interestRemindSetDto.remindDate === null || interestRemindSetDto.remindDate === undefined) {
            FMessageBox.report(t("interbankdeposit.set.interestremindset.remindDatePlaceHold"));
            return false;
          }
        }
      }
      if (interestRemindSetDto.remindType !== interbankInterestRemindType.DAY) {
        if (interestRemindSetDto.advanceDay === undefined || interestRemindSetDto.advanceDay === null) {
          FMessageBox.report(t("interbankdeposit.set.interestremindset.advanceDayPlaceHold"));
          return false;
        }
        if (interestRemindSetDto.remindDay === undefined || interestRemindSetDto.remindDay === null) {
          FMessageBox.report(t("interbankdeposit.set.interestremindset.remindDayPlaceHold"));
          return false;
        }
        if (interestRemindSetDto.advanceDay < interestRemindSetDto.remindDay) {
          FMessageBox.report(t("interbankdeposit.set.interestremindset.remindDayComparePlaceHold"));
          return false;
        }
      }
    }
    return true;
  };

  const getInterBankRemindInfo = (info: InterestRemindSetDto, row: any) => {
    Object.assign(info, row.data);
  };

  const remindTypeChange = (value: any) => {
    if (value === interbankInterestRemindType.DAY) {
      interestRemindSetDto.remindDate = null;
      interestRemindSetDto.remindDate = null; //提醒日期
      interestRemindSetDto.advanceDay = null; //提前提醒天数
      interestRemindSetDto.remindDay = null; //提醒天数
    }
  };

  return {
    interestRemindSetDto,
    officeChange,
    currencyChange,
    buildDateSelect,
    form,
    formValidator,
    getInterBankRemindInfo,
    remindTypeChange
  };
};
