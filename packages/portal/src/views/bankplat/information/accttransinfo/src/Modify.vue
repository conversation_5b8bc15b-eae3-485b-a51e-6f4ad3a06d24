<template>
  <f-blank-scene :title="t('bankplat.information.accttransinfo.modify.modifyTitle')">
    <f-multi-form-panel ref="formRef" :model="basicInfoRef" :rules="rules" :column="3">
      <f-panel id="form1" :title="t('bankplat.information.accttransinfo.basicInformation')">
        <!-- 机构 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.officeLabel')" prop="officeName">
          <f-input v-model="basicInfoRef.officeName" :disabled="true" />
        </f-form-item>
        <!-- 币种 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.currencyLabel')" prop="currencyName">
          <f-input v-model="basicInfoRef.currencyName" :disabled="true" />
        </f-form-item>
      </f-panel>

      <f-panel id="form2" :title="t('bankplat.information.accttransinfo.ourInformationLabel')">
        <!-- 银行账号 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.accountNoLabel')" prop="accountNo">
          <f-input v-model="basicInfoRef.accountNo" :disabled="true" />
        </f-form-item>
        <!-- 账户名称 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.accountNameLabel')" prop="accountName">
          <f-input v-model="basicInfoRef.accountName" :disabled="true" />
        </f-form-item>
        <!-- 开户银行 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.openBankNameLabel')" prop="bankName">
          <f-input v-model="basicInfoRef.bankName" disabled />
        </f-form-item>
        <!-- 交易日期 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.transactionTimeLabel')" prop="transactionTime">
          <f-date-picker
            v-model="basicInfoRef.transactionTime"
            type="date"
            :placeholder="t('bankplat.information.accttransinfo.transactionTimePlaceHolder')"
            disabled
            :disabled-date="disabledDate"
            :default-value="getBeforeDate()"
          />
        </f-form-item>
      </f-panel>

      <f-panel id="form3" :title="t('bankplat.information.accttransinfo.scheduleOfDealingLabel')">
        <f-query-grid
          row-key="id"
          ref="queryTableRef"
          :table-columns="tableColumns"
          :url="acctTransInfQueryTransUrl"
          :params="{ approvalId: basicInfoRef.id }"
          :show-collapse="false"
          :show-summation-sum="false"
          :show-print="false"
          :auto-init="false"
          :showCount="true"
          :show-count-value="false"
          :showQueryPanel="false"
          :show-export="false"
          :showLayout="false"
          @select="handleSelect"
          @select-all="handleSelect"
          @clear-selection="clearSelection"
          autoInit
          :summation-biz-label="t('bankplat.information.accttransinfo.countLabel')"
          :summation-biz-unit="t('bankplat.information.accttransinfo.countUnitLabel')"
          :countLabel="t('bankplat.information.accttransinfo.countLabel')"
          :countLabelUnit="t('bankplat.information.accttransinfo.countUnitLabel')"
        >
          <template #operate>
            <f-button type="primary" @click="addTrans">{{ t("bankplat.information.accttransinfo.addBtn") }}</f-button>
            <f-submit-state
              operate="remove"
              type="danger"
              :disabled="!isChecked"
              :gather-params="gatherDelInfo"
              :url="acctTransInfoDelTransUrl"
              @close="handleSearch"
              compatible
            />
          </template>
          <!--一添加操作按钮-->
          <template #buttons="{ row }">
            <OperateButton :options="generalButtonOption(row)" @on-modify="changeRow(row)" />
          </template>
        </f-query-grid>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <!-- 提交 -->
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="acctTransInfoAddPostUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="goBack"
        v-if="!isSubmit"
      />
      <!-- 待提交提交 -->
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="acctTransInfoAddPostUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="goSubmit"
        v-if="isSubmit"
      />
      <f-submit-state
        :url="acctTransInfoDelApprovalUrl"
        :gather-params="delParams"
        operate="remove"
        type="danger"
        @close="goBack"
      />
      <f-button type="info" plain @click.prevent="goBack" v-if="!isSubmit">{{
        t("bankplat.information.accttransinfo.serachLinkBtn")
      }}</f-button>
      <!-- 待提交返回 -->
      <f-button type="info" plain @click.prevent="goSubmit" v-if="isSubmit">{{
        t("bankplat.information.accttransinfo.serachLinkBtn")
      }}</f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import {
  acctTransInfQueryTransUrl,
  acctTransInfoDelTransUrl,
  acctTransInfoAddPostUrl,
  acctTransInfoDelApprovalUrl
} from "@/views/bankplat/common/url";
import { useI18n } from "vue-i18n";
import {
  useAcctTransInfo,
  useAcctTransInfoRules
} from "@/views/bankplat/information/accttransinfo/hooks/useAcctTransInfo";
import useModify from "../hooks/useModify";
import { usePage } from "@/views/bankplat/information/accttransinfo/hooks/usePage";
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";

const { t } = useI18n();
const { basicInfoRef, getBeforeDate, disabledDate, restockModifyBasicInfoCreate } = useAcctTransInfo();
const rules = useAcctTransInfoRules();
const { pageParams } = usePage();
//待提交页面逻辑处理
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
const route = useRouter();
const goSubmit = () => {
  doBack(route, String(props.backUrl));
};

const {
  tableColumns,
  queryTableRef,
  isChecked,
  changeRow,
  handleSearch,
  handleSelect,
  clearSelection,
  generalButtonOption,
  formRef,
  gatherSaveInfo,
  goBack,
  addTrans,
  gatherDelInfo,
  formValidator,
  initData,
  delParams
} = useModify(basicInfoRef, restockModifyBasicInfoCreate, pageParams?.queryFrom);
// 页面初始化
onMounted(() => {
  if (props?.id) {
    initData(Number(props?.id));
    isSubmit.value = true;
  } else {
    if (pageParams) {
      initData(pageParams?.id);
    }
  }
});
</script>
