<template>
  <f-blank-scene :title="t('bankplat.information.accttransinfo.add.addTitle')">
    <f-multi-form-panel ref="formRef" :model="basicInfoRef" :rules="rules" :column="3">
      <f-panel id="form1" :title="t('bankplat.information.accttransinfo.basicInformation')">
        <!-- 机构 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.officeLabel')" prop="officeId">
          <f-select
            v-model="basicInfoRef.officeId"
            filterable
            auto-select
            value-key="key"
            label="value"
            :url="officeSelectorsUrl"
            method="post"
            :placeholder="t('bankplat.information.accttransinfo.officePlaceHolder')"
            @change="changeOffice"
          />
        </f-form-item>
        <!-- 币种 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.currencyLabel')" prop="currencyId">
          <f-select
            v-model="basicInfoRef.currencyId"
            auto-select
            ref="currencyRef"
            :placeholder="t('bankplat.information.accttransinfo.currencyPlaceHolder')"
            value-key="key"
            label="value"
            :url="currencySelectorsUrl"
            method="post"
            :extra-data="{
              officeId: basicInfoRef.officeId
            }"
            @change="changeCurrency"
          />
        </f-form-item>
      </f-panel>

      <f-panel id="form2" :title="t('bankplat.information.accttransinfo.ourInformationLabel')">
        <!-- 银行账号 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.accountNoLabel')" prop="accountNo">
          <f-magnifier-single
            v-model="basicInfoRef.accountNo"
            :title="t('bankplat.information.accttransinfo.accountNoGlassLabel')"
            :url="bankAccountMagnifierUrl"
            method="post"
            :placeholder="t('bankplat.information.accttransinfo.accountNoPlaceHolder')"
            row-key="accountNo"
            row-label="accountNo"
            input-key="condition"
            autoInit
            :params="{
              isDirectlink: 0,
              officeId: basicInfoRef.officeId,
              currencyId: basicInfoRef.currencyId,
              whetherIncludeCancleStatus: 0
            }"
            @change="accountNoChange"
            @clear="accountNoClear"
          >
            <f-magnifier-column prop="accountNo" :label="t('bankplat.information.accttransinfo.accountNoLabel')" />
            <f-magnifier-column prop="accountName" :label="t('bankplat.information.accttransinfo.accountNameLabel')" />
            <f-magnifier-column
              prop="openBankName"
              :label="t('bankplat.information.accttransinfo.openBankNameLabel')"
            />
            <f-magnifier-column
              prop="currencyInfoName"
              :label="t('bankplat.information.accttransinfo.currencyLabel')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 账户名称 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.accountNameLabel')" prop="accountName">
          <f-input v-model="basicInfoRef.accountName" :disabled="true" />
        </f-form-item>
        <!-- 开户银行 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.openBankNameLabel')" prop="bankName">
          <f-input v-model="basicInfoRef.bankName" disabled />
        </f-form-item>
        <!-- 交易日期 -->
        <f-form-item :label="t('bankplat.information.accttransinfo.transactionTimeLabel')" prop="transactionTime">
          <f-date-picker
            v-model="basicInfoRef.transactionTime"
            type="date"
            :placeholder="t('bankplat.information.accttransinfo.transactionTimePlaceHolder')"
            :disabled-date="disabledDate"
          />
        </f-form-item>
      </f-panel>

      <f-panel id="form3" :title="t('bankplat.information.accttransinfo.scheduleOfDealingLabel')">
        <f-query-grid
          row-key="id"
          ref="queryTableRef"
          :table-columns="tableColumns"
          :url="acctTransInfQueryTransUrl"
          :params="{ approvalId: basicInfoRef.id }"
          :show-collapse="false"
          :show-summation-sum="false"
          :show-print="false"
          :auto-init="false"
          :showCount="false"
          :showQueryPanel="false"
          :show-export="false"
          :showLayout="false"
          @select="handleSelect"
          @select-all="handleSelect"
          @clear-selection="clearSelection"
          autoInit
          :summation-biz-label="t('bankplat.information.accttransinfo.countLabel')"
          :summation-biz-unit="t('bankplat.information.accttransinfo.countUnitLabel')"
        >
          <template #operate>
            <f-button type="primary" @click="addTrans">{{ t("bankplat.information.accttransinfo.addBtn") }}</f-button>
            <f-submit-state
              operate="remove"
              type="danger"
              :disabled="!isChecked"
              :gather-params="gatherDeleteParams"
              :url="acctTransInfoDelTransUrl"
              @close="handleSearch"
              compatible
            />
          </template>
        </f-query-grid>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <!-- 保存-->
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="acctTransInfoAddSaveUrl"
        operate="save"
        :before-trigger="formValidator"
        @submit-success="success"
      />
      <!-- 提交-->
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="acctTransInfoAddPostUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="goBack"
      />
      <f-button type="info" plain @click.prevent="goBack">{{
        t("bankplat.information.accttransinfo.serachLinkBtn")
      }}</f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import {
  officeSelectorsUrl,
  bankAccountMagnifierUrl,
  currencySelectorsUrl,
  acctTransInfoAddSaveUrl,
  acctTransInfoDelTransUrl,
  acctTransInfQueryTransUrl,
  acctTransInfoAddPostUrl
} from "@/views/bankplat/common/url";
import { useI18n } from "vue-i18n";
import { useAcctTransInfo, useAcctTransInfoRules } from "../hooks/useAcctTransInfo";
import useAdd from "../hooks/useAdd";
import { usePage } from "../hooks/usePage";
import { onMounted } from "vue";
const { t } = useI18n();
const { basicInfoRef, disabledDate } = useAcctTransInfo();
//必输项校验
const rules = useAcctTransInfoRules();
const { pageParams } = usePage();
const {
  tableColumns,
  handleSearch,
  clearSelection,
  formRef,
  gatherSaveInfo,
  goBack,
  addTrans,
  formValidator,
  changeOffice,
  changeCurrency,
  accountNoChange,
  accountNoClear,
  currencyRef,
  success,
  isChecked,
  queryTableRef,
  initAddInfo,
  gatherDeleteParams,
  handleSelect,
  defaultOfficeId,
  defaultCurrencyId
} = useAdd(basicInfoRef, pageParams?.queryFrom);
//交易明细新增返回时初始化新增页面
onMounted(() => {
  basicInfoRef.officeId = defaultOfficeId.value;
  basicInfoRef.currencyId = defaultCurrencyId.value;
  initAddInfo(pageParams?.approvalId);
});
</script>
