import type { TransInfoTypeDTO } from "../types";
import { computed, ref, shallowRef } from "vue";
import { goPage } from "./usePage";
import { useI18n } from "vue-i18n";
import useApi from "@/views/bankplat/information/accttransinfo/hooks/useApi";
import { FMessageBox } from "@dtg/frontend-plus";
import { acctTransInfoDelTransUrl } from "@/views/bankplat/common/url";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useAdd = (basicInfoRef: any, queryFrom: any) => {
  const { t } = useI18n();
  const formRef = ref();
  const currencyRef = shallowRef();
  const { defaultOfficeId, defaultCurrencyId } = storeToRefs(useUserStoreHook());

  const { getDetailById } = useApi();
  // 返回列表页
  const goBack = () => {
    goPage("list", { queryFrom: queryFrom });
  };
  // 交易清单列表新增
  const addTrans = () => {
    if (basicInfoRef.id !== null) {
      getDetailById(basicInfoRef.id).then(res => {
        if (res.success && res.data.id !== null) {
          goPage("addTrans", { approvalId: basicInfoRef, queryFrom: queryFrom });
        } else {
          FMessageBox.alert(
            t("bankplat.information.accttransinfo.pleaseSaveMainPlaceHolder"),
            t("bankplat.information.accttransinfo.tipsLabel"),
            {
              confirmButtonText: "确定"
            }
          );
        }
      });
    } else {
      FMessageBox.alert(
        t("bankplat.information.accttransinfo.pleaseSaveMainPlaceHolder"),
        t("bankplat.information.accttransinfo.tipsLabel"),
        {
          confirmButtonText: "确定"
        }
      );
    }
  };
  // 初始化页面
  const initAddInfo = (id: number) => {
    if (id) {
      // 赋值查询列表
      basicInfoRef.id = id;
      // 渲染基本信息
      getDetailById(id).then((res: any) => {
        if (res.success) {
          basicInfoRef.officeId = res.data.officeId;
          basicInfoRef.currencyId = res.data.currencyId;
          basicInfoRef.accountNo = res.data.accountNo;
          basicInfoRef.accountName = res.data.accountName;
          basicInfoRef.bankName = res.data.bankName;
          basicInfoRef.transactionTime = res.data.transactionTime;
        }
      });
    }
  };
  //主表信息保存成功携带返回ID
  const success = (data: any) => {
    basicInfoRef.id = data;
    //赋值UUID
    getDetailById(basicInfoRef.id).then((resp: any) => {
      basicInfoRef.uuid = resp.data.uuid;
    });
  };
  // 已选列表
  const checkedListRef = ref<TransInfoTypeDTO[]>([]);
  // 是否选中checkbox
  const isChecked = computed(() => checkedListRef.value.length > 0);
  // 勾选checkbox
  const handleSelect = (row: TransInfoTypeDTO[]) => {
    checkedListRef.value = row;
  };
  // 取消选择
  const clearSelection = () => {
    checkedListRef.value = [];
  };
  // 表格模板
  const queryTableRef = shallowRef();
  // 列表查询
  const handleSearch = () => {
    queryTableRef.value.renderTableData();
    queryTableRef.value.clearSelection();
  };
  //获取批量删除参数
  const gatherDeleteParams = () => {
    const uuids = [] as any;
    const ids = [] as any;
    checkedListRef.value.forEach(item => {
      uuids.push(item.uuid);
      ids.push(item.id);
    });
    return { ids: ids, uuids: uuids };
  };

  // 列表操作列
  const generalButtonOption = (row: TransInfoTypeDTO) => {
    return [
      {
        type: "modify",
        isShow: true
      },
      {
        type: "remove",
        isShow: true,
        submitComOpt: {
          url: acctTransInfoDelTransUrl,
          gatherParams: (): any => {
            return { ids: Array.of(row.id), uuids: Array.of(row.uuid) };
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ];
  };

  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    return basicInfoRef;
  };
  // 点击保存按钮，弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };

  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    //本方账户号ID
    {
      width: "140px",
      prop: "accountNo",
      label: t("bankplat.information.accttransinfo.accountIdLabel")
    },
    //对方账号
    {
      width: "180px",
      prop: "oppAccountNo",
      label: t("bankplat.information.accttransinfo.oppAccountNoLabel")
    },
    //对方账户名称
    {
      width: "200px",
      prop: "oppAccountName",
      label: t("bankplat.information.accttransinfo.oppAccountNameLabel")
    },
    //交易日期
    {
      width: "140px",
      prop: "transactionTime",
      label: t("bankplat.information.accttransinfo.transactionTimeLabel"),
      formatter: "date"
    },
    //收付方向
    {
      width: "120px",
      prop: "direction",
      label: t("bankplat.information.accttransinfo.directionLabel"),
      formatter: { name: "const", const: "bankplat.Direction" }
    },
    //交易发生额
    {
      width: "140px",
      prop: "amount",
      formatter: "amount",
      label: t("bankplat.information.accttransinfo.amountLabel")
    },
    //摘要
    {
      width: "140px",
      prop: "abstractInfo",
      label: t("bankplat.information.accttransinfo.abstractInfoLabel")
    },
    {
      width: "220px",
      label: t("bankplat.information.accttransinfo.operateColLabel"),
      slots: { default: "buttons" },
      fixed: "right",
      prop: "operate"
    }
  ];

  //机构下拉选change事件
  const changeOffice = () => {
    basicInfoRef.accountId = null;
    basicInfoRef.accountNo = "";
    basicInfoRef.accountName = "";
    basicInfoRef.bankName = "";
    basicInfoRef.currencyId = null;
    currencyRef.value.initRemoteData();
  };
  //币种下拉选change事件
  const changeCurrency = () => {
    basicInfoRef.accountId = null;
    basicInfoRef.accountNo = "";
    basicInfoRef.accountName = "";
    basicInfoRef.bankName = "";
  };
  //银行账号放大镜修改事件
  const accountNoChange = (row: any) => {
    basicInfoRef.accountId = row.id;
    basicInfoRef.accountName = row.accountName;
    basicInfoRef.accountNo = row.accountNo;
    basicInfoRef.officeId = row.officeId;
    basicInfoRef.currencyId = row.currencyId;
    basicInfoRef.bankName = row.openBank.name;
  };
  //银行账号放大镜清空事件
  const accountNoClear = () => {
    basicInfoRef.accountId = null;
    basicInfoRef.accountNo = "";
    basicInfoRef.accountName = "";
    basicInfoRef.officeId = null;
    basicInfoRef.currencyId = null;
    basicInfoRef.bankName = "";
  };
  return {
    tableColumns,
    handleSearch,
    clearSelection,
    formRef,
    gatherSaveInfo,
    goBack,
    addTrans,
    formValidator,
    changeOffice,
    changeCurrency,
    accountNoChange,
    accountNoClear,
    currencyRef,
    success,
    isChecked,
    handleSelect,
    gatherDeleteParams,
    generalButtonOption,
    queryTableRef,
    initAddInfo,
    defaultOfficeId,
    defaultCurrencyId
  };
};

export default useAdd;
