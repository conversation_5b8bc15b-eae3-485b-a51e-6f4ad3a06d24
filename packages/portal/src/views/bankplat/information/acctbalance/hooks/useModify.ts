import { ref, reactive, shallowRef } from "vue";
import { goPage } from "./usePage";
import type { BalanceApproveType } from "../types";
import useApi from "./useApi";

export const useModify = (basicInfo: BalanceApproveType, restockModifyBasicInfoCreate: any) => {
  const formRef = ref();
  //币种符号，默认人民币
  const currencyRef = shallowRef();
  //加载币种额外请求参数
  const currencyExtraData = reactive({ officeId: basicInfo.officeId });
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  const goAdd = () => {
    goPage("add");
  };
  const afterSubmitClose = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };
  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    const saveInfo = { ...basicInfo };
    if (saveInfo.openningBalance === "") {
      saveInfo.openningBalance = 0;
    }
    return saveInfo;
  };

  // 点击保存按钮，弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  //币种下拉选change事件
  const changeCurrency = () => {
    basicInfo.accountId = null;
    basicInfo.accountNo = "";
    basicInfo.accountName = "";
    basicInfo.bankName = "";
  };
  //银行账号放大镜修改事件
  const accountNoChange = (row: any) => {
    if (row) {
      basicInfo.accountId = row.id;
      basicInfo.accountName = row.accountName;
      basicInfo.accountNo = row.accountNo;
      basicInfo.currencyId = row.currencyId;
      basicInfo.bankName = row.openBank.name;
    }
  };
  //银行账号放大镜清空事件
  const accountNoClear = () => {
    basicInfo.accountId = null;
    basicInfo.accountNo = "";
    basicInfo.accountName = "";
    basicInfo.currencyId = null;
    basicInfo.bankName = "";
  };
  //初始化数据
  const { getDetailById } = useApi();
  const initData = (id: number, source?: string) => {
    return getDetailById(id, source).then(res => {
      restockModifyBasicInfoCreate(basicInfo, res);
      currencyExtraData.officeId = basicInfo.officeId;
      currencyRef.value.initRemoteData();
    });
  };
  //获取删除参数
  const gatherDelInfo = () => {
    return { ids: [basicInfo.id], uuids: [basicInfo.uuid] };
  };

  // 构造审批时参数
  const postApprovalInfo = (params: any) => {
    let isApprovalPass = false;
    let transition = "";
    if (params.ifinanceWorkFlowDto.agreeChoose) {
      isApprovalPass = true;
      transition = params.ifinanceWorkFlowDto.agreeChoose;
    } else {
      transition = params.ifinanceWorkFlowDto.refuseChoose;
    }
    return {
      taskId: params.ifinanceWorkFlowDto.taskId,
      approvalPass: isApprovalPass,
      approvalContent: params.ifinanceWorkFlowDto.idea,
      approveMode: params.ifinanceWorkFlowDto.approveMode,
      transition: transition
    };
  };

  //保存成功
  const saveDataSuccess = () => {
    initData(basicInfo.id, "");
  };

  return {
    formRef,
    gatherSaveInfo,
    goBack,
    goAdd,
    formValidator,
    changeCurrency,
    accountNoChange,
    accountNoClear,
    afterSubmitClose,
    initData,
    gatherDelInfo,
    currencyRef,
    currencyExtraData,
    postApprovalInfo,
    saveDataSuccess
  };
};

export default useModify;
