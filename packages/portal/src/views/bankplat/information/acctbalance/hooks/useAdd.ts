import { ref, shallowRef, reactive } from "vue";
import { goPage } from "./usePage";
import type { BalanceApproveType } from "../types";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useAdd = (basicInfo: BalanceApproveType, emptyBasicInfo: BalanceApproveType) => {
  const formRef = ref();
  const currencyRef = shallowRef();
  const { defaultOfficeId, defaultCurrencyId } = storeToRefs(useUserStoreHook());
  //币种符号，默认人民币
  const defaultSymbol = "￥";
  const currencySymbol = ref<string>(defaultSymbol);
  //加载币种额外请求参数
  const currencyExtraData = reactive({ officeId: basicInfo.officeId });
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  const goAdd = () => {
    Object.assign(basicInfo, emptyBasicInfo);
    goPage("add");
  };
  const closePage = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };
  //保存成功，跳转修改页面
  const afterSaveClose = (res: any) => {
    if (res.success) {
      const id = res.data.id;
      const source = res.data.source;
      goPage("modify", { id, source });
    }
  };

  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    const saveInfo = { ...basicInfo };
    if (saveInfo.openningBalance === "") {
      saveInfo.openningBalance = 0;
    }
    return saveInfo;
  };

  // 点击保存按钮，弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  //机构下拉选change事件
  const changeOffice = (val: string) => {
    basicInfo.accountId = null;
    basicInfo.accountNo = "";
    basicInfo.accountName = "";
    basicInfo.bankName = "";
    basicInfo.currencyId = null;
    currencyExtraData.officeId = val;
    currencyRef.value.initRemoteData();
    currencySymbol.value = defaultSymbol;
  };
  //币种下拉选change事件
  const changeCurrency = (val: string, row: any) => {
    basicInfo.accountId = null;
    basicInfo.accountNo = "";
    basicInfo.accountName = "";
    basicInfo.bankName = "";
    currencySymbol.value = row.symbol;
  };
  //银行账号放大镜修改事件
  const accountNoChange = (row: any) => {
    if (row) {
      basicInfo.accountId = row.id;
      basicInfo.accountName = row.accountName;
      basicInfo.accountNo = row.accountNo;
      basicInfo.officeId = row.officeId;
      basicInfo.currencyId = row.currencyId;
      basicInfo.bankName = row.openBank.name;
      currencySymbol.value = row.currencyInfo.symbol;
    }
  };
  //银行账号放大镜清空事件
  const accountNoClear = () => {
    basicInfo.accountId = null;
    basicInfo.accountNo = "";
    basicInfo.accountName = "";
    basicInfo.officeId = null;
    basicInfo.currencyId = null;
    basicInfo.bankName = "";
    currencySymbol.value = defaultSymbol;
  };

  return {
    formRef,
    gatherSaveInfo,
    goBack,
    goAdd,
    formValidator,
    changeOffice,
    changeCurrency,
    accountNoChange,
    accountNoClear,
    afterSaveClose,
    currencyRef,
    closePage,
    currencySymbol,
    currencyExtraData,
    defaultOfficeId,
    defaultCurrencyId
  };
};

export default useAdd;
