export default {
  list: {
    pageTitle: "非直连交易修改-链接查找",
    statusLabel: "状态"
  },
  add: {
    addTitle: "非直连交易修改-新增"
  },
  transAdd: {
    transAddTitle: "交易清单-新增"
  },
  modify: {
    modifyTitle: "非直连交易修改-修改"
  },
  transModify: {
    transModifyTitle: "交易清单-修改"
  },
  detail: {
    detailTitle: "非直连交易修改-查看"
  },
  log: {
    title: "交易清单-查看",
    transCurrencyLabel: "交易币种"
  },
  approval: {
    approvalTitle: "非直连交易修改-审批",
    viewTitle: "非直连交易修改-查看"
  },
  officeLabel: "机构",
  officePlaceHolder: "请选择机构",
  currencyLabel: "币种",
  currencyPlaceHolder: "请选择币种",
  baseInfoTitle: "基础信息",
  ourOwnInfoTitle: "本方信息",
  targetInfoTitle: "对方信息",
  bankAccountNoLabel: "银行账号",
  bankAccountNoGlassLabel: "银行账号放大镜",
  bankAccountNoTitle: "银行账号",
  bankAccountNoPlaceHolder: "请选择银行账号",
  bankAccEnterPlaceHolder: "请输入银行账号",
  bankAccNamePlaceHolder: "请输入银行账号名称",
  bankAccountNameLabel: "账户名称",
  openBankLabel: "开户银行",
  openBankPlaceHolder: "请输入开户银行",
  openBankLocationLabel: "开户行所在地",
  locationPlaceHolder: "请输入开户行所在地",
  transactionTimeLabel: "交易日期",
  transDatePlaceHolder: "请选择交易日期",
  transInventoryTitle: "交易清单",
  countLabel: "记录",
  countUnitLabel: "条",
  doAddBtn: "新增",
  modifyBtn: "修改",
  deleteBtn: "删除",
  batchDeleteBtn: "批量删除",
  queryListBtn: "链接查找",
  operateColLabel: "操作",
  ownAccountIdLabel: "本方账号",
  oppAccountNoLabel: "对方账号",
  oppAccountNameLabel: "对方账户名称",
  transactionTimeDetailLabel: "交易发生日期",
  directionLabel: "收付方向",
  directionPlaceHolder: "请选择收付方向",
  transAmountLabel: "交易发生金额",
  abstractInfoLabel: "摘要",
  abstractInfoPlaceHolder: "请输入摘要",
  useInfoLabel: "用途",
  remarkInfoLabel: "备注",
  remarksPlaceHolder: "请输入备注",
  closeBtn: "关闭",
  transInfoTitle: "交易信息",
  executeBankLabel: "交易执行银行",
  executeBankPlaceHolder: "请输入交易执行银行",
  amountLabel: "金额",
  amountPlaceHolder: "请输入金额",
  amountBigLabel: "金额大写",
  usePlaceHolder: "请输入用途",
  goBackBtn: "返回",
  pleaseSaveMain: "需要先保存基本信息与本方信息",
  importTipMsg: "请先下载模板，在模板中填写完成后再进行上传，请勿修改模板格式",
  tips: "提示",
  approvalOpinion: "审批意见",
  codePatternPlaceHolder: "账号只能使用数字、字母和横杠（-只能出现在字母或数字之间）"
};
