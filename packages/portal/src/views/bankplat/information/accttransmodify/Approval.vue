<template>
  <div>
    <f-approval-scene
      :title="
        urlParams?.pageType === WorkDeskPageType.Approval
          ? t('bankplat.information.accttransmodify.approval.approvalTitle')
          : t('bankplat.information.accttransmodify.approval.viewTitle')
      "
      :allow-operate="urlParams?.pageType === WorkDeskPageType.Approval"
      :approval-agree-url="approvalAgreeUrl"
      :approval-refuse-url="approvalRefuseUrl"
      :post-approval-info="postApprovalInfo"
      :approval-params="state.approvalParams"
      :do-back="handleDoBack"
      :business-data="baseInfo"
    >
      <f-multi-form-panel :model="baseInfo" :column="3">
        <f-panel id="form1" :title="t('bankplat.information.accttransmodify.baseInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.officeLabel')">
            <f-input v-model="baseInfo.officeName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.currencyLabel')">
            <f-input v-model="baseInfo.currencyName" disabled />
          </f-form-item>
        </f-panel>
        <f-panel id="form2" :title="t('bankplat.information.accttransmodify.ourOwnInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')">
            <f-input v-model="baseInfo.accountNo" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')">
            <f-input v-model="baseInfo.accountName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')">
            <f-input v-model="baseInfo.openBankName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.transactionTimeLabel')" prop="transDate">
            <f-date-picker
              v-model="baseInfo.transDate"
              type="date"
              :placeholder="t('bankplat.information.accttransmodify.transDatePlaceHolder')"
              :size="size"
              :disabled="true"
            />
          </f-form-item>
        </f-panel>

        <f-panel id="form3" :title="t('bankplat.information.accttransmodify.transInventoryTitle')">
          <f-query-grid
            :summation-biz-label="t('bankplat.information.accttransmodify.countLabel')"
            :summation-biz-unit="t('bankplat.information.accttransmodify.countUnitLabel')"
            :show-collapse="false"
            :show-summation-sum="false"
            :show-print="false"
            :auto-init="false"
            :showCount="false"
            :showQueryPanel="false"
            :show-export="false"
            :showLayout="false"
            :table-columns="tableColumns"
            :url="pageQueryDetailUrl"
            :params="{ mainId: urlParams?.billId }"
            row-key="id"
            autoInit
          />
        </f-panel>
      </f-multi-form-panel>
    </f-approval-scene>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, watchEffect } from "vue";
import { getUrlSearchAll } from "@/utils/url";
import { doBack, WorkDeskPageType } from "@/utils/wfUtils";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { useAccTrans } from "./hooks/useAccTransModify";
import useApproval from "./hooks/useApproval";
//导入URL
import { pageQueryDetailUrl, approvalAgreeUrl, approvalRefuseUrl } from "./url/index";
const { t } = useI18n();
//审批流参数
const urlParams = getUrlSearchAll();
//路由
const router = useRouter();
const handleDoBack = () => {
  doBack(router, urlParams.backRoute);
};
const { baseInfo, initResultData } = useAccTrans();
const { initAddInfo, tableColumns, postApprovalInfo } = useApproval(baseInfo, initResultData);
// 页面初始化
onMounted(() => {
  initAddInfo(urlParams?.billId);
});
const state = reactive({
  approvalParams: {} as object
});
watchEffect(() => {
  if (baseInfo.id !== null) {
    state.approvalParams = {
      taskId: urlParams.taskId, // 任务id
      cancelType: urlParams.cancelType, // 审批类型
      approveMode: urlParams.approveMode, // 审批模式
      reviewTask: urlParams.reviewTask, // 是否复核节点
      nodeMark: urlParams.nodeMark, // 是否标记节点
      backRoute: urlParams.backRoute, // 返回路由
      billId: urlParams.billId, // 业务单据id
      systemCode: urlParams.systemCode, // 模块编号
      agencyId: baseInfo.officeId, // 组织ID(机构ID或成员单位ID)
      currencyId: baseInfo.currencyId, // 币种ID
      transType: "undirectly_trans_modify", // 业务类型
      recordId: baseInfo.id // 单据id
    };
  }
});
</script>
