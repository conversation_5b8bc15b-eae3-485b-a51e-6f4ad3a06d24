import { computed, ref, shallowRef } from "vue";
import { goPage } from "./usePage";
import { useI18n } from "vue-i18n";
import type { AccTransDetailDTO } from "../types";
import { transDetailDeleteUrl } from "../url/index";
import useApi from "../hooks/useApi";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";
import { FMessageBox } from "@dtg/frontend-plus";

export const useAdd = (basicInfo: any) => {
  const { t } = useI18n();
  const formRef = ref();
  const { defaultOfficeId, defaultCurrencyId } = storeToRefs(useUserStoreHook());
  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    return basicInfo;
  };
  // 点击校验规则按钮之前校验
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  //链接查找
  const goBack = () => {
    goPage("list");
  };
  const { getDetailInfo } = useApi();
  //跳转交易清单新增页面
  const transAdd = () => {
    //新增之前校验主单信息是否保存-为保存提示请先保存基本信息页面不跳转
    if (basicInfo.id !== null) {
      getDetailInfo(basicInfo.id).then(res => {
        if (res.success && res.data.id !== null) {
          //跳转新增交易清单页面
          goPage("transAdd", { mainInfo: basicInfo });
        } else {
          FMessageBox.alert(
            t("bankplat.information.accttransmodify.pleaseSaveMain"),
            t("bankplat.information.accttransmodify.tips"),
            {
              confirmButtonText: "OK"
            }
          );
        }
      });
    } else {
      FMessageBox.alert(
        t("bankplat.information.accttransmodify.pleaseSaveMain"),
        t("bankplat.information.accttransmodify.tips"),
        {
          confirmButtonText: "OK"
        }
      );
    }
  };
  const callBack = (res: any) => {
    if (res.success) {
      goBack();
    }
  };
  //初始化页面
  const initAddInfo = (mainId: number) => {
    //赋值查询列表
    basicInfo.id = mainId;
    //渲染基本信息
    getDetailInfo(mainId).then((res: any) => {
      if (res.success) {
        basicInfo.officeId = res.data.officeId;
        basicInfo.currencyId = res.data.currencyId;
        basicInfo.accountNo = res.data.accountNo;
        basicInfo.accountId = res.data.accountId;
        basicInfo.accountName = res.data.accountName;
        basicInfo.openBankName = res.data.openBankName;
        basicInfo.transDate = res.data.transDate;
      }
    });
  };
  const disabledDate = (time: Date) => {
    return time.getTime() > new Date(new Date().getTime() - 1000 * 3600 * 24);
  };

  //银行账号放大镜修改事件
  const accountNoChange = (row: any) => {
    basicInfo.accountId = row.id;
    basicInfo.accountName = row.accountName;
    basicInfo.accountNo = row.accountNo;
    basicInfo.openBankName = row.openBankName;
    //赋值基本信息
    basicInfo.officeId = row.officeId;
    basicInfo.currencyId = row.currencyId;
  };
  /*交易清单相关页面逻辑*/

  //保存成功-跳转修改页
  const saveDataSuccess = (res: any) => {
    if (res.success) {
      goPage("modify", { id: res.data });
    }
  };
  //勾选数据条数
  const checkNum = ref<number>();
  // 已选列表
  const checkedListRef = ref<AccTransDetailDTO[]>([]);
  // 是否选中checkbox
  const isChecked = computed(() => checkedListRef.value.length > 0);
  // 勾选checkbox
  const handleSelect = (row: AccTransDetailDTO[]) => {
    checkedListRef.value = row;
    checkNum.value = checkedListRef.value.length;
  };
  // 表格模板
  const queryTable = shallowRef();
  // 交易清单列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
  };
  const clearSelection = () => {
    checkedListRef.value = [];
  };
  //获取批量删除参数
  const getDelParams = () => {
    const deleteBatch = [];
    checkedListRef.value.forEach(item => {
      const params = {
        id: item.id,
        uuid: item.uuid
      };
      deleteBatch.push(params);
    });
    return deleteBatch;
  };
  //跳转交易清单修改页面
  const changeRow = (row: AccTransDetailDTO) => {
    goPage("transModify", { id: row.id });
  };
  //列表操作列
  const generalButtonOption = (row: AccTransDetailDTO) => {
    return [
      {
        type: "modify",
        isShow: true
      },
      {
        type: "remove",
        isShow: true,
        submitComOpt: {
          url: transDetailDeleteUrl,
          gatherParams: () => {
            const params = [
              {
                id: row.id,
                uuid: row.uuid
              }
            ];
            return params;
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ];
  };
  // 新增页面交易清单表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left"
    },
    {
      width: "200px",
      prop: "accountNo",
      label: t("bankplat.information.accttransmodify.ownAccountIdLabel")
    },
    {
      width: "200px",
      prop: "oppAccountNo",
      label: t("bankplat.information.accttransmodify.oppAccountNoLabel")
    },
    {
      width: "200px",
      prop: "oppAccountName",
      label: t("bankplat.information.accttransmodify.oppAccountNameLabel")
    },
    {
      width: "200px",
      prop: "transactionTime",
      label: t("bankplat.information.accttransmodify.transactionTimeDetailLabel"),
      formatter: "date"
    },
    {
      width: "100px",
      prop: "direction",
      label: t("bankplat.information.accttransmodify.directionLabel"),
      formatter: { name: "const", const: "bankplat.Direction" }
    },
    {
      width: "100px",
      prop: "amount",
      label: t("bankplat.information.accttransmodify.transAmountLabel"),
      formatter: "amount"
    },
    {
      width: "100px",
      prop: "abstractInfo",
      label: t("bankplat.information.accttransmodify.abstractInfoLabel")
    },
    {
      width: "180px",
      slots: { default: "buttons" },
      prop: "operate",
      label: t("bankplat.information.accttransmodify.operateColLabel")
    }
  ];
  return {
    formRef,
    gatherSaveInfo,
    formValidator,
    disabledDate,
    goBack,
    transAdd,
    accountNoChange,
    tableColumns,
    handleSearch,
    handleSelect,
    isChecked,
    getDelParams,
    generalButtonOption,
    changeRow,
    saveDataSuccess,
    initAddInfo,
    queryTable,
    callBack,
    defaultOfficeId,
    defaultCurrencyId,
    clearSelection,
    checkNum
  };
};
export default useAdd;
