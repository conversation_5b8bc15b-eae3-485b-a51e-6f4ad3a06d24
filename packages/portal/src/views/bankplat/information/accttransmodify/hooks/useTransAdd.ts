import { ref } from "vue";
import { goPage } from "./usePage";

export const useTransAdd = (basicInfo: any) => {
  const formRef = ref();
  // 点击校验规则按钮之前校验
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  const goBack = () => {
    goPage("modify", { id: basicInfo.mainId });
  };
  //获取后台请求参数
  const getReqParams = () => {
    return basicInfo;
  };
  const initMainId = (mainInfo: any) => {
    basicInfo.mainId = mainInfo.id;
    basicInfo.officeId = mainInfo.officeId;
    basicInfo.officeName = mainInfo.officeName;
    basicInfo.currencyId = mainInfo.currencyId;
    basicInfo.currencyName = mainInfo.currencyName;
    basicInfo.accountId = mainInfo.accountId;
    basicInfo.accountNo = mainInfo.accountNo;
    basicInfo.accountName = mainInfo.accountName;
    basicInfo.openBankName = mainInfo.openBankName;
    basicInfo.transactionTime = mainInfo.transDate;
  };
  //提交成功
  const submitSuccess = () => {
    goBack();
  };
  return {
    formRef,
    formValidator,
    goBack,
    getReqParams,
    initMainId,
    submitSuccess
  };
};
export default useTransAdd;
