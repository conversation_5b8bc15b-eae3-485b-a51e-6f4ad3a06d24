import { reactive } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "@ifs/support";

import type { AcctTransModifyDTO, AccTransDetailDTO, AccTransDetailAndMainDTO } from "../types";

const _infoDefault: AcctTransModifyDTO = {
  id: null,
  officeId: null,
  officeName: "",
  currencyId: null,
  currencyName: "",
  accountId: null,
  accountNo: "",
  accountName: "",
  openBankName: "",
  transactionTime: null,
  transDate: null,
  invoiceStatus: null,
  uuid: null,
  acctTransModifyDetailList: []
};
const AccTransDetailDTO: AccTransDetailDTO = {
  id: null,
  mainId: null,
  accountId: null,
  accountNo: "",
  accountName: "",
  bankName: "",
  bankAddress: "",
  oppAccountNo: "",
  oppAccountName: "",
  oppOpenBankName: "",
  oppOpenBankAddress: "",
  amount: "",
  amountBig: "",
  direction: "",
  transactionTime: null,
  excutBank: "",
  abstractInfo: "",
  useInfo: "",
  remarkInfo: "",
  transNoofBank: "",
  invoiceStatus: null,
  officeId: null,
  currencyId: null,
  officeName: null,
  currencyName: null,
  uuid: ""
};
const AccTransDetailAndMainDTO: AccTransDetailAndMainDTO = {
  accountNo: "",
  accountName: "",
  oppAccountNo: "",
  oppAccountName: "",
  oppOpenBankName: "",
  oppOpenBankAddress: "",
  amount: null,
  direction: null,
  transactionTime: null,
  excutBank: "",
  abstractInfo: "",
  useInfo: "",
  remarkInfo: "",
  officeName: "",
  currencyName: "",
  openBankName: "",
  currencyId: null
};
//导出新增页面入参对象
export const useAccTrans = () => {
  const baseInfo = reactive<AcctTransModifyDTO>(cloneDeep(_infoDefault));
  return {
    baseInfo,
    initResultData: (row: any) => {
      initResultData(baseInfo, row);
    }
  };
};
const initResultData = (baseInfo: AcctTransModifyDTO, row: any) => {
  baseInfo.id = row.data.id;
  baseInfo.officeId = row.data.officeId;
  baseInfo.officeName = row.data.officeName;
  baseInfo.currencyId = row.data.currencyId;
  baseInfo.currencyName = row.data.currencyName;
  baseInfo.accountNo = row.data.accountNo;
  baseInfo.accountName = row.data.accountName;
  baseInfo.openBankName = row.data.openBankName;
  baseInfo.invoiceStatus = row.data.invoiceStatus;
  baseInfo.transDate = row.data.transDate;
};
//导出交易清单入参对象
export const useAccTransDetail = () => {
  const baseDetailInfo = reactive<AccTransDetailDTO>(cloneDeep(AccTransDetailDTO));
  return {
    baseDetailInfo,
    initTransDetailData: (row: any) => {
      initTransDetailData(<AccTransDetailDTO>baseDetailInfo, row);
    }
  };
};
//交易清单修改、查看详情数据初始化
const initTransDetailData = (item: AccTransDetailDTO, row: any) => {
  item.id = row.data.id;
  item.mainId = row.data.mainId;
  item.uuid = row.data.uuid;
  item.accountId = row.data.accountId;
  item.accountNo = row.data.accountNo;
  item.accountName = row.data.accountName;
  item.bankName = row.data.bankName;
  item.oppAccountNo = row.data.oppAccountNo;
  item.oppAccountName = row.data.oppAccountName;
  item.oppOpenBankName = row.data.oppOpenBankName;
  item.oppOpenBankAddress = row.data.oppOpenBankAddress;
  item.excutBank = row.data.excutBank;
  item.amount = row.data.amount;
  item.transactionTime = row.data.transactionTime;
  item.direction = row.data.direction;
  item.abstractInfo = row.data.abstractInfo;
  item.useInfo = row.data.useInfo;
  item.remarkInfo = row.data.remarkInfo;
  item.currencyId = row.data.currencyId;
  item.officeId = row.data.officeId;
  item.officeName = row.data.officeName;
  item.currencyName = row.data.currencyName;
};
export const transMainAndDetail = () => {
  const baseMainDetailInfo = reactive<AccTransDetailAndMainDTO>(cloneDeep(AccTransDetailAndMainDTO));
  return {
    baseMainDetailInfo,
    initTransDetailAndMainData: (row: any) => {
      initTransDetailAndMainData(<AccTransDetailAndMainDTO>baseMainDetailInfo, row);
    }
  };
};
const initTransDetailAndMainData = (item: AccTransDetailAndMainDTO, row: any) => {
  item.accountNo = row.data.accountNo;
  item.accountName = row.data.accountName;
  item.oppAccountNo = row.data.oppAccountNo;
  item.oppAccountName = row.data.oppAccountName;
  item.oppOpenBankName = row.data.oppOpenBankName;
  item.oppOpenBankAddress = row.data.oppOpenBankAddress;
  item.amount = row.data.amount;
  item.direction = row.data.direction;
  item.transactionTime = row.data.transactionTime;
  item.excutBank = row.data.excutBank;
  item.abstractInfo = row.data.abstractInfo;
  item.useInfo = row.data.useInfo;
  item.remarkInfo = row.data.remarkInfo;
  item.officeName = row.data.officeName;
  item.currencyName = row.data.currencyName;
  item.openBankName = row.data.openBankName;
  item.currencyId = row.data.currencyId;
};
//定义规则
export const useAcctTransModifyRules = () => {
  const codePattern = /^[a-zA-Z0-9]+([-][a-zA-Z0-9]+)*$/; // 正则表达式，只允许输入字母、数字和横杠（-只能出现在字母或数字之间）
  const { t } = useI18n();
  // 校验规则
  const rules = reactive({
    officeId: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.officePlaceHolder"),
        trigger: "change"
      }
    ],
    currencyId: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.currencyPlaceHolder"),
        trigger: "change"
      }
    ],
    accountId: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.bankAccountNoPlaceHolder"),
        trigger: "blur"
      }
    ],
    accountName: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.bankAccountNoPlaceHolder"),
        trigger: "blur"
      }
    ],
    openBankName: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.bankAccountNoPlaceHolder"),
        trigger: "blur"
      }
    ],
    transDate: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.transDatePlaceHolder"),
        trigger: "change"
      }
    ],
    oppAccountNo: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.bankAccEnterPlaceHolder"),
        trigger: "change"
      },
      // 添加正则表达式校验规则
      {
        pattern: codePattern,
        message: t("bankplat.information.accttransmodify.codePatternPlaceHolder"),
        trigger: "blur"
      }
    ],
    oppAccountName: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.bankAccNamePlaceHolder"),
        trigger: "change"
      }
    ],
    oppOpenBankName: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.openBankPlaceHolder"),
        trigger: "blur"
      }
    ],
    oppOpenBankAddress: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.locationPlaceHolder"),
        trigger: "blur"
      }
    ],
    excutBank: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.executeBankPlaceHolder"),
        trigger: "blur"
      }
    ],
    amount: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.amountPlaceHolder"),
        trigger: "blur"
      }
    ],
    amountBig: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.amountPlaceHolder"),
        trigger: "blur"
      }
    ],
    transactionTime: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.transDatePlaceHolder"),
        trigger: "change"
      }
    ],
    direction: [
      {
        required: true,
        message: t("bankplat.information.accttransmodify.directionPlaceHolder"),
        trigger: "change"
      }
    ]
  });
  return rules;
};
