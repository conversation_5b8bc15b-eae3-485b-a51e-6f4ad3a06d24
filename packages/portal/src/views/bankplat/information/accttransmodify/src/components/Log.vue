<template>
  <f-drawer-scene
    ref="logDrawer"
    :title="t('bankplat.information.accttransmodify.log.title')"
    destroy-on-close
    @close="setFalseToVisible"
    :isPoint="true"
  >
    <template #default>
      <f-multi-form-panel :model="baseMainDetailInfo" :column="3">
        <f-panel id="form1" :title="t('bankplat.information.accttransmodify.baseInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.officeLabel')">
            <f-input v-model="baseMainDetailInfo.officeName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.currencyLabel')">
            <f-input v-model="baseMainDetailInfo.currencyName" disabled />
          </f-form-item>
        </f-panel>
        <f-panel id="form2" :title="t('bankplat.information.accttransmodify.ourOwnInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')">
            <f-input v-model="baseMainDetailInfo.accountNo" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')">
            <f-input v-model="baseMainDetailInfo.accountName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')">
            <f-input v-model="baseMainDetailInfo.openBankName" disabled />
          </f-form-item>
        </f-panel>
        <!--对方信息-->
        <f-panel id="form2" :title="t('bankplat.information.accttransmodify.targetInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')">
            <f-input v-model="baseMainDetailInfo.oppAccountNo" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')">
            <f-input v-model="baseMainDetailInfo.oppAccountName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')">
            <f-input v-model="baseMainDetailInfo.oppOpenBankName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.openBankLocationLabel')">
            <f-input v-model="baseMainDetailInfo.oppOpenBankAddress" disabled />
          </f-form-item>
        </f-panel>
        <!--交易信息-->
        <f-panel id="form2" :title="t('bankplat.information.accttransmodify.transInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.executeBankLabel')">
            <f-input v-model="baseMainDetailInfo.excutBank" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.amountLabel')">
            <f-amount v-model="baseMainDetailInfo.amount" disabled :symbol="currencySymbol" />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.amountBigLabel')">
            <f-amount-chinese v-model="baseMainDetailInfo.amount" :disabled="true" />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.transactionTimeLabel')">
            <f-date-picker v-model="baseMainDetailInfo.transactionTime" disabled type="date" />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.directionLabel')" prop="direction">
            <f-scene-view :search="baseMainDetailInfo.direction" :data="direction" params="value" label="label" />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.log.transCurrencyLabel')">
            <f-input v-model="baseMainDetailInfo.currencyName" disabled />
          </f-form-item>
          <f-form-item :label="t('bankplat.information.accttransmodify.abstractInfoLabel')" :employ="3">
            <f-textarea
              v-model="baseMainDetailInfo.abstractInfo"
              :min-rows="2"
              maxlength="200"
              show-word-limit
              :disabled="true"
            />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.useInfoLabel')" :employ="3">
            <f-textarea
              v-model="baseMainDetailInfo.useInfo"
              :min-rows="2"
              maxlength="200"
              show-word-limit
              :disabled="true"
            />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.remarkInfoLabel')" :employ="3">
            <f-textarea
              v-model="baseMainDetailInfo.remarkInfo"
              :min-rows="2"
              maxlength="200"
              show-word-limit
              :disabled="true"
            />
          </f-form-item>
        </f-panel>
      </f-multi-form-panel>
    </template>
    <template #footer>
      <f-button type="info" @click="setFalseToVisible">{{
        t("bankplat.information.accttransmodify.closeBtn")
      }}</f-button>
    </template>
  </f-drawer-scene>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { detailProps } from "./TransDetail";
import useLog from "../../hooks/useLog";
import { useConst } from "@ifs/support";
import { useCurrency } from "@/hooks/useCommon";

defineOptions({ name: "Log" });
const props = defineProps(detailProps);
const direction = useConst("bankplat.Direction");
const { t } = useI18n();
const { baseMainDetailInfo, logDrawer, setTrueToVisible, setFalseToVisible } = useLog(props);
const { currencySymbol } = useCurrency(baseMainDetailInfo);
defineExpose({ setTrueToVisible, setFalseToVisible });
</script>
