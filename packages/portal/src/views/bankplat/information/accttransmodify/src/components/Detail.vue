<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('bankplat.information.accttransmodify.detail.detailTitle')"
    destroy-on-close
    @close="setFalseToVisible"
  >
    <template #default>
      <Log ref="logDrawer" :id="logId" />

      <f-multi-form-panel :model="baseInfo" :column="3">
        <f-panel id="form1" :title="t('bankplat.information.accttransmodify.baseInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.officeLabel')">
            <f-input v-model="baseInfo.officeName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.currencyLabel')">
            <f-input v-model="baseInfo.currencyName" disabled />
          </f-form-item>
        </f-panel>
        <f-panel id="form2" :title="t('bankplat.information.accttransmodify.ourOwnInfoTitle')">
          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')">
            <f-input v-model="baseInfo.accountNo" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')">
            <f-input v-model="baseInfo.accountName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')">
            <f-input v-model="baseInfo.openBankName" disabled />
          </f-form-item>

          <f-form-item :label="t('bankplat.information.accttransmodify.transactionTimeLabel')" prop="transDate">
            <f-date-picker
              v-model="baseInfo.transDate"
              type="date"
              :placeholder="t('bankplat.information.accttransmodify.transDatePlaceHolder')"
              :size="size"
              :disabled="true"
            />
          </f-form-item>
        </f-panel>

        <f-panel id="form3" :title="t('bankplat.information.accttransmodify.transInventoryTitle')">
          <f-query-grid
            :summation-biz-label="t('bankplat.information.accttransmodify.countLabel')"
            :summation-biz-unit="t('bankplat.information.accttransmodify.countUnitLabel')"
            :show-collapse="false"
            :show-summation-sum="false"
            :show-print="false"
            :auto-init="false"
            :showCount="false"
            :showQueryPanel="false"
            :show-export="false"
            :showLayout="false"
            :table-columns="tableColumns"
            :table-data="tableData"
            row-key="id"
            autoInit
          >
            <!--打开抽屉-->
            <template #accountNo="{ row }">
              <f-button @click="handleOpenTrans(row)" link type="primary">{{ row.accountNo }}</f-button>
            </template>
          </f-query-grid>
        </f-panel>
        <f-panel :title="t('bankplat.information.accttransmodify.approvalOpinion')" id="form4">
          <f-wf-history
            :params="{
              systemCode: 'T16',
              agencyId: baseInfo.officeId,
              transType: 'undirectly_trans_modify',
              currencyId: baseInfo.currencyId,
              recordId: baseInfo.id
            }"
            :is-through="false"
          />
        </f-panel>
      </f-multi-form-panel>
    </template>
    <template #footer>
      <f-button type="info" @click="setFalseToVisible">{{
        t("bankplat.information.accttransmodify.closeBtn")
      }}</f-button>
    </template>
  </f-drawer-scene>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { detailProps } from "./Detail";
import useDetail from "../../hooks/useDetail";
import Log from "./Log.vue";
defineOptions({ name: "Detail" });
const props = defineProps(detailProps);
const { t } = useI18n();
const {
  baseInfo,
  drawerRef,
  setTrueToVisible,
  setFalseToVisible,
  tableColumns,
  handleOpenTrans,
  logDrawer,
  logId,
  tableData
} = useDetail(props);
defineExpose({ setTrueToVisible, setFalseToVisible });
</script>
