<template>
  <f-blank-scene :title="t('bankplat.information.accttransmodify.transAdd.transAddTitle')" :isPoint="false">
    <f-multi-form-panel ref="formRef" :model="baseDetailInfo" :rules="rules" :column="3">
      <!--基本信息-->
      <f-panel id="form1" :title="t('bankplat.information.accttransmodify.baseInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.officeLabel')">
          <f-input v-model="baseDetailInfo.officeName" disabled />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.currencyLabel')">
          <f-input v-model="baseDetailInfo.currencyName" disabled />
        </f-form-item>
      </f-panel>
      <!--本方信息-->
      <f-panel id="form2" :title="t('bankplat.information.accttransmodify.ourOwnInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')">
          <f-input v-model="baseDetailInfo.accountNo" disabled />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')">
          <f-input v-model="baseDetailInfo.accountName" disabled />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')">
          <f-input v-model="baseDetailInfo.openBankName" disabled />
        </f-form-item>
      </f-panel>
      <!--对方信息-->
      <f-panel id="form1" :title="t('bankplat.information.accttransmodify.targetInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')" prop="oppAccountNo">
          <f-input v-model="baseDetailInfo.oppAccountNo" maxlength="50" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')" prop="oppAccountName">
          <f-input v-model="baseDetailInfo.oppAccountName" maxlength="50" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')" prop="oppOpenBankName">
          <f-input v-model="baseDetailInfo.oppOpenBankName" maxlength="50" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.openBankLocationLabel')" prop="oppOpenBankAddress">
          <f-input v-model="baseDetailInfo.oppOpenBankAddress" maxlength="50" />
        </f-form-item>
      </f-panel>
      <!--交易信息-->
      <f-panel id="form2" :title="t('bankplat.information.accttransmodify.transInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.executeBankLabel')" prop="excutBank">
          <f-input v-model="baseDetailInfo.excutBank" maxlength="50" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.amountLabel')" prop="amount">
          <f-amount
            v-model="baseDetailInfo.amount"
            max="*************.99"
            tooltip
            placeholder="t('bankplat.information.accttransmodify.amountPlaceHolder')"
            :precision="2"
            value-of-string
            :symbol="currencySymbol"
          />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.amountBigLabel')">
          <f-amount-chinese v-model="baseDetailInfo.amount" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.transactionTimeLabel')" prop="transactionTime">
          <f-date-picker
            v-model="baseDetailInfo.transactionTime"
            type="date"
            :placeholder="t('bankplat.information.accttransmodify.transDatePlaceHolder')"
            :size="size"
            :disabled="true"
          />
        </f-form-item>
        <f-form-item :label="t('bankplat.information.accttransmodify.directionLabel')" prop="direction">
          <f-select
            v-model="baseDetailInfo.direction"
            init-if-blank
            default-first
            :data="direction"
            :placeholder="t('bankplat.information.accttransmodify.directionPlaceHolder')"
          />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.abstractInfoLabel')" :employ="3">
          <f-textarea
            v-model="baseDetailInfo.abstractInfo"
            :placeholder="t('bankplat.information.accttransmodify.abstractInfoPlaceHolder')"
            :min-rows="2"
            maxlength="300"
            show-word-limit
          />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.useInfoLabel')" :employ="3">
          <f-textarea
            v-model="baseDetailInfo.useInfo"
            :placeholder="t('bankplat.information.accttransmodify.usePlaceHolder')"
            :min-rows="2"
            maxlength="300"
            show-word-limit
          />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.remarkInfoLabel')" :employ="3">
          <f-textarea
            v-model="baseDetailInfo.remarkInfo"
            :placeholder="t('bankplat.information.accttransmodify.remarksPlaceHolder')"
            :min-rows="2"
            maxlength="300"
            show-word-limit
          />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="getReqParams"
        :url="transDetailSaveUrl"
        operate="save"
        :before-trigger="formValidator"
        @submit-success="submitSuccess"
      />
      <f-button type="info" plain @click.prevent="goBack">
        {{ t("bankplat.information.accttransmodify.goBackBtn") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { transDetailSaveUrl } from "../url/index";
import { useI18n } from "vue-i18n";
import { usePage } from "../hooks/usePage";
import { onMounted } from "vue";
import { useConst } from "@ifs/support";
import useTransAdd from "../hooks/useTransAdd";
const { t } = useI18n();
const direction = useConst("bankplat.Direction");
import { useAcctTransModifyRules, useAccTransDetail } from "../hooks/useAccTransModify";
import { useCurrency } from "@/hooks/useCommon";

const { baseDetailInfo } = useAccTransDetail();
const rules = useAcctTransModifyRules();
const { formRef, formValidator, goBack, getReqParams, initMainId, submitSuccess } = useTransAdd(baseDetailInfo);
const { currencySymbol } = useCurrency(baseDetailInfo);
//页面初始化携带的mainId
const { pageParams } = usePage();
onMounted(() => {
  initMainId(pageParams?.mainInfo);
});
</script>
