<template>
  <f-blank-scene :title="t('bankplat.information.accttransmodify.add.addTitle')">
    <f-multi-form-panel ref="formRef" :model="baseInfo" :rules="rules" :column="3">
      <!--基础信息-->
      <f-panel id="form1" :title="t('bankplat.information.accttransmodify.baseInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.officeLabel')" prop="officeId">
          <f-select
            v-model="baseInfo.officeId"
            filterable
            value-key="key"
            label="value"
            auto-select
            :url="officeSelectorsUrl"
            method="post"
            :placeholder="t('bankplat.information.accttransmodify.officePlaceHolder')"
            :disabled="isShow"
          />
        </f-form-item>
        <f-form-item :label="t('bankplat.information.accttransmodify.currencyLabel')" prop="currencyId">
          <f-select
            v-model="baseInfo.currencyId"
            :placeholder="t('bankplat.information.accttransmodify.currencyPlaceHolder')"
            value-key="key"
            label="value"
            auto-select
            :url="currencySelectorsUrl"
            method="post"
            :extra-data="{ officeId: baseInfo.officeId }"
            :disabled="isShow"
          />
        </f-form-item>
      </f-panel>
      <!--本方信息-->
      <f-panel id="form2" :title="t('bankplat.information.accttransmodify.ourOwnInfoTitle')">
        <f-form-item
          :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')"
          prop="accountId"
          v-if="!isShow"
        >
          <f-magnifier-single
            v-model="baseInfo.accountId"
            :title="t('bankplat.information.accttransmodify.bankAccountNoGlassLabel')"
            :url="bankAccountMagnifierUrl"
            :placeholder="t('bankplat.information.accttransmodify.bankAccountNoPlaceHolder')"
            method="post"
            row-key="accountNo"
            row-label="accountNo"
            input-key="condition"
            :params="{
              isDirectlink: 0,
              currencyId: baseInfo.currencyId,
              officeId: baseInfo.officeId,
              whetherIncludeCancleStatus: 0
            }"
            @change="accountNoChange"
          >
            <f-magnifier-column
              prop="accountNo"
              :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')"
            />
            <f-magnifier-column
              prop="accountName"
              :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')"
            />
            <f-magnifier-column prop="openBankName" :label="t('bankplat.information.accttransmodify.openBankLabel')" />
            <f-magnifier-column
              prop="currencyInfoName"
              :label="t('bankplat.information.accttransmodify.currencyLabel')"
            />
          </f-magnifier-single>
        </f-form-item>

        <f-form-item
          :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')"
          prop="accountId"
          v-if="isShow"
        >
          <f-input v-model="baseInfo.accountNo" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')" prop="accountName">
          <f-input v-model="baseInfo.accountName" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')" prop="openBankName">
          <f-input v-model="baseInfo.openBankName" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.transactionTimeLabel')" prop="transDate">
          <f-date-picker
            v-model="baseInfo.transDate"
            type="date"
            :placeholder="t('bankplat.information.accttransmodify.transDatePlaceHolder')"
            :disabled-date="disabledDate"
            :size="size"
            :disabled="isShow"
          />
        </f-form-item>
      </f-panel>
      <!--交易清单表格-->
      <f-panel id="form3" :title="t('bankplat.information.accttransmodify.transInventoryTitle')">
        <!--交易清单的表格-->
        <f-query-grid
          ref="queryTable"
          :summation-biz-label="t('bankplat.information.accttransmodify.countLabel')"
          :summation-biz-unit="t('bankplat.information.accttransmodify.countUnitLabel')"
          :show-collapse="false"
          :show-summation-sum="false"
          :show-print="false"
          :auto-init="false"
          :showCount="false"
          :showQueryPanel="false"
          :show-export="false"
          :showLayout="false"
          :table-columns="tableColumns"
          :url="pageQueryDetailUrl"
          :params="{ mainId: baseInfo.id }"
          @select="handleSelect"
          @select-all="handleSelect"
          @clearSelection="clearSelection"
          row-key="id"
          autoInit
        >
          <!--增加操作类型-->
          <template #operate>
            <!--跳转新增页面-->
            <f-button type="primary" @click="transAdd">{{
              t("bankplat.information.accttransmodify.doAddBtn")
            }}</f-button>
            <!-- 批量删除-->
            <f-submit-state
              :disabled="!isChecked"
              :gather-params="getDelParams"
              :url="transDetailDeleteUrl"
              operate="remove"
              type="danger"
              compatible
              @close="handleSearch"
              :before-trigger="() => beforTriggerChangeTip(checkNum)"
              :confirm-text="beforeTriggerTip"
              :batchConfirmMap="deleteResultConfirm"
            />
          </template>
          <!--一添加操作按钮-->
          <template #buttons="{ row }">
            <OperateButton :options="generalButtonOption(row)" @on-modify="changeRow(row)" />
          </template>
        </f-query-grid>
      </f-panel>
    </f-multi-form-panel>
    <!--右下角按钮-->
    <template #footer>
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="saveMainUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="saveDataSuccess"
      />
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="submitUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="callBack"
      />
      <f-button type="info" plain @click.prevent="goBack">
        {{ t("bankplat.information.accttransmodify.queryListBtn") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { officeSelectorsUrl, currencySelectorsUrl, bankAccountMagnifierUrl } from "@/views/bankplat/common/url";
import { saveMainUrl, submitUrl, transDetailDeleteUrl, pageQueryDetailUrl } from "../url/index";
import { useI18n } from "vue-i18n";
import useAdd from "../hooks/useAdd";
import { useAcctTransModifyRules, useAccTrans } from "../hooks/useAccTransModify";
import { usePage } from "../hooks/usePage";
import { onMounted } from "vue";
import { useBatchDelete } from "@/hooks/useCommon";
//国际化使用
const { t } = useI18n();
//定义保存页面响应对象
const { baseInfo } = useAccTrans();
//定义保存页面必输项校验
const rules = useAcctTransModifyRules();
const { beforTriggerChangeTip, beforeTriggerTip, deleteResultConfirm } = useBatchDelete();
const {
  formRef,
  gatherSaveInfo,
  formValidator,
  disabledDate,
  goBack,
  transAdd,
  accountNoChange,
  tableColumns,
  handleSearch,
  handleSelect,
  isChecked,
  getDelParams,
  generalButtonOption,
  changeRow,
  saveDataSuccess,
  initAddInfo,
  queryTable,
  callBack,
  defaultOfficeId,
  defaultCurrencyId,
  clearSelection,
  checkNum
} = useAdd(baseInfo);
//交易明细新增返回时初始化新增页面
const { pageParams } = usePage();
const isShow = pageParams?.isShow;

onMounted(() => {
  baseInfo.officeId = defaultOfficeId.value;
  baseInfo.currencyId = defaultCurrencyId.value;
  if (pageParams && pageParams.mainId) {
    initAddInfo(pageParams?.mainId);
  }
});
</script>
