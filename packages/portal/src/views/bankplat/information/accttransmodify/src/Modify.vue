<template>
  <f-blank-scene :title="t('bankplat.information.accttransmodify.modify.modifyTitle')">
    <f-multi-form-panel ref="formRef" :model="baseInfo" :rules="rules" :column="3">
      <!--基本信息-->
      <f-panel id="form1" :title="t('bankplat.information.accttransmodify.baseInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.officeLabel')" prop="officeName">
          <f-input v-model="baseInfo.officeName" :disabled="true" />
        </f-form-item>
        <f-form-item :label="t('bankplat.information.accttransmodify.currencyLabel')" prop="currencyName">
          <f-input v-model="baseInfo.currencyName" :disabled="true" />
        </f-form-item>
      </f-panel>
      <!--本方信息-->
      <f-panel id="form2" :title="t('bankplat.information.accttransmodify.ourOwnInfoTitle')">
        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNoLabel')" prop="accountId">
          <f-input v-model="baseInfo.accountNo" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.bankAccountNameLabel')" prop="accountName">
          <f-input v-model="baseInfo.accountName" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.openBankLabel')" prop="openBankName">
          <f-input v-model="baseInfo.openBankName" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.information.accttransmodify.transactionTimeLabel')" prop="transDate">
          <f-date-picker
            v-model="baseInfo.transDate"
            type="date"
            :placeholder="t('bankplat.information.accttransmodify.transDatePlaceHolder')"
            :size="size"
            :disabled="true"
          />
        </f-form-item>
      </f-panel>

      <f-panel id="form3" :title="t('bankplat.information.accttransmodify.transInventoryTitle')">
        <f-query-grid
          ref="queryTable"
          :summation-biz-label="t('bankplat.information.accttransmodify.countLabel')"
          :summation-biz-unit="t('bankplat.information.accttransmodify.countUnitLabel')"
          :countLabel="t('bankplat.information.accttransmodify.countLabel')"
          :countLabelUnit="t('bankplat.information.accttransmodify.countUnitLabel')"
          :show-collapse="false"
          :show-summation-sum="false"
          :show-print="false"
          :auto-init="false"
          :showCount="true"
          :show-count-value="false"
          :showQueryPanel="false"
          :show-export="false"
          :showLayout="false"
          :table-columns="tableColumns"
          :url="pageQueryDetailUrl"
          :params="{ mainId: baseInfo.id }"
          @select="handleSelect"
          @select-all="handleSelect"
          @clearSelection="clearSelection"
          row-key="id"
          autoInit
        >
          <!--增加操作类型-->
          <template #operate>
            <!--跳转新增页面-->
            <f-button type="primary" @click="transAdd">
              {{ t("bankplat.information.accttransmodify.doAddBtn") }}
            </f-button>
            <!-- 批量删除-->
            <f-submit-state
              :disabled="!isChecked"
              :gather-params="getDelParams"
              :url="transDetailDeleteUrl"
              operate="remove"
              type="danger"
              compatible
              @close="handleSearch"
              :before-trigger="() => beforTriggerChangeTip(checkNum)"
              :confirm-text="beforeTriggerTip"
              :batchConfirmMap="deleteResultConfirm"
            />
          </template>
          <!--一添加操作按钮-->
          <template #buttons="{ row }">
            <OperateButton :options="generalButtonOption(row)" @on-modify="changeRow(row)" />
          </template>
        </f-query-grid>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state :gather-params="getReqParams" :url="submitUrl" operate="submit" @close="callBack" />
      <f-submit-state :url="deleteMainUrl" :gather-params="delParams" operate="remove" type="danger" @close="goBack" />
      <f-button v-if="!isSubmit" type="info" plain @click.prevent="goBack">
        {{ t("bankplat.information.accttransmodify.queryListBtn") }}
      </f-button>
      <f-button v-if="isSubmit" type="info" plain @click.prevent="goSubmit">
        {{ t("bankplat.information.accttransmodify.goBackBtn") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { transDetailDeleteUrl, pageQueryDetailUrl, submitUrl, deleteMainUrl } from "../url/index";
import { useI18n } from "vue-i18n";
import { onMounted, ref } from "vue";
import { useAcctTransModifyRules, useAccTrans } from "../hooks/useAccTransModify";
import useModify from "../hooks/useModify";
import { usePage } from "../hooks/usePage";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
import { useBatchDelete } from "@/hooks/useCommon";
const { baseInfo, initResultData } = useAccTrans();
const rules = useAcctTransModifyRules();
const { t } = useI18n();
const { beforTriggerChangeTip, beforeTriggerTip, deleteResultConfirm } = useBatchDelete();
const {
  queryTable,
  formRef,
  goBack,
  getReqParams,
  initData,
  tableColumns,
  generalButtonOption,
  changeRow,
  isChecked,
  handleSelect,
  handleSearch,
  getDelParams,
  transAdd,
  callBack,
  delParams,
  clearSelection,
  checkNum
} = useModify(baseInfo, initResultData);
const { pageParams } = usePage();
// 页面初始化
onMounted(() => {
  if (props?.id) {
    initData(Number(props?.id));
    isSubmit.value = true;
  } else {
    if (pageParams) {
      initData(pageParams?.id);
    }
  }
});
//待提交页面逻辑
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
const route = useRouter();
//返回待提交页面
const goSubmit = () => {
  doBack(route, String(props.backUrl));
};
</script>
