export const CODE = "bankplat.information.accttransmodify";

export const KEY = Symbol(CODE);

//新增页请求参数
export type AcctTransModifyDTO = {
  id: null | number | string;
  officeId: null | number;
  officeName: null | string;
  currencyId: null | number;
  currencyName: null | string;
  accountId: null | number | string;
  accountNo: null | string;
  accountName: null | string;
  openBankName: null | string;
  transactionTime: null | string;
  transDate: null | string | number;
  invoiceStatus: number | null;
  uuid: null | string | number;
  acctTransModifyDetailList: [];
};
//链接查找请求参数
export type AcctTransQueryDTO = {
  officeIdList: [];
  currencyIdList: [];
  accountId: null | number | string;
  accountNo: null | string;
  transactionTime: null | string;
  invoiceStatusList: [];
};
//链接查找表格元素
export type AcctTransReturnDTO = {
  currencyId: null | number | string;
  officeId: null | number | string;
  inputUserId: null | number | string;
  inputTime: null | number | string;
  modifyUserId: null | number | string;
  modifyTime: null | number | string;
  uuid: null | number | string;
  statusId: null | number | string;
  id: null | number | string;
  accountId: null | number | string;
  accountNo: null | number | string;
  accountName: null | number | string;
  openBankName: null | number | string;
  currencyName: null | number | string;
  officeName: null | number | string;
  transDate: null | number | string;
  invoiceStatus: null | number | string;
};
//交易清单相关元素字段
export type AccTransDetailDTO = {
  id: null | number;
  mainId: null | number;
  accountId: null | number;
  accountNo: null | number | string;
  accountName: null | number | string;
  bankName: null | string;
  bankAddress: null | string;
  oppAccountNo: null | string;
  oppAccountName: null | string;
  oppOpenBankName: null | string;
  oppOpenBankAddress: null | string;
  amount: null | number | string;
  amountBig: null | string;
  direction: null | number | string;
  transactionTime: null | string;
  excutBank: null | number | string;
  abstractInfo: null | string;
  useInfo: null | string;
  remarkInfo: null | string;
  transNoofBank: null | string;
  invoiceStatus: null;
  officeId: null | number | string;
  currencyId: null | string;
  officeName: null | string;
  currencyName: null | string;
  uuid: null | string;
};
//交易清单详情查看
export type AccTransDetailAndMainDTO = {
  accountNo: null | number | string;
  accountName: null | number | string;
  oppAccountNo: null | string;
  oppAccountName: null | string;
  oppOpenBankName: null | string;
  oppOpenBankAddress: null | string;
  amount: null | number | string;
  direction: null | number | string;
  transactionTime: null | string;
  excutBank: null | number | string;
  abstractInfo: null | string;
  useInfo: null | string;
  remarkInfo: null | string;
  officeName: null | string;
  currencyName: null | string;
  openBankName: null | string;
  currencyId: null | number;
};
