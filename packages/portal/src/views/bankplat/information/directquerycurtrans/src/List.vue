<template>
  <f-query-scene :title="t('bankplat.information.directQueryCurTrans.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="code"
        :table-columns="tableColumns"
        :table-data="queryData"
        @query-table="handleSearch"
        border
        :form-data="queryFrom"
        show-header
        auto-reset
        :auto-init="false"
        :rules="rules"
        @on-loaded="calculateCurrentPageAmount"
        count-prop="examineAmount"
        :export-url="exportExcel"
        :show-print="false"
        :show-count="true"
        :config="{
          timeout: 600000
        }"
        :show-summation-sum="true"
        :summation="summation"
        :summation-biz-label="t('bankplat.information.directQueryCurTrans.countLabel')"
        :summation-biz-unit="t('bankplat.information.directQueryCurTrans.countUnitLabel')"
        :countLabel="t('bankplat.information.directQueryCurTrans.countLabel')"
        :countLabelUnit="t('bankplat.information.directQueryCurTrans.countUnitLabel')"
        query-comp-id="bankplat-setting-directQueryCurTrans-query"
        table-comp-id="bankplat-setting-directQueryCurTrans-table"
        showSummationDetail
      >
        <template #query-panel>
          <!-- 机构-->
          <f-form-item :label="t('bankplat.information.directQueryCurTrans.office')" prop="officeIds">
            <f-select
              v-model="queryFrom.officeIds"
              value-key="key"
              label="value"
              :url="officeList"
              method="post"
              multiple
              select-all
              collapse-tags
              init-if-blank
              filterable
              auto-select
            />
          </f-form-item>

          <!-- 币种-->
          <f-form-item :label="t('bankplat.information.directQueryCurTrans.currency')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              :placeholder="t('bankplat.information.directQueryCurTrans.currency')"
              value-key="key"
              label="value"
              :url="currencyList"
              method="post"
              multiple
              select-all
              collapse-tags
              init-if-blank
              filterable
              auto-select
              @change="currencySelectionEvent"
            />
          </f-form-item>

          <!-- 银行类型-->
          <f-form-item :label="t('bankplat.information.directQueryCurTrans.bankType')" prop="bankId" required>
            <f-select
              v-model="queryFrom.bankId"
              :placeholder="t('bankplat.information.directQueryCurTrans.contractTypePlaceHolder')"
              value-key="key"
              label="value"
              filterable
              init-if-blank
              :url="bankTypeList"
              method="post"
              @change="accountNoClear"
            />
          </f-form-item>

          <!-- 银行账号-->
          <f-form-item :label="t('bankplat.information.directQueryCurTrans.status')" prop="bankAccountInfoNo">
            <f-magnifier-single
              v-model="queryFrom.bankAccountInfoNo"
              :title="t('bankplat.information.directQueryCurTrans.contractCodeGlass')"
              :url="bankAccountMagnifierUrl"
              :placeholder="t('bankplat.information.directQueryCurTrans.contractCodePlaceHolder')"
              method="post"
              row-key="accountNo"
              row-label="accountNo"
              input-key="condition"
              auto-init
              @change="checkGetData"
              :params="{
                whetherIncludeCancleStatus: 0,
                bankId: queryFrom.bankId,
                currencyList: queryFrom.currencyIds,
                officeList: queryFrom.officeIds
              }"
            >
              <f-magnifier-column
                prop="accountNo"
                :label="t('bankplat.information.directQueryCurTrans.contractCode')"
              />
              <f-magnifier-column
                prop="accountName"
                :label="t('bankplat.information.directQueryCurTrans.loanTypeName')"
              />
              <f-magnifier-column
                prop="openBankName"
                :label="t('bankplat.information.directQueryCurTrans.borrowName')"
              />
              <f-magnifier-column
                prop="currencyInfoName"
                :label="t('bankplat.information.directQueryCurTrans.examineAmount')"
              />
            </f-magnifier-single>
          </f-form-item>

          <!-- 收付方向-->
          <f-form-item :label="t('bankplat.information.directQueryCurTrans.amount')" prop="directions">
            <f-select
              v-model="queryFrom.directions"
              :data="lendingDirection"
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              select-all
              init-if-blank
            />
          </f-form-item>

          <!--交易金额-->
          <f-form-item :label="t('bankplat.information.directQueryCurTrans.transactionAmount')" prop="amountArr">
            <f-amount-range
              v-model="queryFrom.amountArr"
              @change="amountChange"
              value-of-number
              tooltip
              max="*************.99"
              min="0"
              :precision="2"
              size="small"
            />
          </f-form-item>
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>

<script setup lang="ts">
import { exportExcel, officeList, currencyList, bankTypeList, bankAccountMagnifierUrl } from "../../../common/url";
import useList from "../hooks/useList";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";

// 收付方向枚举
const lendingDirection = useConst("bankplat.LendingDirection");

const {
  rules,
  checkGetData,
  tableColumns,
  queryFrom,
  queryTable,
  handleSearch,
  amountChange,
  accountNoClear,
  queryData,
  calculateCurrentPageAmount,
  summation,
  currencySelectionEvent
} = useList();

const { t } = useI18n();
</script>
