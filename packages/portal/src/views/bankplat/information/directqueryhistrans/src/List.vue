<template>
  <f-query-scene :title="t('bankplat.information.directqueryhistrans.list.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="code"
        :table-columns="tableColumns"
        border
        :formData="queryFrom"
        :table-data="queryData"
        :show-header="true"
        @query-table="handleSearch"
        auto-reset
        :auto-init="false"
        :rules="rules"
        countProp="examineAmount"
        :export-url="exportExcel"
        :show-print="false"
        :show-collapse="false"
        :config="{
          timeout: 600000
        }"
        @on-loaded="calculateCurrentPageAmount"
        showSummationDetail
        :summation="summation"
        :show-summation-sum="true"
        :summation-biz-label="t('bankplat.information.directqueryhistrans.list.countLabel')"
        :summation-biz-unit="t('bankplat.information.directqueryhistrans.list.countUnitLabel')"
        :countLabel="t('bankplat.information.directqueryhistrans.list.countLabel')"
        :countLabelUnit="t('bankplat.information.directqueryhistrans.list.countUnitLabel')"
        query-comp-id="bankplat-information-directqueryhistrans-query-001"
        table-comp-id="bankplat-information-directqueryhistrans-table-001"
        @resetForm="clearQueryParams"
      >
        <template #query-panel>
          <!-- 机构-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.officeLabel')" prop="officeIds">
            <f-select
              v-model="queryFrom.officeIds"
              value-key="key"
              label="value"
              :placeholder="t('bankplat.information.directqueryhistrans.list.officePlaceHolder')"
              :url="officeSelectorsUrl"
              method="post"
              multiple
              select-all
              collapse-tags
              init-if-blank
              filterable
              auto-select
            />
          </f-form-item>

          <!-- 币种-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.currencyLabel')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              :placeholder="t('bankplat.information.directqueryhistrans.list.currencyPlaceHolder')"
              value-key="key"
              label="value"
              :url="currencySelectorsUrl"
              method="post"
              multiple
              select-all
              collapse-tags
              init-if-blank
              filterable
              auto-select
              @change="currencySelectionEvent"
            />
          </f-form-item>

          <!-- 银行类型-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.bankTypeLabel')" prop="bankId">
            <f-select
              v-model="queryFrom.bankId"
              :placeholder="t('bankplat.information.directqueryhistrans.list.bankTypePlaceHolder')"
              value-key="key"
              filterable
              initIfBlank
              label="value"
              :url="banktypeSelectorsUrl"
              method="post"
              @change="accountNoClear"
            />
          </f-form-item>

          <!-- 银行账号-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.statusLabel')" prop="id">
            <f-magnifier-single
              v-model="queryFrom.id"
              :title="t('bankplat.information.directqueryhistrans.list.contractCodeGlassLabel')"
              :url="bankAccountMagnifierUrl"
              :placeholder="t('bankplat.information.directqueryhistrans.list.contractCodePlaceHolder')"
              method="post"
              row-key="id"
              row-label="accountNo"
              input-key="condition"
              auto-init
              @change="checkGetData"
              :params="{
                whetherIncludeCancleStatus: 0,
                bankId: queryFrom.bankId,
                currencyList: queryFrom.currencyIds,
                officeList: queryFrom.officeIds
              }"
            >
              <f-magnifier-column
                prop="accountNo"
                :label="t('bankplat.information.directqueryhistrans.list.contractCodeLabel')"
              />
              <f-magnifier-column
                prop="accountName"
                :label="t('bankplat.information.directqueryhistrans.list.loanTypeNameLabel')"
              />
              <f-magnifier-column
                prop="openBankName"
                :label="t('bankplat.information.directqueryhistrans.list.borrowNameLabel')"
              />
              <f-magnifier-column
                prop="currencyInfoName"
                :label="t('bankplat.information.directqueryhistrans.list.examineAmountLabel')"
              />
            </f-magnifier-single>
          </f-form-item>

          <!--查询日期-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.queryDateLabel')" prop="queryDate">
            <f-date-picker
              unlink-panels
              v-model="queryFrom.queryDate"
              type="daterange"
              :start-placeholder="t('bankplat.information.directqueryhistrans.list.dateFormatLabel')"
              :end-placeholder="t('bankplat.information.directqueryhistrans.list.dateFormatLabel')"
              @change="queryDateChange"
              :range-separator="t('bankplat.information.directqueryhistrans.list.toLabel')"
              :disabled-date="disabledDate"
              :default-value="now"
              size="small"
              @calendar-change="handleQueryDateChange"
              :editable="false"
            />
          </f-form-item>

          <!-- 收付方向-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.amountLabel')" prop="directions">
            <f-select
              v-model="queryFrom.directions"
              :data="lendingDirection"
              :placeholder="t('bankplat.information.directqueryhistrans.list.selectPaymentDirectionPlaceHolder')"
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              select-all
              init-if-blank
            />
          </f-form-item>

          <!-- 交易金额-->
          <f-form-item :label="t('bankplat.information.directqueryhistrans.list.amountRangeLabel')" prop="transAmount">
            <f-amount-range
              v-model="queryFrom.transAmount"
              @change="amountChange"
              value-of-number
              tooltip
              size="small"
            />
          </f-form-item>
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>

<script setup lang="ts">
//后台url
import { exportExcel } from "../url/index";
//查询条件url
import {
  officeSelectorsUrl,
  currencySelectorsUrl,
  banktypeSelectorsUrl,
  bankAccountMagnifierUrl
} from "../../../common/url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useList } from "../hooks/useList";

const { t } = useI18n();
// 收付方向枚举
const lendingDirection = useConst("bankplat.LendingDirection");
const now = new Date();

const {
  tableColumns,
  queryFrom,
  queryTable,
  handleSearch,
  rules,
  queryDateChange,
  disabledDate,
  clearQueryParams,
  amountChange,
  accountNoClear,
  handleQueryDateChange,
  queryData,
  calculateCurrentPageAmount,
  summation,
  checkGetData,
  currencySelectionEvent
} = useList();
</script>
