<template>
  <f-blank-scene :title="t('bankplat.information.acctbalancemodify.add.addTitle')" :isPoint="false">
    <f-form-panel ref="formRef" :model="baseInfo" :rules="rules" :column="4">
      <f-form-item :label="t('bankplat.information.acctbalancemodify.officeLabel')" prop="officeId">
        <f-select
          v-model="baseInfo.officeId"
          auto-select
          value-key="key"
          label="value"
          :url="officeSelectorsUrl"
          @change="eventsChange"
          method="post"
          :placeholder="t('bankplat.information.acctbalancemodify.officePlaceHolder')"
        />
      </f-form-item>
      <f-form-item :label="t('bankplat.information.acctbalancemodify.currencyLabel')" prop="currencyId">
        <f-select
          v-model="baseInfo.currencyId"
          :placeholder="t('bankplat.information.acctbalancemodify.currencyPlaceHolder')"
          value-key="key"
          label="value"
          auto-select
          :url="currencySelectorsUrl"
          method="post"
          :extra-data="{ officeId: baseInfo.officeId }"
          @change="currencyChange"
        />
      </f-form-item>

      <f-form-item :label="t('bankplat.information.acctbalancemodify.bankAccountNoLabel')" prop="accountNo">
        <f-magnifier-single
          v-model="baseInfo.accountNo"
          :title="t('bankplat.information.acctbalancemodify.bankAccountNoGlassLabel')"
          :url="bankAccountMagnifierUrl"
          :placeholder="t('bankplat.information.acctbalancemodify.bankAccountNoPlaceHolder')"
          method="post"
          row-key="accountNo"
          row-label="accountNo"
          input-key="condition"
          :params="{
            isDirectlink: 0,
            whetherIncludeCancleStatus: 0,
            currencyId: baseInfo.currencyId,
            officeId: baseInfo.officeId
          }"
          @change="accountNoChange"
        >
          <f-magnifier-column
            prop="accountNo"
            :label="t('bankplat.information.acctbalancemodify.bankAccountNoLabel')"
          />
          <f-magnifier-column
            prop="accountName"
            :label="t('bankplat.information.acctbalancemodify.bankAccountNameLabel')"
          />
          <f-magnifier-column prop="openBankName" :label="t('bankplat.information.acctbalancemodify.openBankLabel')" />
          <f-magnifier-column
            prop="currencyInfoName"
            :label="t('bankplat.information.acctbalancemodify.currencyLabel')"
          />
        </f-magnifier-single>
      </f-form-item>
      <f-form-item>
        <f-button type="primary" plain @click.prevent="doQuery(baseInfo)">
          {{ t("bankplat.information.acctbalancemodify.queryBtn") }}
        </f-button>
      </f-form-item>

      <f-form-item :label="t('bankplat.information.acctbalancemodify.queryDateLabel')" prop="queryDate">
        <f-date-picker
          v-model="createDate"
          type="daterange"
          :placeholder="t('bankplat.information.acctbalancemodify.queryDateLabel')"
          :size="size"
          @change="queryDateChange"
          :default-value="now"
        />
      </f-form-item>
      <!--历史余额信息展示列表-->
      <f-query-grid
        ref="queryTable"
        :summation-biz-label="t('bankplat.information.acctbalancemodify.countLabel')"
        :summation-biz-unit="t('bankplat.information.acctbalancemodify.countUnitLabel')"
        :countLabel="t('bankplat.information.acctbalancemodify.modify.countLabel')"
        :countLabelUnit="t('bankplat.information.acctbalancemodify.modify.countUnitLabel')"
        :show-collapse="false"
        :show-summation-sum="false"
        :show-print="false"
        :auto-init="false"
        :showQueryPanel="false"
        :show-export="false"
        :showLayout="false"
        :table-columns="tableColumns"
        :tableData="hisBalanceTableData"
        @select="handleSelect"
        @select-all="handleSelect"
        row-key="id"
        autoInit
        @clearSelection="clearSelection"
        :showTop="false"
        :pagination="false"
      >
        <template #afterBalanceEdit="{ row }">
          <f-amount
            v-model="row.afterBalance"
            :symbol="row.currencySymbol"
            :placeholder="t('bankplat.information.acctbalancemodify.amountPlaceHolder')"
          />
        </template>

        <template #button="{ row }">
          <f-button size="small" @click="openEdit(row)">
            {{ t("bankplat.information.acctbalancemodify.editBtn") }}
          </f-button>
          <f-button size="small" @click="closeEdit(row)">
            {{ t("bankplat.information.acctbalancemodify.closeBtn") }}
          </f-button>
        </template>
      </f-query-grid>
    </f-form-panel>
    <!--右下角按钮-->
    <template #footer>
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="saveMainUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="saveDataSuccess"
      />
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="postSaveSubmitUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="callBack"
      />
      <f-button type="info" plain @click.prevent="goBack">
        {{ t("bankplat.information.acctbalancemodify.queryListBtn") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { officeSelectorsUrl, currencySelectorsUrl, bankAccountMagnifierUrl } from "@/views/bankplat/common/url";
import { saveMainUrl, postSaveSubmitUrl } from "../url/index";
import { useI18n } from "vue-i18n";
import useAdd from "../hooks/useAdd";
import { useBalanceModifyRules, useBalance } from "../hooks/useAccBalanceModify";
import { onMounted } from "vue";
//国际化使用
const { t } = useI18n();
//定义保存页面响应对象
const { baseInfo } = useBalance();
//定义保存页面必输项校验
const rules = useBalanceModifyRules();
const now = new Date();
const {
  formRef,
  gatherSaveInfo,
  formValidator,
  goBack,
  accountNoChange,
  tableColumns,
  handleSelect,
  queryTable,
  queryDateChange,
  hisBalanceTableData,
  doQuery,
  initPage,
  openEdit,
  closeEdit,
  createDate,
  saveDataSuccess,
  callBack,
  defaultOfficeId,
  defaultCurrencyId,
  clearSelection,
  currencyChange,
  eventsChange
} = useAdd(baseInfo);
onMounted(() => {
  initPage();
  baseInfo.officeId = defaultOfficeId.value;
  baseInfo.currencyId = defaultCurrencyId.value;
});
</script>
