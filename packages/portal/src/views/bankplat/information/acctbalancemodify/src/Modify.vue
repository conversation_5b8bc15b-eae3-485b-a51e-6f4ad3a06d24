<template>
  <f-blank-scene :title="t('bankplat.information.acctbalancemodify.modify.modifyTitle')" :isPoint="false">
    <f-form-panel ref="formRef" :model="baseInfo" :rules="rules" :column="3">
      <f-form-item :label="t('bankplat.information.acctbalancemodify.officeLabel')">
        <f-input v-model="baseInfo.officeName" disabled />
      </f-form-item>

      <f-form-item :label="t('bankplat.information.acctbalancemodify.currencyLabel')">
        <f-input v-model="baseInfo.currencyName" disabled />
      </f-form-item>

      <f-form-item :label="t('bankplat.information.acctbalancemodify.bankAccountNoLabel')">
        <f-input v-model="baseInfo.accountNo" disabled />
      </f-form-item>

      <f-form-item :label="t('bankplat.information.acctbalancemodify.queryDateLabel')">
        <f-date-picker
          v-model="queryDate"
          type="daterange"
          :placeholder="t('bankplat.information.acctbalancemodify.queryDateLabel')"
          :size="size"
          :disabled="true"
          :default-value="now"
        />
      </f-form-item>

      <!--历史余额信息展示列表-->
      <f-query-grid
        ref="queryTable"
        :summation-biz-label="t('bankplat.information.acctbalancemodify.countLabel')"
        :summation-biz-unit="t('bankplat.information.acctbalancemodify.countUnitLabel')"
        :countLabel="t('bankplat.information.acctbalancemodify.modify.countLabel')"
        :countLabelUnit="t('bankplat.information.acctbalancemodify.modify.countUnitLabel')"
        :show-collapse="false"
        :show-summation-sum="false"
        :show-print="false"
        :showQueryPanel="false"
        :show-export="false"
        :showLayout="false"
        :table-columns="tableColumns"
        :url="pageQueryDetailUrl"
        :params="{ id: baseInfo.id }"
        @select="handleSelect"
        @select-all="handleSelect"
        @clearSelection="clearSelection"
        row-key="id"
        autoInit
        :showTop="false"
        :pagination="false"
      >
        <template #afterBalanceEdit="{ row }">
          <f-amount
            v-model="row.afterBalance"
            :symbol="row.currencySymbol"
            :placeholder="t('bankplat.information.acctbalancemodify.amountPlaceHolder')"
          />
        </template>

        <template #button="{ row }">
          <f-button size="small" @click="openEdit(row)">{{
            t("bankplat.information.acctbalancemodify.editBtn")
          }}</f-button>
          <f-button size="small" @click="closeEdit(row)">{{
            t("bankplat.information.acctbalancemodify.closeBtn")
          }}</f-button>
        </template>
      </f-query-grid>
    </f-form-panel>
    <!--右下角按钮-->
    <template #footer>
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="editMainUrl"
        operate="save"
        @close="saveDataSuccess"
        :before-trigger="formValidator"
      />
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="postModifySubmitUrl"
        operate="submit"
        @close="callBack"
        :before-trigger="formValidator"
      />

      <f-submit-state
        :url="deleteMainUrl"
        :gather-params="delParams"
        operate="remove"
        type="danger"
        @close="callBack"
      />

      <f-button type="info" plain @click.prevent="goBack" v-if="!isSubmit">
        {{ t("bankplat.information.acctbalancemodify.queryListBtn") }}
      </f-button>

      <f-button type="primary" plain @click.prevent="goSubmit" v-if="isSubmit">
        {{ t("bankplat.information.acctbalancemodify.modify.goBackBtn") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>

<script setup lang="ts">
import { deleteMainUrl, editMainUrl, pageQueryDetailUrl, postModifySubmitUrl } from "../url/index";
import { useI18n } from "vue-i18n";
import { onMounted, ref } from "vue";
import { useBalance, useBalanceModifyRules } from "../hooks/useAccBalanceModify";
import { useModify } from "../hooks/useModify";
import { usePage } from "../hooks/usePage";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";

const { rules } = useBalanceModifyRules;
const { baseInfo, initResultData } = useBalance();
const { t } = useI18n();
const now = new Date();
const testFlag = ref(false);
const {
  formRef,
  gatherSaveInfo,
  handleSelect,
  queryTable,
  goBack,
  tableColumns,
  initData,
  delParams,
  queryDate,
  openEdit,
  closeEdit,
  saveDataSuccess,
  clearSelection,
  callBack,
  formValidator
} = useModify(baseInfo, initResultData);
const { pageParams } = usePage();
onMounted(() => {
  testFlag.value = true;
  if (props?.id) {
    initData(Number(props?.id));
    isSubmit.value = true;
  } else {
    if (pageParams) {
      initData(pageParams?.id);
    }
  }
});
//待提交页面逻辑
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
const route = useRouter();
//返回待提交页面
const goSubmit = () => {
  doBack(route, String(props.backUrl));
};
</script>
