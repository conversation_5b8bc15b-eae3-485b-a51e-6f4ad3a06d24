<template>
  <f-query-scene :title="t('bankplat.information.acctbalancemodify.list.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="pageQueryUrl"
        :border="true"
        stripe
        :form-data="queryFrom"
        :summation-biz-label="t('bankplat.information.acctbalancemodify.list.countLabel')"
        :summation-biz-unit="t('bankplat.information.acctbalancemodify.list.countUnitLabel')"
        :count-label="t('bankplat.information.acctbalancemodify.list.countLabel')"
        :count-label-unit="t('bankplat.information.acctbalancemodify.list.countUnitLabel')"
        query-comp-id="bankplat.information.acctbalancemodify-query"
        table-comp-id="bankplat.information.acctbalancemodify-table"
        @clearSelection="clearSelection"
        @query-table="cleanSelect"
        @select="handleSelect"
        @select-all="handleSelect"
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :show-collapse="false"
        :show-summation-sum="false"
        :show-print="false"
        :auto-init="true"
        :tile-panel="true"
        :showCount="false"
        :show-count="true"
        show-header
        :allow-sort="allowSort"
      >
        <!--增加操作类型-->
        <template #operate>
          <!--跳转新增页面-->
          <f-button type="primary" @click="add">
            {{ t("bankplat.information.acctbalancemodify.list.addBtn") }}
          </f-button>
          <!-- 批量删除-->
          <f-submit-state
            :disabled="!isChecked"
            :gather-params="getDelParams"
            :url="deleteListUrl"
            :is-batch="true"
            operate="remove"
            type="danger"
            @close="handleSearch"
            compatible
            :before-trigger="() => beforTriggerChangeTip(checkNum)"
            :confirm-text="beforeTriggerTip"
            :batchConfirmMap="deleteResultConfirm"
          />
        </template>
        <!-- 机构 -->
        <template #query-panel>
          <f-form-item :label="t('bankplat.information.acctbalancemodify.officeLabel')" prop="officeIdList">
            <f-select
              v-model="queryFrom.officeIdList"
              :url="officeSelectorsUrl"
              value-key="key"
              label="value"
              init-if-blank
              multiple
              collapse-tags
              collapse-tags-tooltip
              select-all
              :placeholder="t('bankplat.information.acctbalancemodify.officePlaceHolder')"
            />
          </f-form-item>
          <!--币种-->
          <f-form-item :label="t('bankplat.information.acctbalancemodify.currencyLabel')" prop="currencyIdList">
            <f-select
              v-model="queryFrom.currencyIdList"
              :placeholder="t('bankplat.information.acctbalancemodify.currencyPlaceHolder')"
              value-key="key"
              label="value"
              init-if-blank
              multiple
              collapse-tags
              collapse-tags-tooltip
              select-all
              :url="currencySelectorsUrl"
              method="post"
            />
          </f-form-item>
          <!-- 银行账号 -->
          <f-form-item :label="t('bankplat.information.acctbalancemodify.bankAccountNoLabel')" prop="accountNo">
            <f-magnifier-single
              v-model="queryFrom.accountNo"
              :title="t('bankplat.information.acctbalancemodify.bankAccountNoGlassLabel')"
              :url="bankAccountMagnifierUrl"
              :placeholder="t('bankplat.information.acctbalancemodify.bankAccountNoPlaceHolder')"
              method="post"
              row-key="accountNo"
              row-label="accountNo"
              input-key="condition"
              autoInit
              :params="{
                isDirectlink: 0,
                currencyList: queryFrom.currencyIdList,
                officeList: queryFrom.officeIdList,
                whetherIncludeCancleStatus: 0
              }"
              @change="accountNoChange"
            >
              <f-magnifier-column
                prop="accountNo"
                :label="t('bankplat.information.acctbalancemodify.bankAccountNoLabel')"
              />
              <f-magnifier-column
                prop="accountName"
                :label="t('bankplat.information.acctbalancemodify.bankAccountNameLabel')"
              />
              <f-magnifier-column
                prop="openBankName"
                :label="t('bankplat.information.acctbalancemodify.openBankLabel')"
              />
              <f-magnifier-column
                prop="currencyInfoName"
                :label="t('bankplat.information.acctbalancemodify.currencyLabel')"
              />
            </f-magnifier-single>
          </f-form-item>
          <!-- 余额日期 -->
          <f-form-item :label="t('bankplat.information.acctbalancemodify.balanceDateLabel')" prop="date">
            <f-date-picker
              v-model="queryFrom.date"
              type="date"
              :disabled-date="disabledDate"
              :placeholder="t('bankplat.information.acctbalancemodify.balanceDatePlaceHolder')"
            />
          </f-form-item>
          <!-- 状态 -->
          <f-form-item :label="t('bankplat.information.acctbalancemodify.list.statusLabel')" prop="invoiceStatusList">
            <f-select
              v-model="queryFrom.invoiceStatusList"
              init-if-blank
              multiple
              collapse-tags
              collapse-tags-tooltip
              select-all
              :data="invoiceStatus"
            />
          </f-form-item>
        </template>
        <!--一添加操作按钮-->
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="changeRow(row)" />
        </template>
        <!--打开明细页面抽屉-->
        <template #id="{ row }">
          <f-button @click="handleOpen(row)" link type="primary">{{ row.id }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detailRef" :id="rowIdRef" />
  </f-query-scene>
</template>

<script setup lang="ts">
//访问路径
import { officeSelectorsUrl, currencySelectorsUrl, bankAccountMagnifierUrl } from "../../../common/url";
import { deleteListUrl } from "../url";
//导入url
import { pageQueryUrl, exportUrl } from "../url/index";
import Detail from "./components/Detail.vue";
import useList from "../hooks/useList";
import { useBatchDelete } from "@/hooks/useCommon";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import OperateButton from "@/components/operate-button/operate-button";

const { t } = useI18n();
//状态下拉框-yaml
const invoiceStatus = useConst("bankplat.InvoiceStatus");
const { beforTriggerChangeTip, beforeTriggerTip, deleteResultConfirm } = useBatchDelete();
const {
  tableColumns,
  queryFrom,
  queryTable,
  handleSearch,
  handleSelect,
  clearSelection,
  cleanSelect,
  disabledDate,
  generalButtonOption,
  changeRow,
  isChecked,
  add,
  handleOpen,
  rowIdRef,
  detailRef,
  accountNoChange,
  getDelParams,
  checkNum,
  allowSort
} = useList();
</script>
