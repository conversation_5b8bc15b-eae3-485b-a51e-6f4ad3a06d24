import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { computed, nextTick, reactive, ref, shallowRef } from "vue";
import type { AcctBalanceQueryTableDTO, AcctBalanceQueryTypeDTO } from "../types";
import { deleteMainUrl, postListSubmitUrl } from "../url";
import { goPage } from "../hooks/usePage";

export const useList = () => {
  const { t } = useI18n();
  const allowSort = [
    "id",
    "accountNo",
    "accountName",
    "bankName",
    "currencyName",
    "beginningDate",
    "endingDate",
    "inputTime"
  ];
  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      fixed: "left",
      selectable(row: AcctBalanceQueryTableDTO) {
        return [invoiceStatusEnum.SAVED, invoiceStatusEnum.REFUSED].includes(row.invoiceStatus);
      }
    },
    {
      width: "120px",
      prop: "id",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.applyCodeLabel"),
      slots: { default: "id" }
    },
    {
      width: "180px",
      prop: "accountNo",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.bankAccountNoLabel")
    },
    {
      width: "200px",
      prop: "accountName",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.bankAccountNameLabel")
    },
    {
      width: "200px",
      prop: "bankName",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.openBankLabel")
    },
    {
      width: "80px",
      prop: "currencyName",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.currencyLabel")
    },
    {
      width: "120px",
      prop: "beginningDate",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.list.beginningDateLabel"),
      formatter: "date"
    },
    {
      width: "120px",
      prop: "endingDate",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.list.endingDateLabel"),
      formatter: "date"
    },
    {
      width: "80px",
      prop: "invoiceStatus",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.list.statusLabel"),
      formatter: { name: "const", const: "bankplat.InvoiceStatus" }
    },
    {
      width: "140x",
      prop: "inputTime",
      align: "left",
      label: t("bankplat.information.acctbalancemodify.list.inputTimeLabel")
    },
    {
      width: "200px",
      slots: { default: "buttons" },
      prop: "operate",
      fixed: "right",
      label: t("bankplat.information.acctbalancemodify.list.operateColLabel")
    }
  ];

  // 表格查询对象
  const queryFrom = reactive<AcctBalanceQueryTypeDTO>({
    accountId: null,
    accountNo: "",
    currencyIdList: [],
    invoiceStatusList: [],
    officeIdList: [],
    date: ""
  });
  // 表格模板
  const queryTable = shallowRef();
  //详情模板
  const rowIdRef = ref<number>();
  //勾选数据条数
  const checkNum = ref<number>();
  const detailRef = shallowRef();
  // // 已选列表
  const checkedListRef = ref<AcctBalanceQueryTableDTO[]>([]);
  // // 是否选中checkbox
  const isChecked = computed(() => checkedListRef.value.length > 0);
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  // 勾选checkbox
  const handleSelect = (row: AcctBalanceQueryTableDTO[]) => {
    checkedListRef.value = row;
    checkNum.value = checkedListRef.value.length;
  };
  //获取批量删除参数
  const getDelParams = () => {
    const uuids = <any>[];
    const ids = <any>[];
    const codes = <any>[];
    checkedListRef.value.forEach(item => {
      ids.push(item.id);
      uuids.push(item.uuid);
      codes.push(item.id);
    });
    const params = { ids: ids, uuids: uuids, codes: codes };
    return params;
  };
  const clearSelection = () => {
    checkedListRef.value = [];
    checkNum.value = [];
  };
  const cleanSelect = () => {
    checkedListRef.value = [];
    checkNum.value = [];
    queryTable.value.clearSelection();
  };

  const disabledDate = (time: Date) => {
    const cur = new Date();
    const yesterday = new Date(cur.getTime() - 1000 * 60 * 60 * 24);
    return time.getTime() > Number(yesterday);
  };
  //跳转修改页面
  const changeRow = (row: AcctBalanceQueryTableDTO) => {
    goPage("modify", { id: row.id });
  };
  // 新增跳转
  const add = () => {
    goPage("add");
  };
  //打开抽屉
  const handleOpen = (row: AcctBalanceQueryTableDTO) => {
    rowIdRef.value = row.id as number;
    nextTick(() => {
      detailRef.value.setTrueToVisible();
    });
  };

  //银行账号放大镜修改事件
  const accountNoChange = (row: any) => {
    queryFrom.accountId = row.id;
    queryFrom.accountNo = row.accountNo;
  };
  //状态枚举  操作列中的修改、删除按钮，状态是已保存和已拒绝时显示，已审核和审批中状态不显示
  const invoiceStatusEnum = useConst("bankplat.InvoiceStatus");
  //列表操作列
  const generalButtonOption = (row: AcctBalanceQueryTableDTO) => {
    const invoiceStatus = row.invoiceStatus;
    return [
      {
        type: "modify",
        isShow: [invoiceStatusEnum.SAVED, invoiceStatusEnum.REFUSED].includes(invoiceStatus)
      },
      {
        type: "submit",
        isShow: [invoiceStatusEnum.SAVED, invoiceStatusEnum.REFUSED].includes(invoiceStatus),
        submitComOpt: {
          url: postListSubmitUrl,
          gatherParams: () => {
            return { id: row.id };
          },
          close: (res: any) => {
            if (res.success) {
              handleSearch();
            }
          }
        }
      },
      {
        type: "remove",
        isShow: [invoiceStatusEnum.SAVED, invoiceStatusEnum.REFUSED].includes(invoiceStatus),
        submitComOpt: {
          url: deleteMainUrl,
          gatherParams: () => {
            const ids = <any>[];
            const uuids = <any>[];
            ids.push(row.id);
            uuids.push(row.uuid);
            return { ids: ids, uuids: uuids };
          },
          close: (response: any) => {
            if (response.success) {
              handleSearch();
            }
          }
        }
      }
    ];
  };
  return {
    tableColumns,
    queryFrom,
    queryTable,
    handleSearch,
    handleSelect,
    clearSelection,
    cleanSelect,
    disabledDate,
    generalButtonOption,
    changeRow,
    isChecked,
    add,
    handleOpen,
    rowIdRef,
    detailRef,
    accountNoChange,
    getDelParams,
    checkNum,
    allowSort
  };
};

export default useList;
