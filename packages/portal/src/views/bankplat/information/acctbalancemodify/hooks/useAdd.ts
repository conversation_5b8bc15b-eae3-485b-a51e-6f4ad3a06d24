import { reactive, ref, shallowRef } from "vue";
import { goPage } from "./usePage";
import { useI18n } from "vue-i18n";
import type { HisBalanceTableTypeDTO, AcctBalanceModifyTypeDTO } from "../types";
import useApi from "../hooks/useApi";
import { FMessageBox } from "@dtg/frontend-plus";
import { formatDate } from "@/utils/date";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useAdd = (basicInfo: any) => {
  const { t } = useI18n();
  const formRef = ref();
  const { defaultOfficeId, defaultCurrencyId } = storeToRefs(useUserStoreHook());
  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    return basicInfo;
  };
  const { getHisBalanceList } = useApi();
  // 点击校验规则按钮之前校验
  const formValidator = async () => {
    //赋值明细
    if (checkedListRef.value.length > 0) {
      const detail = <any>[];
      checkedListRef.value.forEach((item: any) => {
        detail.push({
          accountId: item.accountId,
          balanceDate: item.date,
          beforeBalance: item.endingBalance,
          afterBalance: item.afterBalance,
          officeId: basicInfo.officeId,
          currencyId: basicInfo.currencyId
        });
      });
      basicInfo.acctBalanceModifyDetailList = detail;
    }
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    if (result) {
      result = checkSaveExistDetail();
    }
    return result;
  };
  //链接查找
  const goBack = () => {
    goPage("list");
  };
  //保存成功-跳转修改页
  const saveDataSuccess = (res: any) => {
    if (res.success) {
      goPage("modify", { id: res.data.id });
    }
  };
  const callBack = (res: any) => {
    if (res.success) {
      goBack();
    }
  };

  //银行账号放大镜修改事件
  const accountNoChange = (row: any) => {
    if (row) {
      basicInfo.accountId = row.id;
      basicInfo.accountName = row.accountName;
      basicInfo.accountNo = row.accountNo;
      basicInfo.officeId = row.officeId;
      basicInfo.currencyId = row.currencyId;
    }
    eventsChange();
  };
  //查询日期选择时间
  const queryDateChange = (value: string[]) => {
    basicInfo.beginningDate = value[0];
    basicInfo.endingDate = value[1];
  };
  // 已选列表
  const checkedListRef = ref<HisBalanceTableTypeDTO[]>([]);

  //存放所有的记录。关闭了一条移除一条
  const tempList = new Set();
  const handleSelect = (rows: HisBalanceTableTypeDTO[]) => {
    checkedListRef.value = rows;
    if (rows.length > 0) {
      const openArr = new Set();
      rows.forEach((row: any) => {
        const temp = { id: row.id };
        tempList.add(temp);
        openArr.add(temp);
      });
      const closeArr = new Set([...tempList].filter(x => !openArr.has(x)));
      if (closeArr.size > 0) {
        closeArr.forEach((b: any) => {
          closeEdit(b);
          tempList.delete(b.id);
        });
      }
      if (openArr.size > 0) {
        openArr.forEach(open => {
          openEdit(open);
        });
      }
    } else {
      tempList.forEach(item => {
        closeEdit(item);
        tempList.delete(item.id);
      });
    }
  };

  const handleSearch = () => {
    queryTable.value.renderTableData();
  };
  // 表格模板
  const queryTable = shallowRef();

  //列表数据渲染
  const hisBalanceTableData = reactive({ data: [], total: 0 });
  //查询历史余额渲染列表
  const doQuery = (baseInfo: AcctBalanceModifyTypeDTO) => {
    if (baseInfo.accountNo === null || baseInfo.accountNo === "") {
      FMessageBox.report(t("bankplat.information.acctbalancemodify.bankAccountNoPlaceHolder"));
      return true;
    }
    const params = {
      officeId: baseInfo.officeId,
      currencyId: baseInfo.currencyId,
      accountNo: baseInfo.accountNo,
      beginDate: baseInfo.beginningDate,
      endDate: baseInfo.endingDate
    };
    getHisBalanceList(params).then((res: any) => {
      if (res.success) {
        hisBalanceTableData.data = [];
        if (res.data.length === 0) {
          FMessageBox.report(t("bankplat.information.acctbalancemodify.queryHisBalancePlaceHolder"));
        } else {
          hisBalanceTableData.total = res.data.length;
          res.data.forEach((item: any) => {
            queryTable.value.closeEdit(item.id);
            const hisBalanceInfo = {
              id: "",
              accountNo: "",
              accountName: "",
              date: null,
              currencyName: "",
              endingBalance: null,
              afterBalance: null,
              accountId: null,
              officeId: null,
              currencyId: null,
              currencySymbol: ""
            };
            hisBalanceInfo.id = item.id;
            hisBalanceInfo.accountId = item.accountId;
            hisBalanceInfo.currencyId = item.currencyId;
            hisBalanceInfo.officeId = item.officeId;
            hisBalanceInfo.date = item.date;
            hisBalanceInfo.accountNo = item.accountNo;
            hisBalanceInfo.accountName = item.accountName;
            hisBalanceInfo.currencyName = item.currencyName;
            hisBalanceInfo.endingBalance = item.endingBalance;
            hisBalanceInfo.afterBalance = item.afterBalance;
            hisBalanceInfo.currencySymbol = item.currencySymbol;
            hisBalanceTableData.data.push(hisBalanceInfo);
          });
        }
      }
    });
  };
  //校验明细信息是否存在
  const checkSaveExistDetail = () => {
    let result = true;
    let errorCount = 0;
    if (basicInfo.acctBalanceModifyDetailList.length === 0) {
      FMessageBox.report(t("bankplat.information.acctbalancemodify.noSaveInfoPlaceHolder"));
      result = false;
    } else {
      basicInfo.acctBalanceModifyDetailList.forEach((item: any) => {
        if (item.afterBalance === null || item.afterBalance === "" || item.afterBalance === undefined) {
          errorCount++;
        }
      });
    }
    if (errorCount !== 0) {
      FMessageBox.report(t("bankplat.information.acctbalancemodify.noAfterBalancePlaceHolder"));
      result = false;
    }
    return result;
  };
  //开启编辑插槽
  const editFlag = ref(false);
  //页面初始化
  const initPage = () => {
    editFlag.value = true;
  };
  //编辑
  const openEdit = (row: any) => {
    queryTable.value.openEdit(row.id);
  };
  const closeEdit = (row: any) => {
    queryTable.value.closeEdit(row.id);
  };
  const now = new Date();
  const defaultDate = formatDate(now);
  basicInfo.beginningDate = defaultDate;
  basicInfo.endingDate = defaultDate;
  const createDate = ref([defaultDate, defaultDate]);
  // 新增表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      fixed: "left"
    },
    {
      width: "130px",
      prop: "date",
      label: t("bankplat.information.acctbalancemodify.queryDateLabel"),
      formatter: "date"
    },
    {
      width: "200px",
      prop: "accountNo",
      label: t("bankplat.information.acctbalancemodify.bankAccountNoLabel")
    },
    {
      width: "200px",
      prop: "accountName",
      label: t("bankplat.information.acctbalancemodify.bankAccountNameLabel")
    },
    {
      width: "100px",
      prop: "currencyName",
      label: t("bankplat.information.acctbalancemodify.currencyLabel")
    },
    {
      width: "100px",
      prop: "endingBalance",
      label: t("bankplat.information.acctbalancemodify.endingBalanceLabel"),
      formatter: "amount"
    },
    {
      width: "200px",
      prop: "afterBalance",
      label: t("bankplat.information.acctbalancemodify.afterBalanceLabel"),
      slots: { edit: "afterBalanceEdit" },
      formatter: "amount"
    }
  ];
  const clearSelection = () => {
    const arr = checkedListRef.value;
    arr.forEach((b: any) => {
      closeEdit(b);
    });
    checkedListRef.value = [];
  };

  const currencyChange = () => {
    basicInfo.accountId = null;
    basicInfo.accountName = "";
    basicInfo.accountNo = "";
    eventsChange();
  };

  const eventsChange = () => {
    hisBalanceTableData.data = [];
    queryTable.value.clearSelection();
  };

  return {
    formRef,
    gatherSaveInfo,
    formValidator,
    goBack,
    accountNoChange,
    tableColumns,
    handleSearch,
    handleSelect,
    queryTable,
    queryDateChange,
    hisBalanceTableData,
    doQuery,
    checkSaveExistDetail,
    initPage,
    openEdit,
    closeEdit,
    createDate,
    saveDataSuccess,
    callBack,
    defaultOfficeId,
    defaultCurrencyId,
    clearSelection,
    currencyChange,
    eventsChange
  };
};
export default useAdd;
