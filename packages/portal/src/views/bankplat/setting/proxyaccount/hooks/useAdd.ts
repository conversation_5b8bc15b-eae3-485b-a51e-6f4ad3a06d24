import { ref, shallowRef } from "vue";
import { goPage } from "./usePage";
import type { Office } from "../types";

export const useAdd = (basicInfo: any) => {
  const formRef = ref();
  //币种下拉选
  const currencyRef = shallowRef();
  const currencyExtraData = ref<Office>({ officeId: basicInfo.officeId });
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  const callBack = (res: any) => {
    if (res.success) {
      goBack();
    }
  };
  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    return basicInfo;
  };
  // 选中财司账户事件
  const checkProxyAcct = (row: any) => {
    if (row) {
      basicInfo.settleAccountId = row.id;
      basicInfo.settleAccountNo = row.accountNo;
      basicInfo.settleAccountName = row.accountName;
    } else {
      basicInfo.settleAccountId = null;
      basicInfo.settleAccountNo = "";
      basicInfo.settleAccountName = "";
    }
  };
  // 点击保存按钮，弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };

  //机构change事件
  const changeOffice = (value: any, info: any) => {
    currencyExtraData.value.officeId = info.key;
    currencyRef.value.initRemoteData();
  };
  return {
    formRef,
    gatherSaveInfo,
    goBack,
    formValidator,
    checkProxyAcct,
    changeOffice,
    currencyRef,
    currencyExtraData,
    callBack
  };
};

export default useAdd;
