<template>
  <f-blank-scene :title="t('bankplat.setting.proxyaccount.add.addTitle')" :isPoint="false">
    <f-multi-form-panel ref="formRef" :model="basicInfoRef" :rules="rules" :column="3">
      <f-panel id="form1">
        <f-form-item :label="t('bankplat.setting.proxyaccount.officeLabel')" prop="officeId">
          <f-select
            v-model="basicInfoRef.officeId"
            filterable
            value-key="key"
            label="value"
            :url="officeSelectorsUrl"
            auto-select
            method="post"
            auto-init
            :placeholder="t('bankplat.setting.proxyaccount.officePlaceHolder')"
            @change="changeOffice"
          />
        </f-form-item>
        <f-form-item :label="t('bankplat.setting.proxyaccount.currencyLabel')" prop="currencyId">
          <f-select
            v-model="basicInfoRef.currencyId"
            :placeholder="t('bankplat.setting.proxyaccount.currencyPlaceHolder')"
            value-key="key"
            label="value"
            :url="currencySelectorsUrl"
            auto-select
            method="post"
            ref="currencyRef"
            :extra-data="currencyExtraData"
          />
        </f-form-item>

        <f-form-item :label="t('bankplat.setting.proxyaccount.channelLabel')" for="channelId" prop="channelId">
          <f-magnifier-single
            v-model="basicInfoRef.channelId"
            :title="t('bankplat.setting.proxyaccount.channelGlassLabel')"
            :url="channelNameMagnifierUrl"
            :placeholder="t('bankplat.setting.proxyaccount.channelPlaceHolder')"
            method="post"
            row-key="id"
            row-label="name"
            input-key="condition"
            auto-init
          >
            <f-magnifier-column prop="code" :label="t('bankplat.setting.proxyaccount.channelNoLabel')" />
            <f-magnifier-column prop="name" :label="t('bankplat.setting.proxyaccount.channelLabel')" />
          </f-magnifier-single>
        </f-form-item>

        <f-form-item
          :label="t('bankplat.setting.proxyaccount.financeAcctLabel')"
          for="settleAccountId"
          prop="settleAccountId"
        >
          <f-magnifier-single
            v-model="basicInfoRef.settleAccountId"
            :title="t('bankplat.setting.proxyaccount.financeAcctGlassLabel')"
            :url="getSKAccount"
            :placeholder="t('bankplat.setting.proxyaccount.financeAcctPlaceHolder')"
            method="post"
            row-key="id"
            row-label="accountNo"
            input-key="condition"
            :params="{
              officeIds: [basicInfoRef.officeId],
              currencyIds: [basicInfoRef.currencyId]
            }"
            @change="checkProxyAcct"
          >
            <f-magnifier-column prop="accountNo" :label="t('bankplat.setting.proxyaccount.financeAcctNoLabel')" />
            <f-magnifier-column prop="accountName" :label="t('bankplat.setting.proxyaccount.financeAcctNameLabel')" />
          </f-magnifier-single>
        </f-form-item>

        <!--财司账户名-->
        <f-form-item
          :label="t('bankplat.setting.proxyaccount.financeAcctNameLabel')"
          for="settleAccountName"
          prop="settleAccountName"
        >
          <f-input v-model="basicInfoRef.settleAccountName" :disabled="true" />
        </f-form-item>

        <!--映射银行虚户-->
        <f-form-item
          :label="t('bankplat.setting.proxyaccount.mapAccountLabel')"
          for="customAccountNo"
          prop="customAccountNo"
        >
          <f-input
            v-model="basicInfoRef.customAccountNo"
            :placeholder="t('bankplat.setting.proxyaccount.inputMapAccountPlaceHolder')"
            maxlength="50"
          />
        </f-form-item>

        <!--映射银行虚户名-->
        <f-form-item
          :label="t('bankplat.setting.proxyaccount.mapAccountNameLabel')"
          for="customAccountName"
          prop="customAccountName"
        >
          <f-input
            v-model="basicInfoRef.customAccountName"
            :placeholder="t('bankplat.setting.proxyaccount.mapAccountNamePlaceHolder')"
            maxlength="50"
          />
        </f-form-item>

        <f-form-item :label="t('bankplat.setting.proxyaccount.acctMaintainIsSyncLabel')" for="statusId" prop="statusId">
          <f-input v-model="basicInfoRef.sync" :disabled="true" :value="t('bankplat.setting.proxyaccount.noSync')" />
        </f-form-item>

        <f-form-item
          :label="t('bankplat.setting.proxyaccount.acctMaintainRemarksLabel')"
          for="remarks"
          prop="remarks"
          :employ="2"
        >
          <f-textarea
            v-model="basicInfoRef.remarks"
            :placeholder="t('bankplat.setting.proxyaccount.remarksPlaceHolder')"
            :min-rows="2"
            maxlength="300"
            show-word-limit
          />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="proxySaveUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="callBack"
      />
      <f-button type="info" plain @click.prevent="goBack">{{ t("bankplat.setting.proxyaccount.goBackBtn") }}</f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { proxyAccountType, useProxyAccountRules } from "../hooks/useProxyAccount";
import { currencySelectorsUrl, officeSelectorsUrl, channelNameMagnifierUrl } from "@/views/bankplat/common/url";
import { proxySaveUrl, getSKAccount } from "@/views/bankplat/setting/proxyaccount/url";
import useAdd from "../hooks/useAdd";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const { basicInfoRef } = proxyAccountType();

const rules = useProxyAccountRules();

const {
  formRef,
  gatherSaveInfo,
  goBack,
  formValidator,
  checkProxyAcct,
  changeOffice,
  currencyRef,
  currencyExtraData,
  callBack
} = useAdd(basicInfoRef);
</script>
