<template>
  <f-blank-scene :title="t('bankplat.setting.inneracctmap.add.addTitle')" :isPoint="false">
    <f-multi-form-panel ref="formRef" :model="basicInfoRef" :rules="rules" :column="3">
      <f-panel id="form1" :title="t('bankplat.setting.inneracctmap.basicInfoTitle')">
        <f-form-item :label="t('bankplat.setting.inneracctmap.officeLabel')" prop="officeId">
          <f-select
            v-model="basicInfoRef.officeId"
            filterable
            value-key="key"
            label="value"
            :url="officeSelectorsUrl"
            auto-select
            method="post"
            :placeholder="t('bankplat.setting.inneracctmap.officePlaceHolder')"
            @change="changeOffice"
          />
        </f-form-item>
        <f-form-item :label="t('bankplat.setting.inneracctmap.currencyLabel')" prop="currencyId">
          <f-select
            v-model="basicInfoRef.currencyId"
            :placeholder="t('bankplat.setting.inneracctmap.currencyPlaceHolder')"
            value-key="key"
            label="value"
            :url="currencySelectorsUrl"
            method="post"
            ref="currencyRef"
            :extra-data="currencyExtraData"
            @change="changeCurrency"
            auto-select
          />
        </f-form-item>
        <!--渠道-->
        <f-form-item :label="t('bankplat.setting.inneracctmap.channelNameLabel')" for="channelId" prop="channelId">
          <f-magnifier-single
            v-model="basicInfoRef.channelId"
            :title="t('bankplat.setting.inneracctmap.channelMagnifierTitle')"
            :url="channelNameMagnifierUrl"
            :placeholder="t('bankplat.setting.inneracctmap.channelPlaceHolder')"
            method="post"
            row-key="id"
            row-label="name"
          >
            <f-magnifier-column prop="code" :label="t('bankplat.setting.inneracctmap.channelCodeLabel')" />
            <f-magnifier-column prop="name" :label="t('bankplat.setting.inneracctmap.channelNameLabel')" />
          </f-magnifier-single>
        </f-form-item>

        <f-form-item
          :label="t('bankplat.setting.inneracctmap.businessTypeLabel')"
          for="businessType"
          prop="businessType"
        >
          <f-select
            v-model="basicInfoRef.businessType"
            :data="businessTypeConst"
            :placeholder="t('bankplat.setting.inneracctmap.businessTypePlaceHolder')"
          />
        </f-form-item>
        <!--银行账号-->
        <f-form-item
          :label="t('bankplat.setting.inneracctmap.bankAccountNoLabel')"
          for="accountId"
          prop="accountId"
          v-if="basicInfoRef.businessType === businessTypeConst.AGENT"
        >
          <f-magnifier-single
            v-model="basicInfoRef.accountId"
            :title="t('bankplat.setting.inneracctmap.bankAccountNoTitle')"
            :url="bankAccountMagnifierUrl"
            :placeholder="t('bankplat.setting.inneracctmap.bankAccountNoPlaceHolder')"
            method="post"
            row-key="id"
            row-label="accountNo"
            :params="{
              ownerType: 1,
              whetherIncludeCancleStatus: 0,
              officeId: basicInfoRef.officeId,
              currencyId: basicInfoRef.currencyId
            }"
            @change="bankAccountChange"
            @clear="bankAccountClear"
          >
            <f-magnifier-column prop="accountNo" :label="t('bankplat.setting.inneracctmap.bankAccountNoLabel')" />
            <f-magnifier-column prop="accountName" :label="t('bankplat.setting.inneracctmap.bankAccountNameLabel')" />
            <f-magnifier-column prop="openBankName" :label="t('bankplat.setting.inneracctmap.openBankNameLabel')" />
            <f-magnifier-column prop="currencyInfoName" :label="t('bankplat.setting.inneracctmap.currencyLabel')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('bankplat.setting.inneracctmap.bankAccountNoLabel')"
          for="accountId"
          prop="accountId"
          v-if="basicInfoRef.businessType !== businessTypeConst.AGENT"
        >
          <f-input v-model="basicInfoRef.accountId" :disabled="true" />
        </f-form-item>
        <!--账户名称-->
        <f-form-item
          :label="t('bankplat.setting.inneracctmap.bankAccountNameLabel')"
          for="bankAccountName"
          prop="bankAccountName"
        >
          <f-input v-model="basicInfoRef.bankAccountName" :disabled="true" />
        </f-form-item>

        <f-form-item :label="t('bankplat.setting.inneracctmap.innerAcctMapLabel')" prop="innerAcctNo" for="innerAcctNo">
          <f-magnifier-single
            v-model="basicInfoRef.innerAcctNo"
            :title="t('bankplat.setting.inneracctmap.innerAcctMapTitle')"
            :url="innerAcctMagnifiersUrl"
            :placeholder="t('bankplat.setting.inneracctmap.innerAcctMapPlaceHolder')"
            method="post"
            row-key="ACCOUNTNO"
            row-label="ACCOUNTNO"
            input-key="condition"
            @change="checkInnerAcct"
            :params="innerAcctNoParams"
          >
            <f-magnifier-column prop="ACCOUNTNO" :label="t('bankplat.setting.inneracctmap.accountNoLabel')" />
            <f-magnifier-column prop="ACCOUNTNAME" :label="t('bankplat.setting.inneracctmap.accountNameLabel')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('bankplat.setting.inneracctmap.innerAcctNameColLabel')"
          for="innerAcctName"
          prop="innerAcctName"
        >
          <f-input v-model="basicInfoRef.innerAcctName" :disabled="true" />
        </f-form-item>

        <f-form-item />

        <f-form-item
          :label="t('bankplat.setting.inneracctmap.mapBankVirAcctLabel')"
          for="innerAcctMap"
          prop="innerAcctMap"
        >
          <f-input
            v-model="basicInfoRef.innerAcctMap"
            :placeholder="t('bankplat.setting.inneracctmap.mapBankVirAcctPlaceHolder')"
            maxlength="50"
          />
        </f-form-item>
        <f-form-item
          :label="t('bankplat.setting.inneracctmap.innerAcctNameMapColLabel')"
          for="innerAcctNameMap"
          prop="innerAcctNameMap"
        >
          <f-input
            v-model="basicInfoRef.innerAcctNameMap"
            :placeholder="t('bankplat.setting.inneracctmap.innerAcctNameMapPlaceHolder')"
            maxlength="50"
          />
        </f-form-item>
        <f-form-item
          :label="t('bankplat.setting.inneracctmap.innerAcctBankMapColLabel')"
          for="innerAcctBankMap"
          prop="innerAcctBankMap"
        >
          <f-input
            v-model="basicInfoRef.innerAcctBankMap"
            :placeholder="t('bankplat.setting.inneracctmap.innerAcctBankMapPlaceHolder')"
            maxlength="50"
          />
        </f-form-item>
        <f-form-item :label="t('bankplat.setting.inneracctmap.remarksLabel')" for="remarks" prop="remarks" :employ="2">
          <f-textarea
            v-model="basicInfoRef.remarks"
            :placeholder="t('bankplat.setting.inneracctmap.remarksPlaceHolder')"
            :min-rows="2"
            maxlength="300"
            show-word-limit
          />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="gatherSaveInfo"
        :url="innerAcctMapAddUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="saveDataSuccess"
      />
      <f-button type="info" plain @click.prevent="goBack">{{
        t("bankplat.setting.inneracctmap.serachLinkBtn")
      }}</f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { useInnerAcctMap, useInnerAcctMapRules } from "../hooks/useInnerAcctMap";
import {
  officeSelectorsUrl,
  innerAcctMagnifiersUrl,
  currencySelectorsUrl,
  innerAcctMapAddUrl,
  channelNameMagnifierUrl,
  bankAccountMagnifierUrl
} from "@/views/bankplat/common/url";
import useAdd from "../hooks/useAdd";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const businessTypeConst = useConst("bankplat.BusinessType");

const { basicInfoRef } = useInnerAcctMap();

const rules = useInnerAcctMapRules();

const {
  formRef,
  gatherSaveInfo,
  goBack,
  formValidator,
  checkInnerAcct,
  changeOffice,
  currencyRef,
  currencyExtraData,
  innerAcctNoParams,
  changeCurrency,
  bankAccountChange,
  bankAccountClear,
  saveDataSuccess
} = useAdd(basicInfoRef);
</script>
