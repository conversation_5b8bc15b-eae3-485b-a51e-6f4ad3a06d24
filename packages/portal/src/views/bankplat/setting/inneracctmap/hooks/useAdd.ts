import { ref, shallowRef } from "vue";
import { goPage } from "./usePage";
import type { OfficeDTO } from "../types";

export const useAdd = (basicInfo: any) => {
  const formRef = ref();
  //币种下拉选
  const currencyRef = shallowRef();
  const currencyExtraData = ref<OfficeDTO>({ officeId: basicInfo.officeId });
  const innerAcctNoParams = ref<any>({ officeId: basicInfo.officeId, currencyId: basicInfo.currencyId });
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };

  // 保存前收集参数信息，数据在这里进行转换
  const gatherSaveInfo = () => {
    return basicInfo;
  };
  // 选中内部账户事件
  const checkInnerAcct = (row: any) => {
    if (row) {
      basicInfo.innerAcctId = row.ID;
      basicInfo.innerAcctNo = row.ACCOUNTNO;
      basicInfo.innerAcctName = row.ACCOUNTNAME;
    } else {
      basicInfo.innerAcctId = null;
      basicInfo.innerAcctNo = "";
      basicInfo.innerAcctName = "";
    }
  };
  // 点击保存按钮，弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await formRef.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };

  //机构change事件
  const changeOffice = (value: any, info: any) => {
    basicInfo.innerAcctId = null;
    basicInfo.innerAcctNo = "";
    basicInfo.innerAcctName = "";
    currencyExtraData.value.officeId = info.key;
    currencyRef.value.initRemoteData();
    innerAcctNoParams.value.officeId = info.key;
  };
  //币种change事件
  const changeCurrency = (value: any) => {
    basicInfo.innerAcctId = null;
    basicInfo.innerAcctNo = "";
    basicInfo.innerAcctName = "";
    innerAcctNoParams.value.currencyId = value;
  };
  //银行中账号选择事件
  const bankAccountChange = (row: any) => {
    basicInfo.bankAccountName = row.accountName;
  };
  //银行中账号清除事件
  const bankAccountClear = () => {
    basicInfo.bankAccountName = "";
  };
  const saveDataSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };
  return {
    formRef,
    gatherSaveInfo,
    goBack,
    formValidator,
    checkInnerAcct,
    changeOffice,
    currencyRef,
    currencyExtraData,
    innerAcctNoParams,
    changeCurrency,
    bankAccountChange,
    bankAccountClear,
    saveDataSuccess
  };
};

export default useAdd;
