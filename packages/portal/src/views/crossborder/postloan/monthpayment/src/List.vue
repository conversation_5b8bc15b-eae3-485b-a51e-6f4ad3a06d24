<template>
  <f-query-scene :title="t('crossborder.postloan.monthpayment.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTableRef"
        row-key="id"
        query-comp-id="crossborder-postloan-monthpayment-query-001"
        table-comp-id="crossborder-postloan-monthpayment-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :form-data="queryForm"
        show-header
        auto-reset
        auto-init
        :export-exclude="['operate']"
        :export-url="exportUrl"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        table-type="Record"
        :allow-sort="allowSort"
        :post-params="postParams"
        :label-width="150"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
      >
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.queryTitle1')" prop="officeIdList">
            <f-select
              v-model="queryForm.officeIdList"
              :url="getOfficeInfoUrl"
              value-key="officeId"
              label="officeName"
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.queryTitle2')" prop="currencyIdList">
            <f-select
              v-model="queryForm.currencyIdList"
              value-key="currencyId"
              label="currencyName"
              :data="globalCurrencyList"
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 借款单位名称 多选 放大镜 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.queryTitle3')" prop="loanClientIdList">
            <f-magnifier-multi
              :title="t('crossborder.postloan.monthpayment.operateColumn3')"
              :url="getClientInfoUrl"
              v-model="queryForm.loanClientIdList"
              method="post"
              row-key="clientId"
              selected-key="clientCode"
              selected-label="clientName"
              row-label="clientCode"
              input-key="clientCodeOrName"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column prop="clientCode" :label="t('crossborder.postloan.monthpayment.magnifierName1')" />
              <f-magnifier-column prop="clientName" :label="t('crossborder.postloan.monthpayment.magnifierName2')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 状态 多选 下拉框 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.queryTitle5')" prop="checkStatus">
            <f-select
              v-model="queryForm.checkStatus"
              :data="
                checkStatusData.pickConst([
                  checkStatusData.SAVE,
                  checkStatusData.APPROVING,
                  checkStatusData.APPROVED,
                  checkStatusData.REFUSE,
                  checkStatusData.REVOKING,
                  checkStatusData.REVOKED
                ])
              "
              clearable
              blank-option
              init-if-blank
              filterable
              multiple
              collapse-tags
              select-all
            />
          </f-form-item>
          <!-- 放款日期 日期区间 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.operateColumn34')" prop="executeDate">
            <f-lax-range-date-picker v-model="queryForm.executeDate" />
          </f-form-item>
          <!-- 分类日期 日期区间 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.queryTitle6')" prop="assortDate">
            <f-lax-range-date-picker v-model="queryForm.assortDate" />
          </f-form-item>
          <!-- 合同编号 -->
          <f-form-item :label="t('crossborder.postloan.monthpayment.operateColumn7')" prop="contractCodeList">
            <f-magnifier-multi
              v-model="queryForm.contractCodeList"
              :title="t('crossborder.postloan.monthpayment.operateColumn7')"
              :url="contractListUrl"
              method="post"
              row-key="contractCode"
              row-label="loanClientName"
              input-key="contractCode"
              collapse-tags-tooltip
              :params="{
                crossBorderFlg: yesOrNo.YES,
                loanType: loanTypeData.CROSS_BORDER
              }"
            >
              <f-magnifier-column prop="contractCode" :label="t('crossborder.postloan.monthpayment.operateColumn7')" />
              <!-- 贷款业务类型 -->
              <f-magnifier-column
                prop="loanBusinessType"
                :label="t('crossborder.postloan.monthpayment.operateColumn12')"
                :formatter="{ name: 'const', const: 'core.LoanBusinessCategory' }"
                :filter-select="searchLoanType"
              />
              <f-magnifier-column prop="loanClientName" :label="t('crossborder.postloan.monthpayment.tableColumn5')" />
              <!-- 合同金额 -->
              <f-magnifier-column
                prop="contractTotalAmount"
                formatter="amount"
                :label="t('crossborder.postloan.monthpayment.operateTitle38')"
              />
              <!-- 合同余额 -->
              <f-magnifier-column
                prop="contractBalance"
                formatter="amount"
                :label="t('crossborder.postloan.monthpayment.operateColumn8')"
              />
            </f-magnifier-multi>
          </f-form-item>
        </template>
        <template #code="{ row }">
          <f-button @click="goDetail(row)" link type="primary">{{ row.receiptCode }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detailRef" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useCurrency } from "@/hooks/useCommon";
import { useBatchOperate } from "@/hooks/biz/useBatchOperate";
import { useCommonList } from "@/hooks/biz/useCommonList";
import useList from "../hooks/useList";
import { getOfficeInfoUrl, listUrl, exportUrl, contractListUrl, getClientInfoUrl } from "../url";
import { KEY } from "../types";
import Detail from "./components/Detail.vue";

const { t } = useI18n();
const loanTypeData = useConst("common.LoanType");
const loanBusinessCategory = useConst("core.LoanBusinessCategory");
const searchLoanType = loanBusinessCategory;

//贷款业务类型
const checkStatusData = useConst("common.CheckStatus");
//是否
const yesOrNo = useConst("loan.YesOrNo");
// 币种列表
const { globalCurrencyList } = useCurrency();

const { selectableAll, handleSelect, clearSelection } = useBatchOperate();

const { rowId, goDetail, detailRef, queryTableRef } = useCommonList(KEY, {});

const { tableColumns, queryForm, allowSort, postParams } = useList();
</script>
