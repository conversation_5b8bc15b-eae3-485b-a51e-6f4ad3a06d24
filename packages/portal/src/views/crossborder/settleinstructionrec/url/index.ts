//获取当前开机日
export const openDateUrl = "{dayend}/api/v1/finance/dayend/remote/server/open-date";
//摘要放大镜
export const getAbstractsInfo = "{basic}/api/v1/finance/basic/remark/remote/server/list";
//币种下拉框
export const getCurrencyInfo = "{system-manage}/api/v1/finance/system/currency/remote/server/list";
//机构下拉框
export const getOfficeInfo = "{system-manage}/api/v1/finance/system/office/remote/server/list";
//客户放大镜
export const getClientInfo = "{clientmanage}/api/v1/finance/client/inner-client/remote/server/list";
//合同/协议号放大镜
export const queryContract =
  "{transaction-query}/api/v1/finance/query/trans-center/instruction/draft-instruction/remote/server/select-contract-magnifier";
//批量接收
export const batchRecUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/draft/batch-instruction-rec";
//审批同意
export const approvalAgreeUrl = "{workflow}/api/v1/finance/workflow/work-desk/do-approval";
//审批拒绝
export const approvalRefuseUrl = "{workflow}/api/v1/finance/workflow/work-desk/do-approval";
// 结算指令接收通知单放大镜
export const getNoticeInfo =
  "{transaction-query}/api/v1/finance/trans-center/instruction-rec/instruction-notice-list-magnifier";
// 结算指令接收列表查询
export const searchUrl = "{transaction-query}/api/v1/finance/trans-center/instruction-rec/loan-list";
// 结算指令接收列表导出
export const exportUrl = "{transaction-query}/api/v1/finance/trans-center/instruction-rec/loan-export-list";

// 跨境融通贷款支付--提交url地址
export const submitLoanPayUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-pay/submit";
// 跨境融通贷款支付--保存url地址
export const saveLoanPayUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-pay/save";
// 跨境融通贷款支付--列表提交url地址
export const listSubmitLoanPayUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-pay/cancel-submit";
// 跨境融通贷款支付--撤销url地址
export const cancelLoanPayUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-pay/cancel-submit";
// 跨境融通贷款支付--作废url地址
export const deleteLoanPayUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-pay/delete";
// 跨境融通贷款支付--详情查询
export const getLoanPayDetailInfoUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/loan-pay/detail-query";
// 跨境融通贷款支付--生成付款交易
export const createPayTransactionUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-pay/create-transaction-list-submit";

// 跨境融通放款提交url地址
export const submitPayNoticeUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/pay-notice/submit";
// 跨境融通还款提交url地址
export const submitRepayNoticeUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/repay-notice/submit";
// 跨境融通免还提交url地址
export const submitLoanFreeUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-free/submit";
// 跨境融通结息提交url地址
export const submitSettleInterestUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/settle-interest/submit";

// 跨境融通放款作废url地址
export const noticePayInvalidUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/pay-notice/delete";
// 跨境融通还款作废url地址
export const noticeRepayInvalidUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/repay-notice/delete";
// 跨境融通免还作废url地址
export const freeApplyInvalidUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-free/delete";
// 跨境融通结息作废url地址
export const settleInterestInvalidUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/settle-interest/delete";

// 跨境融通放款撤销url地址
export const cancelPayNoticeUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/pay-notice/cancel-submit";
// 跨境融通还款撤销url地址
export const cancelRepayNoticeUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/repay-notice/cancel-submit";
// 跨境融通免还撤销url地址
export const cancelLoanFreeUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/loan-free/cancel-submit";
// 跨境融通结息撤销url地址
export const cancelSettleInterestUrl =
  "{settlement-counter-operate}/api/v1/finance/operate/settlement/counter/loan/cross-border/settle-interest/cancel";

// 跨境融通贷款支付--详情查询
export const getLoanPayDetailInfoByIdUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/loan-pay/detail-query-by-id";
// 跨境融通免还详情查询
export const getFreeApplyDetailInfoByIdUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/loan-free/detail-query-by-id";
// 跨境融通放款详情查询
export const getNoticePayDetailInfoByIdUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/pay-notice/detail-query-by-id";
// 跨境融通还款详情查询
export const getNoticeRepayDetailInfoByIdUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/repay-notice/detail-query-by-id";
// 跨境融通结息详情查询
export const getSettleInterestDetailInfoByIdUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/settle-interest/detail-query-by-id";

// 跨境融通免还详情查询
export const getFreeApplyDetailInfoUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/loan-free/detail-query";
// 跨境融通放款详情查询
export const getNoticePayDetailInfoUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/pay-notice/detail-query";
// 跨境融通还款详情查询
export const getNoticeRepayDetailInfoUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/repay-notice/detail-query";
// 跨境融通结息详情查询
export const getSettleInterestDetailInfoUrl =
  "{settlement-counter-query}/api/v1/finance/query/settlement/counter/loan/cross-border/settle-interest/detail-query";

// 代客结汇
export const forexSettlementCusUrl = "{}";
//活期内部账户放大镜
export const getInnerAccountInfo = "{inner-account-query}/api/v1/finance/inner-account/remote/server/list";
//开户行放大镜
export const getOpenBankInfo = "{basic}/api/v1/finance/basic/sett-branch/remote/server/list";
//数据字典Url
export const getMasterDataInfo = "{master-data}/api/v1/finance/query/master-data/dictionary/remote/server/list";
//查询手续费率
export const queryChargeRate =
  "{charge}/api/v1/finance/charge/charge-set/charge-rule-set/remote/server/get-active-rule-list";
export const batchPrintExportUrl = "{forex-manage-operate}/api/v1/finance/forex/core/print/print-start";
//根据币种ID查询币种信息
export const getCurrencyByIdUrl = "{system-manage}/api/v1/finance/system/currency/remote/server/get-by-id";
//查询账户余额信息
export const queryAccountBalanceInfo =
  "{transaction-query}/api/v1/finance/trans-center/acct-balance/remote/server/get-acct-balance";
//复制
export const getBasicInfoSetInfo =
  "{forex-manage-query}/api/v1/finance/query/forex/counter/crossborderpayreceive/basic/get-basic-info";
// 打印申请书
export const getPrintRelationUrl = "{forex-manage-query}/api/v1/finance/forex/core/print/get-print-relation";
//获取用途代码设置信息
export const getPurposeSetList =
  "{forex-manage-query}/api/v1/finance/query/forex/core/set/purpose-code-set/get-purpose-list";
//收款方账户放大镜
export const getExternalAccountInfoUrl =
  "{forex-manage-query}/api/v1/finance/query/forex/counter/crossborderpayreceive/account/no-page-list";
export const cashPoolChannelUrl =
  //资金池通道查询
  "{loan-core-query}/api/v1/finance/query/loan-core/crossborder/set/capitalpool/query-pool-channel-select-list";
//查询银行账户信息
export const queryBankAccount = "{bankportal}/api/v1/finance/bank-plat/bank-acct/remote/server/list";
//数据字典
export const masterDataUrl = "{master-data}/api/v1/finance/query/master-data/dictionary/remote/server/list";
//用途代码放大镜
export const purposeCodeUrl =
  "{forex-interface-query}/api/v1/finance/query/forex/core/set/purpose-code-set/get-purpose-list";
//导出EXCEL
export const exportExcelUrl =
  "{forex-manage-query}/api/v1/finance/query/forex/counter/crossborderpayreceive/payment/export-excel";
// 还款通过编号查询详情
export const getLoanCounterRepayDetailByCodeUrl =
  "{loan-counter-query}/api/v1/finance/query/loan-counter/notice/repay/get-by-code";
// 免还通过编号查询详情
export const getLoanCounterFreeDetailByCodeUrl =
  "{loan-counter-query}/api/v1/finance/query/loan-counter/notice/free-apply/get-by-code";
// 影像补录
export const attmRepairUploadUrl =
  "{forex-manage-operate}/api/v1/finance/operate/forex/counter/crossborderpayreceive/payment/attm-repair-upload";
//获取柜面放款详情信息
export const getLoanCounterPayDetailByCodeUrl =
  "{loan-counter-query}/api/v1/finance/query/loan-counter/notice/pay/get-by-business-no";
//内部客户信息查询
export const innerClientInfoQueryByClientIdUrl =
  "{clientmanage}/api/v1/finance/client/inner-client/remote/server/get-by-id";
