<template>
  <f-multi-form-panel ref="form1" :model="basicInfo" :column="3">
    <!-- 基础信息 -->
    <f-panel :title="t('crossborder.settleinstructionrec.disbursement.basicInfo')">
      <!--机构-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.office')" prop="officeName">
        <f-input v-model="basicInfo.officeName" disabled />
      </f-form-item>
      <!-- 币种 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.currency')" prop="currencyName">
        <f-input v-model="basicInfo.currencyName" disabled />
      </f-form-item>
      <!-- 付款类型 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentType')" prop="paymentType">
        <f-scene-view
          :search="basicInfo.paymentType"
          :data="paymentType.pickConst([paymentType.DOMESTIC_PAYMENT, paymentType.OVERSEA_PAYMENT])"
          params="value"
          label="label"
        />
      </f-form-item>
      <!-- 汇款方式 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentMethod')"
        v-if="crossBorderShowFlag"
        prop="paymentMethod"
      >
        <f-scene-view :search="basicInfo.paymentMethod" :data="paymentMethod" params="value" label="label" />
      </f-form-item>
      <!-- 发电等级 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.sendTelegramLevel')"
        v-if="crossBorderShowFlag"
      >
        <f-radio-group v-model="basicInfo.sendTelegramLevel" :data="remitPriorityEnum" reverseLable="value" disabled />
      </f-form-item>
      <!-- 业务通道 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.businessChannel')"
        prop="businessChannel"
        v-if="crossBorderShowFlag"
      >
        <f-scene-view :search="basicInfo.businessChannel" :data="businessChannel" params="value" label="label" />
      </f-form-item>
      <!-- 付款金额-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentAmount')" prop="paymentAmount">
        <f-amount
          v-model="basicInfo.paymentAmount"
          tooltip
          :negative="false"
          :precision="2"
          :symbol="basicInfo.currencySymbol"
          disabled
        />
      </f-form-item>
    </f-panel>
    <!-- 付款方信息 -->
    <f-panel :title="t('crossborder.settleinstructionrec.disbursement.paymentInfo')" id="form3">
      <!-- 客户名称 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.clientName')" prop="payClientName">
        <f-input v-model="basicInfo.payClientName" disabled />
      </f-form-item>
      <!--付款人账号-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentAccountCode')" prop="payAcctNo">
        <f-input v-model="basicInfo.payAcctNo" disabled />
      </f-form-item>
      <!-- 付款人账户名称 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentAccountName')" prop="payAcctName">
        <f-input v-model="basicInfo.payAcctName" disabled />
      </f-form-item>
      <!-- 账户余额-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.accountBalance')" prop="payBalance">
        <f-amount
          v-model="basicInfo.payBalance"
          tooltip
          :negative="false"
          disabled
          :symbol="basicInfo.currencySymbol"
        />
      </f-form-item>
      <!-- 账户可用余额-->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.availableBalance')"
        prop="payAvailableBalance"
      >
        <f-amount
          v-model="basicInfo.payAvailableBalance"
          tooltip
          :negative="false"
          :symbol="basicInfo.currencySymbol"
          disabled
        />
      </f-form-item>
      <!-- 组织机构代码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.organizationCode')"
        prop="payOrgCode"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.payOrgCode" maxlength="10" disabled />
      </f-form-item>
      <!-- 付款人名称 付款类型为‘境外付款’时，只能填写英文 根据[内部客户信息]带出，可手工调整-->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentName')"
        prop="paymentName"
        v-if="crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.paymentName"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.paymentName" maxlength="140" disabled />
      </f-form-item>
      <!-- 付款人地址 境内付款时非必输，其他情况必输 付款类型为‘境外付款’时，只能填写英文-->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentAddress')"
        prop="payAddress"
        v-if="crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT && crossBorderShowFlag"
          v-model="basicInfo.paymentAddress"
          maxlength="280"
          disabled
        />
        <f-input v-else v-model="basicInfo.payAddress" maxlength="280" disabled />
      </f-form-item>
      <!-- 付款方CIPS ID -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.paymentCipsId')"
        prop="paymentCipsId"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.paymentCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 付款方LEI码 -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.paymentLeiCode')"
        prop="paymentLeiCode"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.paymentLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 付款方EID码 -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.paymentEid')"
        prop="paymentEid"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.paymentEid" maxlength="60" disabled />
      </f-form-item>
      <!-- 开户行 -->
      <!--开户行、银行账号-->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentBankAccountName')"
        prop="payBankName"
      >
        <f-input v-model="basicInfo.payBankName" maxlength="60" disabled />
      </f-form-item>
      <!-- 银行账号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentBankAccountCode')"
        prop="payBankAcctNo"
      >
        <f-input v-model="basicInfo.payBankAcctNo" disabled />
      </f-form-item>
      <!-- 开户行SWIFT号 -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.paymentBankSwiftCode')"
        prop="paymentBankSwiftCode"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.paymentBankSwiftCode" disabled />
      </f-form-item>
      <!-- 财务公司CIPS ID -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.cipsId')"
        prop="cipsId"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.cipsId" maxlength="35" disabled />
      </f-form-item>
      <!--财务公司LEI码-->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.leiCode')"
        prop="leiCode"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.leiCode" maxlength="20" disabled />
      </f-form-item>
    </f-panel>
    <!-- 收款方信息 -->
    <f-panel :title="t('crossborder.settleinstructionrec.disbursement.receiveInfo')" id="form4">
      <!-- 收款人账户类型 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.recAcctType')" prop="recAcctType">
        <f-scene-view :search="basicInfo.recAcctType" :data="recAcctTypeData" params="value" label="label" />
      </f-form-item>
      <!-- 是否对私付款 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.recAcctType')"
        prop="isPaymentPrivate"
        v-if="!crossBorderShowFlag"
      >
        <f-scene-view :search="basicInfo.isPaymentPrivate" :data="yesOrNoData" params="value" label="label" />
      </f-form-item>
      <!-- 收款人账号 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.receiveBankAccountCode')" prop="receiveNo">
        <f-input v-model="basicInfo.receiveNo" disabled />
      </f-form-item>
      <!-- 收款人名称 境外付款时，必须为英文 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveBankAccountName')"
        prop="receiveName"
      >
        <f-input v-model="basicInfo.receiveName" disabled />
      </f-form-item>
      <!-- 是否IBAN账户 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.ibankAccount')" v-if="crossBorderShowFlag">
        <f-switch v-model="basicInfo.ibankAccount" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" disabled />
      </f-form-item>
      <!-- 收款人常驻国家（地区）代码 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.countryCode')" prop="countryCode">
        <f-input v-model="basicInfo.countryName" disabled />
      </f-form-item>
      <!-- 收款人所在省 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.receiveProvince')" prop="receiveProvince">
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.receiveProvince"
          maxlength="20"
          disabled
        />
        <f-input v-else v-model="basicInfo.receiveProvince" maxlength="20" disabled />
      </f-form-item>
      <!-- 收款人所在城市 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.receiveCity')" prop="receiveCity">
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.receiveCity"
          maxlength="20"
          disabled
        />
        <f-input v-else v-model="basicInfo.receiveCity" maxlength="20" disabled />
      </f-form-item>
      <!-- 收款方CIPS ID -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.receiveCipsId')"
        prop="receiveCipsId"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款方LEI码 -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.receiveLeiCode')"
        prop="receiveLeiCode"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 收款方EID码 -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.receiveEid')"
        prop="receiveEid"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveEid" maxlength="60" disabled />
      </f-form-item>
      <!-- 收款人地址 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveAddress')"
        prop="receiveAddress"
        style="width: 66.6%"
        v-if="crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.receiveAddress"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.receiveAddress" maxlength="140" disabled />
      </f-form-item>
      <!-- 收款人开户行名称 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.receiveBankName')" prop="receiveBankName">
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT && crossBorderShowFlag"
          v-model="basicInfo.receiveBankName"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.receiveBankName" maxlength="140" disabled />
      </f-form-item>
      <!-- 收款行SWIFT号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveBankSwiftCode')"
        prop="receiveBankSwiftCode"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveBankSwiftCode" maxlength="11" disabled />
      </f-form-item>
      <!-- 收款行CNAPS号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveBankSwiftCode')"
        prop="receiveBankCnapsCode"
        v-if="basicInfo.paymentType === paymentType.DOMESTIC_PAYMENT"
      >
        <f-input v-model="basicInfo.receiveBankCnapsCode" disabled />
      </f-form-item>
      <!-- 收款行CIPS ID -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.receiveBankCipsId')"
        prop="receiveBankCipsId"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveBankCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款行LEI码 -->
      <f-form-item
        :label="t('forex.counter.crossborderpayreceive.payment.receiveBankLeiCode')"
        prop="receiveBankLeiCode"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveBankLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 收款行联行号 当付款类型=境内付款且收款行为中行时显示且必输 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveCnps')"
        prop="receiveCnps"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.receiveCnps" maxlength="5" disabled />
      </f-form-item>
      <!-- 收款行地址 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveBankAddress')"
        prop="receiveBankAddress"
        style="width: 66.6%"
        v-if="crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.receiveBankAddress"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.receiveBankAddress" maxlength="140" disabled />
      </f-form-item>
      <!-- 收款人(工人)证件类型 TODO没有 -->
      <!-- 收款人(工人)证件号码 TODO没有 -->
      <!-- 代理行名称 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.agencyBankName')"
        prop="agencyBankName"
        v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.agencyBankName"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.agencyBankName" maxlength="140" disabled />
      </f-form-item>
      <!-- 代理行SWIFT号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.agencyBankSwiftCode')"
        prop="agencyBankSwiftCode"
        v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.agencyBankSwiftCode" maxlength="11" disabled />
      </f-form-item>
      <!-- 收款人开户行在代理行账号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveAgencyBankCode')"
        prop="receiveAgencyBankCode"
        v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.receiveAgencyBankCode"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.receiveAgencyBankCode" maxlength="140" disabled />
      </f-form-item>
      <!-- 代理行地址 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.agencyBankAddress')"
        prop="agencyBankAddress"
        style="width: 100%"
        v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.agencyBankAddress"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.agencyBankAddress" maxlength="140" disabled />
      </f-form-item>
      <!-- 汇款附言 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentRemark')"
        prop="paymentRemark"
        style="width: 100%"
        v-if="crossBorderShowFlag"
      >
        <f-input
          v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
          v-model="basicInfo.paymentRemark"
          maxlength="140"
          disabled
        />
        <f-input v-else v-model="basicInfo.paymentRemark" maxlength="140" disabled />
      </f-form-item>
    </f-panel>
    <!-- 费用信息 -->
    <f-panel :title="t('crossborder.settleinstructionrec.disbursement.costInfo')" id="form5" v-if="crossBorderShowFlag">
      <!-- 国内外费用承担方 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.costBearing')" prop="costBearing">
        <f-scene-view :search="basicInfo.costBearing" :data="costBearing" params="value" label="label" />
      </f-form-item>
      <!-- 是否向客户收取 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.collectFromCustomer')">
        <f-switch
          v-model="basicInfo.collectFromCustomer"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          disabled
        />
      </f-form-item>
      <!-- 付费账户号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.chargeAccountCode')"
        prop="chargeAccountCode"
      >
        <f-input v-model="basicInfo.chargeAccountCode" disabled />
      </f-form-item>
      <!-- 费用总金额-->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.chargeTotalAmount')"
        prop="chargeTotalAmount"
      >
        <f-amount v-model="basicInfo.chargeTotalAmount" tooltip :negative="false" :precision="2" disabled />
      </f-form-item>
      <!--手续费率-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.chargeRate')" prop="chargeRate">
        <f-input v-model="basicInfo.chargeRate" disabled />
      </f-form-item>
      <!-- 优惠点数 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.preferentialPoint')"
        prop="preferentialPoint"
      >
        <f-input v-model="basicInfo.preferentialPoint" disabled />
      </f-form-item>
      <!-- 手续费金额-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.chargeAmount')" prop="chargeAmount">
        <f-amount v-model="basicInfo.chargeAmount" tooltip :negative="false" :precision="2" disabled />
      </f-form-item>
      <!-- 加急费率 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.urgentRate')" prop="urgentRate">
        <f-amount v-model="basicInfo.urgentRate" tooltip :negative="false" :precision="2" disabled symbol=" " />
      </f-form-item>
      <!-- 加急费金额-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.urgentAmount')" prop="urgentAmount">
        <f-amount v-model="basicInfo.urgentAmount" tooltip :negative="false" :precision="2" disabled />
      </f-form-item>
      <!-- 电报费金额 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.sendTelegramAmount')"
        prop="sendTelegramAmount"
      >
        <f-amount v-model="basicInfo.sendTelegramAmount" tooltip :negative="false" :precision="2" disabled />
      </f-form-item>
    </f-panel>
    <!-- 其他信息 -->
    <f-panel :title="t('crossborder.settleinstructionrec.disbursement.otherInfo')" id="form6">
      <!-- 款项类型 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.itemType')"
        prop="itemType"
        v-if="crossBorderShowFlag"
      >
        <f-scene-view
          :search="basicInfo.itemType"
          :data="
            itemType.pickConst([itemType.ADVANCE_PAYMENT, itemType.CASH_ON_DELIVERY, itemType.REFUND, itemType.OTHER])
          "
          params="value"
          label="label"
        />
      </f-form-item>
      <!-- 付汇性质 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.pfeNature')"
        prop="pfeNature"
        v-if="crossBorderShowFlag"
      >
        <f-scene-view :search="basicInfo.pfeNature" :data="pfeNature" params="value" label="label" />
      </f-form-item>
      <!-- 本笔款项是否为保税货物项下付款 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.bondedGoods')" v-if="crossBorderShowFlag">
        <f-switch v-model="basicInfo.bondedGoods" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" disabled />
      </f-form-item>
      <!-- 交易编码1 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.transCode1')"
        prop="transCode1"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.transCode1" disabled />
      </f-form-item>
      <!-- 对应金额1 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.transAmount1')"
        prop="transAmount1"
        v-if="crossBorderShowFlag"
      >
        <f-amount
          v-model="basicInfo.transAmount1"
          tooltip
          :negative="false"
          :precision="2"
          :symbol="basicInfo.currencySymbol"
          disabled
        />
      </f-form-item>
      <!-- 交易附言1 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.transRemark1')"
        prop="transRemark1"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.transRemark1" maxlength="50" disabled />
      </f-form-item>
      <!-- 交易编码2 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.transCode2')"
        prop="transCode2"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.transCode2" disabled />
      </f-form-item>
      <!-- 对应金额2 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.transAmount2')"
        prop="transAmount2"
        v-if="crossBorderShowFlag"
      >
        <f-amount
          v-model="basicInfo.transAmount2"
          tooltip
          :negative="false"
          :precision="2"
          :symbol="basicInfo.currencySymbol"
          disabled
        />
      </f-form-item>
      <!-- 交易附言2 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.transRemark2')"
        prop="transRemark2"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.transRemark2" maxlength="50" disabled />
      </f-form-item>
      <!-- 外汇局批件/备案表号/业务编号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.businessCode')"
        prop="businessCode"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.businessCode" maxlength="50" disabled />
      </f-form-item>
      <!-- 用途代码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.purposeCode')"
        prop="purposeName"
        v-if="basicInfo.businessType === businessType.PURCHASE_FOREIGN_EXCHANGE && crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.purposeName" disabled />
      </f-form-item>
      <!-- 合同号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.contractCode')"
        prop="contractCode"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.contractCode" maxlength="50" disabled />
      </f-form-item>
      <!-- 发票号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.invoiceCode')"
        prop="invoiceCode"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.invoiceCode" maxlength="50" disabled />
      </f-form-item>
      <!-- 申请人名称 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.applyUserName')"
        prop="applyUserName"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.applyUserName" maxlength="30" disabled />
      </f-form-item>
      <!-- 申请人电话 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.applyUserPhone')"
        prop="applyUserPhone"
        v-if="crossBorderShowFlag"
      >
        <f-input v-model="basicInfo.applyUserPhone" maxlength="30" disabled />
      </f-form-item>
      <!-- 预付款资料状态 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.advancePaymentInfoStatus')"
        prop="advancePaymentInfoStatus"
        v-if="basicInfo.itemType === itemType.ADVANCE_PAYMENT && crossBorderShowFlag"
      >
        <f-scene-view
          :search="basicInfo.advancePaymentInfoStatus"
          :data="advancePaymentInfoStatus"
          params="value"
          label="label"
        />
      </f-form-item>
      <!-- 起息日 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.interestStartDate')"
        prop="interestStartDate"
      >
        <f-date-picker v-model="basicInfo.interestStartDate" type="date" disabled />
      </f-form-item>
      <!-- 执行日 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.executeDate')" prop="executeDate">
        <f-date-picker v-model="basicInfo.executeDate" type="date" disabled />
      </f-form-item>
      <!-- 是否发送银企指令 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.sendInstruction')"
        v-if="crossBorderShowFlag"
      >
        <f-switch
          v-model="basicInfo.sendInstruction"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          disabled
        />
      </f-form-item>
    </f-panel>
    <!-- CIPS高级信息 -->
    <f-panel
      :title="t('crossborder.settleinstructionrec.disbursement.cipsAdvancedInformation')"
      id="form7"
      v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
    >
      <!-- 付费行1行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paidBankOneLineNumber')"
        prop="paidBankOneLineNumber"
      >
        <f-input v-model="basicInfo.paidBankOneLineNumber" maxlength="35" disabled />
      </f-form-item>
      <!-- 付费行1CIPS ID -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paidBankOneCipsId')"
        prop="paidBankOneCipsId"
      >
        <f-input v-model="basicInfo.paidBankOneCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 付费行1LEI码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paidBankOneLeiCode')"
        prop="paidBankOneLeiCode"
      >
        <f-input v-model="basicInfo.paidBankOneLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 费用币种1 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.costCurrencyOneName')"
        prop="costCurrencyOneId"
      >
        <f-scene-view
          :search="basicInfo.costCurrencyOneId"
          :url="getCurrencyInfo"
          params="currencyId"
          label="currencyName"
        />
      </f-form-item>
      <!-- 费用金额1-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.costAmount1')" prop="costAmount1">
        <f-amount
          v-model="basicInfo.costAmount1"
          tooltip
          :negative="false"
          :precision="2"
          :symbol="basicInfo.costCurrencyOneSymbol"
          disabled
        />
      </f-form-item>
      <f-form-item />
      <!-- 付费行2行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paidBankTwoLineNumber')"
        prop="paidBankTwoLineNumber"
      >
        <f-input v-model="basicInfo.paidBankTwoLineNumber" maxlength="35" disabled />
      </f-form-item>
      <!-- 付费行2CIPS ID -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paidBankTwoCipsId')"
        prop="paidBankTwoCipsId"
      >
        <f-input v-model="basicInfo.paidBankTwoCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 付费行2LEI码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paidBankTwoLeiCode')"
        prop="paidBankTwoLeiCode"
      >
        <f-input v-model="basicInfo.paidBankTwoLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 费用币种2 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.costCurrencyTwoName')"
        prop="costCurrencyTwoId"
      >
        <f-scene-view
          :search="basicInfo.costCurrencyTwoId"
          :url="getCurrencyInfo"
          params="currencyId"
          label="currencyName"
        />
      </f-form-item>
      <!-- 费用金额2-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.costAmount2')" prop="costAmount2">
        <f-amount
          v-model="basicInfo.costAmount2"
          tooltip
          :negative="false"
          :precision="2"
          :symbol="basicInfo.costCurrencyTwoSymbol"
          disabled
        />
      </f-form-item>
      <f-form-item />
      <!-- 付款直接参与者行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentDParticipantBankCode')"
        prop="paymentDParticipantBankCode"
      >
        <f-input v-model="basicInfo.paymentDParticipantBankCode" maxlength="35" disabled />
      </f-form-item>
      <!-- 付款间接参与者行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.paymentIParticipantBankCode')"
        prop="paymentIParticipantBankCode"
      >
        <f-input v-model="basicInfo.paymentIParticipantBankCode" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款直接参与者行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveDParticipantBankCode')"
        prop="receiveDParticipantBankCode"
      >
        <f-input v-model="basicInfo.receiveDParticipantBankCode" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款直接参与者CIPS ID -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveDParticipantCipsId')"
        prop="receiveDParticipantCipsId"
      >
        <f-input v-model="basicInfo.receiveDParticipantCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款直接参与者LEI码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveDParticipantLeiCode')"
        prop="receiveDParticipantLeiCode"
      >
        <f-input v-model="basicInfo.receiveDParticipantLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 收款间接参与者行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveIParticipantBankCode')"
        prop="receiveIParticipantBankCode"
      >
        <f-input v-model="basicInfo.receiveIParticipantBankCode" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款间接参与者CIPS ID -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveIParticipantCipsId')"
        prop="receiveIParticipantCipsId"
      >
        <f-input v-model="basicInfo.receiveIParticipantCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 收款间接参与者LEI码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.receiveIParticipantLeiCode')"
        prop="receiveIParticipantLeiCode"
      >
        <f-input v-model="basicInfo.receiveIParticipantLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 中介机构1行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneBankCode')"
        prop="intermediaryOneBankCode"
      >
        <f-input v-model="basicInfo.intermediaryOneBankCode" maxlength="35" disabled />
      </f-form-item>
      <!-- 中介机构1 CIPS ID -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneCipsId')"
        prop="intermediaryOneCipsId"
      >
        <f-input v-model="basicInfo.intermediaryOneCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 中介机构1LEI码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneLeiCode')"
        prop="intermediaryOneLeiCode"
      >
        <f-input v-model="basicInfo.intermediaryOneLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 中介机构1名称 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneName')"
        prop="intermediaryOneName"
      >
        <f-input v-model="basicInfo.intermediaryOneName" maxlength="140" disabled />
      </f-form-item>
      <!-- 中介机构2行号 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoBankCode')"
        prop="intermediaryTwoBankCode"
      >
        <f-input v-model="basicInfo.intermediaryTwoBankCode" maxlength="35" disabled />
      </f-form-item>
      <!-- 中介机构2 CIPS ID -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoCipsId')"
        prop="intermediaryTwoCipsId"
      >
        <f-input v-model="basicInfo.intermediaryTwoCipsId" maxlength="35" disabled />
      </f-form-item>
      <!-- 中介机构2LEI码 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoLeiCode')"
        prop="intermediaryTwoLeiCode"
      >
        <f-input v-model="basicInfo.intermediaryTwoLeiCode" maxlength="20" disabled />
      </f-form-item>
      <!-- 中介机构2名称 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoName')"
        prop="intermediaryTwoName"
      >
        <f-input v-model="basicInfo.intermediaryTwoName" maxlength="140" disabled />
      </f-form-item>
      <!-- 原始币种 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.originalCurrencyName')"
        prop="originalCurrencyId"
      >
        <f-scene-view
          :search="basicInfo.originalCurrencyId"
          :url="getOfficeInfo"
          params="currencyId"
          label="currencyName"
        />
      </f-form-item>
      <!-- 汇率 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.exchangeRate')" prop="exchangeRate">
        <f-amount v-model="basicInfo.exchangeRate" :negative="false" :precision="6" symbol=" " disabled />
      </f-form-item>
      <!-- 原始金额-->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.originalAmount')" prop="originalAmount">
        <f-amount
          v-model="basicInfo.originalAmount"
          tooltip
          :negative="false"
          :precision="2"
          :symbol="basicInfo.originalCurrencySymbol"
          disabled
        />
      </f-form-item>
      <!-- 业务优先级 -->
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.businessPriority')" prop="businessPriority">
        <f-scene-view :search="basicInfo.businessPriority" :data="businessPriority" params="value" label="label" />
      </f-form-item>
      <!-- 致收款方银行附言 -->
      <f-form-item
        :label="t('crossborder.settleinstructionrec.disbursement.toReceiveBankRemark')"
        prop="toReceiveBankRemark"
      >
        <f-input v-model="basicInfo.toReceiveBankRemark" maxlength="150" disabled />
      </f-form-item>
    </f-panel>
    <!-- 附件 -->
    <f-panel :title="t('crossborder.settleinstructionrec.disbursement.attmInfo')" id="form8" v-if="crossBorderShowFlag">
      <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.attmInfo')" :employ="3">
        <f-attm-upload
          v-model="basicInfo.fileIdArr"
          multiple
          :show-upload="false"
          :is-show-batch-delete="false"
          :is-remove-delete-link="true"
          disabled
        />
      </f-form-item>
    </f-panel>
  </f-multi-form-panel>
  <f-panel :title="t('crossborder.settleinstructionrec.disbursement.history')" id="form6">
    <f-wf-history ref="wfHistory" :auto-init="false" :params="wfHistoryParams" :is-through="false" />
  </f-panel>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { detailProps, useDetail } from "./disbursementDetail.ts";
import { useConst } from "@ifs/support";
import { getCurrencyInfo } from "../../../url";

const { t } = useI18n();
const props = defineProps(detailProps);

//付款类型
const paymentType = useConst("forex.ReceivePaymentType");
//业务类型
const businessType = useConst("forex.BusinessType");
//汇款方式
const paymentMethod = useConst("forex.PaymentMethod");
//业务通道
const businessChannel = useConst("forex.BusinessChannel");
//是否枚举
const yesOrNo = useConst("forex.YesOrNo");
//国内外费用承担方
const costBearing = useConst("forex.CostBearing");
//款项类型
const itemType = useConst("forex.ItemType");
//付汇性质
const pfeNature = useConst("forex.PfeNature");
//预付款资料状态
const advancePaymentInfoStatus = useConst("forex.AdvancePaymentInfoStatus");
//业务优先级
const businessPriority = useConst("forex.BusinessPriority");
const remitPriorityEnum = useConst("counter.RemitPriority");
const recAcctTypeData = useConst("common.AccountType");
const yesOrNoData = useConst("common.YesOrNo");

const { basicInfo, wfHistory, wfHistoryParams, crossBorderShowFlag } = useDetail(props);
</script>
