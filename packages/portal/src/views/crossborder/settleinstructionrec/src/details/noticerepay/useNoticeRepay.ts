import { reactive, shallowRef, ref } from "vue";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { openDateUrl, getLoanCounterRepayDetailByCodeUrl, getNoticeRepayDetailInfoUrl } from "../../../url";
import { goPage } from "../../../hooks/usePage";
import type { FileDto } from "@/views/charge/business/chargeChange/types";
import type { fileIdList } from "@/views/loan/paynotice/types";
import type { CsCrossBorderRepayNoticeDto } from "../../../types";

export const useNoticeRepay = () => {
  // 附件上传
  const upload = shallowRef();
  //上传附件返回的数组信息
  const fileInfoList = ref<FileDto[]>([]);
  const tableGrid = ref();
  const form = shallowRef();
  const loanTypeEnum = useConst("loancounter.LoanType");

  const resetRepayNoticeInfo = reactive<CsCrossBorderRepayNoticeDto>({
    id: null,
    instructionId: null,
    instructionNo: "",
    globalSerial: "",
    refuseReason: "",
    cancelReason: "",
    version: "",
    businessId: null, // 业务id，申请单的业务id
    loanCounterBusinessType: "", // 通知单业务类型
    businessCode: "", // 单据号，申请单的单据号
    businessStatus: "", // 单据状态，变更后的单据状态
    businessProcCode: "", // 业务处理状态编码
    productId: null, // 产品ID
    productCode: "", // 产品编号
    productName: "", // 产品名称
    officeId: null, // 机构ID
    officeCode: "", // 机构编号
    officeName: "", // 机构名称
    currencyId: null, // 币种ID
    currencyCode: "", // 币种编号
    currencyName: "", // 币种名称
    loanClientId: null, // 借款单位ID
    loanClientCode: "", // 借款单位编号
    loanClientName: "", // 借款单位名称
    consignClientId: null, // 委托方客户ID(委托贷款)
    consignClientCode: "", // 委托方客户编号(委托贷款)
    consignClientName: "", // 委托方客户名称(委托贷款)
    repayType: "", // 还款类型
    loanBankRole: null, // 财务公司贷款行角色(银团贷款)
    agencyBankAccNo: "", // 财务公司银行账户号(银团贷款)
    agencyBankAccName: "", // 财务公司银行账户名称(银团贷款)
    contractCode: "", // 合同编号
    contractId: null, // 合同id
    notePayCode: "", // 放款通知单编号
    loanType: "", // 贷款类型
    contractAmount: "", // 合同金额
    contractBalance: "", // 合同余额
    payedAmount: "", // 放款单金额
    repayAmount: "", // 已还款金额
    contractAvailableAmount: "", // 放款通知单余额
    contractStartDate: "", // 合同开始日期
    contractEndDate: "", // 合同结束日期
    contractTerm: null, // 合同期限(月)
    contractExecuteRate: null, // 合同执行利率
    repayDate: "", // 还款日期
    amount: null, // 还款本金
    sumAmount: null, // 还款金额合计
    repayPrincipalAcctId: null, // 借款人还款账户ID
    repayPrincipalAcctNo: "", // 借款人还款账户号
    repayPrincipalAcctName: "", // 借款人还款账户名称
    repayInterestFlag: "", // 是否归还利息
    remark: "", // 备注
    repayTotalInterest: null, // 利息金额合计
    repayTotalAmount: null, // 还款金额合计（总额）
    crossDirection: "", // 融通方向
    cashPoolChannelId: null, // 资金池通道id
    cashPoolChannelName: "", // 资金池通道名称
    cashPoolChannelManagement: "", // 资金池通道管理部门
    toUsdRate: null, // 合同折美元汇率
    toUsdAmount: null, // 合同折美元金额
    repayNoticeToUsdRate: null, // 还款单折美元汇率
    repayNoticeToUsdAmount: null, // 还款单折美元金额
    internalMainAccountId: null, // 国内主账户开户行id
    internalMainAccountCode: "", // 国内主账户银行账号
    internalMainAccountName: "", // 国内主账户开户行名称
    consignSettExchangeFlag: "", // 委托人是否结汇
    partSettExchangeFlag: "", // 是否部分结汇
    settExchangeAmount: "", // 结汇金额
    consignForeignAcctId: null, // 委托人外币账户id
    consignForeignAcctCode: "", // 委托人外币账户编号
    consignForeignAcctName: "", // 委托人外币账户名称
    consignCnyAcctId: null, // 委托人人民币账户id
    consignCnyAcctCode: "", // 委托人人民币账户编号
    consignCnyAcctName: "", // 委托人人民币账户名称
    borrowerPurchaseExchangeFlag: "", // 借款人是否购汇
    partPurchaseExchangeFlag: "", // 是否部分购汇
    purchaseExchangeAmount: null, // 购汇金额
    borrowerCnyAcctId: null, // 借款人人民币账户号id
    borrowerCnyAcctCode: "", // 借款人人民币账户编号
    borrowerCnyAcctName: "", // 借款人人民币账户名称
    borrowerForeignAcctId: null, // 借款人外币账户id
    borrowerForeignAcctCode: "", // 借款人外币账户编号
    borrowerForeignAcctName: "", // 借款人外币账户名称
    consignCurrentAccountId: null, // 委托人活期账户id
    consignCurrentAccountCode: "", // 委托人活期账户编号
    consignCurrentAccountName: "", // 委托人活期账户名称
    rejectionReason: "", // 驳回原因
    clnNoteRepayDetailDtoList: [], // 还款通知单明细信息
    settlementMainAccountId: "", // 国内主账户
    settlementMainAccountNo: "", // 国内主账户
    settlementMainAccountName: "", // 开户行名称
    transactionCode: "", // 交易编码
    purposeCode: "", // 用途代码
    purposeName: "", // 用途名称
    settlementAmount: null, // 金额(合同币种)
    principalAmount: null, // 本金金额(合同币种)
    normalInterest: null, // 正常利息金额（合同币种）
    overdueInterest: null, // 逾期罚息金额（合同币种）
    debitInterest: null, // 欠息金额（合同币种）
    interestStartDate: "", // 起息日
    executeDate: "", // 执行日
    transAbstract: "", // 摘要
    fileIds: "", // 附件信息
    checkStatus: "", // 单据状态
    instructionStatus: "", // 指令状态

    loanClientHisId: null, // 贷款客户历史ID
    consignClientHisId: null, // 委托方客户历史ID
    payPrincipalAcctId: null, // 付本金账户ID
    payPrincipalAcctNo: "", // 付本金账号
    payPrincipalAcctName: "", // 付本金账户名称
    recPrincipalAcctId: null, // 收本金账户ID
    recPrincipalAcctNo: "", // 收本金账号
    recPrincipalAcctName: "", // 收本金账户名称
    recInterestAcctId: null, // 委托方收息账户ID
    recInterestAcctNo: "", // 委托方收息账号
    recInterestAcctName: "" // 委托方收息账户名称
  });

  //获取明细信息
  const getAddInfo = async (row: any) => {
    //根据指令编号查询还款详情信息
    await httpTool
      .post(getLoanCounterRepayDetailByCodeUrl, {
        businessCode: row.instructionNo,
        loanType: loanTypeEnum.CROSS_BORDER
      })
      .then((res: any) => {
        if (res.data) {
          Object.assign(resetRepayNoticeInfo, res.data);
        }
      });
    const noticeRepayAddDetailList = JSON.parse(row.batchDetail);
    noticeRepayAddDetailList.forEach((batchTransDetailClientDto: any) => {
      const crossBorderRepayNoticeDto = JSON.parse(batchTransDetailClientDto.businessDetail);
      resetRepayNoticeInfo.instructionNo = row.instructionNo;
      resetRepayNoticeInfo.globalSerial = row.instructionNo;
      resetRepayNoticeInfo.principalAmount = resetRepayNoticeInfo.amount;
      resetRepayNoticeInfo.instructionId = row.id;
      resetRepayNoticeInfo.loanClientHisId = crossBorderRepayNoticeDto.loanClientHisId; // 贷款客户历史ID
      resetRepayNoticeInfo.consignClientHisId = crossBorderRepayNoticeDto.consignClientHisId; // 委托方客户历史ID
      resetRepayNoticeInfo.payPrincipalAcctId = crossBorderRepayNoticeDto.payPrincipalAcctId; // 付本金账户ID
      resetRepayNoticeInfo.payPrincipalAcctNo = crossBorderRepayNoticeDto.payPrincipalAcctNo; // 付本金账号
      resetRepayNoticeInfo.payPrincipalAcctName = crossBorderRepayNoticeDto.payPrincipalAcctName; // 付本金账户名称
      resetRepayNoticeInfo.recPrincipalAcctId = crossBorderRepayNoticeDto.recPrincipalAcctId; // 收本金账户ID
      resetRepayNoticeInfo.recPrincipalAcctNo = crossBorderRepayNoticeDto.recPrincipalAcctNo; // 收本金账号
      resetRepayNoticeInfo.recPrincipalAcctName = crossBorderRepayNoticeDto.recPrincipalAcctName; // 收本金账户名称
      resetRepayNoticeInfo.recInterestAcctId = crossBorderRepayNoticeDto.recInterestAcctId; // 委托方收息账户ID
      resetRepayNoticeInfo.recInterestAcctNo = crossBorderRepayNoticeDto.recInterestAcctNo; // 委托方收息账号
      resetRepayNoticeInfo.recInterestAcctName = crossBorderRepayNoticeDto.recInterestAcctName; // 委托方收息账户名称
      resetRepayNoticeInfo.interestStartDate = crossBorderRepayNoticeDto.interestStartDate;

      resetRepayNoticeInfo.clnNoteRepayDetailDtoList.forEach((noteRepayDetailDto: any) => {
        if (crossBorderRepayNoticeDto.payFormCode === noteRepayDetailDto.receiptCode) {
          noteRepayDetailDto.realNormalInterest = crossBorderRepayNoticeDto.realNormalInterest; // 实付正常利息
          noteRepayDetailDto.realOverdueInterest = crossBorderRepayNoticeDto.realOverdueInterest; // 实付逾期罚息
          noteRepayDetailDto.realTotalInterest = crossBorderRepayNoticeDto.realTotalInterest; // 实付利息合计
          noteRepayDetailDto.accrualInterest = crossBorderRepayNoticeDto.accrualInterest; // 计提利息
          noteRepayDetailDto.calInterestStartDate = crossBorderRepayNoticeDto.calInterestStartDate; // 计算利息开始日期
          noteRepayDetailDto.calInterestEndDate = crossBorderRepayNoticeDto.calInterestEndDate; // 计算利息结束日期

          noteRepayDetailDto.segmentList.splice(0);
          if (crossBorderRepayNoticeDto.interestList) {
            crossBorderRepayNoticeDto.interestList.forEach((interestDto: any) => {
              noteRepayDetailDto.segmentList.push({
                calculateStartDate: interestDto.startDate, // 计息开始日期
                calculateEndDate: interestDto.endDate, // 计息结束日期
                interestBalance: interestDto.interestBalance, // 计息余额
                rate: interestDto.rate, // 利率
                days: interestDto.days, // 天数
                interest: interestDto.interest, // 利息
                interestClass: interestDto.interestClass, // 利息分类
                interestMethod: interestDto.interestMethod, // 计息方式
                interestAlgorithm: interestDto.interestAlgorithm, // 逐笔计息方式
                diffYearCount: interestDto.diffYearCount, // 年差值
                diffMonthCount: interestDto.diffMonthCount, // 月差值
                diffDayCount: interestDto.diffDayCount // 日差值
              });
            });
          }
        }
      });
    });
    resetRepayNoticeInfo.id = null;
    resetRepayNoticeInfo.version = "";
  };
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      resetRepayNoticeInfo.executeDate = res.data.onlineDate;
    });
  };
  const getSubmitInfo = () => {
    fileInfoList.value.push(...upload.value.fileData);
    if (fileInfoList.value.length > 0) {
      resetRepayNoticeInfo.fileIds = fileInfoList.value.map((item: fileIdList) => item.id).join(",");
    }
    return resetRepayNoticeInfo;
  };
  const submitFormValidator = async () => {
    let result = true;
    await form.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  // 返回列表页
  const handleGoBack = () => {
    goPage("list");
  };
  // 保存成功
  const saveDataSuccess = (res: any) => {
    if (res.success) {
      goPage("list");
    }
  };

  // 代客购汇
  const forexForeignPurchasing = () => {
    // if (){
    //
    // }
    // router.push({
    //   path: "",
    //   query: {}
    // });
  };
  const handler = {
    handlePurposeChange(row) {
      resetRepayNoticeInfo.purposeCode = row.purposeCode;
      resetRepayNoticeInfo.purposeName = row.purposeName;
    },
    handleInternalMainAccountChange(row) {
      resetRepayNoticeInfo.settlementMainAccountId = row.id;
      resetRepayNoticeInfo.settlementMainAccountNo = row.bankAccountNo;
      resetRepayNoticeInfo.settlementMainAccountName = row.bankName;
    }
  };

  const getModifyInfo = (dtoInfo: any) => {
    httpTool.post(getNoticeRepayDetailInfoUrl, dtoInfo).then((res: any) => {
      if (res?.success) {
        Object.assign(resetRepayNoticeInfo, res.data);
      }
    });
  };

  return {
    resetRepayNoticeInfo,
    getAddInfo,
    upload,
    getSubmitInfo,
    submitFormValidator,
    saveDataSuccess,
    handleGoBack,
    form,
    tableGrid,
    forexForeignPurchasing,
    handler,
    getOpenDate,
    getModifyInfo
  };
};
export default useNoticeRepay;
