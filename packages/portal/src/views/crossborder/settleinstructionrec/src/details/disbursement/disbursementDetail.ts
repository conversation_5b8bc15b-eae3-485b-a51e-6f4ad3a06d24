import { nextTick, type PropType, reactive, ref, watch } from "vue";
import type { CsCrossBorderLoanPayDto } from "../../../types";
import {
  getLoanPayDetailInfoUrl,
  getLoanPayDetailInfoByIdUrl,
  queryAccountBalanceInfo,
  getPurposeSetList,
  getExternalAccountInfoUrl,
  innerClientInfoQueryByClientIdUrl
} from "../../../url";
import httpTool from "@/utils/http";
import { useCurrency } from "@/hooks";
import { format } from "@/utils/currency";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";

export const detailProps = {
  param: {
    type: Object as PropType<CsCrossBorderLoanPayDto>,
    required: true
  }
};

export const useDetail = (props?: Record<string, any>) => {
  const basicInfo = reactive<CsCrossBorderLoanPayDto>({
    id: -1, //主键
    instructionNo: "", // 指令号
    officeId: null, // 机构id
    officeCode: "", // 机构编码
    officeName: "", // 机构名称
    currencyId: null, // 币种
    currencyCode: "", // 币种编号
    currencyName: "", // 币种名称
    paymentType: "", // 付款类型，1-境内付款，2-境外付款
    paymentMethod: "", // 汇款方式，0-电汇T/T，1-票汇D/D，2-信汇M/T
    sendTelegramLevel: 1, // 发电等级，0-普通，1-加急
    businessChannel: "", // 业务通道，1-柜台办理，2-银企通道，3-CIPS通道
    payClientId: null, // 付款客户ID
    payClientName: "", // 付款客户名称
    payClientCode: "", // 付款客户编号
    payAcctId: null, // 付款人账户id
    payAcctNo: "", // 付款人账户编号
    payAcctName: "", // 付款人账户名称
    payBalance: null, // 付款账户余额
    payAvailableBalance: null, // 付款账户可用余额
    payOrgCode: "", // 付款人组织机构代码
    payAddress: "", // 付款人地址
    payBankNo: "", // 付款开户行编号
    payBankName: "", // 付款开户行名称
    payBankAcctNo: "", // 付款银行账号
    payBankAcctName: "", // 付款银行账号名称
    receiveId: null, // 收款人账户id
    receiveNo: "", // 收款人账户编号
    receiveName: "", // 收款人账户名称
    countryCode: "", // 收款人常驻国家(地区)代码
    receiveAddress: "", // 收款人地址
    receiveBankName: "", // 收款人开户行名称
    receiveBankSwiftCode: "", // 收款行SWIFT号
    receiveBankAddress: "", // 收款行地址
    agencyBankName: "", // 收款代理行名称
    agencyBankSwiftCode: "", // 收款代理行SWIFT号
    receiveAgencyBankCode: "", // 收款开户行在代理行账号
    agencyBankAddress: "", // 收款代理行地址
    paymentRemark: "", // 收款汇款附言
    costBearing: "", // 国内外费用承担方，0-OUR-汇款人，1-BEN-收款人，2-SHA-共同
    collectFromCustomer: "", // 是否向客户收取，1-是，0-否
    chargeAccountId: null, // 付费账户ID
    chargeAccountCode: "", // 付费账户号
    chargeAccountName: "", // 付费账户名称
    chargeTotalAmount: null, // 费用总金额
    chargeRateId: null, // 手续费率ID
    chargeRate: null, // 手续费率
    preferentialPoint: null, // 优惠点数
    chargeAmount: null, // 手续费金额
    urgentRate: null, // 加急费率
    urgentAmount: null, // 加急费金额
    sendTelegramAmount: null, // 邮电费金额
    itemType: "", // 款项类型，0-预付款，1-货到付款，2-退款，3-其它
    pfeNature: "", // 付汇性质，0-保税区，1-出口加工区，2-钻石交易所，5-其他特殊经济区域，3-深加工结转，4-其他
    bondedGoods: "", // 本笔款项是否为保税货物项下付款，1-是，2-否
    transCode1: "", // 交易编码1
    transAmount1: null, // 对应金额1
    transRemark1: "", // 交易附言1
    transCode2: "", // 交易编码2
    transAmount2: null, // 对应金额2
    transRemark2: "", // 交易附言2
    businessCode: "", // 外汇局批件/备案表号/业务编号
    contractCode: "", // 合同号
    invoiceCode: "", // 发票号
    applyUserName: "", // 申请人名称
    applyUserPhone: "", // 申请人电话
    advancePaymentInfoStatus: "", // 预付款资料状态，1-未补全，2-已补全
    interestStartDate: "", // 起息日
    executeDate: "", // 执行日
    sendInstruction: "", // 是否发送银企指令
    paymentAmount: null, // 付款金额
    paymentName: "", // 付款人名称
    paymentCipsId: null, // 付款方CIPS ID
    paymentLeiCode: null, // 付款方LEI码
    paymentEid: null, // 付款方EID码
    payBankAcctId: null, // 付款银行账号Id
    payBankId: null, // 付款开户行id
    paymentBankSwiftCode: "", // 开户行SWIFT号
    cipsId: "", // 财务公司CIPS ID
    leiCode: "", // 财务公司LEI码
    ibankAccount: null, //是否IBAN账户
    countryName: "", //收款人所在国家（地区）名称
    receiveProvince: "", //收款人所在省
    receiveCity: "", //收款人所在城市
    receiveCipsId: "", //收款方CIPS ID
    receiveLeiCode: "", //收款方LEI码
    receiveEid: "", //收款方EID码
    receiveBankCnapsCode: "", //收款行CNAPS号
    receiveBankCipsId: "", //收款行CIPS ID
    receiveBankLeiCode: "", //收款行LEI码
    receiveCnps: "", //收款人联行号
    paidBankOneLineNumber: "", //付费行1行号
    paidBankOneCipsId: "", //付费行1CIPS ID
    paidBankOneLeiCode: "", //付费行1LEI码
    costCurrencyOneName: "", //费用币种1
    costCurrencyOneId: null, //费用币种1ID
    costCurrencyOneCode: "", //费用币种1code
    costAmount1: null, //费用金额1
    paidBankTwoLineNumber: "", //付费行2行号
    paidBankTwoCipsId: "", //付费行2CIPS ID
    paidBankTwoLeiCode: "", //付费行2LEI码
    costCurrencyTwoName: "", //费用币种2
    costCurrencyTwoId: null, //费用币种2ID
    costCurrencyTwoCode: "", //费用币种2code
    costAmount2: null, //费用金额2
    paymentDParticipantBankCode: "", //付款直接参与者行号
    paymentIParticipantBankCode: "", //付款间接参与者行号
    receiveDParticipantBankCode: "", //收款直接参与者行号
    receiveDParticipantCipsId: "", //收款直接参与者CIPS ID
    receiveDParticipantLeiCode: "", //收款直接参与者LEI码
    receiveIParticipantBankCode: "", //收款间接参与者行号
    receiveIParticipantCipsId: "", //收款间接参与者CIPS ID
    receiveIParticipantLeiCode: "", //收款间接参与者LEI码
    intermediaryOneBankCode: "", //中介机构1行号
    intermediaryOneCipsId: "", //中介机构1 CIPS ID
    intermediaryOneLeiCode: "", //中介机构1LEI码
    intermediaryOneName: "", //中介机构1名称
    intermediaryTwoBankCode: "", //中介机构2行号
    intermediaryTwoCipsId: "", //中介机构2 CIPS ID
    intermediaryTwoLeiCode: "", //中介机构2LEI码
    intermediaryTwoName: "", //中介机构2名称
    originalCurrencyName: "", //原始币种
    originalCurrencyId: null, //原始币种ID
    originalCurrencyCode: "", //原始币种code
    exchangeRate: null, //汇率
    originalAmount: null, //原始金额
    businessPriority: null, //业务优先级
    toReceiveBankRemark: "", //致收款方银行附言
    fileIds: "", // 附件信息
    fileIdArr: [],
    checkStatus: "", // 单据状态
    instructionStatus: "", // 指令状态
    version: "",
    payMethod: "", //支付方式;支付方式，1：自主支付；2：受托支付
    isRiskCheck: "", //是否通过风险校验 no:否 yes:是
    isPaymentPrivate: "", //是否对私支付
    recAcctType: "", //收款方账户类型
    transactionCode: "", //交易编码
    purposeCode: "", //用途代码
    loanStartDate: "", //合同开始日
    loanTerm: null, //期限
    loanEndDate: "", //合同到期日
    executeRate: null, //执行利率
    crossBorderDirection: "" //融通方向
  });
  const { t } = useI18n();

  //汇款方式
  const paymentMethod = useConst("forex.PaymentMethod");
  //业务通道
  const businessChannel = useConst("forex.BusinessChannel");
  //余额类型
  const balanceType = useConst("counter.BalanceType");
  //国内外费用承担方
  const costBearing = useConst("forex.CostBearing");
  //付款类型
  const paymentType = useConst("forex.ReceivePaymentType");
  const recAcctTypeData = useConst("common.AccountType");
  const remitPriorityEnum = useConst("counter.RemitPriority");
  //查询账户可用余额入参
  const accountBalanceParams = reactive({
    accountId: "",
    accountNo: "",
    billCode: "",
    balanceType: "",
    currencyType: "",
    transNo: ""
  });
  const crossBorderShowFlag = ref(false);
  const getLoanPayInfo = async (info: any) => {
    await httpTool.post(getLoanPayDetailInfoUrl, { instructionNo: info.instructionNo }).then(async (res: any) => {
      if (res?.success) {
        if (res.data !== null) {
          Object.assign(basicInfo, res.data);
          if (
            (basicInfo.currencyCode === "CNY" && basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) ||
            basicInfo.currencyCode !== "CNY"
          ) {
            crossBorderShowFlag.value = true;
          } else {
            crossBorderShowFlag.value = false;
          }
          nextTick(() => {
            if (info.id !== null) {
              wfHistoryParams.recordId = info.id;
              wfHistoryParams.agencyId = res.data.officeId;
              wfHistoryParams.currencyId = res.data.currencyId;
              wfHistory.value?.getData();
            }
          });
        } else {
          const disbursementAddDetailInfo = JSON.parse(info.detail);
          basicInfo.instructionId = info.id;
          basicInfo.instructionNo = info.instructionNo;
          basicInfo.globalSerial = info.instructionNo;
          basicInfo.officeId = disbursementAddDetailInfo.officeId; // 机构id
          basicInfo.officeCode = disbursementAddDetailInfo.officeCode; // 机构编码
          basicInfo.officeName = disbursementAddDetailInfo.officeName; // 机构名称
          basicInfo.currencyId = disbursementAddDetailInfo.currencyId; // 币种
          basicInfo.currencyCode = disbursementAddDetailInfo.currencyCode; // 币种编号
          basicInfo.currencyName = disbursementAddDetailInfo.currencyName; // 币种名称
          if (
            disbursementAddDetailInfo.recAcctType === recAcctTypeData.INNER_ACCOUNT ||
            disbursementAddDetailInfo.recAcctList[0].countryCode === "CN"
          ) {
            basicInfo.paymentType = paymentType.DOMESTIC_PAYMENT; // 付款类型，1-境内付款，2-境外付款
          } else {
            basicInfo.paymentType = paymentType.OVERSEA_PAYMENT; // 付款类型，1-境内付款，2-境外付款
          }
          basicInfo.paymentMethod = paymentMethod.WIRE_TRANSFER; // 汇款方式，0-电汇T/T，1-票汇D/D，2-信汇M/T
          basicInfo.sendTelegramLevel = remitPriorityEnum.URGENT; // 发电等级，0-普通，1-加急
          if (disbursementAddDetailInfo.recAcctType === recAcctTypeData.INNER_ACCOUNT) {
            basicInfo.businessChannel = businessChannel.COUNTER_HANDLING; // 业务通道，1-柜台办理，2-银企通道，3-CIPS通道
          }
          if (
            (basicInfo.currencyCode === "CNY" && basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) ||
            basicInfo.currencyCode !== "CNY"
          ) {
            crossBorderShowFlag.value = true;
          } else {
            crossBorderShowFlag.value = false;
          }
          basicInfo.payClientId = disbursementAddDetailInfo.loanClientId; // 付款客户ID
          basicInfo.payClientName = disbursementAddDetailInfo.loanClientName; // 付款客户名称
          basicInfo.payClientCode = disbursementAddDetailInfo.loanClientCode; // 付款客户编号
          basicInfo.payAcctId = disbursementAddDetailInfo.payAcctId; // 付款人账户id
          basicInfo.payAcctNo = disbursementAddDetailInfo.payAcctNo; // 付款人账户编号
          basicInfo.payAcctName = disbursementAddDetailInfo.payAcctName; // 付款人账户名称
          //调用接口查询账户可用余额
          accountBalanceParams.accountId = disbursementAddDetailInfo.payAcctId;
          accountBalanceParams.accountNo = disbursementAddDetailInfo.payAcctNo;
          accountBalanceParams.balanceType = balanceType.AVAILABLE;
          //调用接口查询账户可用余额
          await queryAccountBalance(accountBalanceParams);
          await httpTool
            .post(innerClientInfoQueryByClientIdUrl, { clientId: basicInfo.payClientId })
            .then((res: any) => {
              if (basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) {
                //境外付款取英文
                basicInfo.payOrgCode = res.data.organCodeCert; // 付款人组织机构代码
                basicInfo.payAddress = res.data.engRegisteredAddress; // 付款人地址
                basicInfo.paymentName = res.data.englishName; // 付款客户名称
              } else {
                //境内取中文
                basicInfo.payOrgCode = res.data.organCodeCert; // 付款人组织机构代码
                basicInfo.payAddress = res.data.registeredAddress; // 付款人地址
                basicInfo.paymentName = res.data.clientName; // 付款客户名称
              }
            });
          basicInfo.payBankNo = ""; // 付款开户行编号
          basicInfo.payBankName = disbursementAddDetailInfo.payBankName; // 付款开户行名称
          basicInfo.payBankAcctNo = disbursementAddDetailInfo.payBankAcctNo; // 付款银行账号
          basicInfo.payBankAcctName = disbursementAddDetailInfo.payBankAcctName; // 付款银行账号名称
          if (disbursementAddDetailInfo.recAcctType === recAcctTypeData.INNER_ACCOUNT) {
            basicInfo.receiveId = disbursementAddDetailInfo.recAcctId; // 收款人账户id
            basicInfo.receiveNo = disbursementAddDetailInfo.recAcctNo; // 收款人账户编号
            basicInfo.receiveName = disbursementAddDetailInfo.recAcctName; // 收款人账户名称
          } else {
            basicInfo.receiveNo = disbursementAddDetailInfo.recBankAcctNo; // 收款人账户编号
            basicInfo.receiveName = disbursementAddDetailInfo.recBankAcctName; // 收款人账户名称
            await getExternalAccountInfo({
              officeId: basicInfo.officeId,
              currencyId: basicInfo.currencyId,
              codeAndName: basicInfo.receiveNo
            });
          }
          basicInfo.countryCode = disbursementAddDetailInfo.countryCode; // 收款人常驻国家(地区)代码
          basicInfo.receiveAddress = ""; // 收款人地址
          basicInfo.receiveBankName = disbursementAddDetailInfo.remitBank; // 收款人开户行名称
          basicInfo.costBearing = costBearing.OUR; // 国内外费用承担方，0-OUR-汇款人，1-BEN-收款人，2-SHA-共同
          basicInfo.itemType = disbursementAddDetailInfo.itemType; // 款项类型，0-预付款，1-货到付款，2-退款，3-其它
          basicInfo.pfeNature = disbursementAddDetailInfo.pfeNature; // 付汇性质，0-保税区，1-出口加工区，2-钻石交易所，5-其他特殊经济区域，3-深加工结转，4-其他
          basicInfo.bondedGoods = disbursementAddDetailInfo.bondedGoods; // 本笔款项是否为保税货物项下付款，1-是，2-否
          basicInfo.businessCode = disbursementAddDetailInfo.transactionCode; // 外汇局批件/备案表号/业务编号
          basicInfo.contractCode = disbursementAddDetailInfo.recContractCode; // 合同号
          basicInfo.invoiceCode = disbursementAddDetailInfo.invoiceCode; // 发票号
          basicInfo.interestStartDate = disbursementAddDetailInfo.interestStartDate; // 起息日
          basicInfo.executeDate = disbursementAddDetailInfo.executeDate; // 执行日
          basicInfo.paymentAmount = disbursementAddDetailInfo.amount; // 付款金额
          basicInfo.payBankAcctId = disbursementAddDetailInfo.payBankAcctId; // 付款银行账号Id
          basicInfo.payBankId = disbursementAddDetailInfo.payBankId; // 付款开户行id
          basicInfo.receiveProvince = disbursementAddDetailInfo.remitProvince; //收款人所在省
          basicInfo.receiveCity = disbursementAddDetailInfo.remitCity; //收款人所在城市
          basicInfo.originalAmount = basicInfo.paymentAmount;
          basicInfo.payMethod = disbursementAddDetailInfo.payMethod; //支付方式;支付方式，1：自主支付；2：受托支付
          basicInfo.isRiskCheck = disbursementAddDetailInfo.isRiskCheck; //是否通过风险校验 no:否 yes:是
          basicInfo.isPaymentPrivate = disbursementAddDetailInfo.isPaymentPrivate; //是否对私支付
          basicInfo.recAcctType = disbursementAddDetailInfo.recAcctType; //收款方账户类型
          basicInfo.transactionCode = disbursementAddDetailInfo.transactionCode; //交易编码
          basicInfo.purposeCode = disbursementAddDetailInfo.purposeCode; //用途代码
          await httpTool.post(getPurposeSetList, { purposeCode: basicInfo.purposeCode }).then((res: any) => {
            if (res.data) {
              basicInfo.purposeName = res.data[0].purposeName; //用途代码
            }
          });
          basicInfo.loanStartDate = disbursementAddDetailInfo.loanStartDate; //合同开始日
          basicInfo.loanTerm = disbursementAddDetailInfo.loanTerm; //期限
          basicInfo.loanEndDate = disbursementAddDetailInfo.loanEndDate; //合同到期日
          basicInfo.executeRate = disbursementAddDetailInfo.executeRate; //执行利率
          basicInfo.crossBorderDirection = disbursementAddDetailInfo.crossBorderDirection; //融通方向
          basicInfo.remark =
            t("crossborder.settleinstructionrec.disbursement.pay") +
            "USD" +
            format(basicInfo.paymentAmount) +
            t("crossborder.settleinstructionrec.disbursement.to") +
            basicInfo.receiveBankName;
        }
      }
    });
  };
  //查询账户可用余额
  const queryAccountBalance = async (accountBalanceParams: any) => {
    await httpTool.post(queryAccountBalanceInfo, accountBalanceParams).then((res: any) => {
      if (res?.success) {
        basicInfo.payBalance = res.data.balance;
        basicInfo.payAvailableBalance = res.data.availableBalance;
      }
    });
  };

  //查询账户可用余额
  const getExternalAccountInfo = async (bankAcctInfo: any) => {
    await httpTool.post(getExternalAccountInfoUrl, bankAcctInfo).then((res: any) => {
      if (res?.success && res.data !== null) {
        basicInfo.receiveNo = res.data.exBankAccountCode;
        basicInfo.receiveName = res.data.exBankAccountName;
        basicInfo.receiveBankSwiftCode = res.data.bankSwiftCode;
        basicInfo.receiveBankName = res.data.bankName;
        basicInfo.receiveBankAddress = res.data.bankAddress;
        basicInfo.countryName = res.data.countryName;
        basicInfo.countryCode = res.data.countryCode;
        basicInfo.receiveCnps = res.data.cnaps;
        basicInfo.receiveCity = res.data.city;
      }
    });
  };
  const getLoanPayInfoById = (info: any) => {
    httpTool.post(getLoanPayDetailInfoByIdUrl, { id: info.id }).then((res: any) => {
      if (res?.success) {
        Object.assign(basicInfo, res.data);
        nextTick(() => {
          if (info.id !== null) {
            wfHistoryParams.recordId = info.id;
            wfHistoryParams.agencyId = res.data.officeId;
            wfHistoryParams.currencyId = res.data.currencyId;
            wfHistory.value?.getData();
          }
        });
      }
    });
  };
  watch(
    () => props?.param,
    () => {
      if (props?.param) {
        getLoanPayInfo(props?.param);
      }
    },
    { deep: true, immediate: true }
  );

  const wfHistory = ref();
  //审批历史查询参数
  const wfHistoryParams = reactive<any>({
    systemCode: "Z02",
    agencyId: basicInfo.officeId,
    currencyId: basicInfo.currencyId,
    transType: "Z020162",
    recordId: basicInfo.id
  });

  const { currencySymbol } = useCurrency(basicInfo);

  return {
    getLoanPayInfoById,
    basicInfo,
    getLoanPayInfo,
    wfHistory,
    wfHistoryParams,
    currencySymbol,
    crossBorderShowFlag
  };
};
