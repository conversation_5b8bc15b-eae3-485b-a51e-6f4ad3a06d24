<template>
  <f-blank-scene :title="t('crossborder.settleinstructionrec.disbursement.modifyTitle')">
    <f-multi-form-panel ref="form1" :model="basicInfo" :column="3">
      <!-- 基础信息 -->
      <f-panel :title="t('crossborder.settleinstructionrec.disbursement.basicInfo')">
        <!--机构-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.office')" prop="officeName">
          <f-input v-model="basicInfo.officeName" disabled />
        </f-form-item>
        <!-- 币种 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.currency')" prop="currencyName">
          <f-input v-model="basicInfo.currencyName" disabled />
        </f-form-item>
        <!-- 付款类型 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentType')" prop="paymentType">
          <f-select
            v-model="basicInfo.paymentType"
            :data="paymentType.pickConst([paymentType.DOMESTIC_PAYMENT, paymentType.OVERSEA_PAYMENT])"
            @change="paymentTypeChange"
            disabled
          />
        </f-form-item>
        <!-- 汇款方式 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentMethod')"
          prop="paymentMethod"
          v-if="crossBorderShowFlag"
          required
        >
          <f-select v-model="basicInfo.paymentMethod" :data="paymentMethod" />
        </f-form-item>
        <!-- 发电等级 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.sendTelegramLevel')"
          v-if="crossBorderShowFlag"
        >
          <f-radio-group v-model="basicInfo.sendTelegramLevel" :data="remitPriorityEnum" reverseLable="value" />
        </f-form-item>
        <!-- 业务通道 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.businessChannel')"
          prop="businessChannel"
          v-if="crossBorderShowFlag"
          required
        >
          <f-select
            v-model="basicInfo.businessChannel"
            :data="businessChannel"
            @change="businessChannelChange"
            :disabled="basicInfo.receiveId !== null && basicInfo.receiveId !== ''"
          />
        </f-form-item>
        <!-- 付款金额-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentAmount')" prop="paymentAmount">
          <f-amount
            v-model="basicInfo.paymentAmount"
            tooltip
            :negative="false"
            :precision="2"
            :symbol="basicInfo.currencySymbol"
            @change="paymentAmountChange"
            disabled
          />
        </f-form-item>
      </f-panel>
      <!-- 付款方信息 -->
      <f-panel :title="t('crossborder.settleinstructionrec.disbursement.paymentInfo')" id="form3">
        <!-- 客户名称 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.clientName')"
          prop="payClientName"
          required
        >
          <f-input v-model="basicInfo.payClientName" disabled />
        </f-form-item>
        <!--付款人账号-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentAccountCode')" prop="payAcctNo">
          <f-input v-model="basicInfo.payAcctNo" disabled />
        </f-form-item>
        <!-- 付款人账户名称 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.paymentAccountName')" prop="payAcctName">
          <f-input v-model="basicInfo.payAcctName" disabled />
        </f-form-item>
        <!-- 账户余额-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.accountBalance')" prop="payBalance">
          <f-amount
            v-model="basicInfo.payBalance"
            tooltip
            :negative="false"
            disabled
            :symbol="basicInfo.currencySymbol"
          />
        </f-form-item>
        <!-- 账户可用余额-->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.availableBalance')"
          prop="payAvailableBalance"
        >
          <f-amount
            v-model="basicInfo.payAvailableBalance"
            tooltip
            :negative="false"
            :symbol="basicInfo.currencySymbol"
            disabled
          />
        </f-form-item>
        <!-- 组织机构代码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.organizationCode')"
          prop="payOrgCode"
          v-if="crossBorderShowFlag"
          :required="basicInfo.paymentType !== paymentType.DOMESTIC_PAYMENT"
        >
          <f-input v-model="basicInfo.payOrgCode" maxlength="10" />
        </f-form-item>
        <!-- 付款人名称 付款类型为‘境外付款’时，只能填写英文 根据[内部客户信息]带出，可手工调整-->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentName')"
          prop="paymentName"
          v-if="crossBorderShowFlag"
          required
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.paymentName"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.paymentName" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 付款人地址 境内付款时非必输，其他情况必输 付款类型为‘境外付款’时，只能填写英文-->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentAddress')"
          prop="payAddress"
          v-if="crossBorderShowFlag"
          :required="basicInfo.paymentType !== paymentType.DOMESTIC_PAYMENT"
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.paymentAddress"
            maxlength="280"
          />
          <f-input v-else v-model="basicInfo.payAddress" maxlength="280" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 付款方CIPS ID -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.paymentCipsId')"
          prop="paymentCipsId"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.paymentCipsId" maxlength="35" />
        </f-form-item>
        <!-- 付款方LEI码 -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.paymentLeiCode')"
          prop="paymentLeiCode"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.paymentLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 付款方EID码 -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.paymentEid')"
          prop="paymentEid"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.paymentEid" maxlength="60" />
        </f-form-item>
        <!-- 开户行 -->
        <!--开户行、银行账号-->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentBankAccountName')"
          prop="payBankAcctId"
          required
        >
          <f-magnifier-single
            :title="t('crossborder.settleinstructionrec.disbursement.openBankMagnifier')"
            :url="getOpenBankInfo"
            method="post"
            v-model="basicInfo.payBankAcctId"
            row-key="bankAccountId"
            row-label="openBankName"
            input-key="codeAndName"
            auto-init
            :params="{
              clientId: basicInfo.clientId,
              officeId: basicInfo.officeId
            }"
            @change="openBankChange"
            @clear="openBankClear"
          >
            <f-magnifier-column
              prop="openBankName"
              :label="t('crossborder.settleinstructionrec.disbursement.bankName')"
            />
            <f-magnifier-column
              prop="bankAccountCode"
              :label="t('crossborder.settleinstructionrec.disbursement.openBankAcctNo')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 银行账号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentBankAccountCode')"
          prop="payBankAcctNo"
          required
        >
          <f-input v-model="basicInfo.payBankAcctNo" disabled />
        </f-form-item>
        <!-- 开户行SWIFT号 -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.paymentBankSwiftCode')"
          prop="paymentBankSwiftCode"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
          required
        >
          <f-input v-model="basicInfo.paymentBankSwiftCode" disabled />
        </f-form-item>
        <!-- 财务公司CIPS ID -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.cipsId')"
          prop="cipsId"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
          required
        >
          <f-input v-model="basicInfo.cipsId" maxlength="35" />
        </f-form-item>
        <!--财务公司LEI码-->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.leiCode')"
          prop="leiCode"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
          required
        >
          <f-input v-model="basicInfo.leiCode" maxlength="20" />
        </f-form-item>
      </f-panel>
      <!-- 收款方信息 -->
      <f-panel :title="t('crossborder.settleinstructionrec.disbursement.receiveInfo')" id="form4">
        <!-- 收款人账户类型 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.recAcctType')" prop="recAcctType">
          <f-scene-view :search="basicInfo.recAcctType" :data="recAcctTypeData" params="value" label="label" />
        </f-form-item>
        <!-- 是否对私付款 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.recAcctType')"
          prop="isPaymentPrivate"
          v-if="!crossBorderShowFlag"
        >
          <f-scene-view :search="basicInfo.isPaymentPrivate" :data="yesOrNoData" params="value" label="label" />
        </f-form-item>
        <!-- 收款人账号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveBankAccountCode')"
          prop="receiveNo"
        >
          <f-input v-model="basicInfo.receiveNo" disabled />
        </f-form-item>
        <!-- 收款人名称 境外付款时，必须为英文 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveBankAccountName')"
          prop="receiveName"
        >
          <f-input v-model="basicInfo.receiveName" @change="buildRemark" disabled />
        </f-form-item>
        <!-- 是否IBAN账户 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.ibankAccount')"
          v-if="crossBorderShowFlag"
          required
        >
          <f-switch v-model="basicInfo.ibankAccount" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" />
        </f-form-item>
        <!-- 收款人常驻国家（地区）代码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.countryCode')"
          prop="countryCode"
          :required="true"
        >
          <f-magnifier-single
            v-model="basicInfo.countryCode"
            :title="t('crossborder.settleinstructionrec.disbursement.countryCodeMagnifier')"
            :url="getMasterDataInfo"
            method="post"
            row-key="dictKey"
            row-label="dictKey"
            iinput-key="dictKeyOrDictValueLike"
            auto-init
            @confirm="countryChange"
            disabled
            :params="{
              parentDictKeyEq: 'ISO_3166_alpha_2_Country_Dic',
              dictKeyEq: basicInfo.countryCode,
              dictValueLike: basicInfo.countryName
            }"
          >
            <f-magnifier-column
              prop="dictKey"
              query="dictKeyLike"
              :label="t('crossborder.settleinstructionrec.disbursement.countryNo')"
            />
            <f-magnifier-column
              prop="dictValue"
              query="dictValueLike"
              :label="t('crossborder.settleinstructionrec.disbursement.countryNameChinese')"
            />
            <f-magnifier-column
              prop="dictValueEn"
              query="dictValueEnLike"
              :label="t('crossborder.settleinstructionrec.disbursement.countryNameEnglish')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 收款人所在省 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveProvince')"
          prop="receiveProvince"
          required
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.receiveProvince"
            maxlength="20"
          />
          <f-input v-else v-model="basicInfo.receiveProvince" maxlength="20" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 收款人所在城市 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveCity')"
          prop="receiveCity"
          required
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.receiveCity"
            maxlength="20"
          />
          <f-input v-else v-model="basicInfo.receiveCity" maxlength="20" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 收款方CIPS ID -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.receiveCipsId')"
          prop="receiveCipsId"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.receiveCipsId" maxlength="35" />
        </f-form-item>
        <!-- 收款方LEI码 -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.receiveLeiCode')"
          prop="receiveLeiCode"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.receiveLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 收款方EID码 -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.receiveEid')"
          prop="receiveEid"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.receiveEid" maxlength="60" />
        </f-form-item>
        <!-- 收款人地址 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveAddress')"
          prop="receiveAddress"
          style="width: 66.6%"
          v-if="crossBorderShowFlag"
          required
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.receiveAddress"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.receiveAddress" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 收款人开户行名称 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveBankName')"
          prop="receiveBankName"
          v-if="crossBorderShowFlag"
          required
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.receiveBankName"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.receiveBankName" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 收款行SWIFT号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveBankSwiftCode')"
          prop="receiveBankSwiftCode"
          v-if="crossBorderShowFlag"
          :required="basicInfo.paymentType === paymentType.OVERSEA_PAYMENT"
        >
          <f-input v-model="basicInfo.receiveBankSwiftCode" maxlength="11" />
        </f-form-item>
        <!-- 收款行CNAPS号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveBankSwiftCode')"
          prop="receiveBankCnapsCode"
          v-if="basicInfo.paymentType === paymentType.DOMESTIC_PAYMENT"
        >
          <f-input v-model="basicInfo.receiveBankCnapsCode" />
        </f-form-item>
        <!-- 收款行CIPS ID -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.receiveBankCipsId')"
          prop="receiveBankCipsId"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
          required
        >
          <f-input v-model="basicInfo.receiveBankCipsId" maxlength="35" />
        </f-form-item>
        <!-- 收款行LEI码 -->
        <f-form-item
          :label="t('forex.counter.crossborderpayreceive.payment.receiveBankLeiCode')"
          prop="receiveBankLeiCode"
          v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
          required
        >
          <f-input v-model="basicInfo.receiveBankLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 收款行联行号 当付款类型=境内付款且收款行为中行时显示且必输 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveCnps')"
          prop="receiveCnps"
          v-if="crossBorderShowFlag"
          :required="
            basicInfo.paymentType === paymentType.DOMESTIC_PAYMENT && basicInfo.receiveBankName.indexOf('中行') > 0
          "
        >
          <f-input v-model="basicInfo.receiveCnps" maxlength="5" />
        </f-form-item>
        <!-- 收款行地址 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveBankAddress')"
          prop="receiveBankAddress"
          style="width: 66.6%"
          v-if="crossBorderShowFlag"
          required
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.receiveBankAddress"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.receiveBankAddress" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 收款人(工人)证件类型 TODO没有 -->
        <!-- 收款人(工人)证件号码 TODO没有 -->
        <!-- 代理行名称 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.agencyBankName')"
          prop="agencyBankName"
          v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.agencyBankName"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.agencyBankName" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 代理行SWIFT号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.agencyBankSwiftCode')"
          prop="agencyBankSwiftCode"
          v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.agencyBankSwiftCode" maxlength="11" />
        </f-form-item>
        <!-- 收款人开户行在代理行账号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveAgencyBankCode')"
          prop="receiveAgencyBankCode"
          v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.receiveAgencyBankCode"
            maxlength="140"
          />
          <f-input
            v-else
            v-model="basicInfo.receiveAgencyBankCode"
            maxlength="140"
            specialCharactor="levelConvention"
          />
        </f-form-item>
        <!-- 代理行地址 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.agencyBankAddress')"
          prop="agencyBankAddress"
          style="width: 100%"
          v-if="basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL && crossBorderShowFlag"
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.agencyBankAddress"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.agencyBankAddress" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
        <!-- 汇款附言 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentRemark')"
          prop="paymentRemark"
          style="width: 100%"
          v-if="crossBorderShowFlag"
        >
          <f-input
            v-if="basicInfo.paymentType !== paymentType.OVERSEA_PAYMENT"
            v-model="basicInfo.paymentRemark"
            maxlength="140"
          />
          <f-input v-else v-model="basicInfo.paymentRemark" maxlength="140" specialCharactor="levelConvention" />
        </f-form-item>
      </f-panel>
      <!-- 费用信息 -->
      <f-panel
        :title="t('crossborder.settleinstructionrec.disbursement.costInfo')"
        id="form5"
        v-if="crossBorderShowFlag"
      >
        <!-- 国内外费用承担方 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.costBearing')" prop="costBearing">
          <f-select v-model="basicInfo.costBearing" :data="costBearing" diabled />
        </f-form-item>
        <!-- 是否向客户收取 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.collectFromCustomer')" required>
          <f-switch v-model="basicInfo.collectFromCustomer" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" />
        </f-form-item>
        <!-- 付费账户号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.chargeAccountCode')"
          prop="chargeAccountId"
          :required="basicInfo.collectFromCustomer === yesOrNo.YES"
        >
          <f-magnifier-single
            :title="t('crossborder.settleinstructionrec.disbursement.chargeAccountMagnifier')"
            :url="getInnerAccountInfo"
            method="post"
            v-model="basicInfo.chargeAccountId"
            row-key="accountId"
            row-label="accountCode"
            input-key="accountCodeOrName"
            auto-init
            :params="{
              clientId: basicInfo.clientId,
              officeId: basicInfo.officeId,
              currencyId: 1,
              accountGroup: accountGroup.CURRENT,
              excludeAccountFrozenSealedType: [frozenSealed.NO_COLLECT_AND_NO_PAY],
              ailmentsFlag: yesOrNo.YES
            }"
            @change="chargeAccountChange"
            @clear="clearChargeAccount"
          >
            <f-magnifier-column
              prop="accountCode"
              :label="t('crossborder.settleinstructionrec.disbursement.accountNo')"
            />
            <f-magnifier-column
              prop="accountName"
              :label="t('crossborder.settleinstructionrec.disbursement.accountName')"
            />
            <f-magnifier-column
              prop="accountTypeName"
              :label="t('crossborder.settleinstructionrec.disbursement.accountTypeName')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 费用总金额-->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.chargeTotalAmount')"
          prop="chargeTotalAmount"
        >
          <f-amount v-model="basicInfo.chargeTotalAmount" tooltip :negative="false" :precision="2" disabled />
        </f-form-item>
        <!--手续费率-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.chargeRate')" prop="chargeRate">
          <f-magnifier-single
            v-model="basicInfo.chargeRateId"
            :title="t('crossborder.settleinstructionrec.disbursement.chargeRateMagnifier')"
            :url="queryChargeRate"
            method="post"
            row-key="id"
            row-label="chargeRate"
            input-key="name"
            @change="chargeRateChange"
            auto-init
          >
            <f-magnifier-column prop="ruleName" :label="t('crossborder.settleinstructionrec.disbursement.ruleName')" />
            <f-magnifier-column prop="chargeRate" :label="t('crossborder.settleinstructionrec.disbursement.rate')" />
            <f-magnifier-column
              prop="minChargeAmount"
              :label="t('crossborder.settleinstructionrec.disbursement.minChargeAmount')"
            />
            <f-magnifier-column
              prop="maxChargeAmount"
              :label="t('crossborder.settleinstructionrec.disbursement.maxChargeAmount')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 优惠点数 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.preferentialPoint')"
          prop="preferentialPoint"
        >
          <f-input v-model="basicInfo.preferentialPoint" disabled />
        </f-form-item>
        <!-- 手续费金额-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.chargeAmount')" prop="chargeAmount">
          <f-amount v-model="basicInfo.chargeAmount" tooltip :negative="false" :precision="2" />
        </f-form-item>
        <!-- 加急费率 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.urgentRate')" prop="urgentRate">
          <f-amount v-model="basicInfo.urgentRate" tooltip :negative="false" :precision="2" disabled symbol=" " />
        </f-form-item>
        <!-- 加急费金额-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.urgentAmount')" prop="urgentAmount">
          <f-amount v-model="basicInfo.urgentAmount" tooltip :negative="false" :precision="2" />
        </f-form-item>
        <!-- 电报费金额 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.sendTelegramAmount')"
          prop="sendTelegramAmount"
        >
          <f-amount v-model="basicInfo.sendTelegramAmount" tooltip :negative="false" :precision="2" />
        </f-form-item>
      </f-panel>
      <!-- 其他信息 -->
      <f-panel :title="t('crossborder.settleinstructionrec.disbursement.otherInfo')" id="form6">
        <!-- 款项类型 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.itemType')"
          prop="itemType"
          required
          v-if="crossBorderShowFlag"
        >
          <f-select
            v-model="basicInfo.itemType"
            :data="
              itemType.pickConst([itemType.ADVANCE_PAYMENT, itemType.CASH_ON_DELIVERY, itemType.REFUND, itemType.OTHER])
            "
          />
        </f-form-item>
        <!-- 付汇性质 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.pfeNature')"
          prop="pfeNature"
          v-if="crossBorderShowFlag"
        >
          <f-select v-model="basicInfo.pfeNature" :data="pfeNature" />
        </f-form-item>
        <!-- 本笔款项是否为保税货物项下付款 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.bondedGoods')"
          required
          v-if="crossBorderShowFlag"
        >
          <f-switch v-model="basicInfo.bondedGoods" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" />
        </f-form-item>
        <!-- 交易编码1 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.transCode1')"
          prop="transCode1"
          required
          v-if="crossBorderShowFlag"
        >
          <f-magnifier-single
            :title="t('crossborder.settleinstructionrec.disbursement.transCodeMagnifier')"
            :url="getMasterDataInfo"
            method="post"
            v-model="basicInfo.transCode1"
            row-key="dictKey"
            row-label="dictKey"
            input-key="dictKeyOrDictValueLike"
            :params="{
              dictClassEq: 'FX_TRANS_CODE',
              isLeaf: yesOrNo.YES,
              dictKeyEq: basicInfo.transCode1
            }"
            auto-init
            @change="transCode1Change"
            @clear="clearTransCode1"
          >
            <f-magnifier-column
              prop="dictKey"
              query="dictKeyLike"
              :label="t('crossborder.settleinstructionrec.disbursement.transCode')"
            />
            <f-magnifier-column
              prop="dictValue"
              query="dictValueLike"
              :label="t('crossborder.settleinstructionrec.disbursement.transName')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 对应金额1 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.transAmount1')"
          prop="transAmount1"
          required
          v-if="crossBorderShowFlag"
        >
          <f-amount
            v-model="basicInfo.transAmount1"
            tooltip
            :negative="false"
            :precision="2"
            :symbol="basicInfo.currencySymbol"
          />
        </f-form-item>
        <!-- 交易附言1 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.transRemark1')"
          prop="transRemark1"
          required
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.transRemark1" maxlength="50" />
        </f-form-item>
        <!-- 交易编码2 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.transCode2')"
          prop="transCode2"
          v-if="crossBorderShowFlag"
        >
          <f-magnifier-single
            :title="t('crossborder.settleinstructionrec.disbursement.transCodeMagnifier')"
            :url="getMasterDataInfo"
            method="post"
            v-model="basicInfo.transCode2"
            row-key="dictKey"
            row-label="dictKey"
            input-key="dictKeyOrDictValueLike"
            :params="{
              dictClassEq: 'FX_TRANS_CODE',
              isLeaf: yesOrNo.YES,
              dictKeyEq: basicInfo.transCode2
            }"
            auto-init
            @change="transCode2Change"
            @clear="clearTransCode2"
          >
            <f-magnifier-column
              prop="dictKey"
              query="dictKeyLike"
              :label="t('crossborder.settleinstructionrec.disbursement.transCode')"
            />
            <f-magnifier-column
              prop="dictValue"
              query="dictValueLike"
              :label="t('crossborder.settleinstructionrec.disbursement.transName')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 对应金额2 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.transAmount2')"
          prop="transAmount2"
          v-if="crossBorderShowFlag"
        >
          <f-amount
            v-model="basicInfo.transAmount2"
            tooltip
            :negative="false"
            :precision="2"
            :symbol="basicInfo.currencySymbol"
          />
        </f-form-item>
        <!-- 交易附言2 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.transRemark2')"
          prop="transRemark2"
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.transRemark2" maxlength="50" />
        </f-form-item>
        <!-- 外汇局批件/备案表号/业务编号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.businessCode')"
          prop="businessCode"
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.businessCode" maxlength="50" />
        </f-form-item>
        <!-- 用途代码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.purposeCode')"
          prop="purposeCode"
          v-if="basicInfo.businessType === businessType.PURCHASE_FOREIGN_EXCHANGE && crossBorderShowFlag"
          required
        >
          <f-magnifier-single
            :title="t('crossborder.settleinstructionrec.disbursement.purposeCodeeMagnifier')"
            :url="getPurposeSetList"
            method="post"
            v-model="basicInfo.purposeCode"
            row-key="purposeCode"
            row-label="purposeCode"
            input-key="codeOrName"
            @change="purposeCodeChange"
            auto-init
          >
            <f-magnifier-column
              prop="purposeCode"
              :label="t('crossborder.settleinstructionrec.disbursement.moneyUseCode')"
            />
            <f-magnifier-column
              prop="purposeName"
              :label="t('crossborder.settleinstructionrec.disbursement.moneyUseExplain')"
            />
          </f-magnifier-single>
        </f-form-item>
        <!-- 合同号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.contractCode')"
          prop="contractCode"
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.contractCode" maxlength="50" />
        </f-form-item>
        <!-- 发票号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.invoiceCode')"
          prop="invoiceCode"
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.invoiceCode" maxlength="50" />
        </f-form-item>
        <!-- 申请人名称 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.applyUserName')"
          prop="applyUserName"
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.applyUserName" maxlength="30" />
        </f-form-item>
        <!-- 申请人电话 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.applyUserPhone')"
          prop="applyUserPhone"
          v-if="crossBorderShowFlag"
        >
          <f-input v-model="basicInfo.applyUserPhone" maxlength="30" />
        </f-form-item>
        <!-- 预付款资料状态 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.advancePaymentInfoStatus')"
          prop="advancePaymentInfoStatus"
          v-if="basicInfo.itemType === itemType.ADVANCE_PAYMENT && crossBorderShowFlag"
          required
        >
          <f-select v-model="basicInfo.advancePaymentInfoStatus" :data="advancePaymentInfoStatus" />
        </f-form-item>
        <!-- 起息日 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.interestStartDate')"
          prop="interestStartDate"
        >
          <f-date-picker v-model="basicInfo.interestStartDate" type="date" />
        </f-form-item>
        <!-- 执行日 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.executeDate')" prop="executeDate">
          <f-date-picker v-model="basicInfo.executeDate" type="date" disabled />
        </f-form-item>
        <!-- 是否发送银企指令 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.sendInstruction')"
          v-if="crossBorderShowFlag"
        >
          <f-switch
            v-model="basicInfo.sendInstruction"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
            disabled
          />
        </f-form-item>
      </f-panel>
      <!-- CIPS高级信息 -->
      <f-panel
        :title="t('crossborder.settleinstructionrec.disbursement.cipsAdvancedInformation')"
        id="form7"
        v-if="basicInfo.businessChannel === businessChannel.CIPS_CHANNEL && crossBorderShowFlag"
      >
        <!-- 付费行1行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paidBankOneLineNumber')"
          prop="paidBankOneLineNumber"
        >
          <f-input v-model="basicInfo.paidBankOneLineNumber" maxlength="35" />
        </f-form-item>
        <!-- 付费行1CIPS ID -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paidBankOneCipsId')"
          prop="paidBankOneCipsId"
        >
          <f-input v-model="basicInfo.paidBankOneCipsId" maxlength="35" />
        </f-form-item>
        <!-- 付费行1LEI码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paidBankOneLeiCode')"
          prop="paidBankOneLeiCode"
        >
          <f-input v-model="basicInfo.paidBankOneLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 费用币种1 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.costCurrencyOneName')"
          prop="costCurrencyOneId"
        >
          <f-select
            ref="currencyRef"
            v-model="basicInfo.costCurrencyOneId"
            value-key="currencyId"
            label="currencyName"
            :url="getCurrencyInfo"
            method="post"
            :extra-data="currencyParams"
            auto-select
            @change="costCurrencyChange1"
          />
        </f-form-item>
        <!-- 费用金额1-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.costAmount1')" prop="costAmount1">
          <f-amount
            v-model="basicInfo.costAmount1"
            tooltip
            :negative="false"
            :precision="2"
            :symbol="basicInfo.costCurrencyOneSymbol"
          />
        </f-form-item>
        <f-form-item />
        <!-- 付费行2行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paidBankTwoLineNumber')"
          prop="paidBankTwoLineNumber"
        >
          <f-input v-model="basicInfo.paidBankTwoLineNumber" maxlength="35" />
        </f-form-item>
        <!-- 付费行2CIPS ID -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paidBankTwoCipsId')"
          prop="paidBankTwoCipsId"
        >
          <f-input v-model="basicInfo.paidBankTwoCipsId" maxlength="35" />
        </f-form-item>
        <!-- 付费行2LEI码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paidBankTwoLeiCode')"
          prop="paidBankTwoLeiCode"
        >
          <f-input v-model="basicInfo.paidBankTwoLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 费用币种2 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.costCurrencyTwoName')"
          prop="costCurrencyTwoId"
        >
          <f-select
            ref="currencyRef"
            v-model="basicInfo.costCurrencyTwoId"
            value-key="currencyId"
            label="currencyName"
            :url="getCurrencyInfo"
            method="post"
            :extra-data="currencyParams"
            auto-select
            @change="costCurrencyChange2"
          />
        </f-form-item>
        <!-- 费用金额2-->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.costAmount2')" prop="costAmount2">
          <f-amount
            v-model="basicInfo.costAmount2"
            tooltip
            :negative="false"
            :precision="2"
            :symbol="basicInfo.costCurrencyTwoSymbol"
          />
        </f-form-item>
        <f-form-item />
        <!-- 付款直接参与者行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentDParticipantBankCode')"
          prop="paymentDParticipantBankCode"
        >
          <f-input v-model="basicInfo.paymentDParticipantBankCode" maxlength="35" />
        </f-form-item>
        <!-- 付款间接参与者行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.paymentIParticipantBankCode')"
          prop="paymentIParticipantBankCode"
        >
          <f-input v-model="basicInfo.paymentIParticipantBankCode" maxlength="35" />
        </f-form-item>
        <!-- 收款直接参与者行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveDParticipantBankCode')"
          prop="receiveDParticipantBankCode"
        >
          <f-input v-model="basicInfo.receiveDParticipantBankCode" maxlength="35" />
        </f-form-item>
        <!-- 收款直接参与者CIPS ID -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveDParticipantCipsId')"
          prop="receiveDParticipantCipsId"
        >
          <f-input v-model="basicInfo.receiveDParticipantCipsId" maxlength="35" />
        </f-form-item>
        <!-- 收款直接参与者LEI码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveDParticipantLeiCode')"
          prop="receiveDParticipantLeiCode"
        >
          <f-input v-model="basicInfo.receiveDParticipantLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 收款间接参与者行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveIParticipantBankCode')"
          prop="receiveIParticipantBankCode"
        >
          <f-input v-model="basicInfo.receiveIParticipantBankCode" maxlength="35" />
        </f-form-item>
        <!-- 收款间接参与者CIPS ID -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveIParticipantCipsId')"
          prop="receiveIParticipantCipsId"
        >
          <f-input v-model="basicInfo.receiveIParticipantCipsId" maxlength="35" />
        </f-form-item>
        <!-- 收款间接参与者LEI码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.receiveIParticipantLeiCode')"
          prop="receiveIParticipantLeiCode"
        >
          <f-input v-model="basicInfo.receiveIParticipantLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 中介机构1行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneBankCode')"
          prop="intermediaryOneBankCode"
        >
          <f-input v-model="basicInfo.intermediaryOneBankCode" maxlength="35" />
        </f-form-item>
        <!-- 中介机构1 CIPS ID -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneCipsId')"
          prop="intermediaryOneCipsId"
        >
          <f-input v-model="basicInfo.intermediaryOneCipsId" maxlength="35" />
        </f-form-item>
        <!-- 中介机构1LEI码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneLeiCode')"
          prop="intermediaryOneLeiCode"
        >
          <f-input v-model="basicInfo.intermediaryOneLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 中介机构1名称 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryOneName')"
          prop="intermediaryOneName"
        >
          <f-input v-model="basicInfo.intermediaryOneName" maxlength="140" />
        </f-form-item>
        <!-- 中介机构2行号 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoBankCode')"
          prop="intermediaryTwoBankCode"
        >
          <f-input v-model="basicInfo.intermediaryTwoBankCode" maxlength="35" />
        </f-form-item>
        <!-- 中介机构2 CIPS ID -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoCipsId')"
          prop="intermediaryTwoCipsId"
        >
          <f-input v-model="basicInfo.intermediaryTwoCipsId" maxlength="35" />
        </f-form-item>
        <!-- 中介机构2LEI码 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoLeiCode')"
          prop="intermediaryTwoLeiCode"
        >
          <f-input v-model="basicInfo.intermediaryTwoLeiCode" maxlength="20" />
        </f-form-item>
        <!-- 中介机构2名称 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.intermediaryTwoName')"
          prop="intermediaryTwoName"
        >
          <f-input v-model="basicInfo.intermediaryTwoName" maxlength="140" />
        </f-form-item>
        <!-- 原始币种 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.originalCurrencyName')"
          prop="originalCurrencyId"
          required
        >
          <f-select
            ref="currencyRef"
            v-model="basicInfo.originalCurrencyId"
            value-key="currencyId"
            label="currencyName"
            :url="getCurrencyInfo"
            method="post"
            :extra-data="currencyParams"
            auto-select
            @change="originalCurrencyChange"
          />
        </f-form-item>
        <!-- 汇率 -->
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.exchangeRate')" prop="exchangeRate">
          <f-amount v-model="basicInfo.exchangeRate" :negative="false" :precision="6" symbol=" " />
        </f-form-item>
        <!-- 原始金额-->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.originalAmount')"
          prop="originalAmount"
          required
        >
          <f-amount
            v-model="basicInfo.originalAmount"
            tooltip
            :negative="false"
            :precision="2"
            :symbol="basicInfo.originalCurrencySymbol"
          />
        </f-form-item>
        <!-- 业务优先级 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.businessPriority')"
          prop="businessPriority"
          required
        >
          <f-select v-model="basicInfo.businessPriority" :data="businessPriority" />
        </f-form-item>
        <!-- 致收款方银行附言 -->
        <f-form-item
          :label="t('crossborder.settleinstructionrec.disbursement.toReceiveBankRemark')"
          prop="toReceiveBankRemark"
        >
          <f-input v-model="basicInfo.toReceiveBankRemark" maxlength="150" />
        </f-form-item>
      </f-panel>
      <!-- 附件 -->
      <f-panel
        :title="t('crossborder.settleinstructionrec.disbursement.attmInfo')"
        id="form8"
        v-if="crossBorderShowFlag"
      >
        <f-form-item :label="t('crossborder.settleinstructionrec.disbursement.attmInfo')" :employ="3">
          <f-attm-upload ref="upload" v-model="basicInfo.fileIdArr" drag multiple />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="saveInfo"
        :url="saveLoanPayUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="saveDataSuccess"
      />
      <f-button type="primary" @click.prevent="print">{{
        t("crossborder.settleinstructionrec.disbursement.printTitle")
      }}</f-button>
      <!-- 导出Excel -->
      <f-button link @click="exportExcel(basicInfo)" type="primary">
        {{ t("crossborder.settleinstructionrec.disbursement.exportExcel") }}
      </f-button>
      <f-submit-state
        :gather-params="saveInfo"
        :url="submitLoanPayUrl"
        operate="submit"
        :before-trigger="formValidator"
        @close="submitDataSuccess"
      />
      <f-button :plain="!isSubmit" type="info" @click.prevent="isSubmit ? goSubmit() : goBack()">
        {{ t("crossborder.settleinstructionrec.queryList") }}
      </f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { doBack } from "@/utils/wfUtils";
import { usePage } from "../../../hooks/usePage";
import useDisbursement from "../disbursement/useDisbursement";
import { useConst } from "@ifs/support";
import {
  getInnerAccountInfo,
  getOpenBankInfo,
  getMasterDataInfo,
  queryChargeRate,
  submitLoanPayUrl,
  saveLoanPayUrl,
  getPurposeSetList,
  getCurrencyInfo
} from "../../../url";
import { useModify } from "./disbursementModify.ts";

//付款类型
const paymentType = useConst("forex.ReceivePaymentType");
//业务类型
const businessType = useConst("forex.BusinessType");
//汇款方式
const paymentMethod = useConst("forex.PaymentMethod");
//业务通道
const businessChannel = useConst("forex.BusinessChannel");
// //业务种类编码
// const businessCategoryCode = useConst("forex.BusinessCategoryCode");
//是否枚举
const yesOrNo = useConst("forex.YesOrNo");
//国内外费用承担方
const costBearing = useConst("forex.CostBearing");
//款项类型
const itemType = useConst("forex.ItemType");
//付汇性质
const pfeNature = useConst("forex.PfeNature");
//预付款资料状态
const advancePaymentInfoStatus = useConst("forex.AdvancePaymentInfoStatus");
// //业务类型
// const forexBusinessType = useConst("forex.ForexBusinessType");
//业务优先级
const businessPriority = useConst("forex.BusinessPriority");
const accountGroup = useConst("inneraccount.AccountGroup");
const frozenSealed = useConst("inneraccount.FrozenSealedType");
const remitPriorityEnum = useConst("counter.RemitPriority");
const recAcctTypeData = useConst("common.AccountType");
const yesOrNoData = useConst("common.YesOrNo");
const { t } = useI18n();

const router = useRouter();
const { pageParams } = usePage();
//定义父组件传参, 参数不唯一，根据⻚面需要参数动态添加
const props = defineProps({ id: { type: Number }, backUrl: { type: String } });
const isSubmit = ref<boolean>(false);
const {
  basicInfo,
  formValidator,
  form1,
  openBankChange,
  openBankClear,
  countryChange,
  chargeAccountChange,
  clearChargeAccount,
  chargeRateChange,
  transCode1Change,
  clearTransCode1,
  transCode2Change,
  clearTransCode2,
  paymentAmountChange,
  businessChannelChange,
  paymentTypeChange,
  buildRemark,
  print,
  purposeCodeChange,
  currencyParams,
  costCurrencyChange1,
  costCurrencyChange2,
  originalCurrencyChange,
  saveInfo,
  upload,
  saveDataSuccess,
  exportExcel,
  submitDataSuccess
} = useDisbursement();
const { goBack, getModifyInfo, crossBorderShowFlag } = useModify(basicInfo);
//修改⻚初始化方法修改
onMounted(() => {
  if (pageParams) {
    getModifyInfo(pageParams?.dto);
  } else {
    getModifyInfo({ id: props?.id });
    isSubmit.value = true;
  }
});

//返回待提交⻚面
const goSubmit = () => {
  doBack(router, String(props.backUrl));
};
</script>
