import { reactive, ref, shallowRef } from "vue";
import { goPage } from "../../../hooks/usePage";
import { getLoanPayDetailInfoByIdUrl, getLoanPayDetailInfoUrl } from "../../../url";
import httpTool from "@/utils/http";
import { useCurrency } from "@/hooks";
import type { CsCrossBorderLoanPayDto } from "../../../types";

export const useModify = (basicInfo: CsCrossBorderLoanPayDto) => {
  const goBack = () => {
    goPage("list");
  };

  const recDataSuccess = (res: any) => {
    if (res.success) {
      goBack();
    }
  };

  const form = shallowRef();

  const formValidator = async () => {
    let result = true;
    await form.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    if (!result) {
      return false;
    }

    return result;
  };

  const backParam = reactive({
    refuseReason: ""
  });
  const crossBorderShowFlag = ref(false);
  const getModifyInfo = (dtoInfo: any) => {
    if (dtoInfo.instructionNo !== undefined) {
      httpTool.post(getLoanPayDetailInfoUrl, dtoInfo).then((res: any) => {
        if (res?.success) {
          Object.assign(basicInfo, res.data);
          if (
            (basicInfo.currencyCode === "CNY" && basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) ||
            basicInfo.currencyCode !== "CNY"
          ) {
            crossBorderShowFlag.value = true;
          } else {
            crossBorderShowFlag.value = false;
          }
        }
      });
    } else {
      httpTool.post(getLoanPayDetailInfoByIdUrl, dtoInfo).then((res: any) => {
        if (res?.success) {
          Object.assign(basicInfo, res.data);
          if (
            (basicInfo.currencyCode === "CNY" && basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) ||
            basicInfo.currencyCode !== "CNY"
          ) {
            crossBorderShowFlag.value = true;
          } else {
            crossBorderShowFlag.value = false;
          }
        }
      });
    }
  };

  const { currencySymbol } = useCurrency(basicInfo);

  return {
    basicInfo,
    goBack,
    recDataSuccess,
    form,
    formValidator,
    backParam,
    getModifyInfo,
    currencySymbol,
    crossBorderShowFlag
  };
};
