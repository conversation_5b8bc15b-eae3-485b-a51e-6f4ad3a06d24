import { reactive, shallowRef, ref } from "vue";
import { FMessageBox } from "@dtg/frontend-plus";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import {
  queryAccountBalanceInfo,
  openDateUrl,
  getBasicInfoSetInfo,
  getPrintRelationUrl,
  getLoanPayDetailInfoUrl,
  getExternalAccountInfoUrl,
  exportExcelUrl,
  getPurposeSetList,
  innerClientInfoQueryByClientIdUrl
} from "../../../url";
import httpTool from "@/utils/http";
import { useConversion } from "@/hooks/conversion";
import { format } from "@/utils/currency";
import type { CsCrossBorderLoanPayDto } from "../../../types";
import { goPage } from "../../../hooks/usePage";

export const useDisbursement = () => {
  const form1 = shallowRef();
  const currencyRef = shallowRef();
  const printDialog = shallowRef();
  const { t } = useI18n();
  //汇款方式
  const paymentMethod = useConst("forex.PaymentMethod");
  //业务通道
  const businessChannel = useConst("forex.BusinessChannel");
  //余额类型
  const balanceType = useConst("counter.BalanceType");
  //国内外费用承担方
  const costBearing = useConst("forex.CostBearing");
  // //业务类型
  // const businessType = useConst("forex.BusinessType");
  //是否枚举
  const yesOrNo = useConst("forex.YesOrNo");
  //付款类型
  const paymentType = useConst("forex.ReceivePaymentType");
  //业务类型
  const forexBusinessType = useConst("forex.ForexBusinessType");
  //预付款资料状态
  const advancePaymentInfoStatus = useConst("forex.AdvancePaymentInfoStatus");
  const recAcctTypeData = useConst("common.AccountType");
  const remitPriorityEnum = useConst("counter.RemitPriority");
  const basicInfo = reactive<CsCrossBorderLoanPayDto>({
    id: -1, //主键
    instructionNo: "", // 指令号
    officeId: null, // 机构id
    officeCode: "", // 机构编码
    officeName: "", // 机构名称
    currencyId: null, // 币种
    currencyCode: "", // 币种编号
    currencyName: "", // 币种名称
    paymentType: "", // 付款类型，1-境内付款，2-境外付款
    paymentMethod: paymentMethod.WIRE_TRANSFER, // 汇款方式，0-电汇T/T，1-票汇D/D，2-信汇M/T
    sendTelegramLevel: remitPriorityEnum.URGENT, // 发电等级，0-普通，1-加急
    businessChannel: paymentMethod.COUNTER_HANDLING, // 业务通道，1-柜台办理，2-银企通道，3-CIPS通道
    payClientId: null, // 付款客户ID
    payClientName: "", // 付款客户名称
    payClientCode: "", // 付款客户编号
    payAcctId: null, // 付款人账户id
    payAcctNo: "", // 付款人账户编号
    payAcctName: "", // 付款人账户名称
    payBalance: null, // 付款账户余额
    payAvailableBalance: null, // 付款账户可用余额
    payOrgCode: "", // 付款人组织机构代码
    payAddress: "", // 付款人地址
    payBankNo: "", // 付款开户行编号
    payBankName: "", // 付款开户行名称
    payBankAcctNo: "", // 付款银行账号
    payBankAcctName: "", // 付款银行账号名称
    receiveId: null, // 收款人账户id
    receiveNo: "", // 收款人账户编号
    receiveName: "", // 收款人账户名称
    countryCode: "", // 收款人常驻国家(地区)代码
    receiveAddress: "", // 收款人地址
    receiveBankName: "", // 收款人开户行名称
    receiveBankSwiftCode: "", // 收款行SWIFT号
    receiveBankAddress: "", // 收款行地址
    agencyBankName: "", // 收款代理行名称
    agencyBankSwiftCode: "", // 收款代理行SWIFT号
    receiveAgencyBankCode: "", // 收款开户行在代理行账号
    agencyBankAddress: "", // 收款代理行地址
    paymentRemark: "", // 收款汇款附言
    costBearing: costBearing.OUR, // 国内外费用承担方，0-OUR-汇款人，1-BEN-收款人，2-SHA-共同
    collectFromCustomer: "", // 是否向客户收取，1-是，0-否
    chargeAccountId: null, // 付费账户ID
    chargeAccountCode: "", // 付费账户号
    chargeAccountName: "", // 付费账户名称
    chargeTotalAmount: null, // 费用总金额
    chargeRateId: null, // 手续费率ID
    chargeRate: null, // 手续费率
    preferentialPoint: null, // 优惠点数
    chargeAmount: null, // 手续费金额
    urgentRate: null, // 加急费率
    urgentAmount: null, // 加急费金额
    sendTelegramAmount: null, // 邮电费金额
    itemType: "", // 款项类型，0-预付款，1-货到付款，2-退款，3-其它
    pfeNature: "", // 付汇性质，0-保税区，1-出口加工区，2-钻石交易所，5-其他特殊经济区域，3-深加工结转，4-其他
    bondedGoods: "", // 本笔款项是否为保税货物项下付款，1-是，2-否
    transCode1: "", // 交易编码1
    transAmount1: null, // 对应金额1
    transRemark1: "", // 交易附言1
    transCode2: "", // 交易编码2
    transAmount2: null, // 对应金额2
    transRemark2: "", // 交易附言2
    businessCode: "", // 外汇局批件/备案表号/业务编号
    contractCode: "", // 合同号
    invoiceCode: "", // 发票号
    applyUserName: "", // 申请人名称
    applyUserPhone: "", // 申请人电话
    advancePaymentInfoStatus: advancePaymentInfoStatus.INCOMPLETE, // 预付款资料状态，1-未补全，2-已补全
    interestStartDate: "", // 起息日
    executeDate: "", // 执行日
    sendInstruction: "", // 是否发送银企指令
    paymentAmount: null, // 付款金额
    paymentName: "", // 付款人名称
    paymentCipsId: null, // 付款方CIPS ID
    paymentLeiCode: null, // 付款方LEI码
    paymentEid: null, // 付款方EID码
    payBankAcctId: null, // 付款银行账号Id
    payBankId: null, // 付款开户行id
    paymentBankSwiftCode: "", // 开户行SWIFT号
    cipsId: "", // 财务公司CIPS ID
    leiCode: "", // 财务公司LEI码
    ibankAccount: null, //是否IBAN账户
    countryName: "", //收款人所在国家（地区）名称
    receiveProvince: "", //收款人所在省
    receiveCity: "", //收款人所在城市
    receiveCipsId: "", //收款方CIPS ID
    receiveLeiCode: "", //收款方LEI码
    receiveEid: "", //收款方EID码
    receiveBankCnapsCode: "", //收款行CNAPS号
    receiveBankCipsId: "", //收款行CIPS ID
    receiveBankLeiCode: "", //收款行LEI码
    receiveCnps: "", //收款人联行号
    paidBankOneLineNumber: "", //付费行1行号
    paidBankOneCipsId: "", //付费行1CIPS ID
    paidBankOneLeiCode: "", //付费行1LEI码
    costCurrencyOneName: "", //费用币种1
    costCurrencyOneId: null, //费用币种1ID
    costCurrencyOneCode: "", //费用币种1code
    costAmount1: null, //费用金额1
    paidBankTwoLineNumber: "", //付费行2行号
    paidBankTwoCipsId: "", //付费行2CIPS ID
    paidBankTwoLeiCode: "", //付费行2LEI码
    costCurrencyTwoName: "", //费用币种2
    costCurrencyTwoId: null, //费用币种2ID
    costCurrencyTwoCode: "", //费用币种2code
    costAmount2: null, //费用金额2
    paymentDParticipantBankCode: "", //付款直接参与者行号
    paymentIParticipantBankCode: "", //付款间接参与者行号
    receiveDParticipantBankCode: "", //收款直接参与者行号
    receiveDParticipantCipsId: "", //收款直接参与者CIPS ID
    receiveDParticipantLeiCode: "", //收款直接参与者LEI码
    receiveIParticipantBankCode: "", //收款间接参与者行号
    receiveIParticipantCipsId: "", //收款间接参与者CIPS ID
    receiveIParticipantLeiCode: "", //收款间接参与者LEI码
    intermediaryOneBankCode: "", //中介机构1行号
    intermediaryOneCipsId: "", //中介机构1 CIPS ID
    intermediaryOneLeiCode: "", //中介机构1LEI码
    intermediaryOneName: "", //中介机构1名称
    intermediaryTwoBankCode: "", //中介机构2行号
    intermediaryTwoCipsId: "", //中介机构2 CIPS ID
    intermediaryTwoLeiCode: "", //中介机构2LEI码
    intermediaryTwoName: "", //中介机构2名称
    originalCurrencyName: "", //原始币种
    originalCurrencyId: null, //原始币种ID
    originalCurrencyCode: "", //原始币种code
    exchangeRate: null, //汇率
    originalAmount: null, //原始金额
    businessPriority: null, //业务优先级
    toReceiveBankRemark: "", //致收款方银行附言
    fileIds: "", // 附件信息
    fileIdArr: [],
    checkStatus: "", // 单据状态
    instructionStatus: "", // 指令状态
    version: "",
    // 打印导出使用
    paymentBankAccountCode: "",
    paymentAddress: "",
    receiveBankAccountCode: "",
    receiveBankAccountName: "",
    paymentOpenBankName: "",

    payMethod: "", //支付方式;支付方式，1：自主支付；2：受托支付
    isRiskCheck: "", //是否通过风险校验 no:否 yes:是
    isPaymentPrivate: "", //是否对私支付
    recAcctType: "", //收款方账户类型
    transactionCode: "", //交易编码
    purposeCode: "", //用途代码
    purposeName: "", //用途名称
    loanStartDate: "", //合同开始日
    loanTerm: null, //期限
    loanEndDate: "", //合同到期日
    executeRate: null, //执行利率
    crossBorderDirection: "", //融通方向
    cancelReason: "",
    globalSerial: "",
    instructionId: 0,
    refuseReason: ""
  });
  // 点击提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    const result = await form1.value.form.validate();
    if (!result) {
      return false;
    }
    // 校验对应金额1+对应金额2=付款金额
    let totalAmount = 0;
    if (basicInfo.transAmount1 !== "") {
      totalAmount = totalAmount + Number(basicInfo.transAmount1);
    }
    if (basicInfo.transAmount2 !== "") {
      totalAmount = totalAmount + Number(basicInfo.transAmount2);
    }
    if (totalAmount !== basicInfo.paymentAmount) {
      FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.amountError"));
      return false;
    }
    if (basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) {
      // 校验付款人姓名如果是境外付款只能输入英文
      const englishRegex = /^[a-zA-Z0-9\s.,!?;:'"-]+$/;
      if (!englishRegex.test(basicInfo.paymentName)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidPaymentName"));
        return false;
      }
      // 校验付款人地址只能输入英文
      if (!englishRegex.test(basicInfo.payAddress)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidPaymentAddress"));
        return false;
      }
      // 校验收款人名称只能输入英文
      if (!englishRegex.test(basicInfo.receiveName)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidReceiveBankAccountName"));
        return false;
      }
      // 校验收款人地址只能输入英文
      if (!englishRegex.test(basicInfo.receiveAddress)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidReceiveAddress"));
        return false;
      }
      // 校验收款人开户行名称只能输入英文
      if (!englishRegex.test(basicInfo.receiveBankName)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidReceiveBankName"));
        return false;
      }
      // 代理行地址只能输入英文
      if (!englishRegex.test(basicInfo.agencyBankAddress)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidAgencyBankAddress"));
        return false;
      }
      // 代理行地址只能输入英文
      if (!englishRegex.test(basicInfo.paymentRemark)) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.invalidPaymentRemark"));
        return false;
      }
    }
    // 校验交易编码2相关逻辑
    if (basicInfo.transCode2) {
      // 校验交易编码1和交易编码2是否相同
      if (basicInfo.transCode1 === basicInfo.transCode2) {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.transCode1TransCode2Same"));
        return false;
      }
      // 校验交易编码2、对应金额2、交易附言2必须同时存在
      if (!basicInfo.transAmount2 || !basicInfo.transRemark2) {
        FMessageBox.report(
          t("crossborder.settleinstructionrec.disbursement.transCode2Amount2Remark2Required", {
            transCode2: t("crossborder.settleinstructionrec.disbursement.transCode2"),
            transAmount2: t("crossborder.settleinstructionrec.disbursement.transAmount2"),
            transRemark2: t("crossborder.settleinstructionrec.disbursement.transRemark2")
          })
        );
        return false;
      }
    } else {
      // 如果没有交易编码2，则对应金额2和交易附言2不应该填写
      if (basicInfo.transAmount2 || basicInfo.transRemark2) {
        FMessageBox.report(
          t("crossborder.settleinstructionrec.disbursement.transCode2NotFilledButAmount2Remark2Filled")
        );
        return false;
      }
    }
    return true;
  };
  // 返回列表页
  const goBack = () => {
    goPage("list");
  };
  //附件
  const upload = shallowRef();
  //上传附件返回的数组信息
  const fileInfos = ref<any[]>([]);
  // 保存
  const saveInfo = () => {
    if (upload.value) {
      fileInfos.value.splice(0);
      fileInfos.value.push(...upload.value.fileData);
      if (fileInfos.value.length > 0) {
        basicInfo.fileIds = fileInfos.value.map((item: any) => item.id).join(",");
      }
    }
    return basicInfo;
  };
  //修改保存后数据回显到页面
  const saveDataSuccess = (res: any) => {
    if (res.success) {
      basicInfo.id = res.data.id;
      basicInfo.version = res.data.version;
      basicInfo.checkStatus = res.data.checkStatus;
      basicInfo.inputUserId = res.data.inputUserId;
      basicInfo.inputUserName = res.data.inputUserName;
      basicInfo.inputTime = res.data.inputTime;
    }
  };
  const submitDataSuccess = (res: any) => {
    if (res?.success) {
      //返回列表页面
      goPage("list");
    }
  };
  const crossBorderShowFlag = ref(false);
  //获取明细信息
  const getAddInfo = async (row: any) => {
    const disbursementAddDetailInfo = JSON.parse(row.detail);
    basicInfo.instructionId = row.id;
    basicInfo.instructionNo = row.instructionNo;
    basicInfo.globalSerial = row.instructionNo;
    basicInfo.officeId = disbursementAddDetailInfo.officeId; // 机构id
    basicInfo.officeCode = disbursementAddDetailInfo.officeCode; // 机构编码
    basicInfo.officeName = disbursementAddDetailInfo.officeName; // 机构名称
    basicInfo.currencyId = disbursementAddDetailInfo.currencyId; // 币种
    basicInfo.currencyCode = disbursementAddDetailInfo.currencyCode; // 币种编号
    basicInfo.currencyName = disbursementAddDetailInfo.currencyName; // 币种名称
    basicInfo.isPaymentPrivate = disbursementAddDetailInfo.isPaymentPrivate; //是否对私付款
    if (
      disbursementAddDetailInfo.recAcctType === recAcctTypeData.INNER_ACCOUNT ||
      disbursementAddDetailInfo.recAcctList[0].countryCode === "CN"
    ) {
      basicInfo.paymentType = paymentType.DOMESTIC_PAYMENT; // 付款类型，1-境内付款，2-境外付款
    } else {
      basicInfo.paymentType = paymentType.OVERSEA_PAYMENT; // 付款类型，1-境内付款，2-境外付款
    }
    basicInfo.paymentMethod = paymentMethod.WIRE_TRANSFER; // 汇款方式，0-电汇T/T，1-票汇D/D，2-信汇M/T
    basicInfo.sendTelegramLevel = remitPriorityEnum.URGENT; // 发电等级，0-普通，1-加急
    if (disbursementAddDetailInfo.recAcctType === recAcctTypeData.INNER_ACCOUNT) {
      basicInfo.businessChannel = businessChannel.COUNTER_HANDLING; // 业务通道，1-柜台办理，2-银企通道，3-CIPS通道
    }
    if (
      (basicInfo.currencyCode === "CNY" && basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) ||
      basicInfo.currencyCode !== "CNY"
    ) {
      crossBorderShowFlag.value = true;
    } else {
      crossBorderShowFlag.value = false;
    }
    basicInfo.payClientId = disbursementAddDetailInfo.loanClientId; // 付款客户ID
    basicInfo.payClientName = disbursementAddDetailInfo.loanClientName; // 付款客户名称
    basicInfo.payClientCode = disbursementAddDetailInfo.loanClientCode; // 付款客户编号
    basicInfo.payAcctId = disbursementAddDetailInfo.payAcctId; // 付款人账户id
    basicInfo.payAcctNo = disbursementAddDetailInfo.payAcctNo; // 付款人账户编号
    basicInfo.payAcctName = disbursementAddDetailInfo.payAcctName; // 付款人账户名称
    //调用接口查询账户可用余额
    accountBalanceParams.accountId = disbursementAddDetailInfo.payAcctId;
    accountBalanceParams.accountNo = disbursementAddDetailInfo.payAcctNo;
    accountBalanceParams.balanceType = balanceType.AVAILABLE;
    //调用接口查询账户可用余额
    await queryAccountBalance(accountBalanceParams);
    await httpTool.post(innerClientInfoQueryByClientIdUrl, { clientId: basicInfo.payClientId }).then((res: any) => {
      if (basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) {
        //境外付款取英文
        basicInfo.payOrgCode = res.data.organCodeCert; // 付款人组织机构代码
        basicInfo.payAddress = res.data.engRegisteredAddress; // 付款人地址
        basicInfo.paymentName = res.data.englishName; // 付款客户名称
      } else {
        //境内取中文
        basicInfo.payOrgCode = res.data.organCodeCert; // 付款人组织机构代码
        basicInfo.payAddress = res.data.registeredAddress; // 付款人地址
        basicInfo.paymentName = res.data.clientName; // 付款客户名称
      }
    });
    basicInfo.payBankNo = ""; // 付款开户行编号
    basicInfo.payBankName = disbursementAddDetailInfo.payBankName; // 付款开户行名称
    basicInfo.payBankAcctNo = disbursementAddDetailInfo.payBankAcctNo; // 付款银行账号
    basicInfo.payBankAcctName = disbursementAddDetailInfo.payBankAcctName; // 付款银行账号名称
    if (disbursementAddDetailInfo.recAcctType === recAcctTypeData.INNER_ACCOUNT) {
      basicInfo.receiveId = disbursementAddDetailInfo.recAcctId; // 收款人账户id
      basicInfo.receiveNo = disbursementAddDetailInfo.recAcctNo; // 收款人账户编号
      basicInfo.receiveName = disbursementAddDetailInfo.recAcctName; // 收款人账户名称
    } else {
      basicInfo.receiveNo = disbursementAddDetailInfo.recBankAcctNo; // 收款人账户编号
      basicInfo.receiveName = disbursementAddDetailInfo.recBankAcctName; // 收款人账户名称
      await getExternalAccountInfo({
        officeId: basicInfo.officeId,
        currencyId: basicInfo.currencyId,
        codeAndName: basicInfo.receiveNo
      });
    }
    basicInfo.countryCode = disbursementAddDetailInfo.countryCode; // 收款人常驻国家(地区)代码
    basicInfo.receiveAddress = ""; // 收款人地址
    basicInfo.receiveBankName = disbursementAddDetailInfo.remitBank; // 收款人开户行名称
    basicInfo.costBearing = costBearing.OUR; // 国内外费用承担方，0-OUR-汇款人，1-BEN-收款人，2-SHA-共同
    basicInfo.itemType = disbursementAddDetailInfo.itemType; // 款项类型，0-预付款，1-货到付款，2-退款，3-其它
    basicInfo.pfeNature = disbursementAddDetailInfo.pfeNature; // 付汇性质，0-保税区，1-出口加工区，2-钻石交易所，5-其他特殊经济区域，3-深加工结转，4-其他
    basicInfo.bondedGoods = disbursementAddDetailInfo.bondedGoods; // 本笔款项是否为保税货物项下付款，1-是，2-否
    basicInfo.businessCode = disbursementAddDetailInfo.transactionCode; // 外汇局批件/备案表号/业务编号
    basicInfo.contractCode = disbursementAddDetailInfo.recContractCode; // 合同号
    basicInfo.invoiceCode = disbursementAddDetailInfo.invoiceCode; // 发票号
    basicInfo.interestStartDate = disbursementAddDetailInfo.interestStartDate; // 起息日
    basicInfo.executeDate = disbursementAddDetailInfo.executeDate; // 执行日
    basicInfo.paymentAmount = disbursementAddDetailInfo.amount; // 付款金额
    basicInfo.payBankAcctId = disbursementAddDetailInfo.payBankAcctId; // 付款银行账号Id
    basicInfo.payBankId = disbursementAddDetailInfo.payBankId; // 付款开户行id
    basicInfo.receiveProvince = disbursementAddDetailInfo.remitProvince; //收款人所在省
    basicInfo.receiveCity = disbursementAddDetailInfo.remitCity; //收款人所在城市
    basicInfo.originalAmount = basicInfo.paymentAmount;
    basicInfo.payMethod = disbursementAddDetailInfo.payMethod; //支付方式;支付方式，1：自主支付；2：受托支付
    basicInfo.isRiskCheck = disbursementAddDetailInfo.isRiskCheck; //是否通过风险校验 no:否 yes:是
    basicInfo.isPaymentPrivate = disbursementAddDetailInfo.isPaymentPrivate; //是否对私支付
    basicInfo.recAcctType = disbursementAddDetailInfo.recAcctType; //收款方账户类型
    basicInfo.transactionCode = disbursementAddDetailInfo.transactionCode; //交易编码
    basicInfo.purposeCode = disbursementAddDetailInfo.purposeCode; //用途代码
    await httpTool.post(getPurposeSetList, { purposeCode: basicInfo.purposeCode }).then((res: any) => {
      if (res.data) {
        basicInfo.purposeName = res.data[0].purposeName; //用途代码
      }
    });
    basicInfo.loanStartDate = disbursementAddDetailInfo.loanStartDate; //合同开始日
    basicInfo.loanTerm = disbursementAddDetailInfo.loanTerm; //期限
    basicInfo.loanEndDate = disbursementAddDetailInfo.loanEndDate; //合同到期日
    basicInfo.executeRate = disbursementAddDetailInfo.executeRate; //执行利率
    basicInfo.crossBorderDirection = disbursementAddDetailInfo.crossBorderDirection; //融通方向
    basicInfo.remark =
      t("crossborder.settleinstructionrec.disbursement.pay") +
      "USD" +
      format(basicInfo.paymentAmount) +
      t("crossborder.settleinstructionrec.disbursement.to") +
      basicInfo.receiveBankName;
    basicInfo.id = null;
    basicInfo.version = "";
  };

  //查询账户可用余额
  const getExternalAccountInfo = async (bankAcctInfo: any) => {
    await httpTool.post(getExternalAccountInfoUrl, bankAcctInfo).then((res: any) => {
      if (res?.success && res.data !== null) {
        basicInfo.receiveNo = res.data.exBankAccountCode;
        basicInfo.receiveName = res.data.exBankAccountName;
        basicInfo.receiveBankSwiftCode = res.data.bankSwiftCode;
        basicInfo.receiveBankName = res.data.bankName;
        basicInfo.receiveBankAddress = res.data.bankAddress;
        basicInfo.countryName = res.data.countryName;
        basicInfo.countryCode = res.data.countryCode;
        basicInfo.receiveCnps = res.data.cnaps;
        basicInfo.receiveCity = res.data.city;
      }
    });
  };

  //获取明细信息
  const getDetailInfo = (id: number | string, modifyFlag: boolean) => {
    if (id) {
      return httpTool.post(getLoanPayDetailInfoUrl, { id }).then((res: any) => {
        if (modifyFlag) {
          getRemoteDetailInfo(res, modifyFlag);
        } else {
          if (res.data) {
            res.data.id = "";
            res.data.checkStatus = "";
            res.data.transStatus = "";
            res.data.transId = "";
            res.data.transNo = "";
            res.data.revokeReason = "";
            res.data.failedReason = "";
          }
          getRemoteDetailInfo(res, modifyFlag);
        }
        if (basicInfo.fileIds) {
          basicInfo.fileIdArr = basicInfo.fileIds.split(","); // 如果后端返回的文件id字符串是用逗号拼接的
        } else {
          basicInfo.fileIdArr = [];
        }
        if (upload.value) {
          upload.value.init(basicInfo.fileIdArr);
        }
      });
    }
  };

  // 机构下拉框
  const officeChange = (value: any, info: any) => {
    basicInfo.officeCode = info.officeCode;
    basicInfo.officeName = info.officeName;
    currencyParams.officeId = info.officeId;
    currencyRef.value.initRemoteData();
    basicInfo.currencyId = null;
    basicInfo.currencyCode = "";
    basicInfo.currencyName = "";
  };
  // 币种查询入参
  const currencyParams = reactive({
    officeId: basicInfo.officeId
  });
  // 币种下拉框
  const currencyChange = (value: any, info: any) => {
    basicInfo.currencyId = info.currencyId;
    basicInfo.currencyCode = info.currencyCode;
    basicInfo.currencyName = info.currencyName;
    basicInfo.currencySymbol = info.currencySymbol;
    basicInfo.costCurrencyOneId = info.currencyId;
    basicInfo.costCurrencyOneCode = info.currencyCode;
    basicInfo.costCurrencyOneName = info.currencyName;
    basicInfo.costCurrencyOneSymbol = info.currencySymbol;
    basicInfo.costCurrencyTwoId = info.currencyId;
    basicInfo.costCurrencyTwoCode = info.currencyCode;
    basicInfo.costCurrencyTwoName = info.currencyName;
    basicInfo.costCurrencyTwoSymbol = info.currencySymbol;
    basicInfo.originalCurrencyId = info.currencyId;
    basicInfo.originalCurrencyCode = info.currencyCode;
    basicInfo.originalCurrencyName = info.currencyName;
    basicInfo.originalCurrencySymbol = info.currencySymbol;
  };
  //客户编号放大镜change事件
  const clientCodeChange = (row: any) => {
    basicInfo.clientId = row.clientId;
    basicInfo.clientCode = row.clientCode;
    basicInfo.clientName = row.clientName;
    basicInfo.payOrgCode = row.organizationCode;
    basicInfo.paymentName = row.clientName;
  };
  //客户编号放大镜clear事件
  const clearClientCode = () => {
    basicInfo.clientId = "";
    basicInfo.clientCode = "";
    basicInfo.clientName = "";
    basicInfo.payOrgCode = "";
  };
  //查询账户可用余额入参
  const accountBalanceParams = reactive({
    accountId: "",
    accountNo: "",
    billCode: "",
    balanceType: "",
    currencyType: "",
    transNo: ""
  });
  //查询账户可用余额
  const queryAccountBalance = async (accountBalanceParams: any) => {
    await httpTool.post(queryAccountBalanceInfo, accountBalanceParams).then((res: any) => {
      if (res?.success) {
        basicInfo.payBalance = res.data.balance;
        basicInfo.payAvailableBalance = res.data.availableBalance;
      } // 对于新开立的账户查询账户余额失败，给出提示并清除所选信息
      else {
        FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.accountBlanceRrror"));
      }
    });
  };

  //开户行放大镜change事件
  const openBankChange = (row: any) => {
    basicInfo.payBankId = row.openBankId;
    basicInfo.payBankNo = row.openBankCode;
    basicInfo.payBankName = row.openBankName;
    basicInfo.payBankAcctId = row.bankAccountId;
    basicInfo.payBankAcctNo = row.bankAccountCode;
    basicInfo.payBankAcctName = row.bankAccountName;
  };

  //开户行放大镜clear事件
  const openBankClear = () => {
    basicInfo.payBankAcctId = null;
    basicInfo.payBankAcctNo = "";
    basicInfo.payBankAcctName = "";
    basicInfo.payBankId = null;
    basicInfo.payBankNo = "";
    basicInfo.payBankName = "";
  };
  //国别放大镜change事件
  const countryChange = (row: any) => {
    basicInfo.countryCode = row.dictKey;
    basicInfo.countryName = row.dictValue;
  };

  //付费账户放大镜change事件
  const chargeAccountChange = (row: any) => {
    basicInfo.chargeAccountId = row.accountId;
    basicInfo.chargeAccountCode = row.accountCode;
  };

  //购汇账户放大镜clear事件
  const clearChargeAccount = () => {
    basicInfo.chargeAccountId = null;
    basicInfo.chargeAccountCode = "";
  };
  //手续费率放大镜change事件
  const chargeRateChange = (row: any) => {
    if (row) {
      basicInfo.chargeRateId = row.id;
      basicInfo.chargeRate = row.chargeRate;
    } else {
      basicInfo.chargeRateId = null;
      basicInfo.chargeRate = null;
    }
  };
  //交易编号放大镜change事件
  const transCode1Change = (row: any) => {
    basicInfo.transCode1 = row.dictKey;
  };

  //交易编号放大镜clear事件
  const clearTransCode1 = () => {
    basicInfo.transCode1 = "";
  };
  //交易编号放大镜change事件
  const transCode2Change = (row: any) => {
    basicInfo.transCode2 = row.dictKey;
  };

  //交易编号放大镜clear事件
  const clearTransCode2 = () => {
    basicInfo.transCode2 = "";
  };
  //获取开机日
  const getOpenDate = () => {
    httpTool.post(openDateUrl).then((res: any) => {
      basicInfo.executeDate = res.data.onlineDate;
      basicInfo.interestStartDate = res.data.onlineDate;
    });
  };

  //获取跨境基础信息设置
  const getFinanceBasicinfo = () => {
    httpTool.post(getBasicInfoSetInfo, { cipsId: basicInfo.cipsId }).then((res: any) => {
      if (res?.success && res.data !== null) {
        basicInfo.cipsId = res.data.cipsId;
        basicInfo.leiCode = res.data.leiCode;
        basicInfo.paymentBankAddress = res.data.address;
      }
    });
  };
  // 币种下拉框
  const costCurrencyChange1 = (value: any, info: any) => {
    basicInfo.costCurrencyOneId = info.currencyId;
    basicInfo.costCurrencyOneCode = info.currencyCode;
    basicInfo.costCurrencyOneName = info.currencyName;
    basicInfo.costCurrencyOneSymbol = info.currencySymbol;
  };
  // 币种下拉框
  const costCurrencyChange2 = (value: any, info: any) => {
    basicInfo.costCurrencyTwoId = info.currencyId;
    basicInfo.costCurrencyTwoCode = info.currencyCode;
    basicInfo.costCurrencyTwoName = info.currencyName;
    basicInfo.costCurrencyTwoSymbol = info.currencySymbol;
  };
  // 币种下拉框
  const originalCurrencyChange = (value: any, info: any) => {
    basicInfo.originalCurrencyId = info.currencyId;
    basicInfo.originalCurrencyCode = info.currencyCode;
    basicInfo.originalCurrencyName = info.currencyName;
    basicInfo.originalCurrencySymbol = info.currencySymbol;
  };
  //查询数据库返回数据下拉框，需要转成字符串的字段
  const { convert } = useConversion([""]);
  //查询详情信息
  const getRemoteDetailInfo = (info: CsCrossBorderLoanPayDto, row: any) => {
    Object.assign(info, convert(row.data));
  };
  // 计算现汇金额
  const paymentAmountChange = () => {
    basicInfo.originalAmount = basicInfo.paymentAmount;
    buildRemark();
  };
  const businessChannelChange = () => {
    if (basicInfo.businessChannel === businessChannel.BANK_ENTERPRISE_CHANNEL) {
      basicInfo.sendInstruction = yesOrNo.YES;
    }
    if (basicInfo.businessChannel === businessChannel.CIPS_CHANNEL) {
      getFinanceBasicinfo();
    }
  };
  const paymentTypeChange = () => {
    if (basicInfo.paymentType === paymentType.OVERSEA_PAYMENT) {
      basicInfo.countryName = "";
    } else {
      basicInfo.countryName = t("crossborder.settleinstructionrec.disbursement.cNcountry");
    }
  };
  // 自动生成摘要
  const buildRemark = () => {
    basicInfo.remark =
      t("crossborder.settleinstructionrec.disbursement.pay") +
      "USD" +
      format(basicInfo.paymentAmount) +
      t("crossborder.settleinstructionrec.disbursement.to") +
      basicInfo.receiveBankName;
  };
  const print = () => {
    if (basicInfo.id <= 0 || basicInfo.id === "") {
      FMessageBox.report(t("crossborder.settleinstructionrec.disbursement.saveApply"));
      return false;
    }
    // 查询打印设置信息
    httpTool
      .post(getPrintRelationUrl, {
        officeId: basicInfo.officeId,
        functionType: "SINGLE_DATA",
        businessTypeCode: forexBusinessType.FOREX_CROSSBORDER_PAYMENT,
        businessOperateType: "SAVE",
        printTarget: "FINANCE",
        batchFlag: "NO"
      })
      .then((res: any) => {
        if (res?.success) {
          if (res.data) {
            printDialog.value.openPrintDialog(res.data);
          }
        }
      });
  };
  const purposeCodeChange = (row: any) => {
    basicInfo.purposeName = row.purposeName;
    basicInfo.purposeId = row.Id;
  };
  // 导出
  const exportExcel = (row: any) => {
    row.paymentBankAccountCode = row.payBankAcctNo;
    row.paymentAddress = row.payAddress;
    row.receiveBankAccountCode = row.receiveNo;
    row.receiveBankAccountName = row.receiveName;
    row.paymentOpenBankName = row.payBankName;
    httpTool.doExport(exportExcelUrl, "xlsx", row);
  };
  return {
    basicInfo,
    formValidator,
    form1,
    officeChange,
    currencyParams,
    currencyChange,
    clientCodeChange,
    clearClientCode,
    openBankChange,
    openBankClear,
    countryChange,
    chargeAccountChange,
    clearChargeAccount,
    chargeRateChange,
    transCode1Change,
    clearTransCode1,
    transCode2Change,
    clearTransCode2,
    getOpenDate,
    costCurrencyChange1,
    costCurrencyChange2,
    originalCurrencyChange,
    getRemoteDetailInfo: (row: any) => {
      getRemoteDetailInfo(basicInfo, row);
    },
    paymentAmountChange,
    businessChannelChange,
    paymentTypeChange,
    getFinanceBasicinfo,
    buildRemark,
    printDialog,
    print,
    purposeCodeChange,
    goBack,
    saveInfo,
    upload,
    saveDataSuccess,
    getDetailInfo,
    getAddInfo,
    exportExcel,
    submitDataSuccess,
    crossBorderShowFlag
  };
};
export default useDisbursement;
