export default {
  title: "结算指令接收",
  viewStatus: "状态",
  viewOperate: "操作",
  viewRecordUnit: "条",
  viewRecord: "记录",
  businessStatus: "单据状态",
  instructionType: "业务类型",
  contractCode: "合同编号",
  clientCode: "客户号",
  clientName: "客户名称",
  amount: "金额",
  noticeCode: "通知单号",
  noticeCodeMagnifier: "通知单放大镜",
  brClientName: "借款单位名称",
  clientCodeMagnifier: "客户放大镜",
  operateRec: "接收",
  cancel: "撤销",
  cancelSuccess: "撤销成功",
  cancelReason: "撤销原因",
  payNoticeTitle: "放款结算指令接收",
  repayNoticeTitle: "还款结算指令接收",
  loanFreeTitle: "免还结算指令接收",
  settleInterestTitle: "结息结算指令接收",
  loanPayTitle: "贷款支付结算指令接收",

  instructionNo: "指令号",
  contractCodeMagnifier: "合同/协议号放大镜",
  protocolCode: "承兑/贴现协议号",
  marginAmount: "解付保证金金额",
  mechanismFlags: "是否显示机制",
  viewExecuteDate: "执行日",
  interestStartDate: "起息日",
  discountInterest: "贴现利息",
  fileInfo: "附件信息",
  file: "附件",
  transAbstract: "备注",
  infoRec: "确认接收",
  infoRecSuccess: "接收完成",
  operateSubmit: "提交",
  infoSubmit: "是否提交？",
  infoSubmitSuccess: "提交成功",
  modify: "修改",
  cancelTip: "是否撤销？",
  operateBack: "退回",
  infoBack: "确认退回",
  infoBackSuccess: "退回完成",
  viewBackReason: "退回原因",
  close: "关闭",
  queryList: "链接查找",
  payWayPlaceHolder: "付款方账户或收款方账户不可直连支付",
  payBankAccountCode: "付款方账号",
  payAccountCodeMagnifier: "付款方账号放大镜",
  bankCode: "银行账号",
  bankName: "银行账户名称",
  openBankName: "银行账户开户行",
  recBankAccountCode: "收款方账号",
  office: "机构",
  currency: "币种",
  frontApplyCode: "前置业务单据号",
  payBankAccountName: "付款方账户名称",
  payOpenBankName: "付款方账户开户行",
  recBankAccountName: "收款方账户名称",
  recOpenBankName: "收款方账户开户行",
  expectationExecuteDate: "期望执行日",
  payWay: "支付方式",
  amountUse: "资金用途",
  instStatus: "指令状态",
  failReason: "驳回/失败原因",
  sendBankInstFlag: "是否发送银企指令",
  bankInstBackReason: "银企指令撤销原因",
  bankInstPayStatus: "银企指令支付状态",
  inputTime: "录入时间",
  operate: "操作",
  history: "审批历史",
  chooseReceiveTip: "共勾选{0}条数据，是否全部接收？",
  batchReceive: "批量接收",
  transAbstractMagnifier: "摘要放大镜",
  imageSupplement: "影像补充",
  fileListIsNullTip: "请上传附件信息",
  uploadSuccess: "上传成功",
  invalidHandle: "作废",
  invalidHandleConfirm: "是否确认作废?",
  invalidHandleSuccess: "作废成功",
  printForm: "打印申请书",
  printFormSuccess: "打印成功",
  exportExcel: "导出Excel",
  exportExcelSuccess: "导出成功",
  createPayTransaction: "生成付款交易",
  createPayTransactionConfirm: "是否生成付款交易?",
  createPayTransactionSuccess: "生成付款交易成功",
  forexSettlementCus: "代客结汇",
  receiveDate: "接收日期",
  disbursement: {
    recAcctType: "收款人账户类型",
    addTitle: "跨境融通贷款支付-新增",
    modifyTitle: "跨境融通贷款支付-修改",
    ListTitle: "跨境融通贷款支付-链接查找",
    detailTitleInfo: "跨境融通贷款支付-查看",
    approvalTitleInfo: "跨境融通贷款支付-审批",
    approvalTitle: "跨境融通贷款支付-审批",
    detailTitle: "跨境融通贷款支付-查看",
    linkQuery: "链接查找",
    recording: "记录",
    strip: "条",
    add: "新增",
    basicInfo: "基础信息",
    office: "机构",
    currency: "币种",
    paymentType: "付款类型",
    businessType: "业务类型",
    paymentMethod: "汇款方式",
    sendTelegramLevel: "发电等级",
    ordinary: "普通",
    urgent: "加急",
    businessChannel: "业务通道",
    businessCategoryCode: "业务种类编码",
    orderCode: "订单编号",
    orderDescription: "订单描述",
    paymentAmount: "付款金额",
    partForeignExchange: "是否部分购汇",
    land: "是否落地",
    paymentInfo: "付款方信息",
    clientName: "客户名称",
    clientCode: "客户编号",
    clientNameMagnifier: "客户名称放大镜",
    seAccountCode: "现汇账户号",
    seAccountName: "现汇账户名称",
    seAccountBalance: "现汇账户余额",
    seAccountMagnifier: "现汇账户号放大镜",
    accountTypeName: "账户类型",
    accountNo: "账户号",
    accountName: "账户名称",
    seAccountAvailableBalance: "现汇账户可用余额",
    seAmount: "现汇金额",
    feAccountCode: "购汇账户号",
    feAccountName: "购汇账户名称",
    feAccountBalance: "购汇账户余额",
    feAccountAvailableBalance: "购汇账户可用余额",
    feAccountMagnifier: "购汇账户放大镜",
    accountBlanceRrror: "该账户无可用余额，请重新选择！",
    feAmount: "购汇金额",
    financeTransitAccCode: "财司过渡户",
    financeTransitAccMagnifier: "财司过渡户放大镜",
    paymentName: "付款人名称",
    paymentAddress: "付款人地址",
    organizationCode: "组织机构代码",
    paymentCipsId: "付款方CIPS ID",
    paymentLeiCode: "付款方LEI码",
    paymentEid: "付款方EID码",
    paymentBankAccountName: "开户行",
    paymentBankAccountCode: "银行账号",
    paymentBankSwiftCode: "开户行SWIFT号",
    cipsId: "财务公司CIPS ID",
    leiCode: "财务公司LEI码",
    paymentBankAddress: "开户行地址",
    openBankMagnifier: "开户行放大镜",
    receiveInfo: "收款方信息",
    bankName: "开户行名称",
    openBankAcctNo: "开户行账号",
    receiveBankAccountCode: "收款人账号",
    receiveAccountNoMagnifier: "收款人账号放大镜",
    receiveBankAccountName: "收款人名称",
    ibankAccount: "是否IBAN账户",
    countryCode: "收款人常驻国家（地区）代码",
    receiveCipsId: "收款方CIPS ID",
    receiveLeiCode: "收款方LEI码",
    receiveEid: "收款方EID码",
    receiveCity: "收款人所在城市",
    receiveAddress: "收款人地址",
    receiveBankName: "收款人开户行名称",
    receiveBank: "收款人开户行",
    receiveCnps: "收款人联行号",
    receiveBankSwiftCode: "收款行SWIFT号",
    receiveBankCipsId: "收款行CIPS ID",
    receiveBankLeiCode: "收款行LEI码",
    receiveBankAddress: "收款行地址",
    agencyBankName: "代理行名称",
    agencyBankSwiftCode: "代理行SWIFT号",
    receiveAgencyBankCode: "收款人开户行在代理行账号",
    agencyBankAddress: "代理行地址",
    paymentRemark: "汇款附言",
    exBankAccountCode: "账户号",
    exBankAccountName: "账户名称",
    countryName: "国别",
    bankSwiftCode: "收款行SWIFT号",
    countryCodeMagnifier: "收款人常驻国家（地区）代码放大镜",
    countryNo: "国家编码",
    countryNameEnglish: "国家（英文）",
    countryNameChinese: "国家（中文）",
    costInfo: "费用信息",
    costBearing: "国内外费用承担方",
    collectFromCustomer: "是否向客户收取",
    chargeAccountCode: "付费账户号",
    chargeTotalAmount: "费用总金额",
    chargeRate: "手续费率",
    preferentialPoint: "优惠点数",
    chargeAmount: "手续费金额",
    urgentRate: "加急费率",
    urgentAmount: "加急费金额",
    sendTelegramAmount: "电报费金额",
    chargeAccountMagnifier: "付费账户号放大镜",
    chargeRateMagnifier: "手续费率放大镜",
    ruleName: "规则名称",
    rate: "费率",
    minChargeAmount: "最低限额",
    maxChargeAmount: "最高限额",
    otherInfo: "其他信息",
    itemType: "款项类型",
    pfeNature: "付汇性质",
    bondedGoods: "本笔款项是否为保税货物项下付款",
    transCode1: "交易编码1",
    transAmount1: "对应金额1",
    transRemark1: "交易附言1",
    transCode2: "交易编码2",
    transAmount2: "对应金额2",
    transRemark2: "交易附言2",
    businessCode: "外汇局批件/备案表号/业务编号",
    contractCode: "合同号",
    invoiceCode: "发票号",
    purposeCode: "用途代码",
    purposeDescription: "用途说明",
    applyUserName: "申请人名称",
    applyUserPhone: "申请人电话",
    interestStartDate: "起息日",
    executeDate: "执行日",
    advancePaymentInfoStatus: "预付款资料状态",
    abstract: "摘要",
    sendInstruction: "是否发送银企指令",
    transCodeMagnifier: "交易编码放大镜",
    transCode: "交易编码",
    transName: "交易名称",
    purposeCodeeMagnifier: "用途代码放大镜",
    moneyUseCode: "用途代码",
    moneyUseExplain: "用途代码名称",
    cipsAdvancedInformation: "CIPS高级信息",
    paidBankOneLineNumber: "付费行1行号",
    paidBankOneCipsId: "付费行1CIPS ID",
    paidBankOneLeiCode: "付费行1LEI码",
    costCurrencyOneName: "费用币种1",
    costAmount1: "费用金额1",
    paidBankTwoLineNumber: "付费行2行号",
    paidBankTwoCipsId: "付费行2CIPS ID",
    paidBankTwoLeiCode: "付费行2LEI码",
    costCurrencyTwoName: "费用币种2",
    costAmount2: "费用金额2",
    paymentDParticipantBankCode: "付款直接参与者行号",
    paymentIParticipantBankCode: "付款间接参与者行号",
    receiveDParticipantBankCode: "收款直接参与者行号",
    receiveDParticipantCipsId: "收款直接参与者CIPS ID",
    receiveDParticipantLeiCode: "收款直接参与者LEI码",
    receiveIParticipantBankCode: "收款间接参与者行号",
    receiveIParticipantCipsId: "收款间接参与者CIPS ID",
    receiveIParticipantLeiCode: "收款间接参与者LEI码",
    intermediaryOneBankCode: "中介机构1行号",
    intermediaryOneCipsId: "中介机构1 CIPS ID",
    intermediaryOneLeiCode: "中介机构1LEI码",
    intermediaryOneName: "中介机构1名称",
    intermediaryTwoBankCode: "中介机构2行号",
    intermediaryTwoCipsId: "中介机构2 CIPS ID",
    intermediaryTwoLeiCode: "中介机构2LEI码",
    intermediaryTwoName: "中介机构2名称",
    originalCurrencyName: "原始币种",
    exchangeRate: "汇率",
    originalAmount: "原始金额",
    businessPriority: "业务优先级",
    toReceiveBankRemark: "致收款方银行附言",
    attmInfo: "附件信息",
    failedReason: "驳回/失败原因",
    bankInstrPaymentStatus: "银企支付状态",
    bankInstrRevokeReason: "银企指令撤销原因",
    exchangeApplyNo: "代客售汇申请号",
    riskType: "风险类型",
    riskItem: "风险项",
    riskVerifier: "风险核实人",
    riskVerificationMethod: "风险核实方式",
    transStatus: "单据状态",
    paymentAccountCode: "付款人账号",
    paymentAccountName: "付款人账户名称",
    accountBalance: "账户余额",
    availableBalance: "账户可用余额",
    applyNo: "单据号",
    paymentOpenBank: "付款人开户行",
    inputTime: "录入时间",
    operate: "操作",
    paymentAccountCodetMagnifier: "付款人账号放大镜",
    history: "审批历史",
    close: "关闭",
    sendInstrTitle: "发送指令",
    temporaryConfirm: "是否发送指令？",
    sendInstrSuccess: "指令发送成功",
    sendInstrFailed: "指令发送失败",
    printTitle: "打印申请书",
    printConfirm: "是否打印申请书？",
    printSuccess: "申请书打印成功",
    printFailed: "申请书打印失败",
    chooseDeleteTip: "共勾选{0}条数据，是否全部删除？",
    chooseSubmitTip: "共勾选{0}条数据，是否全部提交？",
    deleteSuccess: "删除成功",
    deleteFail: "删除失败",
    submitFail: "提交失败",
    submitSuccess: "提交成功",
    cancel: "撤销",
    revokeReason: "撤销原因",
    createPaymentTrans: "生成付款交易",
    createPaymentTransTip: "勾选的数据中存在单据状态不为待交易、已购汇、交易已拒绝的数据，不能生成付款交易！",
    copy: "复制",
    copyTip: "一次只允许复制一条数据！",
    isCopy: "是否复制？",
    copySuccess: "复制成功",
    deleteTip: "勾选的数据中存在单据状态不为已保存、已拒绝的数据，不允许删除！",
    submitTip: "勾选的数据中存在单据状态不为已保存、已拒绝的数据，不允许提交！",
    invalidReceiveAccountCode: "收款人账号只能输入字母或者数字",
    amountError: "对应金额需等于付款金额",
    amountError2: "现汇账户可用余额不足",
    validateError: "付款开户行≠工行、中行时，不可选银企通道",
    invalidPaymentName: "付款类型为境外付款时，付款人姓名只能输入英文",
    invalidPaymentAddress: "付款类型为境外付款时，付款人地址只能输入英文",
    invalidReceiveBankAccountName: "收款类型为境外收款时，收款人名称只能输入英文",
    invalidReceiveAddress: "收款类型为境外收款时，收款人地址只能输入英文",
    invalidReceiveBankName: "收款类型为境外收款时，收款人开户行名称只能输入英文",
    invalidAgencyBankAddress: "收款类型为境外收款时，代理行地址只能输入英文",
    invalidPaymentRemark: "收款类型为境外收款时，汇款附言只能输入英文",
    transCode1TransCode2Same: "交易码1与交易码2 不可相同，请修改",
    transCode2Amount2Remark2Required:
      "交易码、对应金额、交易附言必须同时存在，请填写{transCode2}、{transAmount2}、{transRemark2}",
    transCode2NotFilledButAmount2Remark2Filled: "交易编码2未填写，但对应金额2或交易附言2已填写",
    pay: "付",
    to: "至",
    saveApply: "请先保存单据再打印！",
    cNcountry: "中华人民共和国",
    receiveProvince: "收款人所在省",
    exportExcel: "导出Excel"
  },
  freeapply: {
    internalMainAccount: "国内主账户",
    internalMainAccountInput: "请选择国内主账户",
    bankAccountCode: "银行账号",
    openBankName: "开户行",
    baseInfo: "基础信息",
    loanInfo: "合同信息",
    freeInfo: "免还信息",
    contractStartDate: "合同开始日",
    contractEndDate: "合同到期日",
    receiptAmount: "放款单金额",
    receiptBalance: "放款单通知单余额",
    repayAmount: "已还款金额",
    freeDate: "免还日期",
    freePrincipalFlg: "免还本金",
    freeNormalInterestFlg: "免还利息",
    freeOweInterestFlg: "免还欠息",
    freeOverdueInterestFlg: "免还罚息",
    freeFeeAmountFlg: "免还手续费",
    freeAmountTotal: "本次免还金额合计",
    freeTypeList: "免还类型",
    inputDateArray: "录入日期",
    channelList: "数据来源",

    freeNormalInterest: "免还利息金额",
    freeOweInterest: "免还未结欠息金额",
    freeOverdueInterest: "免还逾期罚息金额",
    freeFee: "免还手续费金额",
    freeFeeAmount: "免还手续费金额",

    interestCalculateType: "利息计算方式",
    calculateInterest: "计算利息和费用",

    listTitle: "跨境融通免还 - 链接查找",
    addTitle: "跨境融通免还 - 新增",
    modifyTitle: "跨境融通免还 - 修改",
    approvalTitle: "跨境融通免还 - 审批",
    viewTitle: "跨境融通免还 - 查看",
    detailTitle: "跨境融通免还 - 查看",
    add: "新增",
    goBack: "链接查找",
    close: "关闭",
    fileInfo: "附件详情",
    file: "附件",
    operate: "操作",
    revokeBtch: "批量撤销",
    revokeConfirm: "确认撤销？",
    revokeSuccess: "撤销成功",
    businessCode: "免还申请单号",
    businessCodeInput: "请选择申请单编号",
    officeName: "机构",
    currencyName: "币种",
    contractCode: "合同编号",
    contractCodeInput: "请选择合同编号",
    contractAmount: "合同金额",
    contractBalance: "合同余额",
    contractTerm: "期限",
    loanPurpose: "借款用途",
    notePayCode: "放款通知单号",
    notePayInfo: "放款通知单信息",
    loanClientCode: "借款单位编号",
    loanClientInfo: "单位信息",
    loanClientName: "借款单位名称",
    loanClientNameInfo: "借款单位",
    clientCode: "单位编号",
    clientName: "单位名称",
    loanClientCodeInput: "请选择借款单位编号",
    loanClientNameInput: "请选择借款单位名称",
    consignClientCode: "委托单位编号",
    consignClientName: "委托单位名称",
    consignClientInfo: "委托单位",
    executeRate: "执行利率",
    loanStartDate: "贷款开始日期",
    loanEndDate: "贷款结束日期",
    loanTerm: "贷款期限",
    freePrincipalAmount: "免还本金金额",
    freePrincipalAmountInput: "请输入免还本金金额",
    freeInterestTotal: "本次免还金额合计",
    inputUserName: "录入人",
    inputTime: "录入时间",
    businessStatus: "单据状态",
    freePrincipalAmountArray: "免还本金",
    freeAmountTotalArray: "免还金额",
    receiptId: "放款通知单号",
    receiptCode: "放款通知单编号",
    receiptIdInput: "请选择放款通知单编号",
    loanType: "贷款业务种类",
    payAmount: "放款金额",
    payDate: "放款日期",

    freeFeeFlag: "免还手续费",
    shouldBePaid: "应当支付",
    willBeFree: "本次免还",

    normalInterest: "利息金额",
    oweInterest: "未结欠息",
    overdueInterest: "逾期罚息",
    freeInterest: "免还剩余利息",
    freeReason: "免还原因",
    freeRemark: "免还备注",
    feeAmount: "应付手续费",
    repayPlanId: "还款计划",
    repayPlanIdInput: "请选择还款计划",
    planCode: "还款计划编号",
    planDate: "还款计划日期",
    relatedRepayPlans: "关联还款计划",
    freeTheInterest: "免还剩余利息",
    freeInterestTotalMin: "请输入免还本金或者利息",
    month: "月",
    interestNotice: "利息信息",
    wfHistoryTitle: "审批历史",
    productName: "贷款产品",
    compInterest: "未结复利",
    freeCompInterestFlg: "免还复利",
    freeCompInterest: "免还未结复利金额",
    businessInfo: "单据信息",
    officeInfo: "机构信息",
    freeAmount: "免还费用",
    receiptInfo: "放款单信息",
    endDate: "结束日期",
    crossDirection: "融通方向",
    usdExchangeRate: "折美元汇率",
    usdAmount: "折美元金额",
    shouldPay: "应当支付",
    rejectionReason: "驳回原因",
    contractTermInfo: "合同期限",
    settlementInfo: "结算信息",
    settlementMainAccountId: "国内主账户",
    transactionCode: "交易编码",
    transactionCodeMagnifier: "交易编码放大镜",
    transactionName: "交易编号名称",
    purposeCode: "用途代码",
    purposeCodeMagnifier: "用途代码放大镜",
    purposeName: "用途代码名称",
    settlementAmount: "金额(合同币种)",
    principalAmount: "本金金额(合同币种)",
    debitInterest: "欠息金额（合同币种）",
    interestStartDate: "起息日",
    executeDate: "执行日",
    transAbstract: "摘要",
    settlementFreeAmount: "免还本金金额（合同币种）",
    settlementFreeNormalInterest: "免还正常利息金额（合同币种）",
    settlementFreeOverdueInterest: "免还逾期罚息金额（合同币种）",
    settlementFreeOweInterest: "免还欠息金额（合同币种）",
    settlementFreeCnyAmount: "免还费用金额（人民币）"
  },
  noticepay: {
    forexSettlementCus: "代客购汇",
    rateAdjustWay: "利率调整方式",
    rateAdjustFreq: "利率调整周期",
    firstDayAdjustFlag: "周期第一天调整",
    fixedAdjustDate: "调整日期",
    repeatAnnuallyFlag: "每年重复",
    freeChargeFlag: "是否免收手续费",
    displayBackContractDateFlag: "是否返显合同日期",
    fixedRate: "固定利率",
    crossDirection: "融通方向",
    payInterestAcctNo: "借款人付息账户号",
    repayPrincipalAcctNo: "借款人还本账户号",
    recPrincipalAcctNo: "借款人贷款专户",
    consignRecInterestAccountNo: "委托人收息账户号",
    consignCurrentAccountNo: "委托人活期账户号",
    factoringRecourseFlag: "有无追索权",
    factoringPublicFlag: "是否公开",
    factoringRealInterest: "实付利息",
    factoringRealPayAmount: "实际发放金额",
    loanTermArray: "贷款期限",
    businessInfo: "单据信息",
    billCode: "单据号",
    officeInfo: "办理机构",
    loanTypeInfo: "贷款合同",
    compactLoanType: "贷款种类",
    compactLoanBizType: "贷款类型",
    compactLoanTerm: "期限（月）",
    compactLoanStartDate: "开始日",
    compactLoanEndDate: "到期日",
    compactLoanClientCode: "借款客户",
    compactConsignClientCode: "委托客户",
    loanClientCodeList: "借款客户名称",
    consignClientCodeList: "委托客户名称",
    clientCode: "客户编号",
    inputDateArray: "录入日期",
    businessDataSourceList: "数据来源",
    industryCategoryFuzzy: "贷款投向行业",
    loanInvestArea: "贷款投向地区",
    fundsSource: "资金来源",
    listTitle: "放款通知单 - 链接查找",
    businessCode: "放款通知单号",
    businessCodeInput: "请选择放款通知单编号",
    officeName: "机构",
    currencyName: "币种",
    loanType: "贷款业务种类",
    loanBizType: "贷款业务类型",
    loanProduct: "贷款产品",
    loanProductCode: "贷款产品编号",
    loanProductName: "贷款产品名称",
    loanProductNameInput: "贷款产品接口尚未提供",
    businessType: "业务类型",
    contractCode: "合同编号",
    loanClientCode: "借款单位编号",
    loanClientName: "借款单位名称",
    consignClientCode: "委托单位编号",
    consignClientName: "委托单位名称",
    payAmount: "放款金额",
    payAmountInput: "请输入放款金额",
    payAmountMax: "放款金额不能大于可发放金额",
    contractAmount: "合同金额",
    contractExecuteRate: "合同执行利率",
    executeRate: "执行利率",
    greenCredit: "是否绿色贷款",
    greenCreditType: "绿色贷款分类",
    technologyLoans: "是否科技贷款",
    technologyLoansName: "科技贷款分类",
    cycleLoan: "是否循环贷款",
    fullEntrustedPay: "是否全额受托支付",
    businessStatus: "单据状态",
    inputUserName: "录入人",
    inputTime: "录入时间",
    payAmountArray: "放款金额",
    listedCompany: "是否上市",
    isMechanism: "是否显示机制",
    add: "新增",
    batchDelete: "批量删除",
    addPayNotice: "新增放款通知单",
    goBack: "链接查找",
    doReturn: "返回",
    payFee: "委贷费用信息",
    addTitle: "放款通知单 - 新增",
    contractAmountTitle: "合同金额",
    payAvailableAmount: "可发放金额",
    contractStartDate: "合同开始日",
    contractEndDate: "合同结束日",
    contractTerm: "期限（月）",
    payAccountNo: "借款人贷款专户",
    payAccountName: "账户名称",
    payAmountTitle: "金额",
    rateRunType: "贷款利率方式",
    loanRateType: "贷款利率类型",
    fixedRateRunType: "固定利率",
    floatingRateRunType: "浮动利率",
    floatingRateId: "挂牌利率",
    floatingRate: "浮动利率",
    floatingWay: "浮动方式",
    floatingType: "浮动比例",
    floatingRatio: "浮动比例",
    basePoint: "基点",
    pointType: "基点浮动方式",
    pointFloating: "基点浮动值",
    referenceRateId: "参考利率",
    interestSettTerm: "结息周期",
    interestSettDateFirst: "首次结息日",
    loanStartDate: "贷款开始日",
    loanEndDate: "贷款结束日",
    loanTerm: "期限（月）",
    loanTermMonth: "贷款期限（月）",
    rejectionReason: "驳回原因",
    revocationReason: "撤销原因",
    remark: "备注",
    contractCodeInput: "请选择合同编号",
    internalMainAccountInput: "请选择国内主账户",
    loanClientCodeInput: "请选择借款单位",
    inGrantAmount: "已发放金额",
    unGrantAmount: "未发放金额",
    repaymentAmount: "已还款金额",
    usageOfLoan: "借款用途",
    delegatePayType: "支付方式",
    interestSettNo: "借款人还本付息账户号",
    interestSettNoInput: "请选择借款人还本付息账户号",
    interestSettClientName: "借款人还本付息账户名称",
    payDate: "放款日期",
    autoRepayFlag: "是否自动还款",
    payAccountNoInput: "请选择借款人贷款专户",
    payAmountChinese: "金额大写",
    floatingRateIdInput: "请选择挂牌利率",
    floatingRateCode: "利率编号",
    floatingRateName: "利率名称",
    accountBank: "开户行",
    accountBankInput: "请选择开户行",
    batchSubmit: "批量提交",
    batchRevoke: "批量撤销",
    buyAddFlag: "是否补录",
    yes: "是",
    no: "否",
    settleMethod: "结息方式",
    accountCode: "账户编号",
    accountName: "账户名称",
    consignCurrentAccountId: "委托人活期账户号",
    payFeeAcct: "付手续费账户",
    payFeeAcctInput: "请选择付手续费账户",
    consignCurrentAccountIdInput: "请选择委托人活期账户号",
    recInterestAccountId: "委托人收息账户号",
    recInterestAccountIdInput: "请选择委托人收息账户号",
    entrustInterestName: "委托方收息客户名称",
    payPlanId: "放款计划",
    payPlanIdInput: "请选择放款计划",
    payPlanCode: "放款计划编号",
    payPlanName: "放款计划名称",
    payPlanDate: "计划日期",
    autoRepayMode: "还本息方式",
    chargeType: "手续费收取方式",
    chargeBasics: "计费基础",
    commissionRate: "手续费费率",
    chargeAmount: "手续费金额",
    chargeCycle: "手续费收取周期",
    firstChargeDate: "首次收费日",
    chargeAccountId: "手续费账户号",
    chargeAccountIdInput: "请选择手续费账户",
    chargeAccountName: "手续费账号名称",
    payPlanTitle: "执行计划",
    save: "保存",
    submit: "提交",
    revoke: "撤销",
    delete: "删除",
    close: "关闭",
    viewPayPlay: "查看执行计划",
    deleteConfirm: "确认删除？",
    deleteSuccess: "删除成功！",
    submitConfirm: "确认提交？",
    submitSuccess: "提交成功！",
    revokeConfirm: "确认撤销？",
    revokeSuccess: "撤销成功！",
    operate: "操作",
    payPlayCode: "还款计划编号",
    payPlayName: "还款计划名称",
    modifyTitle: "放款通知单 - 修改",
    viewTitle: "放款通知单 - 查看",
    modify: "修改",
    revokeBtch: "批量撤销",
    approvalTitle: "跨境融通放款 - 审批",
    detailTitle: "跨境融通放款 - 查看",
    planDate: "计划日期",
    planType: "计划类型",
    planAmount: "计划金额",
    loanRepayPlanType: "类型",
    fileInfo: "附件详情",
    file: "附件",
    emptyRate: "利率不能为空",
    emptyLoanStartDate: "贷款开始日期不能为空",
    loanStartDateMin: "贷款开始日期不能早于合同开始日期",
    loanStartDateMax: "贷款开始日期不能晚于合同结束日期",
    emptyLoanEndDate: "贷款结束日期不能为空",
    loanEndDateMin: "贷款结束日期不能早于合同开始日期",
    loanEndDateMax: "贷款结束日期不能晚于合同结束日期",
    emptyPayDate: "放款日期不能为空",
    payDateMin: "放款日期不能早于合同开始日期",
    payDateMax: "放款日期不能晚于合同结束日期",
    emptyFirstInterestSettDate: "首期结息日不能为空",
    firstInterestSettDateMin: "首期结息日不能早于贷款开始日期",
    firstInterestSettDateMax: "首期结息日不能晚于贷款结束日期",
    month: "月",
    day: "天",
    calTrialResult: "测算",
    basicInfo: "基础信息",
    loanInfo: "合同基本信息",
    payNotice: "放款单信息",
    payWay: "放款方式",
    wfHistoryTitle: "审批历史",
    marginRatio: "保证金比例",
    marginAmount: "保证金金额",
    gracePeriod: "宽限期",
    needLoanAcct: "请先开立贷款账户",
    clientName: "客户名称",
    fileClassification: "文件分类名称",
    fileName: "文件名",
    fileSize: "文件大小",
    uploadTime: "上传时间",
    download: "下载",
    uploader: "上传人",
    serialNumber: "序号",
    operation: "操作",
    actualPayAmount: "实际发放金额",
    prepaidInterest: "实付利息",
    usdExchangeRate: "折美元汇率",
    usdAmount: "折美元金额",
    internalMainAccount: "国内主账户",
    bankAccountCode: "银行账号",
    openBankName: "开户行名称",
    consignPurchaseExchangeFlag: "委托人是否购汇",
    partPurchaseExchangeFlag: "是否部分购汇",
    purchaseExchangeAmount: "购汇金额",
    consignCnyAcctId: "委托人人民币账号",
    accountType: "账户类型",
    consignForeignAcctId: "委托人外币账户号",
    borrowCurrentAccount: "借款人活期账户",
    borrowerSettExchangeFlag: "借款人是否结汇",
    partSettExchangeFlag: "是否部分结汇",
    settExchangeAmount: "结汇金额",
    borrowerCnyAcctId: "借款人人民币账户",
    toCnyRate: "折人民币汇率",
    cashPoolChannelId: "资金池通道",
    settlementInfo: "结算信息",
    transactionCode: "交易编码",
    transactionCodeMagnifier: "交易编码放大镜",
    transactionName: "交易编号名称",
    purposeCode: "用途代码",
    purposeCodeMagnifier: "用途代码放大镜",
    purposeName: "用途代码名称",
    settlementAmount: "金额(合同币种)",
    principalAmount: "本金金额(合同币种)",
    normalInterest: "正常利息金额（合同币种）",
    overdueInterest: "逾期罚息金额（合同币种）",
    debitInterest: "欠息金额（合同币种）",
    interestStartDate: "起息日",
    executeDate: "执行日",
    transAbstract: "摘要"
  },
  noticerepay: {
    settlementInfo: "结算信息",
    internalMainAccountInput: "请选择国内主账户",
    transactionCode: "交易编码",
    transactionName: "交易名称",
    transactionCodeMagnifier: "交易编码放大镜",
    purposeCode: "用途代码",
    purposeCodeMagnifier: "用途代码放大镜",
    purposeName: "用途代码名称",
    settlementAmount: "金额(合同币种)",
    principalAmount: "本金金额(合同币种)",
    debitInterest: "欠息金额（合同币种）",
    interestStartDate: "起息日",
    executeDate: "执行日",
    transAbstract: "摘要",
    forexSettlementCus: "代客购汇",
    approvalTitle: "跨境融通还款 - 审批",
    detailTitle: "还款通知单 - 查看",
    listTitle: "还款通知单 - 链接查找",
    businessCode: "还款通知单编号",
    businessCodeInput: "请选择还款通知单编号",
    officeName: "机构",
    currencyName: "币种",
    loanType: "贷款类型",
    businessType: "业务类型",
    contractCode: "合同编号",
    loanClientCode: "借款单位编号",
    loanClientName: "借款单位名称",
    consignClientCode: "委托单位编号",
    consignClientName: "委托单位名称",
    payAmount: "放款单金额",
    payBalance: "放款单余额",
    compoundInterest: "未结复利",
    freeResidualOweInterestFlag: "免还剩余欠息",
    freeCompoundInterestFlag: "免还剩余复利",
    repayTotalInterest: "利息合计金额",
    repayTotalAmount: "还款金额合计",
    repayType: "还款类型",
    contractAmount: "合同金额",
    amount: "还款本金",
    executeRate: "执行利率",
    greenCredit: "是否绿色信贷",
    businessStatus: "单据状态",
    inputUserName: "录入人",
    inputTime: "录入日期",
    repayAmountArray: "还款金额",
    listedCompany: "是否上市",
    isMechanism: "是否显示机制",
    add: "新增",
    batchDelete: "批量删除",
    addPayNotice: "新增放款通知单",
    goBack: "链接查找",
    doReturn: "返回",
    addTitle: "还款通知单 - 新增",
    contractAmountTitle: "合同金额",
    contractTitle: "合同信息",
    payAvailableAmount: "合同余额",
    contractStartDate: "合同开始日期",
    contractEndDate: "合同结束日期",
    contractTerm: "合同期限",
    payAccountNo: "放款账户账号",
    payAccountName: "放款账户名称",
    payAmountTitle: "放款金额",
    rateRunType: "利率方式",
    floatRateRunType: "浮动利率",
    fixedRateRunType: "固定利率",
    floatingRateId: "利率类型",
    floatingType: "浮动方式",
    floatingRatio: "浮动比例",
    pointType: "基点浮动方式",
    pointFloating: "基点浮动值",
    referenceRateId: "参考利率",
    interestSettTerm: "结息周期",
    interestSettDateFirst: "首次结息日",
    loanStartDate: "贷款开始日期",
    loanEndDate: "贷款结束日期",
    loanTerm: "贷款期限",
    remark: "备注",
    contractCodeInput: "请选择合同编号",
    loanClientCodeInput: "请选择贷款单位编号",
    inGrantAmount: "已发放金额",
    unGrantAmount: "未发放金额",
    repaymentAmount: "已归还金额",
    usageOfLoan: "借款用途",
    delegatePayType: "支付方式",
    interestSettNo: "结息账户",
    interestSettNoInput: "请选择结息账户",
    interestSettClientName: "结息账户客户名称",
    payDate: "放款日期",
    autoRepayFlag: "是否自动还款",
    payAccountNoInput: "请选择放款账户",
    payAmountChinese: "放款金额大写",
    floatingRateIdInput: "请选择利率类型",
    floatingRateCode: "利率编号",
    floatingRateName: "利率名称",
    accountBank: "开户行",
    accountBankInput: "请选择开户行",

    batchSubmit: "批量提交",
    batchRevoke: "批量撤销",
    buyAddFlag: "是否补录",
    yes: "是",
    no: "否",
    settleMethod: "结息方式",
    loanAccountId: "借款单位贷款专户",
    loanAccountIdInput: "请选择借款单位贷款专户",
    loanAccountCode: "贷款专户编号",
    loanAccountName: "贷款专户名称",
    consignCurrentAccountId: "委托方活期账户",
    consignCurrentAccountIdInput: "请选择委托方活期账户",
    recInterestAccountId: "委托方收息账户",
    recInterestAccountIdInput: "请选择委托方收息账户",
    entrustInterestName: "委托方收息客户名称",
    payPlanId: "还款计划",
    payPlanIdInput: "请选择放款计划",
    autoRepayMode: "还本息方式",
    chargeType: "手续费收取方式",
    chargeBasics: "手续费计费基础",
    commissionRate: "手续费率",
    chargeAmount: "应付手续费",
    realChargeAmount: "实付手续费",
    chargeCycle: "手续费收费周期",
    firstChargeDate: "首次收费日期",
    chargeAccountId: "手续费账户号",
    chargeAccountIdInput: "请选择手续费账户",
    chargeAccountName: "手续费账号名称",
    payPlanTitle: "执行计划",
    save: "保存",
    submit: "提交",
    revoke: "撤销",
    delete: "删除",
    close: "关闭",
    viewPayPlay: "查看执行计划",
    deleteConfirm: "确认删除？",
    deleteSuccess: "删除成功！",
    submitConfirm: "确认提交？",
    submitSuccess: "提交成功！",
    revokeConfirm: "确认撤销？",
    revokeSuccess: "撤销成功！",
    notePayCode: "放款通知单编号",
    aheadRepayFlag: "是否提前还款",
    operate: "操作",
    notePayCodeInput: "请选择放款通知单编号",
    repayInterestFlag: "是否归还利息",
    balance: "放款通知单余额",
    repayAmountChinese: "还款金额大写",
    repayDate: "还款日期",
    currentAccountId: "活期存款账户",
    repayPrincipalAcctNo: "借款人还款账户号",
    repayPrincipalAcctName: "借款人还款账户名称",
    normalInterest: "正常利息",
    overdueInterest: "逾期罚息",
    oweInterest: "未结欠息",
    totalInterest: "利息金额合计",
    interestCalculateType: "利息计算方式",
    interestCalculate: "计算利息",
    realNormalInterest: "利息金额",
    realOverdueInterest: "逾期罚息金额",
    realOweInterest: "欠息金额",
    realTotalInterest: "利息金额合计",
    freeNormalInterestFlag: "免还剩余利息",
    freeOverdueInterestFlag: "免还剩余罚息",
    ALL_INTEREST: "全部利息",
    WITH_PRINCIPAL: "利随本清",
    currentAccountIdInput: "请选择活期存款账户",
    guaranteeType: "担保类型",
    assure: "保证",
    credit: "信用",
    pledge: "抵押",
    impawn: "质押",
    comfort: "安慰函及其他",
    margin: "保证金",
    revokeBtch: "批量撤销",
    modifyTitle: "还款通知单 - 修改",
    viewTitle: "还款通知单 - 查看",
    calculateInterest: "匡算利息",
    shouldBePaid: "应当支付",
    actualPayment: "实际支付",
    freeFeeFlag: "免还手续费",
    repayPlanId: "还款计划",
    repayPlanIdInput: "请选择还款计划",
    planCode: "计划编号",
    planDate: "计划日期",
    relatedRepayPlans: "关联还款计划",
    guaranteeTypeContract: "合同占用额度",
    guaranteeTypeUnRepay: "未归还额度",
    guaranteeTypeRepay: "本次归还额度",
    repayAmountShow: "还款金额不能大于放款单余额",
    repayAmountTotal: "本次归还额度之和必须等于还款金额",
    repayAssureTotal: "本次归还额度(保证)不能大于未归还额度(保证)！",
    repayCreditTotal: "本次归还额度(信用)不能大于未归还额度(信用)！",
    repayPledgeTotal: "本次归还额度(抵押)不能大于未归还额度(抵押)！",
    repayImpawnTotal: "本次归还额度(质押)不能大于未归还额度(质押)！",
    repayComfortTotal: "本次归还额度(安慰函及其他)不能大于未归还额度(安慰函及其他)！",
    repayMarginTotal: "本次归还额度(保证金)不能大于未归还额度(保证金)！",
    realNormalInterestMax: "实际支付利息金额应小于等于应当支付利息金额！",
    realNormalInterestTotal: "实际支付利息金额应等于应当支付利息金额！",
    realOverdueInterestMin: "实际支付罚息金额应小于应当支付罚息金额！",
    realOverdueInterestTotal: "实际支付罚息金额应等于应当支付罚息金额！",
    realOweInterestMin: "实际支付欠息金额应小于等于应当支付欠息金额！",
    realOweInterestTotal: "实际支付利息不等于应当支付利息时必须勾选免还剩余利息！",
    realOweInterestCond: "实际支付罚息金额不等于应当支付罚息金额时必须勾选免还剩余罚息！",
    fileInfo: "附件详情",
    file: "附件",
    planType: "计划类型",
    planAmount: "计划金额",
    loanRepayPlanType: "类型",
    reayAmountMin: "还款金额必须大于0！",
    repayDateMin: "还款日期不能小于放款日期！",
    month: "月",
    repayNotice: "还款通知单",
    repayNoticeTitle: "还款信息",
    interestNotice: "利息信息",
    guarantee: "担保品信息",
    wfHistoryTitle: "审批历史",
    accountCode: "账户编号",
    accountName: "账户名称",
    recBankAcctId: "开户银行账户编号",
    recBankAcctIdInput: "请选择开户银行账户编号",
    drawOpenAccNo: "开户银行账户编号",
    drawOpenAccName: "开户银行账户名称",
    groupIndex: "分组",
    noRepayAmount: "未转款金额",
    groupDetail: "入账信息",
    financialCompanyBankAccountNo: "财务公司银行账户号",
    loanBankRole: "财务公司贷款行角色",
    relationalRecordedAccountNumber: "账户编号",
    relationalRecordedAccountName: "账户名称",
    relationalRecordedCounterpartyAccountNumber: "对方账号",
    relationalRecordedCounterpartyAccountName: "对方账户名称",
    relationalRecordedCounterpartyBank: "对方开户行银行",
    relationalRecordedTransactionAmount: "交易额",
    relationalRecordedTransactionDate: "交易日",
    relationalRecordedExecutionDate: "执行日期",

    crossDirection: "融通方向",
    cashPoolChannelId: "资金池通道",
    loanBusinessType: "贷款业务类型",
    usdExchangeRate: "折美元汇率",
    usdAmount: "折美元金额",
    loanProductName: "贷款产品名称",
    amountChinese: "金额大写",
    internalMainAccount: "国内主账户",
    bankAccountCode: "银行账号",
    openBankName: "开户行名称",
    consignSettExchangeFlag: "委托人是否结汇",
    partSettExchangeFlag: "是否部分结汇",
    settExchangeAmount: "结汇金额",
    consignForeignAcctId: "委托人外币账户号",
    accountType: "账户类型",
    consignCnyAcctId: "委托人人民币账户号",
    borrowPurchaseExchangeFlag: "借款人是否购汇",
    partPurchaseExchangeFlag: "是否部分购汇",
    purchaseExchangeAmount: "购汇金额",
    borrowCnyAcctId: "借款人人民币账户号",
    borrowForeignAcctId: "借款人外币账户号",
    consignCurrentAccountNo: "委托人活期账户号",
    baseMessage: "基础信息",
    repayAmount: "还款金额",
    loanTermMonth: "贷款期限",
    noFeeAmount: "未结手续费",
    freeFeeAmountFlag: "免还剩余手续费"
  },
  settleinterest: {
    approvalTitle: "跨境融通结息-审批",
    addTitle: "跨境融通结息-新增",
    detailTitle: "跨境融通结息-查看",
    accountNo: "账户号",
    accountName: "账户名称",
    contractCode: "合同编号",
    payFormCode: "放款通知单号",
    officeName: "机构",
    currencyName: "币种",
    startDate: "开始日期",
    endDate: "结束日期",
    days: "天数",
    interestBalance: "通知单余额",
    rate: "年利率%",
    receivableInterest: "本次利息",
    oweInterest: "历史欠息",
    compoundInterest: "复利",
    totalInterest: "利息总额",
    availableSettleInterest: "本次可结利息",
    fileIds: "附件",
    submitValidator1: "本次可结利息不能小于等于零",
    segmentInterestTile: "利息分段明细",
    calInterestStartDate: "开始日期",
    calInterestEndDate: "结束日期",
    yearRate: "年利率%",
    interest: "利息",
    interestClass: "利息类型"
  }
};
