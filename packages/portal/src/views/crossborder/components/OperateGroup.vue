<template>
  <OperateButton :options="operateButtonOption(row)" @on-modify="handleModify(row)">
    <template #prefix v-if="row.businessStatus === 'APPROVED'">
      <f-submit-state
        :gather-params="() => row"
        :url="revokeUrl"
        operate="cancel"
        :is-batch="false"
        :operate-name="t('common.operate.cancel')"
        confirm-text=" "
        :icon="DtgCopy"
        link
        :is-show-result-btn-group="false"
        :result-confirm="t('common.operate.cancelSuccess')"
        :beforeConfirm="handleUndoConfirm"
        @close="handleOperateSuccess"
      >
        <template #confirmEdit>
          <f-multi-form-panel ref="undoFormRef" :model="cancelModel">
            <f-form-item :label="t('common.operate.cancelReason')" prop="revokeReason" required>
              <f-input v-model="cancelModel.revokeReason" size="large" maxlength="50" />
            </f-form-item>
          </f-multi-form-panel>
        </template>
      </f-submit-state>
    </template>
  </OperateButton>
</template>
<script setup lang="ts">
import { reactive, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { DtgCopy } from "@dtg/frontend-plus-icons";

const { t } = useI18n();

const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  revokeUrl: {
    type: String,
    required: true
  },
  deleteUrl: {
    type: String,
    required: true
  },
  submitUrl: {
    type: String,
    required: true
  }
});

const emits = defineEmits(["on-modify", "operate-success"]);
// 撤销表单实例
const undoFormRef = shallowRef();
// 撤销确认
const handleUndoConfirm = async () => {
  try {
    await undoFormRef.value.form.validate();
    return true;
  } catch (error) {
    console.warn(error);
    return false;
  }
};
const operateButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: ["SAVE", "REFUSE"].includes(row.businessStatus)
    },
    {
      type: "submit",
      isShow: ["SAVE", "REFUSE"].includes(row.businessStatus),
      submitComOpt: {
        url: props.submitUrl,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    },
    {
      type: "remove",
      isShow: ["SAVE", "REFUSE"].includes(row.businessStatus),
      submitComOpt: {
        url: props.deleteUrl,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    }
  ];
};

const cancelModel = reactive({
  revokeReason: ""
});

const handleModify = (row: Record<string, any>) => {
  emits("on-modify", row);
};

const handleOperateSuccess = res => {
  emits("operate-success", res.data);
};
</script>
