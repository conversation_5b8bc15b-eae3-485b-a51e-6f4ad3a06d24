<template>
  <f-multi-form-panel ref="formRef" :model="dto" :rules="necessaryRules" :column="3">
    <!-- 基础信息 -->
    <f-panel :title="t(`${CODE}.basicInfo`)">
      <!-- 放款申请单编号 -->
      <f-form-item v-if="!!dto.businessCode" prop="businessCode" :label="t(`${CODE}.businessCode`)">
        <f-input v-model="dto.businessCode" disabled />
      </f-form-item>
      <!-- 机构 -->
      <f-form-item prop="officeId" :label="t(`${CODE}.officeName`)">
        <f-input v-if="baseInfoReadonly" v-model="dto.officeName" disabled />
        <f-select
          v-else
          v-model="dto.officeId"
          :url="listOfficeUrl"
          value-key="officeId"
          label="officeName"
          @change="handler.handleOfficeChange"
        />
      </f-form-item>
      <!-- 币种 -->
      <f-form-item prop="currencyId" :label="t(`${CODE}.currencyName`)">
        <f-input v-if="baseInfoReadonly" v-model="dto.currencyName" disabled />
        <f-select
          v-else
          v-model="dto.currencyId"
          ref="currencyRef"
          :url="listCurrencyUrl"
          :extra-data="currencyQueryParam"
          value-key="currencyId"
          label="currencyName"
          @change="handler.handleCurrencyChange"
        />
      </f-form-item>
      <!-- 融通方向 -->
      <f-form-item prop="crossDirection" :label="t(`${CODE}.crossDirection`)" required>
        <f-select
          v-model="dto.crossDirection"
          :data="loanCrossBorderDirectionEnum"
          :disabled="baseInfoReadonly"
          @change="handler.handleCrossDirectionChange"
        />
      </f-form-item>
      <!-- 资金池通道 -->
      <f-form-item prop="cashPoolChannelId" :label="t(`${CODE}.loanPoolChannel`)" required>
        <f-input v-if="baseInfoReadonly" v-model="dto.cashPoolChannelName" disabled />
        <f-select
          v-else
          v-model="dto.cashPoolChannelId"
          ref="cashPoolRef"
          :url="cashPoolChannelUrl"
          value-key="capitalPoolChannelId"
          label="capitalPoolChannel"
          :extra-data="{
            officeId: dto.officeId,
            currencyId: dto.currencyId
          }"
          @change="handler.handleCashPoolChannelChange"
        />
      </f-form-item>
      <!-- 借款单位名称 -->
      <f-form-item prop="loanClientId" :label="t(`${CODE}.loanClientName`)">
        <f-input v-if="baseInfoReadonly" v-model="dto.loanClientName" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.loanClientId"
          :url="listLoanClientCodeUrl"
          :title="t(`${CODE}.loanClientCode`)"
          :placeholder="t(`${CODE}.loanClientCodeInput`)"
          row-key="clientId"
          row-label="clientName"
          input-key="clientCode"
          :params="loanClientQueryParam"
          :selected-data="{
            clientName: dto.loanClientName
          }"
          @change="handler.handleLoanClientChange"
        >
          <f-magnifier-column prop="clientCode" :label="t(`${CODE}.loanClientCode`)" />
          <f-magnifier-column prop="clientName" :label="t(`${CODE}.loanClientName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 合同编号 -->
      <f-form-item prop="contractId" :label="t(`${CODE}.contractCode`)" :required="true">
        <f-input v-if="baseInfoReadonly" v-model="dto.contractCode" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.contractId"
          :url="listContractCodeUrl"
          :disabled="baseInfoReadonly"
          :title="t(`${CODE}.contractCode`)"
          :placeholder="t(`${CODE}.contractCodeInput`)"
          :params="contractQueryParam"
          row-key="contractId"
          row-label="contractCode"
          input-key="contractCode"
          @change="handler.handleContractChange"
        >
          <!-- 合同编号、融通方向、借款单位名称、委托单位名称、合同金额、贷款产品名称、合同结束日 -->
          <!-- 合同编号 -->
          <f-magnifier-column prop="contractCode" :label="t(`${CODE}.contractCode`)" width="160px" />
          <!-- 融通方向 -->
          <f-magnifier-column
            prop="crossDirection"
            :label="t(`${CODE}.crossDirection`)"
            :filter-select="loanCrossBorderDirectionEnum"
            :formatter="{ name: 'const', const: 'common.LoanCrossBorderDirection' }"
            width="120px"
          />
          <!-- 借款单位名称 -->
          <f-magnifier-column prop="loanClientName" :label="t(`${CODE}.loanClientName`)" width="150px" />
          <!-- 委托单位名称 -->
          <f-magnifier-column prop="consignClientName" :label="t(`${CODE}.consignClientName`)" width="150px" />
          <!-- 合同金额 -->
          <f-magnifier-column
            prop="contractAmount"
            :label="t(`${CODE}.contractAmount`)"
            formatter="amount"
            width="150px"
          />
          <!-- 贷款产品名称 -->
          <f-magnifier-column prop="loanProductName" :label="t(`${CODE}.loanProductName`)" width="150px" />
          <!-- 合同结束日 -->
          <f-magnifier-column prop="contractEndDate" :label="t(`${CODE}.loanEndDate`)" width="150px" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 贷款业务类型 -->
      <f-form-item prop="loanBusinessType" :label="t(`${CODE}.loanBizType`)">
        <f-scene-view :search="dto.loanBusinessType" :data="loanBusinessCategory" params="value" />
      </f-form-item>
      <!-- 贷款产品 -->
      <f-form-item prop="productName" :label="t(`${CODE}.loanProductName`)">
        <f-input v-model="dto.productName" disabled />
      </f-form-item>
      <!-- 委托单位名称 -->
      <f-form-item prop="consignClientName" :label="t(`${CODE}.consignClientName`)">
        <f-input v-model="dto.consignClientName" disabled />
      </f-form-item>
    </f-panel>

    <!-- 合同基本信息 -->
    <f-panel :title="t(`${CODE}.loanInfo`)">
      <!-- 合同金额 -->
      <f-form-item prop="contractAmount" :label="t(`${CODE}.contractAmount`)">
        <f-amount v-model="dto.contractAmount" :symbol="currencySymbol" disabled />
      </f-form-item>
      <!-- 已发放金额 -->
      <f-form-item :label="t(`${CODE}.inGrantAmount`)">
        <f-amount v-model="dto.payedAmount" :symbol="currencySymbol" disabled />
      </f-form-item>
      <!-- 已还款金额 -->
      <f-form-item :label="t(`${CODE}.repaymentAmount`)">
        <f-amount v-model="dto.repayAmount" :symbol="currencySymbol" disabled />
      </f-form-item>
      <!-- 可发放金额 -->
      <f-form-item prop="payAvailableAmount" :label="t(`${CODE}.payAvailableAmount`)">
        <f-amount v-model="dto.contractAvailableAmount" :symbol="currencySymbol" disabled />
      </f-form-item>
      <!-- 合同开始日期 -->
      <f-form-item prop="contractStartDate" :label="t(`${CODE}.contractStartDate`)">
        <f-input v-model="dto.contractStartDate" disabled />
      </f-form-item>
      <!-- 合同期限 -->
      <f-form-item prop="contractTerm" :label="t(`${CODE}.contractTerm`)">
        <f-number v-model="dto.contractTerm" disabled>
          <template #suffix>
            <span>{{ t(`${CODE}.month`) }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 合同结束日期 -->
      <f-form-item prop="contractEndDate" :label="t(`${CODE}.contractEndDate`)">
        <f-input v-model="dto.contractEndDate" disabled />
      </f-form-item>
      <!-- 宽限期 -->
      <f-form-item prop="gracePeriod" :label="t(`${CODE}.gracePeriod`)">
        <f-number v-model="dto.gracePeriod" whole-number :min="0" :negative="false" disabled>
          <template #suffix>
            <span>年</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 合同执行利率 -->
      <f-form-item prop="contractExecuteRate" :label="t(`${CODE}.contractExecuteRate`)">
        <f-number v-model="dto.contractExecuteRate" :precision="4" is-rate disabled />
      </f-form-item>
      <template v-if="showUsdAmount">
        <!-- 折美元汇率 -->
        <f-form-item prop="toUsdRate" :label="t(`${CODE}.usdExchangeRate`)">
          <f-number v-model="dto.toUsdRate" :precision="4" is-rate disabled />
        </f-form-item>
        <!-- 折美元金额 -->
        <f-form-item prop="toUsdAmount" :label="t(`${CODE}.usdAmount`)">
          <f-amount v-model="dto.toUsdAmount" symbol="$" disabled />
        </f-form-item>
      </template>
      <!-- 借款用途 -->
      <f-form-item prop="loanPurpose" :label="t(`${CODE}.usageOfLoan`)" :employ="2">
        <f-input v-model="dto.loanPurpose" disabled />
      </f-form-item>
    </f-panel>

    <!-- 放款单信息 -->
    <f-panel :title="t(`${CODE}.payNotice`)">
      <!-- 放款日期 -->
      <f-form-item prop="payDate" :label="t(`${CODE}.payDate`)" :required="necessary">
        <f-date-picker
          v-model="dto.payDate"
          type="date"
          :disabled-date="disabledPayDate"
          :disabled="props.readonly"
          @change="handlePayDateChange"
        />
      </f-form-item>
      <!-- 国内主账户 -->
      <f-form-item prop="internalMainAccountId" :label="t(`${CODE}.internalMainAccount`)" required>
        <f-input v-if="baseInfoReadonly" v-model="dto.internalMainAccountCode" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.internalMainAccountId"
          :url="queryBankAccount"
          :title="t(`${CODE}.contractCode`)"
          :placeholder="t(`${CODE}.internalMainAccountInput`)"
          row-key="id"
          row-label="bankAccountNo"
          :selected-data="{
            bankAccountNo: dto.internalMainAccountCode
          }"
          @change="handler.handleInternalMainAccountChange"
        >
          <f-magnifier-column prop="bankAccountNo" :label="t(`${CODE}.bankAccountCode`)" />
          <f-magnifier-column prop="bankName" :label="t(`${CODE}.openBankName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 开户行名称 -->
      <f-form-item prop="internalMainAccountName" :label="t(`${CODE}.openBankName`)">
        <f-input v-model="dto.internalMainAccountName" disabled />
      </f-form-item>
      <!-- 放款金额 -->
      <f-form-item prop="payAmount" :label="t(`${CODE}.payAmount`)" :required="necessary">
        <f-amount
          v-model="dto.payAmount"
          :symbol="currencySymbol"
          :max="dto.contractAvailableAmount || 10000"
          :disabled="props.readonly"
          @change="handlePayAmountChange"
        />
      </f-form-item>
      <!-- 金额大写 -->
      <f-form-item :label="t(`${CODE}.payAmountChinese`)">
        <f-amount-chinese v-model="dto.payAmount" :symbol="currencySymbol" />
      </f-form-item>
      <!-- 折美元汇率 -->
      <f-form-item
        v-if="dto.cashPoolChannelManagement === 'SAFE' && dto.currency !== 2"
        prop="toUsdRate"
        :label="t(`${CODE}.usdExchangeRate`)"
        required
      >
        <f-number
          v-model="dto.payNoticeToUsdRate"
          :precision="4"
          max="99.9999"
          min="0.0000"
          is-rate
          :disabled="props.readonly"
        />
      </f-form-item>
      <!-- 折美元金额 -->
      <f-form-item prop="payNoticeToUsdAmount" :label="t(`${CODE}.usdAmount`)">
        <f-amount v-model="dto.payNoticeToUsdAmount" symbol="$" disabled />
      </f-form-item>
      <!-- 委托人是否购汇 -->
      <f-form-item
        v-if="dto.crossDirection === 'EXTERNAL_LENDING' && dto.currencyId > 1"
        prop="consignPurchaseExchangeFlag"
        :label="t(`${CODE}.consignPurchaseExchangeFlag`)"
        required
      >
        <f-switch
          v-model="dto.consignPurchaseExchangeFlag"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          :disabled="props.readonly"
        />
      </f-form-item>
      <!-- 是否部分购汇 -->
      <f-form-item
        v-if="
          dto.crossDirection === 'EXTERNAL_LENDING' &&
          dto.currencyId > 1 &&
          dto.consignPurchaseExchangeFlag === yesOrNo.YES
        "
        :required="dto.consignPurchaseExchangeFlag === yesOrNo.YES"
        prop="partPurchaseExchangeFlag"
        :label="t(`${CODE}.partPurchaseExchangeFlag`)"
      >
        <f-switch
          v-model="dto.partPurchaseExchangeFlag"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          :disabled="props.readonly"
        />
      </f-form-item>
      <!-- 购汇金额 -->
      <f-form-item
        v-if="
          dto.crossDirection === 'EXTERNAL_LENDING' &&
          dto.currencyId > 1 &&
          dto.consignPurchaseExchangeFlag === yesOrNo.YES &&
          dto.partPurchaseExchangeFlag === yesOrNo.YES
        "
        :required="dto.partPurchaseExchangeFlag === yesOrNo.YES"
        prop="purchaseExchangeAmount"
        :label="t(`${CODE}.purchaseExchangeAmount`)"
      >
        <f-amount v-model="dto.purchaseExchangeAmount" :symbol="currencySymbol" :disabled="props.readonly" />
      </f-form-item>
      <!-- 委托人人民币账户号 -->
      <f-form-item
        v-if="
          dto.crossDirection === 'EXTERNAL_LENDING' &&
          dto.currencyId > 1 &&
          dto.consignPurchaseExchangeFlag === yesOrNo.YES &&
          dto.partPurchaseExchangeFlag === yesOrNo.YES
        "
        :required="dto.partPurchaseExchangeFlag === yesOrNo.YES"
        prop="consignCnyAcctId"
        :label="t(`${CODE}.consignCnyAcctId`)"
      >
        <f-input v-if="props.readonly" v-model="dto.consignCnyAcctCode" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.consignCnyAcctId"
          :title="t(`${CODE}.consignCnyAcctId`)"
          :url="queryInnerAccount"
          row-key="accountId"
          row-label="accountCode"
          :params="consignCnyAcctQueryParams"
          :selected-data="{
            accountCode: dto.consignCnyAcctCode
          }"
          @change="handler.handleConsignCnyAcctChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.accountCode`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.accountName`)" />
          <f-magnifier-column prop="accountTypeName" :label="t(`${CODE}.accountType`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托人外币账户号 -->
      <f-form-item
        v-if="
          dto.crossDirection === 'EXTERNAL_LENDING' &&
          dto.currencyId > 1 &&
          dto.consignPurchaseExchangeFlag === yesOrNo.YES &&
          dto.partPurchaseExchangeFlag === yesOrNo.YES
        "
        prop="consignForeignAcctId"
        :label="t(`${CODE}.consignForeignAcctId`)"
        required
      >
        <f-input v-if="props.readonly" v-model="dto.consignForeignAcctCode" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.consignForeignAcctId"
          :title="t(`${CODE}.consignForeignAcctId`)"
          :url="queryInnerAccount"
          method="post"
          row-key="accountId"
          row-label="accountCode"
          :params="consignAccountQueryParams"
          :selected-data="{
            accountCode: dto.consignForeignAcctCode
          }"
          @change="handler.handleConsignForeignAcctChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.accountCode`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.accountName`)" />
          <f-magnifier-column prop="accountTypeName" :label="t(`${CODE}.accountType`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托活期账户 -->
      <f-form-item
        v-if="showConsignCurrentAcctNo"
        prop="consignCurrentAcctNo"
        :label="t(`${CODE}.consignCurrentAccountNo`)"
        :required="necessary"
      >
        <f-input v-if="props.readonly" v-model="dto.consignCurrentAcctNo" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.consignCurrentAcctNo"
          :url="listConsignCurrentAccountUrl"
          :title="t(`${CODE}.consignCurrentAccountId`)"
          :placeholder="t(`${CODE}.consignCurrentAccountIdInput`)"
          :params="consignAccountQueryParams"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :selected-data="{
            accountCode: dto.consignCurrentAcctNo
          }"
          @change="handler.handleConsignCurrentAccountNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.accountCode`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.accountName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托收息账户 -->
      <f-form-item
        prop="consignRecInterestAcctNo"
        :label="t(`${CODE}.consignRecInterestAccountNo`)"
        :required="necessary"
      >
        <f-input v-if="props.readonly" v-model="dto.consignRecInterestAcctNo" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.consignRecInterestAcctNo"
          :url="listRecInterestAccountUrl"
          :title="t(`${CODE}.recInterestAccountId`)"
          :placeholder="t(`${CODE}.recInterestAccountIdInput`)"
          :params="consignAccountQueryParams"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :selected-data="{
            accountCode: dto.consignRecInterestAcctNo
          }"
          @change="handler.handlecConsignRecInterestAccountNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.accountCode`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.accountName`)" />
          <f-magnifier-column prop="clientName" :label="t(`${CODE}.clientName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人付息账户号 -->
      <f-form-item prop="payInterestAcctNo" :label="t(`${CODE}.payInterestAcctNo`)" :required="necessary">
        <f-input v-if="props.readonly" v-model="dto.payInterestAcctNo" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.payInterestAcctNo"
          :url="listInterestSettNoUrl"
          :title="t(`${CODE}.interestSettNo`)"
          :placeholder="t(`${CODE}.interestSettNoInput`)"
          :params="loanAccountQueryParams"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :selected-data="{
            accountCode: dto.payInterestAcctNo
          }"
          @change="handler.handlePayInterestAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.interestSettNo`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.interestSettClientName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人活期账户号/借款人贷款专户 -->
      <f-form-item prop="recPrincipalAcctNo" :label="t(`${CODE}.borrowCurrentAccount`)" :required="necessary">
        <f-input
          v-if="dto.crossDirection !== 'EXTERNAL_LENDING' || props.readonly"
          v-model="dto.recPrincipalAcctNo"
          disabled
        />
        <f-magnifier-single
          v-else
          v-model="dto.recPrincipalAcctNo"
          :url="listPayAccountNoUrl"
          :title="t(`${CODE}.borrowCurrentAccount`)"
          :params="loanAccountQueryParams"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :selected-data="{
            accountCode: dto.recPrincipalAcctNo
          }"
          @change="handler.handleRecPrincipalAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.payAccountNo`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.payAccountName`)" />
          <f-magnifier-column prop="accountTypeName" :label="t(`${CODE}.accountTypeName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人是否结汇 -->
      <f-form-item
        v-if="dto.crossDirection === 'INTRODUCE_DEBT' && dto.currencyId !== 1"
        :required="dto.crossDirection === 'INTRODUCE_DEBT' && dto.currencyId !== 1"
        prop="borrowerSettExchangeFlag"
        :label="t(`${CODE}.borrowerSettExchangeFlag`)"
      >
        <f-switch
          v-model="dto.borrowerSettExchangeFlag"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          :disabled="props.readonly"
        />
      </f-form-item>
      <!-- 是否部分结汇 -->
      <f-form-item
        v-if="
          dto.crossDirection === 'INTRODUCE_DEBT' &&
          dto.currencyId !== 1 &&
          dto.borrowerSettExchangeFlag === yesOrNo.YES
        "
        prop="partSettExchangeFlag"
        :label="t(`${CODE}.partSettExchangeFlag`)"
      >
        <f-switch
          v-model="dto.partSettExchangeFlag"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          :disabled="props.readonly"
        />
      </f-form-item>
      <!-- 结汇金额 -->
      <f-form-item
        v-if="
          dto.crossDirection === 'INTRODUCE_DEBT' &&
          dto.currencyId !== 1 &&
          dto.borrowerSettExchangeFlag === yesOrNo.YES &&
          dto.partSettExchangeFlag === yesOrNo.YES
        "
        :required="dto.partSettExchangeFlag === yesOrNo.YES"
        prop="settExchangeAmount"
        :label="t(`${CODE}.settExchangeAmount`)"
      >
        <f-amount v-model="dto.settExchangeAmount" :symbol="currencySymbol" :disabled="props.readonly" />
      </f-form-item>
      <!-- 借款人人民币账户号 -->
      <f-form-item
        v-if="dto.crossDirection === 'INTRODUCE_DEBT' && dto.borrowerSettExchangeFlag === yesOrNo.YES"
        :required="dto.borrowerSettExchangeFlag === yesOrNo.YES"
        prop="borrowerCnyAcctId"
        :label="t(`${CODE}.borrowerCnyAcctId`)"
      >
        <f-input v-if="props.readonly" v-model="dto.borrowerCnyAcctCode" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.borrowerCnyAcctId"
          :url="listBorrowerCnyAcctUrl"
          :title="t(`${CODE}.borrowerCnyAcctId`)"
          :params="borrowerCnyAcctQueryParams"
          :placeholder="t(`${CODE}.borrowerCnyAcctInput`)"
          row-key="accountId"
          row-label="accountCode"
          input-key="accountCode"
          :selected-data="{
            accountCode: dto.borrowerCnyAcctCode
          }"
          @change="handler.handleBorrowerCnyAcctChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.payAccountNo`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.payAccountName`)" />
          <f-magnifier-column prop="accountTypeName" :label="t(`${CODE}.accountTypeName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 贷款开始日期 -->
      <f-form-item :label="t(`${CODE}.loanStartDate`)" :required="necessary" disabled>
        <f-date-picker v-model="dto.loanStartDate" type="date" disabled />
      </f-form-item>
      <!-- 贷款期限 -->
      <f-form-item prop="loanTerm" :label="t(`${CODE}.loanTerm`)" :required="necessary" disabled>
        <f-number v-model="dto.loanTerm" disabled>
          <template #suffix>
            <span>{{ t(`${CODE}.month`) }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 贷款结束日期 -->
      <f-form-item :label="t(`${CODE}.loanEndDate`)" disabled>
        <f-date-picker v-model="dto.loanEndDate" type="date" disabled />
      </f-form-item>
      <!-- 贷款利率类型 -->
      <f-form-item prop="rateRunType" :label="t(`${CODE}.loanRateType`)">
        <f-scene-view :search="dto.rateRunType" :data="loanContractRateRunType" params="value" />
      </f-form-item>
      <!-- 固定利率 -->
      <f-form-item prop="fixedRate" :label="t(`${CODE}.fixedRate`)">
        <f-number v-model="dto.fixedRate" :precision="4" min="0.0000" is-rate disabled />
      </f-form-item>
      <!-- 浮动利率 -->
      <f-form-item prop="floatingRate" :label="t(`${CODE}.floatingRate`)">
        <f-number v-model="dto.floatingRate" :precision="6" max="99.999999" min="0.0000" is-rate disabled />
      </f-form-item>
      <!-- 浮动方式 -->
      <f-form-item prop="floatingWay" :label="t(`${CODE}.floatingWay`)">
        <f-scene-view :search="dto.floatingWay" params="value" :data="loanRateFloatType" />
      </f-form-item>
      <!-- 浮动比例 -->
      <f-form-item prop="floatingRatio" :label="t(`${CODE}.floatingType`)">
        <f-number v-model="dto.floatingRatio" :precision="4" is-rate disabled />
      </f-form-item>
      <!-- 基点BP -->
      <f-form-item prop="basicPoint" :label="t(`${CODE}.basePoint`)">
        <f-number v-model="dto.basicPoint" :whole-number="true" :min="0" disabled />
      </f-form-item>
      <!-- 执行利率 -->
      <f-form-item prop="executeRate" :label="t(`${CODE}.executeRate`)">
        <f-number v-model="dto.executeRate" :precision="6" max="99.999999" min="0.000000" is-rate disabled />
      </f-form-item>
      <!-- 利率调整方式 -->
      <f-form-item prop="rateAdjustWay" :label="t(`${CODE}.rateAdjustWay`)">
        <f-scene-view :search="dto.rateAdjustWay" :data="loanRateAdjustWayEnum" params="value" />
      </f-form-item>
      <!-- 利率调整周期 -->
      <f-form-item prop="rateAdjustFreq" :label="t(`${CODE}.rateAdjustFreq`)">
        <f-scene-view :search="dto.rateAdjustFreq" :data="loanRateAdjustFreqEnum" params="value" />
      </f-form-item>
      <!-- 是否周期第一天调整 -->
      <f-form-item prop="firstDayAdjustFlag" :label="t(`${CODE}.firstDayAdjustFlag`)">
        <f-switch v-model="dto.firstDayAdjustFlag" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" disabled />
      </f-form-item>
      <!-- 调整日期 -->
      <f-form-item :label="t(`${CODE}.fixedAdjustDate`)" disabled>
        <f-date-picker v-model="dto.fixedAdjustDate" type="date" disabled />
      </f-form-item>
      <!-- 是否按年重复 -->
      <f-form-item prop="repeatAnnuallyFlag" :label="t(`${CODE}.repeatAnnuallyFlag`)">
        <f-switch v-model="dto.repeatAnnuallyFlag" :active-value="yesOrNo.YES" :inactive-value="yesOrNo.NO" disabled />
      </f-form-item>
      <!-- 结息周期 -->
      <f-form-item prop="interestSettFreq" :label="t(`${CODE}.interestSettTerm`)">
        <f-select v-model="dto.interestSettFreq" :data="interestFreqEnum" :disabled="props.readonly" />
      </f-form-item>
      <!-- 首次结息日 -->
      <f-form-item prop="firstInterestDate" :label="t(`${CODE}.interestSettDateFirst`)" :required="necessary">
        <f-date-picker
          v-model="dto.firstInterestDate"
          type="date"
          :disabled-date="disabledFirstInterestDate"
          :disabled="props.readonly"
        />
      </f-form-item>
      <!-- 备注 -->
      <f-form-item prop="remark" :label="t(`${CODE}.remark`)" :employ="3">
        <f-textarea v-model="dto.remark" :min-rows="3" :disabled="props.readonly" />
      </f-form-item>
    </f-panel>

    <!-- 费用信息 -->
    <f-panel v-if="showFeeCharge" :title="t(`${CODE}.payFee`)">
      <!-- 手续费收取方式 -->
      <f-form-item prop="feeChargeWay" :label="t(`${CODE}.chargeType`)" :required="necessary">
        <f-select v-model="dto.feeChargeWay" :data="loanFeeChargeWayEnum" disabled />
      </f-form-item>
      <!-- 计费基础 -->
      <f-form-item v-if="showNotCharge" prop="feeChargeBasic" :label="t(`${CODE}.chargeBasics`)">
        <f-scene-view :search="dto.feeChargeBasic" :data="feeChargeBasicEnum" params="value" />
      </f-form-item>
      <!-- 手续费收取周期 -->
      <f-form-item prop="feeChargeFreq" :label="t(`${CODE}.chargeCycle`)">
        <f-select v-model="dto.feeChargeFreq" :data="countPeriodTypeEnum" :disabled="props.readonly" />
      </f-form-item>
      <!-- 付手续费账户号 -->
      <f-form-item v-if="showNotCharge" prop="payFeeAcctNo" :label="t(`${CODE}.payFeeAcct`)">
        <f-input v-if="props.readonly" v-model="dto.payFeeAcctNo" disabled />
        <f-magnifier-single
          v-else
          v-model="dto.payFeeAcctNo"
          :url="listConsignCurrentAccountUrl"
          :title="t(`${CODE}.consignCurrentAccountId`)"
          :placeholder="t(`${CODE}.payFeeAcctInput`)"
          :params="consignAccountQueryParams"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          :selected-data="{
            accountCode: dto.payFeeAcctNo
          }"
          @change="handler.handlePayFeeAcctNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t(`${CODE}.accountCode`)" />
          <f-magnifier-column prop="accountName" :label="t(`${CODE}.accountName`)" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托贷款时手续费率 -->
      <f-form-item v-if="showNotCharge" prop="feeChargeRate" :label="t(`${CODE}.commissionRate`)">
        <f-number v-model="dto.feeChargeRate" :precision="4" max="99.9999" min="0" is-rate :disabled="props.readonly" />
      </f-form-item>
      <!-- 折人民币汇率 -->
      <f-form-item prop="toCnyRate" :label="t(`${CODE}.toCnyRate`)">
        <f-number v-model="dto.toCnyRate" :precision="4" max="99.9999" min="0" is-rate :disabled="props.readonly" />
      </f-form-item>
      <!-- 委托贷款时手续费金额 -->
      <f-form-item v-if="showNotCharge" prop="feeAmount" :label="t(`${CODE}.chargeAmount`)">
        <f-amount v-model="dto.feeAmount" :symbol="currencySymbol" disabled />
      </f-form-item>
      <!-- 首次收费日 -->
      <f-form-item v-if="showNotFreeCharge" prop="firstFeeChargeDate" :label="t(`${CODE}.firstChargeDate`)">
        <f-date-picker v-model="dto.firstFeeChargeDate" type="date" :disabled="props.readonly" />
      </f-form-item>
      <!-- 是否免收手续费 -->
      <f-form-item prop="freeChargeFlag" :label="t(`${CODE}.freeChargeFlag`)">
        <f-switch
          v-model="dto.freeChargeFlag"
          :active-value="yesOrNo.YES"
          :inactive-value="yesOrNo.NO"
          :disabled="props.readonly"
        />
      </f-form-item>
    </f-panel>

    <!-- 附件信息 -->
    <f-panel :title="t(`${CODE}.fileInfo`)">
      <!-- 附件 -->
      <f-form-item :label="t(`${CODE}.file`)" :employ="3">
        <f-attm-upload
          v-model="dto.fileList"
          drag
          multiple
          :show-upload="!props.readonly"
          :is-show-batch-delete="!props.readonly"
          :is-remove-delete-link="props.readonly"
        />
      </f-form-item>
    </f-panel>
    <slot />
  </f-multi-form-panel>
</template>
<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { useCurrency } from "@/hooks/useCommon";
import { monthBetween } from "@/utils/date";
import useContractInfo from "../hooks/add/useContractInfo";
import useApplication from "../hooks/useApplication";
import useAppValidate from "../hooks/useAppValidate";
import useEnum from "../hooks/useEnum";
import {
  listOfficeUrl,
  listCurrencyUrl,
  listContractCodeUrl,
  listLoanClientCodeUrl,
  listInterestSettNoUrl,
  listConsignCurrentAccountUrl,
  listRecInterestAccountUrl,
  listPayAccountNoUrl,
  cashPoolChannelUrl,
  queryBankAccount,
  queryInnerAccount,
  listBorrowerCnyAcctUrl
} from "../url";
import { CODE } from "../types";

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  }
});

const emits = defineEmits(["on-loaded"]);

const { t } = useI18n();

const {
  loanBusinessCategory,
  loanFeeChargeWayEnum,
  feeChargeBasicEnum,
  countPeriodTypeEnum,
  interestFreqEnum,
  loanRateAdjustWayEnum,
  loanRateAdjustFreqEnum,
  loanRateFloatType,
  loanCrossBorderDirectionEnum,
  loanContractRateRunType,
  yesOrNo
} = useEnum();

const { contractInfo, contractMethods } = useContractInfo();

const {
  dto,
  state,
  currencyQueryParam,
  loanClientQueryParam,
  contractQueryParam,
  loanAccountQueryParams,
  consignAccountQueryParams,
  handler,
  methods,
  currencyRef,
  formRef,
  cashPoolRef,
  borrowerCnyAcctQueryParams,
  consignCnyAcctQueryParams
} = useApplication(t, props, emits, contractInfo, contractMethods);

const rules = useAppValidate(t, state, contractInfo);
const { currencySymbol } = useCurrency(dto);

const baseInfoReadonly = computed(() => props.readonly || !!dto.id);

// 是否显示折算美元
const showUsdAmount = computed(
  () => !((dto.cashPoolChannelManagement === "SAFE" && dto.currencyId === 2) || dto.cashPoolChannelManagement === "PBC")
);

// 显示手续费相关控件
const showFeeCharge = computed(() => dto.feeChargeBasic === feeChargeBasicEnum.PAY_FORM_BALANCE);
// 手续费收取方式不为不计费的条件
const showNotCharge = computed(() => showFeeCharge.value && dto.feeChargeBasic !== loanFeeChargeWayEnum.NOT_CHARGE);
// 是否免收手续费
const showNotFreeCharge = computed(() => dto.freeChargeFlag === yesOrNo.NO);
// 是否显示委托人活期账户号 元素  融通方向为“外债引入”或者融通方向为“对外放款”&外币&委托人购汇开关“关闭”或者融通方向为“对外放款”&人民币时显示且必输
const showConsignCurrentAcctNo = computed(
  () =>
    dto.crossDirection === "INTRODUCE_DEBT" ||
    (dto.crossDirection === "EXTERNAL_LENDING" && dto.currencyId > 1 && dto.consignPurchaseExchangeFlag === "NO") ||
    (dto.crossDirection === "EXTERNAL_LENDING" && dto.currencyId === 1)
);

const necessary = computed(() => !props.readonly);
const necessaryRules = computed(() => (props.readonly ? [] : rules));

const disabledPayDate = date => {
  if (!dto.contractEndDate) {
    return true;
  }
  return new Date(dto.contractEndDate + " 00:00:00") < date;
};

const disabledFirstInterestDate = date => {
  if (!dto.payDate) {
    return true;
  }
  if (!dto.loanEndDate) {
    return true;
  }
  if (date < new Date(dto.payDate + " 00:00:00")) {
    return true;
  }
  if (date > new Date(dto.loanEndDate + " 00:00:00")) {
    return true;
  }
  return false;
};

const handlePayDateChange = () => {
  dto.loanStartDate = dto.payDate;
  dto.loanTerm = monthBetween(dto.loanStartDate, dto.loanEndDate);
  dto.firstFeeChargeDate = dto.payDate;
};

const handlePayAmountChange = () => {
  dto.prepaidInterest = dto.payAmount * (dto.executeRate / 100) * 30;
  dto.actualPayAmount = dto.payAmount - dto.prepaidInterest;
};

defineExpose(methods);
</script>
