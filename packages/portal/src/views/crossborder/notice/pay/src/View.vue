<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t(`${CODE}.viewTitle`)"
    :isPoint="false"
    destroy-on-close
    @close="closeDrawer"
  >
    <Application ref="appInfo" readonly :id="id" @on-loaded="handleAppLoaded">
      <f-panel :title="t(`${CODE}.wfHistoryTitle`)">
        <f-wf-history ref="wfHistoryRef" :params="wfParams" :auto-init="false" :is-through="false" />
      </f-panel>
    </Application>
    <template #footer>
      <f-button type="info" plain @click="closeDrawer">{{ t(`${CODE}.close`) }}</f-button>
    </template>
  </f-drawer-scene>
</template>

<script setup lang="ts">
import { shallowRef, reactive } from "vue";
import { useBizDetail } from "@/hooks/biz";
import { useI18n } from "vue-i18n";
import Application from "./Application.vue";
import { CODE } from "../types";

const { t } = useI18n();

const props = defineProps<{
  id?: number;
}>();

const wfHistoryRef = shallowRef();

const wfParams = reactive({
  systemCode: "Z67",
  transType: "Z670005",
  agencyId: null,
  currencyId: null,
  recordId: null
});

const handleAppLoaded = info => {
  wfParams.agencyId = info.officeId; // 组织ID(机构ID或成员单位ID)
  wfParams.currencyId = 0; // info.currencyId; // 币种ID
  wfParams.recordId = info.id; // 业务单据id

  wfHistoryRef.value?.getData();
};

const initData = () => {};

const { drawerRef, setVisible, closeDrawer } = useBizDetail(props, initData);

defineExpose({ setVisible });
</script>
