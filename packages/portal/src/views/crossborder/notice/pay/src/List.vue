<template>
  <f-query-scene :title="t(`${CODE}.listTitle`)">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :url="listPayNoticeUrl"
        label-width="120px"
        :form-data="queryForm"
        :export-url="exportPayNoticeUrl"
        :table-columns="tableColumns"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        :is-single="false"
        table-type="Record"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :show-collapse="true"
        :export-exclude="['operate']"
        query-comp-id="crossborder-notice-pay-query"
        table-comp-id="crossborder-notice-pay-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :status-enum="loanPayNoticeStatusEnum"
            :submit-batch-url="submitPayNoticerBatchUrl"
            :delete-batch-url="deletePayNoticeBatchUrl"
            :revoke-batch-url="revokePayNoticeBatchUrl"
            @on-add="handler.handleAdd"
          />
        </template>

        <!-- 查询面本表单项 -->
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item prop="officeList" :label="t(`${CODE}.officeName`)">
            <f-select
              v-model="queryForm.officeList"
              :url="listOfficeUrl"
              value-key="officeId"
              label="officeName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item prop="currencyList" :label="t(`${CODE}.currencyName`)">
            <f-select
              v-model="queryForm.currencyList"
              :url="listCurrencyUrl"
              value-key="currencyId"
              label="currencyName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 单据状态 -->
          <f-form-item prop="businessStatusList" :label="t(`${CODE}.businessStatus`)">
            <f-select
              v-model="queryForm.businessStatusList"
              :data="loanPayNoticeStatusEnum"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 放款通知单号 -->
          <f-form-item prop="businessCode" :label="t(`${CODE}.businessCode`)">
            <f-input v-model="queryForm.businessCode" />
          </f-form-item>
          <!-- 录入日期 -->
          <f-form-item prop="inputTimeArray" :label="t(`${CODE}.inputDateArray`)">
            <f-lax-range-date-picker v-model="queryForm.inputDateArray" />
          </f-form-item>
          <!-- 放款金额 -->
          <f-form-item prop="payAmountArray" :label="t(`${CODE}.payAmountArray`)">
            <f-amount-range v-model="queryForm.payAmountArray" min="0" max="9999999999999.99" tooltip :precision="2" />
          </f-form-item>
          <!-- 借款单位 -->
          <f-form-item prop="loanClientCodeList" :label="t(`${CODE}.loanClient`)">
            <f-magnifier-multi
              :title="t(`${CODE}.loanClient`)"
              :url="clientListUrl"
              v-model="queryForm.loanClientCodeList"
              row-key="clientCode"
              row-label="clientName"
              input-key="clientCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column prop="clientCode" :label="t(`${CODE}.clientCode`)" />
              <f-magnifier-column prop="clientName" :label="t(`${CODE}.clientName`)" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 委托单位 -->
          <f-form-item prop="consignClientCodeList" :label="t(`${CODE}.consignClient`)">
            <f-magnifier-multi
              :title="t(`${CODE}.consignClient`)"
              :url="clientListUrl"
              v-model="queryForm.consignClientCodeList"
              row-key="clientCode"
              row-label="clientName"
              input-key="clientCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column prop="clientCode" :label="t(`${CODE}.clientCode`)" />
              <f-magnifier-column prop="clientName" :label="t(`${CODE}.clientName`)" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 贷款期限 -->
          <f-form-item prop="loanTermArray" :label="t(`${CODE}.loanTermArray`)">
            <f-amount-range v-model="queryForm.loanTermArray" wholeNumber symbol=" " min="1" tooltip />
          </f-form-item>
          <!-- 融通方向 -->
          <f-form-item prop="crossDirectionList" :label="t(`${CODE}.crossDirection`)">
            <f-select
              v-model="queryForm.crossDirectionList"
              :data="loanCrossBorderDirectionEnum"
              value-key="value"
              label="label"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 合同编号 -->
          <f-form-item prop="contractIdList" :label="t(`${CODE}.contractCode`)">
            <f-magnifier-multi
              :title="t(`${CODE}.contractCode`)"
              :url="listContractCodeUrl"
              method="post"
              v-model="queryForm.contractIdList"
              row-key="contractId"
              row-label="contractCode"
              input-key="contractCode"
              collapse-tags-tooltip
              auto-init
              :params="{
                loanType: loanTypeEnum.CROSS_BORDER,
                crossBorderFlg: yesOrNo.YES
              }"
            >
              <f-magnifier-column prop="contractCode" :label="t(`${CODE}.contractCode`)" />
              <f-magnifier-column
                prop="crossDirection"
                :label="t(`${CODE}.crossDirection`)"
                :formatter="{ name: 'const', const: 'common.LoanCrossBorderDirection' }"
                :filter-select="loanCrossBorderDirectionEnum"
              />
              <f-magnifier-column prop="loanClientName" :label="t(`${CODE}.loanClientCodeList`)" />
              <f-magnifier-column prop="consignClientName" :label="t(`${CODE}.consignClientCodeList`)" />
              <f-magnifier-column prop="contractAmount" :label="t(`${CODE}.contractAmount`)" formatter="amount" />
              <f-magnifier-column prop="productName" :label="t(`${CODE}.loanProductName`)" />
              <f-magnifier-column prop="contractEndDate" :label="t(`${CODE}.contractEndDate`)" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 放款日期 -->
          <f-form-item prop="payDateArray" :label="t(`${CODE}.payDate`)">
            <f-lax-range-date-picker v-model="queryForm.payDateArray" />
          </f-form-item>
          <!-- 贷款业务类型 -->
          <f-form-item prop="loanBusinessTypeList" :label="t(`${CODE}.loanBizType`)">
            <f-select
              v-model="queryForm.loanBusinessTypeList"
              :data="loanBusinessCategory"
              value-key="value"
              label="label"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 贷款产品名称 -->
          <f-form-item prop="loanProductIdList" :label="t(`${CODE}.loanProductName`)">
            <f-magnifier-multi
              :title="t(`${CODE}.loanProductName`)"
              :url="queryProductBasic"
              v-model="queryForm.loanProductIdList"
              row-key="id"
              row-label="productName"
              collapse-tags-tooltip
              auto-init
              :params="{
                productLine: 'LOAN'
              }"
            >
              <f-magnifier-column prop="productCode" :label="t(`${CODE}.loanProductCode`)" />
              <f-magnifier-column prop="productName" :label="t(`${CODE}.loanProductName`)" />
            </f-magnifier-multi>
          </f-form-item>
        </template>
        <!-- 表格列 -->
        <template #link="{ row }">
          <f-button link type="primary" @click="openDetail(row)">
            {{ row.businessCode }}
          </f-button>
        </template>
        <template #buttons="{ row }">
          <OperateGroup
            :row="row"
            :submit-url="submitPayNoticerUrl"
            :delete-url="deletePayNoticeUrl"
            :revoke-url="revokePayNoticeUrl"
            @on-modify="handler.handleModify"
            @operate-success="methods.reloadTable"
          />
        </template>
      </f-query-grid>
    </template>
    <View ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useListDetail } from "@/hooks/biz";
import OperateGroup from "@/views/crossborder/components/OperateGroup.vue";
import OperateBatchGroup from "@/views/crossborder/components/OperateBatchGroup.vue";
import View from "./View.vue";
import {
  listPayNoticeUrl,
  exportPayNoticeUrl,
  listOfficeUrl,
  listCurrencyUrl,
  clientListUrl,
  submitPayNoticerUrl,
  revokePayNoticeUrl,
  deletePayNoticeUrl,
  submitPayNoticerBatchUrl,
  deletePayNoticeBatchUrl,
  revokePayNoticeBatchUrl,
  listContractCodeUrl,
  queryProductBasic
} from "../url";
import useTableColumns from "../hooks/list/useTableColumns";
import useList from "../hooks/useList";
import useEnum from "../hooks/useEnum";
import { CODE } from "../types";

const { t } = useI18n();

const { loanTypeEnum, loanBusinessCategory, loanPayNoticeStatusEnum, loanCrossBorderDirectionEnum, yesOrNo } =
  useEnum();

const queryTable = shallowRef();
const operateGroup = shallowRef();

const { tableColumns, columnSort, defaultSort } = useTableColumns(t, queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};

const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const { id, detail, open } = useListDetail();

const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};

const { queryForm, handler, methods } = useList(t, queryTable);
</script>
