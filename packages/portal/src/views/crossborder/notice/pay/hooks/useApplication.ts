import { computed, nextTick, onBeforeMount, reactive, shallowRef } from "vue";
import { FMessage } from "@dtg/frontend-plus";
import { useDefaultOfficeCurrency } from "@/hooks/biz";
import { formatDate, monthBetween } from "@/utils/date";
import httpTool from "@/utils/http";
import {
  getPayNoticeUrl,
  getRecentlyEffectiveDate,
  getLoanAccountTypeUrl,
  getContractDetailUrl,
  getTrialResult,
  openDateUrl,
  listPayAccountNoUrl,
  listBorrowerCnyAcctUrl
} from "../url";
import { usePage } from "./usePage";
import useEnum from "./useEnum";
import { CODE } from "../types";
import { useUserStoreHook } from "@/stores/modules/user";

export default function useApplication(t, props, emits, contractInfo, contractMethods) {
  const defaultOfficeAndCurrency = useDefaultOfficeCurrency();
  const userStore = useUserStoreHook();
  const {
    accountGroupEnum,
    autoRepayModeEnum,
    feeChargeBasicEnum,
    floatDirectionEnum,
    floatPointEnum,
    loanContractStatusEnum,
    loanRateTypeEnum,
    loanSetRateTypeEnum,
    loanTypeEnum,
    yesOrNo
  } = useEnum();

  const dto = reactive({
    id: null,
    globalSerial: "",
    businessCode: "",
    businessType: "",
    businessStatus: "",
    crossDirection: "", //融通方向
    cashPoolChannelId: null, //资金池通道
    cashPoolChannelName: "", //资金池通道名称
    loanType: loanTypeEnum.CROSS_BORDER,
    loanClientId: null,
    loanClientCode: "",
    loanClientName: "",
    loanAcctId: null, // 贷款账户相关
    loanAcctNo: "",
    loanAcctName: "",
    consignClientId: null,
    consignClientCode: "",
    consignClientName: "",
    productId: null, // 产品ID
    productCode: "", // 产品编号
    productName: "", // 产品名称
    contractId: null,
    contractCode: "",
    contractAmount: null,
    loanStartDate: "", //贷款开始日期
    loanEndDate: "", //贷款结束日期
    loanTerm: null, // 贷款期限
    greenCredit: "",
    consignCurrentAccountId: null, // 委托方活期户-代偿单位贷款专户相关
    consignCurrentAccountNo: "",
    consignCurrentAccountName: "",
    consignInterestAccountId: null, // 委托方收息账户相关
    consignInterestAccountNo: "",
    consignInterestClientName: "",
    borrowerCnyAcctId: null,
    borrowerCnyAcctCode: "",
    borrowerCnyAcctName: "",
    feeAcctNo: "",
    feeAcctName: "",
    feeRulesId: null,
    feeRulesVersion: "",
    feeChargeWay: "",
    feeChargeBasic: "",
    feeChargeRate: "",
    feeAmount: "",
    payDate: "", //放款日期
    recPrincipalAcctId: null, // 收本金账户相关
    recPrincipalAcctNo: "",
    recPrincipalAcctName: "",
    payAmount: "",
    actualPayAmount: "", //实际发放金额
    prepaidInterest: "", //预收利息
    rateRunType: "", // 利率方式
    executeRate: null, // 执行利率
    referenceRateId: "", // 固定利率
    referenceRateName: "",
    floatingRateId: null,
    floatingRateCode: "", //挂牌利率率代码
    floatingRateName: "",
    floatingWay: "", // 浮动方式
    floatingType: "", // 上浮还是下浮
    floatingRatio: 0, // 浮动比例
    pointType: "", // 加基点还是减基点
    pointFloating: 0, //基点加减点数
    floatingRate: null, // 这个字段需要确认-从放大镜中选择的浮动利率
    interestSettTerm: "", // 结息结息周期
    interestSettDateFirst: "", // 首期结息日
    remark: "", // 备注
    fileIds: "",
    fileList: [],
    clnLoanFileList: [], // 贷款申请客户详情
    payPlanId: null, // 合同自动还款还款计划相关
    payPlanCode: "",
    autoRepayMode: "", // 合同自动还款还本息方式 PRINCIPAL: 1、只还本金，末次还本付息 2、每次还本付息
    // 下面这些字段目前后台接口并没有定义
    fixedRate: 0,
    consignPurchaseExchangeFlag: yesOrNo.YES, // "YES",
    borrowerSettExchangeFlag: yesOrNo.NO, // "NO",
    partPurchaseExchangeFlag: yesOrNo.NO, // "NO",
    partSettExchangeFlag: yesOrNo.NO, // "NO",
    ...defaultOfficeAndCurrency
  });

  const state = reactive({
    recentlyEffectiveDate: "", // 挂牌利率生效日
    loanAccountType: null,
    businessCode: "" // 修改、查看页面在查询合同时，需要传递业务编号，便于核心将该笔单据所预占金额加回来
  });

  const currencyRef = shallowRef(null);
  const formRef = shallowRef(null);
  const cashPoolRef = shallowRef(null);

  const doResetDefaultData = () => {
    dto.feeChargeBasic = feeChargeBasicEnum.CONTRACT_AMOUNT;
    dto.rateRunType = loanSetRateTypeEnum.FLOAT;
    dto.floatingType = floatDirectionEnum.GOUP;
    dto.pointType = floatPointEnum.PLUS;
    dto.autoRepayMode = autoRepayModeEnum.PRINCIPAL;
    if (contractInfo.contractPriceRateDtos && contractInfo.contractPriceRateDtos.length > 0) {
      const normalRateData = contractInfo.contractPriceRateDtos.find(x => x.rateType === "NORMAL");
      if (normalRateData) {
        dto.rateRunType = normalRateData.rateRunType;
        dto.floatingRateId = normalRateData.boardRateId;
        dto.floatingRateCode = normalRateData.boardRateCode;
        dto.floatingRate = normalRateData.floatingRate;
        dto.floatingType = normalRateData.floatingRatio >= 0 ? floatDirectionEnum.GOUP : floatDirectionEnum.GODOWN;
        dto.floatingRatio = Math.abs(normalRateData.floatingRatio);
        dto.pointType = normalRateData.basicPoint >= 0 ? floatPointEnum.PLUS : floatPointEnum.MINUS;
        dto.pointFloating = Math.abs(normalRateData.basicPoint);
        if (normalRateData.rateRunType === loanSetRateTypeEnum.FLOAT) {
          doCalRate();
        } else {
          dto.executeRate = dto.fixedRate = normalRateData.fixedRate;
        }
      }
    }
  };

  const doCalRate = () => {
    let tmp = (dto.floatingRate || 0) / 100;
    const proportion = dto.floatingType === floatDirectionEnum.GOUP ? 100 + dto.floatingRatio : 100 - dto.floatingRatio;
    tmp = tmp * proportion;
    const points = dto.pointType === floatPointEnum.PLUS ? dto.pointFloating : 0 - dto.pointFloating;
    tmp = tmp + points / 100;
    dto.executeRate = tmp.toFixed(6);
  };

  doResetDefaultData();

  const currencyQueryParam = computed(() => {
    return { officeId: dto.officeId };
  });

  const loanClientQueryParam = computed(() => {
    return {
      officeId: dto.officeId,
      currencyId: dto.currencyId,
      clientclass: 1 // 只查内部账户
    };
  });

  const contractQueryParam = computed(() => {
    return {
      loanType: loanTypeEnum.CROSS_BORDER, // 只查跨境融通的数据
      crossBorderFlg: yesOrNo.YES,
      officeId: dto.officeId,
      currencyId: dto.currencyId,
      loanClientId: dto.loanClientId,
      crossDirection: dto.crossDirection,
      cashPoolChannelId: dto.cashPoolChannelId,
      managerUserNo: userStore.userNo,
      contractStatusList: [
        loanContractStatusEnum.NOT_ACTIVE,
        loanContractStatusEnum.NOT_EXECUTE,
        loanContractStatusEnum.EXECUTING, // 执行中
        loanContractStatusEnum.EXTEND // 已展期
      ]
    };
  });

  // 借款人人民币账户查询参数
  const borrowerCnyAcctQueryParams = computed(() => {
    return {
      officeId: dto.officeId,
      officeCode: dto.officeCode,
      currencyId: 1,
      clientIds: dto.loanClientId,
      clientCode: dto.loanClientCode,
      includeLoanFlag: yesOrNo.NO,
      includeFxPayAcctFlag: yesOrNo.YES,
      accountGroup: accountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  // 贷款方账户查询参数
  const loanAccountQueryParams = computed(() => {
    return {
      officeId: dto.officeId,
      officeCode: dto.officeCode,
      currencyId: dto.currencyId,
      currencyCode: dto.currencyCode,
      clientIds: dto.loanClientId,
      clientCode: dto.loanClientCode,
      clientName: dto.loanClientName,
      accountGroup: accountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  // 委托方人民币账户查询参数
  const consignCnyAcctQueryParams = computed(() => {
    return {
      officeId: dto.officeId,
      officeCode: dto.officeCode,
      currencyId: 1,
      currencyCode: dto.currencyCode,
      clientIds: dto.consignClientId,
      clientCode: dto.consignClientCode,
      clientName: dto.consignClientName,
      accountGroup: accountGroupEnum.CURRENT // 查询活期类账户
    };
  });
  // 委托方账户查询参数
  const consignAccountQueryParams = computed(() => {
    return {
      officeId: dto.officeId,
      officeCode: dto.officeCode,
      currencyId: dto.currencyId,
      currencyCode: dto.currencyCode,
      clientIds: dto.consignClientId,
      clientCode: dto.consignClientCode,
      clientName: dto.consignClientName,
      accountGroup: accountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  const boardRateQueryParams = computed(() => {
    return {
      businessType: "LOAN",
      currencyId: contractInfo.currencyId,
      currencyName: contractInfo.currencyName,
      effectiveDate: state.recentlyEffectiveDate
    };
  });

  const payPlanQueryParams = computed(() => {
    return {
      contractId: contractInfo.contractId,
      contractCode: contractInfo.contractCode,
      planType: "PAY",
      ids: dto.payPlanId ? [dto.payPlanId] : []
    };
  });

  const handler = {
    async handleOfficeChange(val, row) {
      dto.officeCode = !val ? "" : row.officeCode;
      dto.officeName = !val ? "" : row.officeName;
      dto.currencyId = null;
      dto.currencyCode = "";
      dto.currencyName = "";
      dto.loanClientId = null;
      dto.loanClientCode = "";
      dto.loanClientName = "";
      dto.contractId = null;
      dto.contractCode = "";
      dto.contractName = "";

      await nextTick();
      currencyRef.value?.initRemoteData();
    },
    async handleCurrencyChange(val, row) {
      dto.currencyCode = !val ? "" : row.currencyCode;
      dto.currencyName = !val ? "" : row.currencyName;

      // 清空下级数据
      dto.contractId = null;
      dto.contractCode = "";
      dto.contractName = "";

      await nextTick();
      cashPoolRef.value?.initRemoteData();
    },
    handleCashPoolChannelChange(val, row) {
      if (val) {
        dto.cashPoolChannelId = row.capitalPoolChannelId;
        dto.cashPoolChannelName = row.capitalPoolChannel;
        dto.cashPoolChannelManagement = row.limitManageDepartment;
      } else {
        dto.cashPoolChannelId = null;
        dto.cashPoolChannelName = "";
        dto.cashPoolChannelManagement = "";
      }
    },
    handleCrossDirectionChange(val) {
      //当融通方向为“外债引入”,借款人活期户显示贷款专户
      if (val !== dto.crossDirection) {
        dto.recPrincipalAcctNo = "";
        dto.contractId = null;
        dto.contractCode = "";
        dto.borrowerCnyAcctCode = "";
        dto.borrowerCnyAcctName = "";
      }
    },
    handleInternalMainAccountChange(row) {
      if (row) {
        dto.internalMainAccountId = row.id;
        dto.internalMainAccountCode = row.bankAccountNo;
        dto.internalMainAccountName = row.bankName;
      } else {
        dto.internalMainAccountId = null;
        dto.internalMainAccountCode = "";
        dto.internalMainAccountName = "";
      }
    },
    handleConsignCnyAcctChange(row) {
      if (row) {
        dto.consignCnyAcctCode = row.accountCode;
        dto.consignCnyAcctName = row.accountName;
      } else {
        dto.consignCnyAcctCode = "";
        dto.consignCnyAcctName = "";
      }
    },
    handleConsignForeignAcctChange(row) {
      if (row) {
        dto.consignForeignAcctCode = row.accountCode;
        dto.consignForeignAcctName = row.accountName;
      } else {
        dto.consignForeignAcctCode = "";
        dto.consignForeignAcctName = "";
      }
    },
    handleBorrowerCnyAcctChange(row) {
      if (row) {
        dto.borrowerCnyAcctCode = row.accountCode;
        dto.borrowerCnyAcctName = row.accountName;
      } else {
        dto.borrowerCnyAcctCode = "";
        dto.borrowerCnyAcctName = "";
      }
    },
    handleLoanClientChange(row) {
      dto.loanClientCode = !row || !row.clientId ? "" : row.clientCode;
      dto.loanClientName = !row || !row.clientId ? "" : row.clientName;

      // 清空下级数据
      dto.contractId = null;
      dto.contractCode = "";
      dto.contractName = "";
    },
    handleContractChange(row) {
      if (!row || !row.contractId) {
        dto.contractCode = "";
        dto.contractName = "";
        dto.loanBusinessType = null;
        dto.productId = null;
        dto.productCode = null;
        dto.productName = null;
        dto.consignClientId = null;
        dto.consignClientCode = null;
        dto.consignClientName = null;
        //国内主账户、开户行名称、委托人人民币账号
      } else {
        dto.officeId = row.officeId;
        dto.officeCode = row.officeCode;
        dto.officeName = row.officeName;

        dto.currencyId = row.currencyId;
        dto.currencyCode = row.currencyCode;
        dto.currencyName = row.currencyName;

        dto.crossDirection = row.crossDirection;

        dto.cashPoolChannelId = row.cashPoolChannelId;
        dto.cashPoolChannelName = row.cashPoolChannelName;
        dto.cashPoolChannelManagement = row.cashPoolChannelManagement;

        dto.loanClientId = row.loanClientId;
        dto.loanClientCode = row.loanClientCode;
        dto.loanClientName = row.loanClientName;

        dto.contractId = row.contractId;
        dto.contractCode = row.contractCode;
        dto.contractName = row.contractName;

        dto.toUsdRate = row.toUsdRate;
        dto.toUsdAmount = row.toUsdAmount;

        const paramContractInfo = {
          currencyId: row.contractId,
          contractCode: row.contractCode,
          businessCode: dto.businessCode
        };

        httpTool.post(getContractDetailUrl, paramContractInfo).then(res => {
          if (res.success && res.data) {
            // Object.assign(dto, res.data);
            dto.officeId = res.data.officeId;
            dto.officeCode = res.data.officeCode;
            dto.officeName = res.data.officeName;

            dto.currencyId = res.data.currencyId;
            dto.currencyCode = res.data.currencyCode;
            dto.currencyName = res.data.currencyName;

            dto.loanClientId = res.data.loanClientId;
            dto.loanClientCode = res.data.loanClientCode;
            dto.loanClientName = res.data.loanClientName;

            dto.consignClientId = res.data.consignClientId;
            dto.consignClientCode = res.data.consignClientCode;
            dto.consignClientName = res.data.consignClientName;
            //还本账户
            dto.repayPrincipalAcctId = res.data.repayPrincipalAcctId;
            dto.repayPrincipalAcctNo = res.data.repayPrincipalAcctNo;
            dto.repayPrincipalAcctName = res.data.repayPrincipalAcctName;

            dto.loanType = res.data.loanType;
            dto.loanBusinessType = res.data.loanBusinessType;

            dto.productId = res.data.productId;
            dto.productCode = res.data.productCode;
            dto.productName = res.data.productName;

            dto.toUsdRate = res.data.toUsdRate;
            dto.toUsdAmount = res.data.toUsdAmount;

            dto.contractAmount = res.data.contractAmount;
            dto.contractBalance = res.data.contractBalance;
            dto.payedAmount = res.data.payedAmount;
            dto.unPayedAmount = res.data.unPayedAmount;
            dto.contractAvailableAmount = res.data.contractAvailableAmount;
            dto.repayAmount = res.data.repayAmount;
            dto.contractExecuteRate = res.data.executeRate;

            dto.contractStartDate = res.data.contractStartDate;
            dto.contractEndDate = res.data.contractEndDate;
            dto.loanEndDate = res.data.contractEndDate;

            dto.contractTerm = res.data.contractTerm;
            dto.gracePeriod = res.data.gracePeriod;

            // dto.industryCategoryFuzzy = res.data.industryCategoryFuzzy;
            // dto.loanInvestArea = res.data.loanInvestArea;
            dto.revolvingLoanFlag = res.data.revolvingLoanFlag;
            dto.allEntrustPayFlag = res.data.allEntrustPayFlag;
            // dto.greenCredit = res.data.greenCredit;
            // dto.greenCreditName = res.data.greenCreditName;
            // dto.technologyLoans = res.data.technologyLoans;
            // dto.technologyLoansName = res.data.technologyLoansName;
            dto.loanPurpose = res.data.loanPurpose;
            // dto.fundsSourceName = res.data.fundsSourceName;

            dto.rateRunType = res.data.rateRunType; //利率类型
            dto.floatingRate = res.data.floatingRate; //浮动利率
            dto.floatingWay = res.data.floatingWay; //浮动方式
            dto.floatingRatio = res.data.floatingRatio; //浮动比例
            dto.basicPoint = res.data.basicPoint; //基点
            dto.fixedRate = res.data.fixedRate; //固定利率
            dto.executeRate = res.data.executeRate; //执行利率
            dto.interestSettFreq = res.data.interestSettFreq; //结息周期
            dto.firstInterestDate = res.data.firstInterestDate; //首次结息日
            httpTool.post(openDateUrl).then((res: any) => {
              dto.payDate = res.data.onlineDate; //放款日期，默认系统开机日
              dto.loanStartDate = dto.payDate;
              dto.loanTerm = monthBetween(dto.loanStartDate, dto.loanEndDate);
              dto.firstFeeChargeDate = dto.payDate;
            });
            // 保理贷款
            dto.factoringRecourseFlag = res.data.factoringRecourseFlag; //保理贷款有无追索权
            dto.factoringPublicFlag = res.data.factoringPublicFlag; //保理贷款是否公开
            dto.factoringRealInterest = res.data.factoringRealInterest; //保理实付利息
            dto.factoringRealPayAmount = res.data.factoringRealPayAmount; //保理实际发放金额

            // 从正常利率中获取下述信息
            if (res.data.contractRateList && res.data.contractRateList.length > 0) {
              res.data.contractRateList.map(item => {
                if (item.rateType === loanRateTypeEnum.NORMAL) {
                  dto.floatingRateId = item.rateId;
                  dto.floatingRateCode = item.rateCode; //
                  dto.floatingRateName = item.rateName; //
                  dto.rateAdjustWay = item.rateAdjustWay; //利率调整方式
                  dto.rateAdjustFreq = item.rateAdjustFreq; //利率调整周期
                  dto.firstDayAdjustFlag = item.firstDayAdjustFlag; //是否周期第一天调整
                  dto.fixedAdjustDate = item.fixedAdjustDate; //固定调整日期
                  dto.repeatAnnuallyFlag = item.repeatAnnuallyFlag; //是否按年重复
                }
              });
            }
            // 手续费信息
            if (res.data.contractPriceFeeDto) {
              dto.feeChargeWay = res.data.contractPriceFeeDto.feeChargeWay; //手续费收取方式
              dto.feeChargeFreq = res.data.contractPriceFeeDto.feeChargeFreq; //手续费收费周期
              dto.feeChargeBasic = res.data.contractPriceFeeDto.feeChargeBasic; //计费基础
              dto.feeChargeRate = res.data.contractPriceFeeDto.feeChargeRate; //手续费率
              dto.feeAmount = res.data.contractPriceFeeDto.feeAmount; //手续费金额
              // dto.firstFeeChargeDate = res.data.contractPriceFeeDto.firstFeeChargeDate;//首次收费日
              dto.freeChargeFlag = res.data.contractPriceFeeDto.freeChargeFlag; //是否免收手续费
            }
          }
        });

        // 合同改变后，要重新根据合同开始时间获取挂牌利率生效日
        const paramEffectiveDate = {
          currencyId: contractInfo.currencyId,
          interestStartDate: contractInfo.contractStartDate
        };
        httpTool.post(getRecentlyEffectiveDate, paramEffectiveDate).then(res => {
          if (res.success) {
            state.recentlyEffectiveDate = res.data.effectiveDate;
          }
        });
        if (dto.crossDirection === "INTRODUCE_DEBT") {
          const loanPrincipalAccParams = {
            officeId: dto.officeId,
            officeCode: dto.officeCode,
            currencyId: dto.currencyId,
            currencyCode: dto.currencyCode,
            clientId: dto.loanClientId,
            includeLoanFlag: yesOrNo.YES,
            loanFlag: yesOrNo.YES,
            accountGroups: [accountGroupEnum.CURRENT]
          };
          httpTool.post(listPayAccountNoUrl, loanPrincipalAccParams).then(res => {
            if (res.success) {
              if (res.data) {
                dto.recPrincipalAcctId = res.data[0].accountId;
                dto.recPrincipalAcctNo = res.data[0].accountCode;
                dto.recPrincipalAcctName = res.data[0].accountName;
              } else {
                dto.recPrincipalAcctId = null;
                dto.recPrincipalAcctNo = "";
                dto.recPrincipalAcctName = "";
              }
            }
          });
          if (dto.currencyId !== 1) {
            const borrowerCnyAcctParams = {
              officeId: dto.officeId,
              officeCode: dto.officeCode,
              currencyId: 1,
              clientIds: dto.loanClientId,
              clientCode: dto.loanClientCode,
              fxPayAcctFlag: yesOrNo.YES,
              accountGroup: accountGroupEnum.CURRENT // 查询活期类账户
            };
            httpTool.post(listBorrowerCnyAcctUrl, borrowerCnyAcctParams).then(res => {
              if (res.success) {
                if (res.data) {
                  dto.borrowerCnyAcctId = res.data[0].accountId;
                  dto.borrowerCnyAcctCode = res.data[0].accountCode;
                  dto.borrowerCnyAcctName = res.data[0].accountName;
                } else {
                  dto.borrowerCnyAcctId = null;
                  dto.borrowerCnyAcctCode = "";
                  dto.borrowerCnyAcctName = "";
                }
              }
            });
          }
        }
      }
    },

    handleBoardRateCodeChange() {
      methods.calTrialResult();
    },

    handlePayInterestAcctNoChange(row) {
      if (row && row.accountCode) {
        dto.payInterestAcctId = row.accountId;
        dto.payInterestAcctNo = row.accountCode;
        dto.payInterestAcctName = row.accountName;
      } else {
        dto.payInterestAcctId = null;
        dto.payInterestAcctNo = "";
        dto.payInterestAcctName = "";
      }
    },
    handleRepayPrincipalAcctNoChange(row) {
      if (row && row.accountCode) {
        dto.repayPrincipalAcctId = row.accountId;
        dto.repayPrincipalAcctNo = row.accountCode;
        dto.repayPrincipalAcctName = row.accountName;
      } else {
        dto.repayPrincipalAcctId = null;
        dto.repayPrincipalAcctNo = "";
        dto.repayPrincipalAcctName = "";
      }
    },

    handleRecPrincipalAcctNoChange(row) {
      if (row && row.accountCode) {
        dto.recPrincipalAcctId = row.accountId;
        dto.recPrincipalAcctNo = row.accountCode;
        dto.recPrincipalAcctName = row.accountName;
      } else {
        dto.recPrincipalAcctId = null;
        dto.recPrincipalAcctNo = "";
        dto.recPrincipalAcctName = "";
      }
    },
    handlecConsignRecInterestAccountNoChange(row) {
      if (row && row.accountCode) {
        dto.consignRecInterestAcctId = row.accountId;
        dto.consignRecInterestAcctNo = row.accountCode;
        dto.consignRecInterestAcctName = row.clientName;
      } else {
        dto.consignRecInterestAcctId = null;
        dto.consignRecInterestAcctNo = "";
        dto.consignRecInterestAcctName = "";
      }
    },
    handleConsignCurrentAccountNoChange(row) {
      if (row && row.accountCode) {
        dto.consignCurrentAcctId = row.accountId;
        dto.consignCurrentAcctNo = row.accountCode;
        dto.consignCurrentAcctName = row.accountName;
      } else {
        dto.consignCurrentAcctId = null;
        dto.consignCurrentAcctNo = "";
        dto.consignCurrentAcctName = "";
      }
    },
    handlePayFeeAcctNoChange(row) {
      if (row && row.accountCode) {
        dto.payFeeAcctId = row.accountId;
        dto.payFeeAcctNo = row.accountCode;
        dto.payFeeAcctName = row.accountName;
      } else {
        dto.payFeeAcctId = null;
        dto.payFeeAcctNo = "";
        dto.payFeeAcctName = "";
      }
    },

    handleReferenceRateIdChange(val, row) {
      dto.referenceRateName = val ? row.label : "";
    },
    handlefixedRateChange() {
      dto.executeRate = dto.fixedRate;
    },
    handleRateRunTypeChange() {
      if (dto.rateRunType === loanSetRateTypeEnum.FIXED) {
        dto.executeRate = dto.fixedRate;
      } else {
        methods.doCalRate();
      }
    },
    handlePayPlanChange(row) {
      dto.payPlanCode = row ? row.planCode : "";
    }
  };
  const methods = {
    setBaseInfo(baseInfo) {
      dto.officeId = baseInfo.officeId;
      dto.officeCode = baseInfo.officeCode;
      dto.officeName = baseInfo.officeName;
      dto.currencyId = baseInfo.currencyId;
      dto.currencyCode = baseInfo.currencyCode;
      dto.currencyName = baseInfo.currencyName;
      dto.loanClientId = baseInfo.loanClientId;
      dto.loanClientCode = baseInfo.loanClientCode;
      dto.loanClientName = baseInfo.loanClientName;
      dto.contractId = baseInfo.contractId;
      dto.contractCode = baseInfo.contractCode;
      dto.contractName = baseInfo.contractName;
    },
    renderContractInfo(param) {
      return new Promise(resolve => {
        if (!param) {
          param = {
            contractId: contractInfo.contractId, // 合同ID
            contractCode: contractInfo.contractCode // 合同编号
          };
        }
        // resetContractInfo(contractInfo);
        if (param.contractId || param.contractCode) {
          httpTool.post(getContractDetailUrl, param).then(res => {
            if (res.success && res.data) {
              Object.assign(contractInfo, res.data);
              resolve(contractInfo);
            } else {
              resolve(contractInfo);
            }
          });
        } else {
          resolve(contractInfo);
        }
      });
    },
    setBaseInfoFromContractInfo(contractInfo) {
      state.baseInfo.loanType = contractInfo.loanType;
    },
    getContractInfo(contractParams, cb = () => {}) {
      contractMethods.renderContractInfo({ ...contractParams, businessCode: dto.businessCode }).then(contractInfo => {
        baseInfo.value.setBaseInfo(contractInfo);
        const param = { currencyId: contractInfo.currencyId, interestStartDate: contractInfo.contractStartDate };
        httpTool.post(getRecentlyEffectiveDate, param).then(res => {
          if (res.success) {
            state.recentlyEffectiveDate = res.data.effectiveDate;
          }
        });
        cb(contractInfo);
      });
    },
    getPayNoticeInfo(payNoticeParams, cb = () => {}) {
      httpTool.post(getPayNoticeUrl, payNoticeParams).then(res => {
        if (res.success) {
          Object.assign(dto, res.data);
          if (dto.rateRunType === loanSetRateTypeEnum.FIXED) {
            dto.fixedRate = dto.executeRate;
          }
          dto.businessCode = res.data.businessCode;
          // if (dto.payPlanId) {
          //   dto.payPlanId = Number(dto.payPlanId);
          // }
          cb();
        }
      });
    },
    doCalRate,
    getFormData() {
      return Object.assign({ floatingRateDate: state.recentlyEffectiveDate }, dto);
    },
    async validateForm(cb = () => {}) {
      if (state.loanAccountType === 3 && !dto.loanAcctId) {
        FMessage.error(t(`${CODE}.needLoanAcct`));
        return false;
      }
      return await formRef.value?.form.validate(cb);
    },
    calTrialResult() {
      dto.floatingRate = "";
      if (dto.floatingRateCode) {
        const param = {
          boardRateCode: dto.floatingRateCode,
          currencyId: contractInfo.currencyId,
          computerDate: formatDate(new Date())
        };
        httpTool.post(getTrialResult, param).then(res => {
          if (res.success) {
            dto.floatingRate = res.data.rate;
          }
          doCalRate();
        });
      } else {
        doCalRate();
      }
    }
  };

  onBeforeMount(() => {
    if (props.id) {
      methods.getPayNoticeInfo({ id: props.id }, () => {
        emits("on-loaded", dto);
      });
    } else {
      const { pageParams } = usePage();
      if (pageParams?.type === "modify") {
        if (pageParams.data) {
          // 查询合同信息
          dto.businessCode = pageParams.data.businessCode;

          // 查询通知单信息
          const payNoticeParams = { id: pageParams.data.id, businessCode: pageParams.data.businessCode };
          methods.getPayNoticeInfo(payNoticeParams, () => {
            emits("on-loaded", dto);
          });
        }
      }
    }

    httpTool.get(getLoanAccountTypeUrl).then(res => {
      if (res.success) {
        state.loanAccountType = res.data;
      }
    });
  });

  return {
    state,
    dto,
    currencyQueryParam,
    loanClientQueryParam,
    contractQueryParam,
    loanAccountQueryParams,
    consignAccountQueryParams,
    boardRateQueryParams,
    payPlanQueryParams,
    handler,
    methods,
    currencyRef,
    cashPoolRef,
    formRef,
    borrowerCnyAcctQueryParams,
    consignCnyAcctQueryParams
  };
}
