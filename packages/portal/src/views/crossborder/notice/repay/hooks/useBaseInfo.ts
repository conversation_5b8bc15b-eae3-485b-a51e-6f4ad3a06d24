import { reactive, computed } from "vue";
import { useConst } from "@ifs/support";

export default function useBaseInfo(form, currency, emits, loanTypeList, LoanContractStatusEnum) {
  const state = reactive({
    baseInfo: {
      id: null,
      officeId: null,
      officeCode: "",
      officeName: "",
      currencyId: null,
      currencyCode: "",
      currencyName: "",
      crossDirection: null, //融通方向
      cashPoolChannelId: null, //资金池通道
      cashPoolChannelName: null, //资金池通道
      loanClientId: null,
      loanClientCode: "",
      loanClientName: "",
      contractId: null,
      contractCode: "",
      contractName: "",
      receiptIdList: null, // 借据ID
      receiptCodes: [],
      receiptCodeList: [], // 借据集合
      consignClientName: null, // 委托单位名称
      loanBusinessType: null, // 贷款业务类型
      loanProductName: null, // 贷款产品名称
      loanType: "",
      productName: "",
      receiptList: [],
      receiptId: null
    }
  });

  const YesOrNoEnum = useConst("common.YesOrNo");
  const LoanTypeEnum = useConst("loancounter.LoanType");

  const currencyQueryParam = computed(() => {
    return { officeId: state.baseInfo.officeId };
  });

  const loanClientQueryParam = computed(() => {
    return {
      clientclass: 1 // 只查内部账户
    };
  });

  const contractQueryParam = computed(() => {
    return {
      crossBorderFlg: YesOrNoEnum.YES,
      loanTypeList: [LoanTypeEnum.CROSS_BORDER],
      officeId: state.baseInfo.officeId,
      currencyId: state.baseInfo.currencyId,
      clientId: state.baseInfo.loanClientId,
      crossDirection: state.baseInfo.crossDirection,
      cashPoolChannelId: state.baseInfo.cashPoolChannelId,
      contractStatusList: [
        LoanContractStatusEnum.NOT_EXECUTE,
        LoanContractStatusEnum.EXECUTING,
        LoanContractStatusEnum.EXTEND,
        LoanContractStatusEnum.OVERDUE,
        LoanContractStatusEnum.DELAY_DEBT,
        LoanContractStatusEnum.BAD_DEBT
      ] // 只查未执行、已执行、已展期、已逾期、呆滞、呆账的数据
    };
  });

  const notePayCodeQueryParam = computed(() => {
    return {
      crossBorderFlg: YesOrNoEnum.YES,
      officeId: state.baseInfo.officeId,
      currencyId: state.baseInfo.currencyId,
      clientId: state.baseInfo.loanClientId,
      contractId: state.baseInfo.contractId,
      loanType: state.baseInfo.loanType
    };
  });

  const methods = {
    setBaseInfo(baseInfo) {
      state.baseInfo.officeId = baseInfo[0].officeId;
      state.baseInfo.officeCode = baseInfo[0].officeCode;
      state.baseInfo.officeName = baseInfo[0].officeName;
      state.baseInfo.currencyId = baseInfo[0].currencyId;
      state.baseInfo.currencyCode = baseInfo[0].currencyCode;
      state.baseInfo.currencyName = baseInfo[0].currencyName;
      state.baseInfo.loanClientId = baseInfo[0].loanClientId;
      state.baseInfo.loanClientCode = baseInfo[0].loanClientCode;
      state.baseInfo.loanClientName = baseInfo[0].loanClientName;
      state.baseInfo.contractId = baseInfo[0].contractId;
      state.baseInfo.contractCode = baseInfo[0].contractCode;
      state.baseInfo.contractName = baseInfo[0].contractName;
      state.baseInfo.loanType = baseInfo[0].loanType; //贷款类型
      state.baseInfo.receiptList = baseInfo; // 借据集合
    },
    setBaseShowInfo(baseInfo) {
      state.baseInfo.officeId = baseInfo.officeId;
      state.baseInfo.officeCode = baseInfo.officeCode;
      state.baseInfo.officeName = baseInfo.officeName;
      state.baseInfo.currencyId = baseInfo.currencyId;
      state.baseInfo.currencyCode = baseInfo.currencyCode;
      state.baseInfo.currencyName = baseInfo.currencyName;
      state.baseInfo.loanClientId = baseInfo.loanClientId;
      state.baseInfo.loanClientCode = baseInfo.loanClientCode;
      state.baseInfo.loanClientName = baseInfo.loanClientName;
      state.baseInfo.contractId = baseInfo.contractId;
      state.baseInfo.contractCode = baseInfo.contractCode;
      state.baseInfo.contractName = baseInfo.contractName;
      state.baseInfo.loanType = baseInfo.loanType; //贷款类型
      state.baseInfo.receiptCodes = baseInfo.receiptCodes; // 借据编号结合
      state.baseInfo.receiptList = baseInfo.clnNoteRepayDetailDtoList; // 借据集合
      state.baseInfo.crossDirection = baseInfo.crossDirection;
      state.baseInfo.cashPoolChannelId = baseInfo.cashPoolChannelId;
      state.baseInfo.consignClientName = baseInfo.consignClientName;
      state.baseInfo.loanBusinessType = baseInfo.loanBusinessType;
      state.baseInfo.productName = baseInfo.productName;
      state.baseInfo.businessCode = baseInfo.businessCode;
    }
  };

  const handler = {
    handleOfficeChange(val, row) {
      state.baseInfo.officeCode = !val ? "" : row.officeCode;
      state.baseInfo.officeName = !val ? "" : row.officeName;

      state.baseInfo.currencyId = null;
      state.baseInfo.currencyCode = "";
      state.baseInfo.currencyName = "";
      state.baseInfo.loanClientId = null;
      state.baseInfo.loanClientCode = "";
      state.baseInfo.loanClientName = "";
      state.baseInfo.contractId = null;
      state.baseInfo.contractCode = "";
      state.baseInfo.contractName = "";
      state.baseInfo.receiptId = null;
      // state.baseInfo.receiptCode = "";

      currency.value.initRemoteData();
    },
    handleCurrencyChange(val, row) {
      state.baseInfo.currencyCode = !val ? "" : row.currencyCode;
      state.baseInfo.currencyName = !val ? "" : row.currencyName;

      state.baseInfo.contractId = null;
      state.baseInfo.contractCode = "";
      state.baseInfo.contractName = "";
      state.baseInfo.receiptId = null;
      // state.baseInfo.receiptCode = "";
    },
    handleLoanClientChange(row) {
      state.baseInfo.loanClientCode = !row || !row.clientId ? "" : row.clientCode;
      state.baseInfo.loanClientName = !row || !row.clientId ? "" : row.clientName;

      state.baseInfo.contractId = null;
      state.baseInfo.contractCode = "";
      state.baseInfo.contractName = "";
      state.baseInfo.receiptId = null;
      // state.baseInfo.receiptCode = "";
    },
    handleContractChange(row) {
      if (row && row.contractId) {
        state.baseInfo.contractCode = row.contractCode;
        state.baseInfo.contractName = row.contractName;
        state.baseInfo.contractAmount = row.contractAmount;
        state.baseInfo.contractBalance = row.contractBalance;
        state.baseInfo.officeId = row.officeId;
        state.baseInfo.officeCode = row.officeCode;
        state.baseInfo.officeName = row.officeName;
        state.baseInfo.currencyId = row.currencyId;
        state.baseInfo.currencyCode = row.currencyCode;
        state.baseInfo.currencyName = row.currencyName;
        state.baseInfo.loanClientId = row.loanClientId;
        state.baseInfo.loanClientCode = row.loanClientCode;
        state.baseInfo.loanClientName = row.loanClientName;
        state.baseInfo.loanType = row.loanType;
        state.baseInfo.crossDirection = row.crossDirection;
        state.baseInfo.cashPoolChannelId = row.cashPoolChannelId;
        state.baseInfo.productName = row.productName;
        state.baseInfo.consignClientId = row.consignClientId;
        state.baseInfo.cashPoolChannelName = row.capitalPoolChannel;
        state.baseInfo.consignClientName = row.consignClientName;
        state.baseInfo.crossDirection = row.crossDirection;
        state.baseInfo.loanBusinessType = row.loanBusinessType;
        state.baseInfo.contractStartDate = row.contractStartDate;
        state.baseInfo.contractEndDate = row.contractEndDate;
      } else {
        state.baseInfo.contractCode = "";
        state.baseInfo.contractName = "";
      }

      state.baseInfo.receiptId = null;
      // state.baseInfo.receiptCode = "";

      emits("receipt-change", state.baseInfo);
    },
    handleNotePayCodeChange(row) {
      // if (!row || !row.receiptCode) {
      //   state.baseInfo.receiptId = null;
      //   state.baseInfo.receiptCode = "";
      // } else {
      methods.setBaseInfo(row);
      // }
      emits("receipt-change", state.baseInfo);
    },
    handleRepayInterestChange(val) {
      emits("repay-interest-change", val);
    },
    handleCrossDirectionChange() {
      //清空合同带出的信息
      state.baseInfo.contractId = null;
      state.baseInfo.contractCode = "";
      state.baseInfo.contractName = "";
      state.baseInfo.receiptCode = [];
      state.baseInfo.consignClientName = "";
      state.baseInfo.loanBusinessType = "";
      state.baseInfo.productName = "";
      emits("receipt-change", state.baseInfo);
    },
    handleCashPoolChannelChange(val, row) {
      state.baseInfo.limitManageDepartment = row.limitManageDepartment;
      state.baseInfo.cashPoolChannelName = row.cashPoolChannel;
      //清空合同带出的信息
      state.baseInfo.contractId = null;
      state.baseInfo.contractCode = "";
      state.baseInfo.contractName = "";
      state.baseInfo.receiptCode = [];
      state.baseInfo.consignClientName = "";
      state.baseInfo.loanBusinessType = "";
      state.baseInfo.productName = "";
      emits("receipt-change", state.baseInfo);
    }
  };
  return {
    state,
    currencyQueryParam,
    loanClientQueryParam,
    contractQueryParam,
    notePayCodeQueryParam,
    handler,
    methods
  };
}
