import { reactive, computed, onBeforeMount, ref } from "vue";
import httpTool from "@/utils/http";
import { FMessage } from "@dtg/frontend-plus";
import { usePage } from "./usePage";
import { FMessageBox } from "@dtg/frontend-plus";
import { useConst } from "@ifs/support";
import {
  getRepayNoticeUrl,
  calculateInterestUrl,
  calChargeAmountUrl,
  openDateUrl,
  batchCalculateInterestUrl
} from "../url";
const payNoticeList = reactive<any[]>([]);
const tableGrid = ref();
const ytTableGrid = ref();
const resetRepayNoticeInfo = obj => {
  Object.assign(obj, {
    id: null,
    officeId: null,
    officeCode: "",
    officeName: "",
    currencyId: null,
    currencyCode: "",
    currencyName: "",
    contractName: "",
    receiptId: null,
    businessCode: "", //放款通知单编号
    businessStatus: "", //放款通知单编号
    businessProcCode: "", //业务处理编码
    loanCounterBusinessType: "", //通知单业务类型
    clnNoteRepayDetailDtoList: [], // 还款通知单明细信息
    clnNoteRepayGroupDetailDtoList: [], // 还款通知单银团分组明细
    loanClientId: null, //借款单位ID
    loanClientCode: "", //借款单位编号
    loanClientName: "", //借款单位名称
    consignClientId: null, //委托方客户ID(委托贷款)
    consignClientCode: "", //委托方客户编号(委托贷款)
    consignClientName: "", //委托方客户名称(委托贷款)
    repayType: "", //还款类型(银团贷款;保理)
    loanBankRole: null, //财务公司贷款行角色(银团贷款)
    agencyBankAccNo: "", //财务公司银行账户号(银团贷款)
    agencyBankAccName: "", //财务公司银行账户名称(银团贷款)
    contractId: null, //合同id
    contractCode: "", //合同编号
    notePayCode: "", //放款通知单编号
    loanType: "", // 贷款类型
    contractAmount: "", //合同金额
    contractBalance: "", //合同余额
    payedAmount: "", //放款单金额
    repayAmount: "", //已还款金额
    contractAvailableAmount: "", //放款通知单余额
    contractStartDate: "", //合同开始日期
    contractEndDate: "", //合同结束日期
    contractTerm: "", //合同期限
    contractExecuteRate: "", //合同执行利率
    repayDate: "", //还款日期
    amount: "", //还款本金
    sumAmount: "", //还款金额合计
    repayPrincipalAcctId: null, //借款人还款账户ID
    repayPrincipalAcctNo: "", //借款人还款账户号
    repayPrincipalAcctName: "", //借款人还款账户名称
    repayInterestFlag: "", //是否归还利息
    remark: "", //备注
    checkReceiptList: [] // 勾选的放款单信息
  });
  return obj;
};

const mergeContractToRepayNotice = (contract, repayNotice) => {
  repayNotice.autoRepayFlag = contract.autoRepayFlag;
  repayNotice.officeId = contract.officeId;
  repayNotice.officeCode = contract.officeCode;
  repayNotice.officeName = contract.officeName;
  repayNotice.currencyId = contract.currencyId;
  repayNotice.currencyCode = contract.currencyCode;
  repayNotice.currencyName = contract.currencyName;
  repayNotice.loanClientId = contract.loanClientId;
  repayNotice.loanClientCode = contract.loanClientCode;
  repayNotice.loanClientName = contract.loanClientName;
  repayNotice.consignClientId = contract.consignClientId;
  repayNotice.consignClientCode = contract.consignClientCode;
  repayNotice.consignClientName = contract.consignClientName;
  repayNotice.contractId = contract.contractId;
  repayNotice.contractCode = contract.contractCode;
  repayNotice.contractAmount = contract.contractAmount; //合同金额
  repayNotice.contractBalance = contract.contractAmount; //合同余额
  repayNotice.productId = contract.productId;
  repayNotice.productCode = contract.productCode;
  repayNotice.productName = contract.productName;
  repayNotice.loanType = contract.loanType;
};

const mergeReceiptToRepayNotice = (receipt, repayNotice) => {
  repayNotice.receiptId = receipt.receiptId;
  repayNotice.notePayCode = receipt.receiptCode;
  repayNotice.payAmount = receipt.receiptAmount;
  repayNotice.receiptBalance = receipt.receiptBalance;
  repayNotice.executeRate = receipt.executeRate;
  repayNotice.payDate = receipt.payDate;
};
export default function useApplication(
  t,
  props,
  form,
  emits,
  baseInfo,
  contractInfo,
  gnteeTableData,
  contractMethods,
  receiptInfo,
  receiptMethods,
  payPlan,
  YesOrNoEnum,
  AccountGroupEnum,
  LoanRepayInterestTypeEnum
) {
  const state = reactive({
    repayNoticeInfo: resetRepayNoticeInfo({}),
    calInterestReadonly: true,
    showChargeAmount: false,
    businessCode: "" // 修改、查看页面在查询借据时，需要传递业务编号，便于核心将该笔单据所预占金额扣减回来
  });
  state.repayNoticeInfo.aheadRepayFlag = YesOrNoEnum.NO;
  state.repayNoticeInfo.repayInterestFlag = YesOrNoEnum.NO;
  state.repayNoticeInfo.freeFeeFlag = YesOrNoEnum.NO;
  state.repayNoticeInfo.freeNormalInterestFlag = YesOrNoEnum.NO;
  state.repayNoticeInfo.freeOverdueInterestFlag = YesOrNoEnum.NO;
  state.repayNoticeInfo.interestCalculateType = LoanRepayInterestTypeEnum.WITH_PRINCIPAL;

  const showGuarantee = computed(
    () =>
      contractInfo.loanType !== "OVERDRAW" &&
      contractInfo.revolvingLoanFlag === "NO" &&
      contractInfo.creditFlag === "YES"
  );

  const gnteeDetailData = computed(() => {
    const data = [...gnteeTableData.value];
    data[2].assure = state.repayNoticeInfo.assureRepayAmount || 0;
    data[2].credit = state.repayNoticeInfo.creditRepayAmount || 0;
    data[2].pledge = state.repayNoticeInfo.pledgeRepayAmount || 0;
    data[2].impawn = state.repayNoticeInfo.impawnRepayAmount || 0;
    data[2].comfort = state.repayNoticeInfo.comfortRepayAmount || 0;
    data[2].margin = state.repayNoticeInfo.marginRepayAmount || 0;
    return data;
  });
  const AllLoanTypeEnum = useConst("loancounter.LoanType");
  // 这块参数需要确认委贷款客户id
  const currentAccountQueryParams = computed(() => {
    return {
      loanType: contractInfo.loanType,
      officeId: contractInfo.officeId,
      officeCode: contractInfo.officeCode,
      currencyId: contractInfo.currencyId,
      currencyCode: contractInfo.currencyCode,
      clientIds: contractInfo.loanClientId,
      clientCode: contractInfo.loanClientCode,
      clientName: contractInfo.loanClientName,
      excludeAccountFrozenSealedType: ["NO_COLLECT_AND_NO_PAY"],
      includeLoanFlag: YesOrNoEnum.YES, //是否包含贷款专户
      accountGroup: AccountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  // 银团收款方银行账户查询参数
  const recBankQueryParams = computed(() => {
    return {
      currencyId: contractInfo.currencyId,
      officeId: contractInfo.officeId,
      clientId: contractInfo.loanClientId,
      accountGroup: AccountGroupEnum.CURRENT // 查询活期类账户
    };
  });

  const calChargeAmount = () => {
    if (
      state.repayNoticeInfo.repayAmount &&
      state.repayNoticeInfo.repayAmount > 0 &&
      state.repayNoticeInfo.repayAmount < state.repayNoticeInfo.receiptBalance &&
      state.repayNoticeInfo.payDate
    ) {
      const param = {
        ruleCode: contractInfo.ruleCode,
        ruleVersion: contractInfo.ruleVersion,
        countPeriodType: contractInfo.feeChargeFreq,
        repayAmount: state.repayNoticeInfo.repayAmount
      };
      httpTool.post(calChargeAmountUrl, param).then(res => {
        if (res.success && res.data) {
          if (res.data > 0) {
            state.showChargeAmount = true;
            state.repayNoticeInfo.feeAmount = res.data;
            state.repayNoticeInfo.realFeeAmount = res.data;
          } else {
            state.showChargeAmount = false;
            state.repayNoticeInfo.feeAmount = 0;
            state.repayNoticeInfo.realFeeAmount = 0;
          }
        } else {
          state.showChargeAmount = false;
          state.repayNoticeInfo.feeAmount = 0;
          state.repayNoticeInfo.realFeeAmount = 0;
        }
      });
    }
  };
  //查看入账流水信息
  const dialogValue = ref(false);
  const handler = {
    handleBaseInfoChange(baseInfo) {
      state.baseInfo = baseInfo;
      resetRepayNoticeInfo(state.repayNoticeInfo);
      httpTool.post(openDateUrl).then((res: any) => {
        state.repayNoticeInfo.repayDate = res.data.onlineDate; //还款日期，默认系统开机日
      });
      state.repayNoticeInfo.aheadRepayFlag = YesOrNoEnum.NO;
      state.repayNoticeInfo.repayInterestFlag = YesOrNoEnum.NO;
      state.repayNoticeInfo.freeFeeFlag = YesOrNoEnum.NO;
      state.repayNoticeInfo.freeNormalInterestFlag = YesOrNoEnum.NO;
      state.repayNoticeInfo.freeOverdueInterestFlag = YesOrNoEnum.NO;
      state.repayNoticeInfo.interestCalculateType = LoanRepayInterestTypeEnum.WITH_PRINCIPAL;
      //记录放款通知单明细信息
      // Object.assign(payNoticeList, baseInfo);
      // payNoticeList.data.push(baseInfo);
      state.repayNoticeInfo.clnNoteRepayDetailDtoList = baseInfo.receiptList;
      contractMethods.renderContractInfo(baseInfo).then(contractInfo => {
        mergeContractToRepayNotice(contractInfo, state.repayNoticeInfo);
      });
      // 合同改变后，回显借据详细信息
      receiptMethods.renderReceiptInfo({ ...baseInfo, businessCode: state.businessCode }).then(receiptInfo => {
        mergeReceiptToRepayNotice(receiptInfo, state.repayNoticeInfo);
      });
    },
    handleRepayInterestChange(val) {
      state.repayNoticeInfo.repayInterestFlag = val;
    },
    handleCurrentAccountChange(row) {
      if (row && row.accountId) {
        state.repayNoticeInfo.currentAccountNo = row.accountCode;
        state.repayNoticeInfo.currentAccountName = row.accountName;
      } else {
        state.repayNoticeInfo.currentAccountNo = "";
        state.repayNoticeInfo.currentAccountName = "";
      }
    },
    handleRepayPrincipalAccountChange(row) {
      if (row && row.accountId) {
        state.repayNoticeInfo.repayPrincipalAcctId = row.accountId;
        state.repayNoticeInfo.repayPrincipalAcctNo = row.accountCode;
        state.repayNoticeInfo.repayPrincipalAcctName = row.accountName;
      } else {
        state.repayNoticeInfo.repayPrincipalAcctId = null;
        state.repayNoticeInfo.repayPrincipalAcctNo = "";
        state.repayNoticeInfo.repayPrincipalAcctName = "";
      }
    },
    handleBankAccountChange(row) {
      if (row && row.accountId) {
        state.repayNoticeInfo.repayPrincipalAcctId = row.accountId;
        state.repayNoticeInfo.repayPrincipalAcctNo = row.accountCode;
        state.repayNoticeInfo.repayPrincipalAcctName = row.accountName;
      } else {
        state.repayNoticeInfo.repayPrincipalAcctId = null;
        state.repayNoticeInfo.repayPrincipalAcctNo = "";
        state.repayNoticeInfo.repayPrincipalAcctName = "";
      }
      if (row) {
        state.repayNoticeInfo.agencyBankAccId = row.bankAccountId;
        state.repayNoticeInfo.agencyBankAccNo = row.bankAccountCode;
        state.repayNoticeInfo.agencyBankAccName = row.bankAccountName;
        state.repayNoticeInfo.agencyOpenBankId = row.openBankId;
        state.repayNoticeInfo.agencyOpenBankName = row.agencyOpenBankName;
      } else {
        state.repayNoticeInfo.agencyBankAccId = "";
        state.repayNoticeInfo.agencyBankAccNo = "";
        state.repayNoticeInfo.agencyBankAccName = "";
        state.repayNoticeInfo.agencyOpenBankId = "";
        state.repayNoticeInfo.agencyOpenBankName = "";
      }
    },
    handleRecBankChange(row) {
      if (row && row.bankAccountId) {
        state.repayNoticeInfo.recBankAcctNo = row.bankAccountCode;
        state.repayNoticeInfo.recBankAcctName = row.bankAccountName;
        state.repayNoticeInfo.recBankId = row.openBankId;
        state.repayNoticeInfo.recBankName = row.openBankName;
      } else {
        state.repayNoticeInfo.recBankAcctNo = "";
        state.repayNoticeInfo.recBankAcctName = "";
        state.repayNoticeInfo.recBankId = null;
        state.repayNoticeInfo.recBankName = "";
      }
    },
    handleCalculateInterest() {
      const params = {
        receiptId: receiptInfo.receiptId,
        receiptCode: receiptInfo.receiptCode,
        principalAmount: state.repayNoticeInfo.repayAmount,
        calculateDate: state.repayNoticeInfo.repayDate,
        calculateType: state.repayNoticeInfo.interestCalculateType
      };
      httpTool.post(calculateInterestUrl, params).then(res => {
        if (res.success) {
          state.repayNoticeInfo.normalInterest = res.data.normalInterest;
          state.repayNoticeInfo.overdueInterest = res.data.overdueInterest;
          state.repayNoticeInfo.oweInterest = res.data.oweInterest;
          state.repayNoticeInfo.totalInterest = res.data.totalInterest;
          state.repayNoticeInfo.realNormalInterest = res.data.normalInterest;
          state.repayNoticeInfo.realOverdueInterest = res.data.overdueInterest;
          state.repayNoticeInfo.realOweInterest = res.data.oweInterest;
          state.repayNoticeInfo.realTotalInterest = res.data.totalInterest;
        }
      });
    },
    handleRepayAmountChange(row) {
      if (row.amount > row.receiptBalance) {
        FMessage.error(t("crossborder.notice.repay.repayAmountShow"));
        row.amount = 0;
      }
      let repayTotalAmount = 0;
      for (let i = 0; i < state.repayNoticeInfo.clnNoteRepayDetailDtoList.length; i++) {
        repayTotalAmount = repayTotalAmount + state.repayNoticeInfo.clnNoteRepayDetailDtoList[i].amount;
      }
      state.repayNoticeInfo.repayTotalAmount = repayTotalAmount;
      calChargeAmount();
    },
    handleFreeFeeFlagChange() {
      if (state.repayNoticeInfo.freeFeeFlag === YesOrNoEnum.YES) {
        state.repayNoticeInfo.realFeeAmount = 0;
      } else {
        state.repayNoticeInfo.realFeeAmount = state.repayNoticeInfo.feeAmount;
      }
    },
    handleRealNormalInterestChange() {
      state.repayNoticeInfo.realTotalInterest =
        (state.repayNoticeInfo.realNormalInterest || 0) +
        (state.repayNoticeInfo.realOverdueInterest || 0) +
        (state.repayNoticeInfo.realOweInterest || 0);
    },
    handleRealOverdueInterestChange() {
      state.repayNoticeInfo.realTotalInterest =
        (state.repayNoticeInfo.realNormalInterest || 0) +
        (state.repayNoticeInfo.realOverdueInterest || 0) +
        (state.repayNoticeInfo.realOweInterest || 0);
    },
    //操作列按钮
    generalGroupButtonOption() {
      return reactive([
        {
          type: "groupDetail",
          originalProps: {
            type: "primary"
          },
          buttonText: t("crossborder.notice.repay.groupDetail"),
          isShow: true,
          emitName: "groupDetail"
        }
      ]);
    },
    setFlaseDiaglogVoteVisible() {
      dialogValue.value = false;
    },
    groupDetail() {
      dialogValue.value = true;
    },
    //计算利息
    handInterest() {
      if (state.repayNoticeInfo.repayDate !== "") {
        if (state.repayNoticeInfo.clnNoteRepayDetailDtoList.length > 0) {
          state.repayNoticeInfo.clnNoteRepayDetailDtoList.forEach(element => {
            element.repayDate = state.repayNoticeInfo.repayDate;
          });
          //调用批量计算利息
          httpTool.post(batchCalculateInterestUrl, state.repayNoticeInfo.clnNoteRepayDetailDtoList).then(res => {
            if (res.success) {
              Object.assign(state.repayNoticeInfo.clnNoteRepayDetailDtoList, res.data);
              let repayTotalInterest = 0;
              for (let i = 0; i < state.repayNoticeInfo.clnNoteRepayDetailDtoList.length; i++) {
                repayTotalInterest =
                  repayTotalInterest + state.repayNoticeInfo.clnNoteRepayDetailDtoList[i].totalInterest;
              }
              state.repayNoticeInfo.repayTotalInterest = repayTotalInterest;
            }
          });
        }
      }
    },
    handleInternalMainAccountChange(row) {
      state.repayNoticeInfo.internalMainAccountCode = row.bankAccountNo;
      state.repayNoticeInfo.internalMainAccountName = row.bankName;
    },
    handleRepayNoticeToUsdRateChange(data) {
      state.repayNoticeInfo.repayNoticeToUsdAmount = (state.repayNoticeInfo.amount * data) / 100;
    },
    handleConsignForeignAcctChange(row) {
      if (row && row.accountCode) {
        state.repayNoticeInfo.consignForeignAcctId = row.accountId;
        state.repayNoticeInfo.consignForeignAcctCode = row.accountCode;
        state.repayNoticeInfo.consignForeignAcctName = row.accountName;
      } else {
        state.repayNoticeInfo.consignForeignAcctId = null;
        state.repayNoticeInfo.consignForeignAcctCode = "";
        state.repayNoticeInfo.consignForeignAcctName = "";
      }
    },

    handleConsignCnyAcctChange(row) {
      if (row && row.accountCode) {
        state.repayNoticeInfo.consignCnyAcctId = row.accountId;
        state.repayNoticeInfo.consignCnyAcctCode = row.accountCode;
        state.repayNoticeInfo.consignCnyAcctName = row.accountName;
      } else {
        state.repayNoticeInfo.consignCnyAcctId = null;
        state.repayNoticeInfo.consignCnyAcctCode = "";
        state.repayNoticeInfo.consignCnyAcctName = "";
      }
    },
    handleBorrowCnyAcctChange(row) {
      if (row && row.accountCode) {
        state.repayNoticeInfo.borrowerCnyAcctId = row.accountId;
        state.repayNoticeInfo.borrowerCnyAcctCode = row.accountCode;
        state.repayNoticeInfo.borrowerCnyAcctName = row.accountName;
      } else {
        state.repayNoticeInfo.borrowerCnyAcctId = null;
        state.repayNoticeInfo.borrowerCnyAcctCode = "";
        state.repayNoticeInfo.borrowerCnyAcctName = "";
      }
    },
    handleConsignCurrentAccountNoChange(row) {
      if (row && row.accountCode) {
        state.repayNoticeInfo.consignCurrentAccountId = row.accountId;
        state.repayNoticeInfo.consignCurrentAccountCode = row.accountCode;
        state.repayNoticeInfo.consignCurrentAccountName = row.accountName;
      } else {
        state.repayNoticeInfo.consignCurrentAccountId = null;
        state.repayNoticeInfo.consignCurrentAccountCode = "";
        state.repayNoticeInfo.consignCurrentAccountName = "";
      }
    },
    handleBorrowForeignAcctChange(row) {
      if (row && row.accountCode) {
        state.repayNoticeInfo.borrowerForeignAcctId = row.accountId;
        state.repayNoticeInfo.borrowerForeignAcctCode = row.accountCode;
        state.repayNoticeInfo.borrowerForeignAcctName = row.accountName;
      } else {
        state.repayNoticeInfo.borrowerForeignAcctId = null;
        state.repayNoticeInfo.borrowerForeignAcctCode = "";
        state.repayNoticeInfo.borrowerForeignAcctName = "";
      }
    }
  };

  const methods = {
    clearInterest() {
      state.repayNoticeInfo.normalInterest = null;
      state.repayNoticeInfo.overdueInterest = null;
      state.repayNoticeInfo.oweInterest = null;
      state.repayNoticeInfo.totalInterest = null;
      state.repayNoticeInfo.realNormalInterest = null;
      state.repayNoticeInfo.realOverdueInterest = null;
      state.repayNoticeInfo.realOweInterest = null;
      state.repayNoticeInfo.realTotalInterest = null;
    },
    getContractInfo(params, cb = () => {}) {
      contractMethods.renderContractInfo(params).then(contractInfo => {
        cb(contractInfo);
      });
    },
    getReceiptInfo(params, cb = () => {}) {
      receiptMethods.renderReceiptInfo({ ...params, businessCode: state.businessCode }).then(receiptInfo => {
        cb(receiptInfo);
      });
    },
    getRepayNoticeInfo(params, cb = () => {}) {
      httpTool.post(getRepayNoticeUrl, params).then(res => {
        if (res.success) {
          Object.assign(state.repayNoticeInfo, res.data);

          baseInfo.value.setBaseShowInfo(res.data);
          cb(res.data);
        }
      });
    },
    viewPayPlay() {
      payPlan.value.open({ contractId: contractInfo.id, contractCode: contractInfo.contractCode });
    },
    getFormData() {
      const b = Object.assign(
        {
          assureRepayAmount: showGuarantee.value ? gnteeDetailData?.value[2]?.assure : null,
          creditRepayAmount: showGuarantee.value ? gnteeDetailData?.value[2]?.credit : null,
          pledgeRepayAmount: showGuarantee.value ? gnteeDetailData?.value[2]?.pledge : null,
          impawnRepayAmount: showGuarantee.value ? gnteeDetailData?.value[2]?.impawn : null,
          comfortRepayAmount: showGuarantee.value ? gnteeDetailData?.value[2]?.comfort : null,
          marginRepayAmount: showGuarantee.value ? gnteeDetailData?.value[2]?.margin : null
        },
        state.repayNoticeInfo,
        state.baseInfo
      );
      return b;
    },
    async validateForm(cb = () => {}) {
      if (state.repayNoticeInfo.checkReceiptList.length <= 0) {
        FMessageBox.report(t("crossborder.notice.repay.clnNoteRepayDetailDtoList"));
        return false;
      }
      if (showGuarantee.value) {
        const row = gnteeDetailData.value[2];
        if (row) {
          const total =
            (row.assure || 0) +
            (row.credit || 0) +
            (row.pledge || 0) +
            (row.impawn || 0) +
            (row.comfort || 0) +
            (row.margin || 0);
          if (total !== state.repayNoticeInfo.repayAmount) {
            FMessage.error(t("crossborder.notice.repay.repayAmountTotal"));
            return false;
          }
          const preRow = gnteeDetailData.value[1];
          if ((row.assure || 0) > (preRow.assure || 0)) {
            FMessage.error(t("crossborder.notice.repay.repayAssureTotal"));
            return false;
          }
          if ((row.credit || 0) > (preRow.credit || 0)) {
            FMessage.error(t("crossborder.notice.repay.repayCreditTotal"));
            return false;
          }
          if ((row.pledge || 0) > (preRow.pledge || 0)) {
            FMessage.error(t("crossborder.notice.repay.repayPledgeTotal"));
            return false;
          }
          if ((row.impawn || 0) > (preRow.impawn || 0)) {
            FMessage.error(t("crossborder.notice.repay.repayImpawnTotal"));
            return false;
          }
          if ((row.comfort || 0) > (preRow.comfort || 0)) {
            FMessage.error(t("crossborder.notice.repay.repayComfortTotal"));
            return false;
          }
          if ((row.margin || 0) > (preRow.margin || 0)) {
            FMessage.error(t("crossborder.notice.repay.repayMarginTotal"));
            return false;
          }
        }
      }

      if (state.repayNoticeInfo.repayInterestFlag === YesOrNoEnum.YES) {
        if (
          state.repayNoticeInfo.freeNormalInterestFlag === YesOrNoEnum.YES &&
          state.repayNoticeInfo.normalInterest >= state.repayNoticeInfo.realNormalInterest
        ) {
          FMessage.error(t("crossborder.notice.repay.realNormalInterestMax"));
          return false;
        }
        if (
          state.repayNoticeInfo.freeNormalInterestFlag === YesOrNoEnum.NO &&
          state.repayNoticeInfo.normalInterest !== state.repayNoticeInfo.realNormalInterest
        ) {
          FMessage.error(t("crossborder.notice.repay.realNormalInterestTotal"));
          return false;
        }

        if (
          state.repayNoticeInfo.freeOverdueInterestFlag === YesOrNoEnum.YES &&
          state.repayNoticeInfo.overdueInterest >= state.repayNoticeInfo.realOverdueInterest
        ) {
          FMessage.error(t("crossborder.notice.repay.realOverdueInterestMin"));
          return false;
        }
        if (
          state.repayNoticeInfo.freeOverdueInterestFlag === YesOrNoEnum.NO &&
          state.repayNoticeInfo.overdueInterest !== state.repayNoticeInfo.realOverdueInterest
        ) {
          FMessage.error(t("crossborder.notice.repay.realOverdueInterestTotal"));
          return false;
        }

        if (state.repayNoticeInfo.oweInterest > state.repayNoticeInfo.realOweInterest) {
          FMessage.error(t("crossborder.notice.repay.realOweInterestMin"));
          return false;
        }

        if (
          state.repayNoticeInfo.normalInterest + state.repayNoticeInfo.oweInterest >
            state.repayNoticeInfo.realNormalInterest + state.repayNoticeInfo.realOweInterest &&
          state.repayNoticeInfo.freeNormalInterestFlag === YesOrNoEnum.NO
        ) {
          Message.error(t("crossborder.notice.repay.realOweInterestTotal"));
          return false;
        }

        if (
          state.repayNoticeInfo.overdueInterest > state.repayNoticeInfo.realOverdueInterest &&
          state.repayNoticeInfo.freeOverdueInterestFlag === YesOrNoEnum.NO
        ) {
          Message.error(t("crossborder.notice.repay.realOweInterestCond"));
          return false;
        }
      }
      if (
        contractInfo.loanType === AllLoanTypeEnum.TRUST_LOAN ||
        contractInfo.loanType === AllLoanTypeEnum.CONSIGN_LOAN
      ) {
        const total = state.repayNoticeInfo.checkReceiptList.reduce((sum, item) => sum + item.amount, 0);
        if (total !== state.repayNoticeInfo.amount) {
          FMessageBox.report(t("crossborder.notice.repay.repayAmountTotal"));
          return false;
        }
      } else if (contractInfo.loanType === AllLoanTypeEnum.SYNDICATED_LOAN) {
        const total = state.repayNoticeInfo.clnNoteRepayGroupDetailDtoList.reduce((sum, item) => sum + item.amount, 0);
        if (total !== state.repayNoticeInfo.amount) {
          FMessageBox.report(t("crossborder.notice.repay.repayAmountTotal"));
          return false;
        }
      }
      return await form.value.form.validate(cb);
    }
  };

  onBeforeMount(() => {
    if (props.id) {
      methods.getRepayNoticeInfo({ id: props.id }, data => {
        emits("on-loaded", state.repayNoticeInfo);
        state.businessCode = data.businessCode;
        methods.getContractInfo({ contractId: data.contractId, contractCode: data.contractCode });
        methods.getReceiptInfo({ receiptCode: data.notePayCode });
      });
    } else {
      const { pageParams } = usePage();
      if (pageParams?.type === "modify") {
        if (pageParams.data) {
          state.businessCode = pageParams.data.businessCode;
          // 查询合同信息
          const contractParams = { contractId: pageParams.data.contractId, contractCode: pageParams.data.contractCode };
          methods.getContractInfo(contractParams);
          // 查询借据信息
          methods.getReceiptInfo({ receiptCode: pageParams.data.notePayCode });
          // 查询通知单信息
          const payNoticeParams = { id: pageParams.data.id, businessCode: pageParams.data.businessCode };
          methods.getRepayNoticeInfo(payNoticeParams, () => {
            emits("on-loaded", state.repayNoticeInfo);
          });
        }
      }
    }
  });

  return {
    state,
    showGuarantee,
    currentAccountQueryParams,
    recBankQueryParams,
    gnteeDetailData,
    handler,
    tableGrid,
    ytTableGrid,
    dialogValue,
    payNoticeList,
    methods
  };
}
