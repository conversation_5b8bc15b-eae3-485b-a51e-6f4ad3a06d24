export default {
  approvalTitle: "还款通知单 - 审批",
  listTitle: "还款通知单 - 链接查找",
  businessCode: "还款通知单编号",
  businessCodeInput: "请选择还款通知单编号",
  officeName: "机构",
  currencyName: "币种",
  loanType: "贷款类型",
  businessType: "业务类型",
  contractCode: "合同编号",
  loanClientCode: "借款单位编号",
  loanClientName: "借款单位名称",
  consignClientCode: "委托单位编号",
  consignClientName: "委托单位名称",
  payAmount: "放款单金额",
  payBalance: "放款单余额",
  compoundInterest: "未结复利",
  freeResidualOweInterestFlag: "免还剩余欠息",
  freeCompoundInterestFlag: "免还剩余复利",
  repayTotalInterest: "利息合计金额",
  repayTotalAmount: "还款金额合计",
  repayType: "还款类型",
  contractAmount: "合同金额",
  amount: "还款本金",
  executeRate: "执行利率",
  greenCredit: "是否绿色信贷",
  businessStatus: "单据状态",
  inputUserName: "录入人",
  inputTime: "录入日期",
  repayAmountArray: "还款金额",
  listedCompany: "是否上市",
  isMechanism: "是否显示机制",
  add: "新增",
  batchDelete: "批量删除",
  addPayNotice: "新增放款通知单",
  goBack: "链接查找",
  doReturn: "返回",
  addTitle: "还款通知单 - 新增",
  contractAmountTitle: "合同金额",
  contractTitle: "合同信息",
  payAvailableAmount: "合同余额",
  contractStartDate: "合同开始日期",
  contractEndDate: "合同结束日期",
  contractTerm: "合同期限",
  payAccountNo: "放款账户账号",
  payAccountName: "放款账户名称",
  payAmountTitle: "放款金额",
  rateRunType: "利率方式",
  floatRateRunType: "浮动利率",
  fixedRateRunType: "固定利率",
  floatingRateId: "利率类型",
  floatingType: "浮动方式",
  floatingRatio: "浮动比例",
  pointType: "基点浮动方式",
  pointFloating: "基点浮动值",
  referenceRateId: "参考利率",
  interestSettTerm: "结息周期",
  interestSettDateFirst: "首次结息日",
  loanStartDate: "贷款开始日期",
  loanEndDate: "贷款结束日期",
  loanTerm: "贷款期限",
  remark: "备注",
  contractCodeInput: "请选择合同编号",
  loanClientCodeInput: "请选择贷款单位编号",
  inGrantAmount: "已发放金额",
  unGrantAmount: "未发放金额",
  repaymentAmount: "已归还金额",
  usageOfLoan: "借款用途",
  delegatePayType: "支付方式",
  interestSettNo: "结息账户",
  interestSettNoInput: "请选择结息账户",
  interestSettClientName: "结息账户客户名称",
  payDate: "放款日期",
  autoRepayFlag: "是否自动还款",
  payAccountNoInput: "请选择放款账户",
  payAmountChinese: "放款金额大写",
  floatingRateIdInput: "请选择利率类型",
  floatingRateCode: "利率编号",
  floatingRateName: "利率名称",
  accountBank: "开户行",
  accountBankInput: "请选择开户行",

  batchSubmit: "批量提交",
  batchRevoke: "批量撤销",
  buyAddFlag: "是否补录",
  yes: "是",
  no: "否",
  settleMethod: "结息方式",
  loanAccountId: "借款单位贷款专户",
  loanAccountIdInput: "请选择借款单位贷款专户",
  loanAccountCode: "贷款专户编号",
  loanAccountName: "贷款专户名称",
  consignCurrentAccountId: "委托方活期账户",
  consignCurrentAccountIdInput: "请选择委托方活期账户",
  recInterestAccountId: "委托方收息账户",
  recInterestAccountIdInput: "请选择委托方收息账户",
  entrustInterestName: "委托方收息客户名称",
  payPlanId: "还款计划",
  payPlanIdInput: "请选择放款计划",
  autoRepayMode: "还本息方式",
  chargeType: "手续费收取方式",
  chargeBasics: "手续费计费基础",
  commissionRate: "手续费率",
  chargeAmount: "应付手续费",
  realChargeAmount: "实付手续费",
  chargeCycle: "手续费收费周期",
  firstChargeDate: "首次收费日期",
  chargeAccountId: "手续费账户号",
  chargeAccountIdInput: "请选择手续费账户",
  chargeAccountName: "手续费账号名称",
  payPlanTitle: "执行计划",
  save: "保存",
  submit: "提交",
  revoke: "撤销",
  delete: "删除",
  close: "关闭",
  viewPayPlay: "查看执行计划",
  deleteConfirm: "确认删除？",
  deleteSuccess: "删除成功！",
  submitConfirm: "确认提交？",
  submitSuccess: "提交成功！",
  revokeConfirm: "确认撤销？",
  revokeSuccess: "撤销成功！",
  notePayCode: "放款通知单编号",
  aheadRepayFlag: "是否提前还款",
  operate: "操作",
  notePayCodeInput: "请选择放款通知单编号",
  repayInterestFlag: "是否归还利息",
  balance: "放款通知单余额",
  repayAmountChinese: "还款金额大写",
  repayDate: "还款日期",
  currentAccountId: "活期存款账户",
  repayPrincipalAcctNo: "借款人还款账户号",
  repayPrincipalAcctName: "借款人还款账户名称",
  normalInterest: "正常利息",
  overdueInterest: "逾期罚息",
  oweInterest: "未结欠息",
  interestCalculateType: "利息计算方式",
  interestCalculate: "计算利息",
  realNormalInterest: "利息金额",
  realOverdueInterest: "逾期罚息金额",
  realOweInterest: "欠息金额",
  realTotalInterest: "利息金额合计",
  freeNormalInterestFlag: "免还剩余利息",
  freeOverdueInterestFlag: "免还剩余罚息",
  ALL_INTEREST: "全部利息",
  WITH_PRINCIPAL: "利随本清",
  currentAccountIdInput: "请选择活期存款账户",
  guaranteeType: "担保类型",
  assure: "保证",
  credit: "信用",
  pledge: "抵押",
  impawn: "质押",
  comfort: "安慰函及其他",
  margin: "保证金",
  revokeBtch: "批量撤销",
  modifyTitle: "还款通知单 - 修改",
  viewTitle: "还款通知单 - 查看",
  calculateInterest: "匡算利息",
  shouldBePaid: "应当支付",
  actualPayment: "实际支付",
  freeFeeFlag: "免还手续费",
  repayPlanId: "还款计划",
  repayPlanIdInput: "请选择还款计划",
  planCode: "计划编号",
  planDate: "计划日期",
  relatedRepayPlans: "关联还款计划",
  guaranteeTypeContract: "合同占用额度",
  guaranteeTypeUnRepay: "未归还额度",
  guaranteeTypeRepay: "本次归还额度",
  repayAmountShow: "还款金额不能大于放款单余额",
  repayAmountTotal: "本次还款单还款金额之和必须等于还款本金",
  repayAssureTotal: "本次归还额度(保证)不能大于未归还额度(保证)！",
  repayCreditTotal: "本次归还额度(信用)不能大于未归还额度(信用)！",
  repayPledgeTotal: "本次归还额度(抵押)不能大于未归还额度(抵押)！",
  repayImpawnTotal: "本次归还额度(质押)不能大于未归还额度(质押)！",
  repayComfortTotal: "本次归还额度(安慰函及其他)不能大于未归还额度(安慰函及其他)！",
  repayMarginTotal: "本次归还额度(保证金)不能大于未归还额度(保证金)！",
  realNormalInterestMax: "实际支付利息金额应小于等于应当支付利息金额！",
  realNormalInterestTotal: "实际支付利息金额应等于应当支付利息金额！",
  realOverdueInterestMin: "实际支付罚息金额应小于应当支付罚息金额！",
  realOverdueInterestTotal: "实际支付罚息金额应等于应当支付罚息金额！",
  realOweInterestMin: "实际支付欠息金额应小于等于应当支付欠息金额！",
  realOweInterestTotal: "实际支付利息不等于应当支付利息时必须勾选免还剩余利息！",
  realOweInterestCond: "实际支付罚息金额不等于应当支付罚息金额时必须勾选免还剩余罚息！",
  fileInfo: "附件详情",
  file: "附件",
  planType: "计划类型",
  planAmount: "计划金额",
  loanRepayPlanType: "类型",
  reayAmountMin: "还款金额必须大于0！",
  repayDateMin: "还款日期不能小于放款日期！",
  month: "月",
  repayNotice: "还款通知单",
  repayNoticeTitle: "还款信息",
  interestNotice: "利息信息",
  guarantee: "担保品信息",
  wfHistoryTitle: "审批历史",
  accountCode: "账户编号",
  accountName: "账户名称",
  recBankAcctId: "开户银行账户编号",
  recBankAcctIdInput: "请选择开户银行账户编号",
  drawOpenAccNo: "开户银行账户编号",
  drawOpenAccName: "开户银行账户名称",
  groupIndex: "分组",
  noRepayAmount: "未转款金额",
  groupDetail: "入账信息",
  financialCompanyBankAccountNo: "财务公司银行账户号",
  loanBankRole: "财务公司贷款行角色",
  relationalRecordedAccountNumber: "账户编号",
  relationalRecordedAccountName: "账户名称",
  relationalRecordedCounterpartyAccountNumber: "对方账号",
  relationalRecordedCounterpartyAccountName: "对方账户名称",
  relationalRecordedCounterpartyBank: "对方开户行银行",
  relationalRecordedTransactionAmount: "交易额",
  relationalRecordedTransactionDate: "交易日",
  relationalRecordedExecutionDate: "执行日期",

  crossDirection: "融通方向",
  cashPoolChannelId: "资金池通道",
  loanBusinessType: "贷款业务类型",
  usdExchangeRate: "折美元汇率",
  usdAmount: "折美元金额",
  loanProductName: "贷款产品名称",
  amountChinese: "金额大写",
  internalMainAccount: "国内主账户",
  bankAccountCode: "银行账号",
  openBankName: "开户行名称",
  consignSettExchangeFlag: "委托人是否结汇",
  partSettExchangeFlag: "是否部分结汇",
  settExchangeAmount: "结汇金额",
  consignForeignAcctId: "委托人外币账户号",
  accountType: "账户类型",
  consignCnyAcctId: "委托人人民币账户号",
  borrowerPurchaseExchangeFlag: "借款人是否购汇",
  partPurchaseExchangeFlag: "是否部分购汇",
  purchaseExchangeAmount: "购汇金额",
  borrowCnyAcctId: "借款人人民币账户号",
  borrowForeignAcctId: "借款人外币账户号",
  consignCurrentAccountNo: "委托人活期账户号",

  baseMessage: "基础信息",
  repayAmount: "还款金额",
  inputDateArray: "录入日期",
  repayTotalAmountArray: "还款总额",
  loanClientCodeList: "借款单位",
  consignClientCodeList: "委托单位",
  loanBizType: "贷款业务类型",
  businessInfo: "单据信息",
  officeInfo: "办理机构",
  repayBaseInfo: "还款基本信息",
  loanClientInfo: "借款单位",
  consignClientInfo: "委托单位",
  repayAmountInfo: "还款金额",
  contractInfo: "合同信息",
  compactLoanTerm: "合同期限",
  rejectionReason: "驳回原因",
  returnPrincipal: "归还本金",
  totalInterest: "利息合计",
  totalRepaymentAmount: "还款总额",
  productName: "贷款产品名称",
  receiptEndDate: "结束日",
  normalRate: "执行利率(%)",
  receiptBalance: "放款单余额",
  compactLoanStartDate: "开始日",
  compactLoanEndDate: "结束日",
  loanTermMonth: "贷款期限",
  noFeeAmount: "未结手续费",
  freeFeeAmountFlag: "免还剩余手续费",
  sumAmount: "还款总额",
  clnNoteRepayDetailDtoList: "请选择需要还款的放款单信息"
};
