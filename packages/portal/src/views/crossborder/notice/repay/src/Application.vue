<template>
  <f-multi-form-panel
    ref="form"
    :model="state.repayNoticeInfo"
    :rules="necessaryRules"
    :column="3"
    :disabled="readonly"
  >
    <f-panel :title="t('crossborder.notice.repay.baseMessage')">
      <BaseInfo
        ref="baseInfo"
        :readonly="baseInfoReadonly"
        @receipt-change="handler.handleBaseInfoChange"
        @repay-interest-change="handler.handleRepayInterestChange"
      />
    </f-panel>
    <!--合同信息-->
    <f-panel :title="t('crossborder.notice.repay.contractTitle')">
      <!-- 合同金额 -->
      <f-form-item prop="contractAmount" :label="t('crossborder.notice.repay.contractAmount')">
        <f-amount v-model="contractInfo.contractAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 合同余额 -->
      <f-form-item prop="payAvailableAmount" :label="t('crossborder.notice.repay.payAvailableAmount')">
        <f-amount v-model="contractInfo.contractBalance" maxlength="15" disabled />
      </f-form-item>
      <!-- 折美元汇率 -->
      <f-form-item prop="toUsdRate" :label="t('crossborder.notice.repay.usdExchangeRate')">
        <f-number v-model="contractInfo.toUsdRate" :precision="4" max="99.9999" min="0.0000" is-rate disabled />
      </f-form-item>
      <!-- 折美元金额 -->
      <f-form-item prop="toUsdAmount" :label="t('crossborder.notice.repay.usdAmount')">
        <f-amount v-model="contractInfo.toUsdAmount" disabled />
      </f-form-item>
      <!-- 合同开始日期 -->
      <f-form-item prop="contractStartDate" :label="t('crossborder.notice.repay.contractStartDate')">
        <f-input v-model="contractInfo.contractStartDate" disabled />
      </f-form-item>
      <!-- 合同期限 -->
      <f-form-item prop="contractTerm" :label="t('crossborder.notice.repay.contractTerm')">
        <f-number v-model="contractInfo.contractTerm" disabled>
          <template #suffix>
            <span>{{ t("crossborder.notice.repay.month") }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 合同结束日期 -->
      <f-form-item prop="contractEndDate" :label="t('crossborder.notice.repay.contractEndDate')">
        <f-input v-model="contractInfo.contractEndDate" disabled />
      </f-form-item>
      <!-- 执行利率 -->
      <f-form-item prop="executeRate" :label="t('crossborder.notice.repay.executeRate')">
        <f-number v-model="contractInfo.executeRate" :precision="4" max="99.9999" min="0.0000" is-rate disabled />
      </f-form-item>
    </f-panel>
    <!--还款信息-->
    <f-panel :title="t('crossborder.notice.repay.repayNoticeTitle')">
      <!-- 还款日期 -->
      <f-form-item prop="repayDate" :label="t('crossborder.notice.repay.repayDate')" :required="necessary">
        <f-date-picker
          v-model="state.repayNoticeInfo.repayDate"
          type="date"
          @change="handleRepayDateChange"
          :disabled-date="disabledFirstInterestDate"
        />
      </f-form-item>
      <!-- 还款本金 -->
      <f-form-item prop="amount" :label="t('crossborder.notice.repay.amount')" :required="necessary">
        <f-amount v-model="state.repayNoticeInfo.amount" maxlength="15" />
      </f-form-item>
      <!-- 金额大写 -->
      <f-form-item prop="amount" :label="t('crossborder.notice.repay.amountChinese')" :required="necessary">
        <f-amount-chinese v-model="state.repayNoticeInfo.amount" disabled />
      </f-form-item>
      <!-- 折美元汇率 -->
      <f-form-item
        v-if="state.baseInfo?.quotaManagement === quotaManagementEnum.SAFE && state.baseInfo?.currencyId !== 2"
        prop="repayNoticeToUsdRate"
        :label="t('crossborder.notice.repay.usdExchangeRate')"
        :required="state.baseInfo?.quotaManagement === quotaManagementEnum.SAFE && state.baseInfo?.currencyId !== 2"
      >
        <f-number
          v-model="state.repayNoticeInfo.repayNoticeToUsdRate"
          :precision="4"
          max="99.9999"
          min="0.0000"
          is-rate
          @change="handler.handleRepayNoticeToUsdRateChange"
        />
      </f-form-item>
      <!-- 折美元金额 -->
      <f-form-item
        v-if="state.baseInfo?.quotaManagement === quotaManagementEnum.SAFE && state.baseInfo?.currencyId !== 2"
        prop="repayNoticeToUsdAmount"
        :label="t('crossborder.notice.repay.usdAmount')"
      >
        <f-amount v-model="state.repayNoticeInfo.repayNoticeToUsdAmount" />
      </f-form-item>
      <!-- 国内主账户 -->
      <f-form-item
        prop="internalMainAccountId"
        :label="t('crossborder.notice.repay.internalMainAccount')"
        :required="true"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.internalMainAccountId"
          :url="queryBankAccount"
          :disabled="baseInfoReadonly"
          :title="t('crossborder.notice.repay.internalMainAccount')"
          auto-init
          method="post"
          row-key="id"
          row-label="bankAccountNo"
          @change="handler.handleInternalMainAccountChange"
        >
          <f-magnifier-column prop="bankAccountNo" :label="t('crossborder.notice.repay.bankAccountCode')" />
          <f-magnifier-column prop="bankName" :label="t('crossborder.notice.repay.openBankName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 开户行名称 -->
      <f-form-item prop="internalMainAccountName" :label="t('crossborder.notice.repay.openBankName')">
        <f-input v-model="state.repayNoticeInfo.internalMainAccountName" disabled />
      </f-form-item>
      <!-- 委托人是否结汇 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.EXTERNAL_LENDING &&
          state.baseInfo?.currencyId !== 1
        "
        :required="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.EXTERNAL_LENDING &&
          state.baseInfo?.currencyId !== 1
        "
        prop="consignSettExchangeFlag"
        :label="t('crossborder.notice.repay.consignSettExchangeFlag')"
      >
        <f-switch
          v-model="state.repayNoticeInfo.consignSettExchangeFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
        />
      </f-form-item>
      <!-- 是否部分结汇 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.EXTERNAL_LENDING &&
          state.baseInfo?.currencyId !== 1 &&
          state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.YES
        "
        :required="state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.YES"
        prop="partSettExchangeFlag"
        :label="t('crossborder.notice.repay.partSettExchangeFlag')"
      >
        <f-switch
          v-model="state.repayNoticeInfo.partSettExchangeFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
        />
      </f-form-item>
      <!-- 结汇金额 -->
      <f-form-item
        v-if="state.repayNoticeInfo.partSettExchangeFlag === YesOrNoEnum.YES"
        :required="state.repayNoticeInfo.partSettExchangeFlag === YesOrNoEnum.YES"
        prop="settExchangeAmount"
        :label="t('crossborder.notice.repay.settExchangeAmount')"
      >
        <f-amount v-model="state.repayNoticeInfo.settExchangeAmount" />
      </f-form-item>
      <!-- 委托人外币账户号 -->
      <f-form-item
        v-if="state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.YES"
        :required="state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.YES"
        prop="consignForeignAcctId"
        :label="t('crossborder.notice.repay.consignForeignAcctId')"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.consignForeignAcctId"
          :title="t('crossborder.notice.repay.consignForeignAcctId')"
          :url="listCurrentAccountUrl"
          method="post"
          row-key="accountId"
          row-label="accountCode"
          @change="handler.handleConsignForeignAcctChange"
          :params="{
            exclusionCurrencyIds: [1],
            clientId: state.baseInfo.consignClientId
          }"
          auto-init
        >
          <f-magnifier-column prop="accountCode" :label="t('crossborder.notice.repay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('crossborder.notice.repay.accountName')" />
          <f-magnifier-column prop="accountTypeName" :label="t('crossborder.notice.repay.accountType')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托人人民币账户号 -->
      <f-form-item
        v-if="state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.YES"
        :required="state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.YES"
        prop="consignCnyAcctId"
        :label="t('crossborder.notice.repay.consignCnyAcctId')"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.consignCnyAcctId"
          :title="t('crossborder.notice.repay.consignCnyAcctId')"
          :url="listCurrentAccountUrl"
          method="post"
          row-key="accountId"
          row-label="accountCode"
          @change="handler.handleConsignCnyAcctChange"
          :params="{
            currencyId: 1
          }"
          auto-init
        >
          <f-magnifier-column prop="accountCode" :label="t('crossborder.notice.repay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('crossborder.notice.repay.accountName')" />
          <f-magnifier-column prop="accountTypeName" :label="t('crossborder.notice.repay.accountType')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人活期账户号(还款账号) -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.EXTERNAL_LENDING ||
          (state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
            state.baseInfo?.currencyId === 1) ||
          (state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
            state.repayNoticeInfo.borrowerPurchaseExchangeFlag === YesOrNoEnum.NO)
        "
        prop="repayPrincipalAcctId"
        :label="t('crossborder.notice.repay.repayPrincipalAcctNo')"
        :required="necessary"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.repayPrincipalAcctId"
          :url="listCurrentAccountUrl"
          :title="t('crossborder.notice.repay.currentAccountId')"
          :placeholder="t('crossborder.notice.repay.currentAccountIdInput')"
          :params="currentAccountQueryParams"
          auto-init
          method="post"
          row-key="accountId"
          row-label="accountCode"
          input-key="accountCode"
          @change="handler.handleRepayPrincipalAccountChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('crossborder.notice.repay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('crossborder.notice.repay.accountName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 借款人是否购汇 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.baseInfo?.currencyId !== 1
        "
        :required="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.baseInfo?.currencyId !== 1
        "
        prop="borrowerPurchaseExchangeFlag"
        :label="t('crossborder.notice.repay.borrowerPurchaseExchangeFlag')"
      >
        <f-switch
          v-model="state.repayNoticeInfo.borrowerPurchaseExchangeFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
        />
      </f-form-item>
      <!-- 是否部分购汇 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.baseInfo?.currencyId !== 1 &&
          state.repayNoticeInfo.borrowerPurchaseExchangeFlag === YesOrNoEnum.YES
        "
        :required="state.repayNoticeInfo.borrowerPurchaseExchangeFlag === YesOrNoEnum.YES"
        prop="partPurchaseExchangeFlag"
        :label="t('crossborder.notice.repay.partPurchaseExchangeFlag')"
      >
        <f-switch
          v-model="state.repayNoticeInfo.partPurchaseExchangeFlag"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
        />
      </f-form-item>
      <!-- 购汇金额 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.baseInfo?.currencyId !== 1 &&
          state.repayNoticeInfo.partPurchaseExchangeFlag === YesOrNoEnum.YES
        "
        :required="state.repayNoticeInfo.partPurchaseExchangeFlag === YesOrNoEnum.YES"
        prop="purchaseExchangeAmount"
        :label="t('crossborder.notice.repay.purchaseExchangeAmount')"
      >
        <f-amount v-model="state.repayNoticeInfo.purchaseExchangeAmount" disabled />
      </f-form-item>
      <!-- 借款人人民币账户号 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.repayNoticeInfo.borrowerPurchaseExchangeFlag === YesOrNoEnum.YES
        "
        :required="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.repayNoticeInfo.partPurchaseExchangeFlag === YesOrNoEnum.YES
        "
        prop="borrowCnyAcctId"
        :label="t('crossborder.notice.repay.borrowCnyAcctId')"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.borrowCnyAcctId"
          :title="t('crossborder.notice.repay.borrowCnyAcctId')"
          :url="listCurrentAccountUrl"
          method="post"
          row-key="accountId"
          row-label="accountCode"
          @change="handler.handleBorrowCnyAcctChange"
          :params="{
            currencyId: 1
          }"
          auto-init
        >
          <f-magnifier-column prop="accountCode" :label="t('crossborder.notice.repay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('crossborder.notice.repay.accountName')" />
          <f-magnifier-column prop="accountTypeName" :label="t('crossborder.notice.repay.accountType')" />
        </f-magnifier-single>
      </f-form-item>
      <!--借款人外币账户号 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.repayNoticeInfo.borrowerPurchaseExchangeFlag === YesOrNoEnum.YES
        "
        :required="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT &&
          state.repayNoticeInfo.partPurchaseExchangeFlag === YesOrNoEnum.YES
        "
        prop="borrowForeignAcctId"
        :label="t('crossborder.notice.repay.borrowForeignAcctId')"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.borrowForeignAcctId"
          :title="t('crossborder.notice.repay.borrowForeignAcctId')"
          :url="listCurrentAccountUrl"
          method="post"
          row-key="accountId"
          row-label="accountCode"
          @change="handler.handleBorrowForeignAcctChange"
          :params="{
            exclusionCurrencyIds: [1]
          }"
          auto-init
        >
          <f-magnifier-column prop="accountCode" :label="t('crossborder.notice.repay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('crossborder.notice.repay.accountName')" />
          <f-magnifier-column prop="accountTypeName" :label="t('crossborder.notice.repay.accountType')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托活期账户 -->
      <f-form-item
        v-if="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT ||
          (LoanCrossBorderDirectionEnum.EXTERNAL_LENDING &&
            state.baseInfo?.currencyId !== 1 &&
            state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.NO) ||
          (LoanCrossBorderDirectionEnum.EXTERNAL_LENDING && state.baseInfo?.currencyId === 1)
        "
        :required="
          state.baseInfo?.crossDirection === LoanCrossBorderDirectionEnum.INTRODUCE_DEBT ||
          (LoanCrossBorderDirectionEnum.EXTERNAL_LENDING &&
            state.baseInfo?.currencyId !== 1 &&
            state.repayNoticeInfo.consignSettExchangeFlag === YesOrNoEnum.NO) ||
          (LoanCrossBorderDirectionEnum.EXTERNAL_LENDING && state.baseInfo?.currencyId === 1)
        "
        prop="consignCurrentAcctNo"
        :label="t('crossborder.notice.repay.consignCurrentAccountNo')"
      >
        <f-magnifier-single
          v-model="state.repayNoticeInfo.consignCurrentAcctNo"
          :url="listCurrentAccountUrl"
          :title="t('crossborder.notice.repay.consignCurrentAccountId')"
          :placeholder="t('crossborder.notice.repay.consignCurrentAccountIdInput')"
          :params="consignAccountQueryParams"
          auto-init
          method="post"
          row-key="accountCode"
          row-label="accountCode"
          input-key="accountCode"
          @change="handler.handleConsignCurrentAccountNoChange"
        >
          <f-magnifier-column prop="accountCode" :label="t('crossborder.notice.repay.accountCode')" />
          <f-magnifier-column prop="accountName" :label="t('crossborder.notice.repay.accountName')" />
        </f-magnifier-single>
      </f-form-item>
      <!--是否归还利息-->
      <f-form-item prop="repayInterestFlag" :label="t('crossborder.notice.repay.repayInterestFlag')">
        <f-radio-group v-model="state.repayNoticeInfo.repayInterestFlag" @change="handler.handleRepayInterestChange">
          <f-radio label="YES">{{ t("crossborder.notice.repay.yes") }}</f-radio>
          <f-radio label="NO">{{ t("crossborder.notice.repay.no") }}</f-radio>
        </f-radio-group>
      </f-form-item>
      <f-form-item>
        <f-button @click="handler.handInterest" type="primary">
          {{ t("crossborder.notice.repay.interestCalculate") }}
        </f-button>
      </f-form-item>
      <!--放款通知单信息-->
      <f-form-item :employ="3">
        <f-query-table
          v-if="isTustLoanType || isConsignType"
          ref="tableGrid"
          row-key="id"
          :table-data="{ data: state.repayNoticeInfo.clnNoteRepayDetailDtoList }"
          :show-query-panel="false"
          :show-export="false"
          :show-print="false"
          :show-layout="false"
          :pagination="false"
          :show-summation-sum="false"
          :show-count-value="false"
          table-type="Record"
          @select="handleCheckedList"
          @select-all="handleCheckedList"
          @clear-selection="handleClearSelection"
        >
          <!--序号-->
          <f-table-column type="selection" />
          <!--放款通知单-->
          <f-table-column prop="receiptCode" width="150px" :label="t('crossborder.notice.repay.notePayCode')" />
          <!--放款单金额-->
          <f-table-column
            prop="receiptAmount"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.payAmount')"
          />
          <!--放款单余额-->
          <f-table-column
            prop="receiptBalance"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.payBalance')"
          />
          <!--还款类型-->
          <f-table-column width="150px" :label="t('crossborder.notice.repay.repayType')">
            <template #default="{ row }">
              <f-scene-view :search="row.repayType" :data="LoanRecPayType" params="value" />
            </template>
          </f-table-column>
          <!--还款金额-->
          <f-table-column prop="amount" width="150px" :label="t('crossborder.notice.repay.repayAmountArray')">
            <template #default="{ row }">
              <f-amount v-model="row.amount" maxlength="15" @blur="handler.handleRepayAmountChange(row)" />
            </template>
          </f-table-column>
          <!--正常利息-->
          <f-table-column
            prop="normalInterest"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.normalInterest')"
          />
          <!--未结欠息-->
          <f-table-column
            prop="oweInterest"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.oweInterest')"
          />
          <!--未结复利-->
          <f-table-column
            prop="compoundInterest"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.compoundInterest')"
          />
          <!--逾期罚息-->
          <f-table-column
            prop="overdueInterest"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.overdueInterest')"
          />
          <!--利息合计-->
          <f-table-column
            prop="totalInterest"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.totalInterest')"
          />
          <!--是否免还剩余利息-->
          <f-table-column width="150px" :label="t('crossborder.notice.repay.freeNormalInterestFlag')">
            <template #default="{ row }">
              <f-switch
                v-model="row.freeNormalInterestFlag"
                :active-value="YesOrNoEnum.YES"
                :inactive-value="YesOrNoEnum.NO"
              />
            </template>
          </f-table-column>
          <!--是否免还剩余罚息-->
          <f-table-column width="150px" :label="t('crossborder.notice.repay.freeOverdueInterestFlag')">
            <template #default="{ row }">
              <f-switch
                v-model="row.freeOverdueInterestFlag"
                :active-value="YesOrNoEnum.YES"
                :inactive-value="YesOrNoEnum.NO"
              />
            </template>
          </f-table-column>
          <!--是否免还剩余欠息-->
          <f-table-column width="150px" :label="t('crossborder.notice.repay.freeResidualOweInterestFlag')">
            <template #default="{ row }">
              <f-switch
                v-model="row.freeResidualOweInterestFlag"
                :active-value="YesOrNoEnum.YES"
                :inactive-value="YesOrNoEnum.NO"
              />
            </template>
          </f-table-column>
          <!--是否免还剩余复利-->
          <f-table-column width="150px" :label="t('crossborder.notice.repay.freeCompoundInterestFlag')">
            <template #default="{ row }">
              <f-switch
                v-model="row.freeCompoundInterestFlag"
                :active-value="YesOrNoEnum.YES"
                :inactive-value="YesOrNoEnum.NO"
              />
            </template>
          </f-table-column>
          <!--未结手续费-->
          <f-table-column
            prop="noFeeAmount"
            width="150px"
            headerAlign="right"
            formatter="amount"
            :label="t('crossborder.notice.repay.noFeeAmount')"
          />
          <!--是否免还剩余手续费-->
          <f-table-column width="150px" :label="t('crossborder.notice.repay.freeFeeAmountFlag')">
            <template #default="{ row }">
              <f-switch
                v-model="row.freeFeeAmountFlag"
                :active-value="YesOrNoEnum.YES"
                :inactive-value="YesOrNoEnum.NO"
              />
            </template>
          </f-table-column>
        </f-query-table>
      </f-form-item>
      <!--还款利息合计-->
      <f-form-item prop="repayTotalInterest" :label="t('crossborder.notice.repay.repayTotalInterest')" disabled>
        <f-amount v-model="state.repayNoticeInfo.repayTotalInterest" maxlength="15" />
      </f-form-item>
      <!--还款金额合计-->
      <f-form-item prop="repayTotalAmount" :label="t('crossborder.notice.repay.repayTotalAmount')" disabled>
        <f-amount v-model="state.repayNoticeInfo.repayTotalAmount" maxlength="15" />
      </f-form-item>
      <!--备注-->
      <f-form-item prop="remark" :label="t('crossborder.notice.repay.remark')" :employ="3">
        <f-textarea v-model="state.repayNoticeInfo.remark" :min-rows="3" />
      </f-form-item>
    </f-panel>
    <f-panel :title="t('crossborder.notice.repay.fileInfo')">
      <!-- 附件 -->
      <f-form-item :label="t('crossborder.notice.repay.file')" :employ="3">
        <f-attm-upload ref="upload" v-model="state.repayNoticeInfo.fileList" :disabled="readonly" drag multiple />
      </f-form-item>
    </f-panel>
    <PayPlan ref="payPlan" />
  </f-multi-form-panel>
</template>
<script setup lang="ts">
import { shallowRef, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useContractInfo from "../hooks/add/useContractInfo";
import useContractGnteeDetail from "../hooks/add/useContractGnteeDetail";
import useReceiptInfo from "../hooks/add/useReceiptInfo";
import useApplication from "../hooks/useApplication";
import useAppValidate from "../hooks/useAppValidate";
import BaseInfo from "./BaseInfo.vue";
import PayPlan from "./PayPlan.vue";
import { listCurrentAccountUrl, queryBankAccount } from "../url";

const { t } = useI18n();

const YesOrNoEnum = useConst("common.YesOrNo");
const LoanRecPayType = useConst("counter.LoanRecPayType");
const AccountGroupEnum = useConst("basic.AccountGroup");
const LoanRepayInterestTypeEnum = useConst("loancounter.LoanRepayInterestType");
const AllLoanTypeEnum = useConst("loancounter.LoanType");
// 资金池通道批复额度
const quotaManagementEnum = useConst("common.LoanCrossBorderQuotaManagement");
// 融通方向
const LoanCrossBorderDirectionEnum = useConst("common.LoanCrossBorderDirection");

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  }
});

const emits = defineEmits(["on-loaded"]);

const form = shallowRef();
const baseInfo = shallowRef();
const payPlan = shallowRef();

const { contractInfo, contractMethods } = useContractInfo();
const gnteeTableData = useContractGnteeDetail(t, contractInfo);
const { receiptInfo, receiptMethods } = useReceiptInfo();

const { state, currentAccountQueryParams, handler, tableGrid, methods } = useApplication(
  t,
  props,
  form,
  emits,
  baseInfo,
  contractInfo,
  gnteeTableData,
  contractMethods,
  receiptInfo,
  receiptMethods,
  payPlan,
  YesOrNoEnum,
  AccountGroupEnum,
  LoanRepayInterestTypeEnum,
  AllLoanTypeEnum
);

const rules = useAppValidate(t, state);

const handleRepayDateChange = () => {
  methods.clearInterest();
};

const baseInfoReadonly = computed(() => props.readonly || state.repayNoticeInfo.id);
// 是否自营贷款
const isTustLoanType = computed(() => contractInfo.loanType === AllLoanTypeEnum.TRUST_LOAN);
// 是否委托贷款
// const isConsignType = computed(() => contractInfo.loanType === AllLoanTypeEnum.CONSIGN_LOAN);
const isConsignType = true;

const necessary = computed(() => !props.readonly);
const necessaryRules = computed(() => (props.readonly ? [] : rules));
const handleCheckedList = list => {
  state.repayNoticeInfo.checkReceiptList = list;
};

const handleClearSelection = () => {
  state.repayNoticeInfo.checkReceiptList.splice(0);
};
defineExpose(methods);
const disabledFirstInterestDate = date => {
  if (!contractInfo.contractStartDate) {
    return true;
  }
  if (!contractInfo.contractEndDate) {
    return true;
  }
  if (date < new Date(contractInfo.contractStartDate + " 00:00:00")) {
    return true;
  }
  if (date > new Date(contractInfo.contractEndDate + " 00:00:00")) {
    return true;
  }
  return false;
};
</script>
