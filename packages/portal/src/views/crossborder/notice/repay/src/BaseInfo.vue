<template>
  <f-form-panel ref="form" :model="state.baseInfo" :rules="rules" :column="3" :disabled="readonly">
    <!-- 还款通知单编号 -->
    <f-form-item
      prop="businessCode"
      :label="t('crossborder.notice.repay.businessCode')"
      v-if="state.baseInfo.id !== null || readonly"
    >
      <f-input v-model="state.baseInfo.businessCode" disabled />
    </f-form-item>
    <!--机构-->
    <f-form-item prop="officeId" :label="t('crossborder.notice.repay.officeName')">
      <f-select
        v-model="state.baseInfo.officeId"
        :url="listOfficeUrl"
        value-key="officeId"
        label="officeName"
        @change="handler.handleOfficeChange"
      />
    </f-form-item>
    <!--币种-->
    <f-form-item prop="currencyId" :label="t('crossborder.notice.repay.currencyName')">
      <f-select
        ref="currency"
        v-model="state.baseInfo.currencyId"
        :url="listCurrencyUrl"
        :extra-data="currencyQueryParam"
        value-key="currencyId"
        label="currencyName"
        @change="handler.handleCurrencyChange"
      />
    </f-form-item>
    <!-- 融通方向 -->
    <f-form-item prop="crossDirection" :label="t('crossborder.notice.repay.crossDirection')" required>
      <f-select
        v-model="state.baseInfo.crossDirection"
        :data="LoanCrossBorderDirectionEnum"
        @change="handler.handleCrossDirectionChange"
      />
    </f-form-item>
    <!-- 资金池通道 -->
    <f-form-item prop="cashPoolChannelId" :label="t('crossborder.notice.repay.cashPoolChannelId')" required>
      <f-select
        v-model="state.baseInfo.cashPoolChannelId"
        :url="cashPoolChannelUrl"
        :disabled="baseInfoReadonly"
        value-key="capitalPoolChannelId"
        label="capitalPoolChannel"
        @change="handler.handleCashPoolChannelChange"
      />
    </f-form-item>
    <!--借款单位名称-->
    <f-form-item prop="loanClientId" :label="t('crossborder.notice.repay.loanClientName')">
      <f-magnifier-single
        v-model="state.baseInfo.loanClientId"
        :url="listLoanClientCodeUrl"
        :title="t('crossborder.notice.repay.loanClientCode')"
        :placeholder="t('crossborder.notice.repay.loanClientCodeInput')"
        :params="loanClientQueryParam"
        :auto-init="true"
        method="post"
        row-key="clientId"
        row-label="clientName"
        input-key="clientCode"
        @change="handler.handleLoanClientChange"
      >
        <f-magnifier-column prop="clientCode" :label="t('crossborder.notice.repay.loanClientCode')" />
        <f-magnifier-column prop="clientName" :label="t('crossborder.notice.repay.loanClientName')" />
      </f-magnifier-single>
    </f-form-item>
    <!--合同放大镜-->
    <f-form-item prop="contractId" :label="t('crossborder.notice.repay.contractCode')">
      <f-magnifier-single
        v-model="state.baseInfo.contractId"
        :url="listContractCodeUrl"
        :title="t('crossborder.notice.repay.contractCode')"
        :placeholder="t('crossborder.notice.repay.contractCodeInput')"
        :params="contractQueryParam"
        auto-init
        method="post"
        row-key="contractId"
        row-label="contractCode"
        input-key="contractCode"
        @change="handler.handleContractChange"
      >
        <!--合同编号-->
        <f-magnifier-column prop="contractCode" :label="t('crossborder.notice.repay.contractCode')" width="180" />
        <!--融通方向-->
        <f-magnifier-column
          prop="crossDirection"
          :label="t('crossborder.notice.repay.crossDirection')"
          :filter-select="LoanCrossBorderDirectionEnum"
        >
          <template #default="{ row }">
            {{ LoanCrossBorderDirectionEnum.find(x => x.code === row.crossDirection)?.label }}
          </template>
        </f-magnifier-column>
        <!--借款单位名称-->
        <f-magnifier-column prop="loanClientName" :label="t('crossborder.notice.repay.loanClientName')" />
        <!--委托单位名称-->
        <f-magnifier-column prop="consignClientName" :label="t('crossborder.notice.repay.consignClientName')" />
        <!--合同金额-->
        <f-magnifier-column
          prop="contractAmount"
          formatter="amount"
          :label="t('crossborder.notice.repay.contractAmount')"
        />
        <!--贷款产品名称-->
        <f-magnifier-column prop="productName" :label="t('crossborder.notice.repay.productName')" width="160" />
        <!--合同结束日-->
        <f-magnifier-column prop="contractEndDate" :label="t('crossborder.notice.repay.loanEndDate')" />
      </f-magnifier-single>
    </f-form-item>
    <!--放款通知单多选放大镜-->
    <f-form-item prop="receiptCode" :label="t('crossborder.notice.repay.notePayCode')" v-if="!readonly" required>
      <f-magnifier-multi
        v-model="state.baseInfo.receiptCode"
        :url="listNotePayCodeUrl"
        :title="t('crossborder.notice.repay.notePayCode')"
        :placeholder="t('crossborder.notice.repay.notePayCodeInput')"
        :params="notePayCodeQueryParam"
        auto-init
        collapse-tags-tooltip
        method="post"
        row-key="receiptCode"
        row-label="receiptCode"
        input-key="receiptCode"
        @change="handler.handleNotePayCodeChange"
      >
        <f-magnifier-column prop="receiptCode" :label="t('crossborder.notice.repay.notePayCode')" />
        <f-magnifier-column prop="contractCode" :label="t('crossborder.notice.repay.contractCode')" />
        <f-magnifier-column
          prop="loanType"
          :label="t('crossborder.notice.repay.loanType')"
          :filter-select="loanTypeEnum"
        >
          <template #default="{ row }">
            {{ loanTypeEnum.find(x => x.code === row.loanType)?.label }}
          </template>
        </f-magnifier-column>
        <f-magnifier-column prop="loanClientName" :label="t('crossborder.notice.repay.loanClientName')" />
        <f-magnifier-column prop="receiptAmount" :label="t('crossborder.notice.repay.payAmount')" />
      </f-magnifier-multi>
    </f-form-item>
    <!-- 借款人还款账户名称 -->
    <f-form-item v-else prop="receiptCodesShow" :label="t('crossborder.notice.repay.notePayCode')">
      <f-input v-model="state.baseInfo.receiptCodes" disabled />
    </f-form-item>
    <!-- 委托单位名称 -->
    <f-form-item prop="consignClientName" :label="t('crossborder.notice.repay.consignClientName')">
      <f-input v-model="state.baseInfo.consignClientName" disabled />
    </f-form-item>
    <!-- 贷款业务类型 -->
    <f-form-item prop="loanBusinessType" :label="t('crossborder.notice.repay.loanBusinessType')">
      <f-scene-view :search="state.baseInfo.loanBusinessType" :data="LoanBusinessCategory" params="value" />
    </f-form-item>
    <!-- 贷款产品名称 -->
    <f-form-item prop="productName" :label="t('crossborder.notice.repay.loanProductName')">
      <f-input v-model="state.baseInfo.productName" disabled />
    </f-form-item>
  </f-form-panel>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import useEnum from "../hooks/useEnum";
import useBaseInfo from "../hooks/useBaseInfo";
import {
  listOfficeUrl,
  listCurrencyUrl,
  listContractCodeUrl,
  listLoanClientCodeUrl,
  listNotePayCodeUrl,
  cashPoolChannelUrl
} from "../url";

const { t } = useI18n();

const LoanContractStatusEnum = useConst("common.LoanContractStatus");
const { loanTypeEnum, loanTypeList, LoanCrossBorderDirectionEnum, LoanBusinessCategory } = useEnum(useConst);

defineProps({
  readonly: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(["receipt-change", "repay-interest-change"]);

const form = shallowRef();
const currency = shallowRef();

const { state, currencyQueryParam, loanClientQueryParam, contractQueryParam, notePayCodeQueryParam, handler, methods } =
  useBaseInfo(form, currency, emits, loanTypeList, LoanContractStatusEnum);

defineExpose(methods);
</script>
