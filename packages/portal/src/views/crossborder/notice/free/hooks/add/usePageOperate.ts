import { reactive, computed, onBeforeMount, nextTick, ref } from "vue";
import { goPage, usePage } from "../usePage";
import httpTool from "@/utils/http";
import { getFreeinfoUrl, getReceiptInfoUrl, openDateUrl } from "../../url";
import type { fileIdList } from "../../types";
import { useDefaultOfficeCurrency } from "@/hooks/biz";

const defaultOfficeAndCurrency = useDefaultOfficeCurrency();

const resetFreeinfo = obj => {
  Object.assign(obj, {
    loanClientId: null, // 借款单位ID
    loanClientCode: "", // 借款单位编号
    loanClientName: "", // 借款单位名称
    consignClientId: null, // 委托单位ID
    consignClientCode: "", // 委托单位编号
    consignClientName: "", // 委托单位名称
    contractCode: "", // 合同编号
    contractId: null, // 合同id
    productId: null, // 产品ID
    productCode: "", // 产品编号
    productName: "", // 产品名称

    contractAmount: null, // 合同金额
    contractBalance: null, // 合同余额
    contractStartDate: "", // 贷款开始日期
    contractEndDate: "", // 贷款结束日期
    contractTerm: null, // 贷款期限(月)

    receiptAmount: null, // 借据金额
    receiptBalance: null, // 借据余额
    repayAmount: null, // 已还金额
    executeRate: null, // 执行利率
    freeDate: "", // 免还日期
    calculateStartDate: null, //计息开始日期
    calculateEndDate: null, //计息结束日期
    loanAcctId: null, //贷款账户
    loanAcctNo: null,
    loanAcctName: null,
    consignAcctId: null, //委存账户
    consignAcctNo: null,
    consignAcctName: null,

    freePrincipalAmount: null, // 免还本金金额
    normalInterest: null, // 应付应计利息
    oweInterest: null, // 应付未结欠息
    overdueInterest: null, // 应付逾期罚息
    freeFeeAmount: null, // 应付手续费
    freeAmountTotal: null, // 应付手续费
    freeCompInterestFlg: null, //未结复利
    compInterest: null, //免还复利
    freeCompInterest: null, //免还未结复利金额
    fileList: [], // 附件列表
    clnLoanContractPlanDtoList: [],
    loanType: "CROSS_BORDER",
    ...defaultOfficeAndCurrency
  });
  return obj;
};

const resetReceiptInfo = obj => {
  Object.assign(obj, {
    receiptAmount: null, // 借据金额
    receiptBalance: null, // 借据余额
    repayAmount: null, // 已还金额
    executeRate: null // 执行利率
  });
  return obj;
};

export default function usePageOperate(props, emits, form, YesOrNoEnum, loanTypeEnum, LoanContractStatusEnum, upload) {
  const state = reactive({
    businessCode: "" // 修改、查看页面在查询借据时，需要传递业务编号，便于核心将该笔单据所预占金额扣减回来
  });

  const freeinfo = reactive(resetFreeinfo({}));

  freeinfo.freePrincipalFlg = YesOrNoEnum.NO;
  freeinfo.freeNormalInterestFlg = YesOrNoEnum.NO;
  freeinfo.freeOweInterestFlg = YesOrNoEnum.NO;
  freeinfo.freeOverdueInterestFlg = YesOrNoEnum.NO;
  freeinfo.freeFeeAmountFlg = YesOrNoEnum.NO;
  freeinfo.freeCompInterestFlg = YesOrNoEnum.NO;
  freeinfo.freePrincipalAmount = 0;
  freeinfo.freeNormalInterest = 0;
  freeinfo.freeOweInterest = 0;
  freeinfo.freeOverdueInterest = 0;
  freeinfo.freeFeeAmount = 0;
  freeinfo.freeAmountTotal = 0;

  const baseInfoQuery = computed(() => {
    return {
      currency: { officeId: freeinfo.officeId },
      loanClient: { clientclass: 1 }, // 只查内部账户
      contract: {
        crossBorderFlg: YesOrNoEnum.YES,
        officeId: freeinfo.officeId,
        currencyId: freeinfo.currencyId,
        loanClientId: freeinfo.loanClientId,
        loanTypeList: [loanTypeEnum.CROSS_BORDER], // 只查委托贷款的合同
        crossDirection: freeinfo.crossDirection,
        contractStatusList: [
          LoanContractStatusEnum.NOT_EXECUTE,
          LoanContractStatusEnum.EXECUTING,
          LoanContractStatusEnum.EXTEND,
          LoanContractStatusEnum.OVERDUE,
          LoanContractStatusEnum.DELAY_DEBT,
          LoanContractStatusEnum.BAD_DEBT
        ] // 只查未执行、已执行、已展期、已逾期、呆滞、呆账的数据
      },
      receipt: {
        crossBorderFlg: YesOrNoEnum.YES,
        officeId: freeinfo.officeId,
        currencyId: freeinfo.currencyId,
        loanClientId: freeinfo.loanClientId,
        crossDirection: freeinfo.crossDirection,
        contractId: freeinfo.contractId,
        receiptStatusList: ["EXECUTING", "OVERDUE"],
        loanType: loanTypeEnum.CROSS_BORDER // 只查委托贷款的借据
      }
    };
  });

  const handler = {
    handleGoBack() {
      goPage("list");
    },
    handleOfficeChange(val, row) {
      freeinfo.officeCode = !val ? "" : row.officeCode;
      freeinfo.officeName = !val ? "" : row.officeName;

      freeinfo.currencyId = null;
      freeinfo.currencyCode = "";
      freeinfo.currencyName = "";
      freeinfo.loanClientId = null;
      freeinfo.loanClientCode = "";
      freeinfo.loanClientName = "";
      freeinfo.contractId = null;
      freeinfo.contractCode = "";
      freeinfo.contractName = "";
      freeinfo.receiptId = null;
      freeinfo.receiptCode = "";
    },
    handleCurrencyChange(val, row) {
      freeinfo.currencyCode = !val ? "" : row.currencyCode;
      freeinfo.currencyName = !val ? "" : row.currencyName;

      freeinfo.contractId = null;
      freeinfo.contractCode = "";
      freeinfo.contractName = "";
      freeinfo.receiptId = null;
      freeinfo.receiptCode = "";
    },
    handleLoanClientChange(row) {
      freeinfo.loanClientCode = !row || !row.clientId ? "" : row.clientCode;
      freeinfo.loanClientName = !row || !row.clientId ? "" : row.clientName;

      freeinfo.contractId = null;
      freeinfo.contractCode = "";
      freeinfo.contractName = "";
      freeinfo.receiptId = null;
      freeinfo.receiptCode = "";
    },
    handleContractChange(row) {
      if (row && row.contractId) {
        freeinfo.contractCode = row.contractCode;
        freeinfo.contractName = row.contractName;
        freeinfo.contractId = row.id;
        freeinfo.crossDirection = row.crossDirection;

        freeinfo.officeId = row.officeId;
        freeinfo.officeCode = row.officeCode;
        freeinfo.officeName = row.officeName;
        freeinfo.currencyId = row.currencyId;
        freeinfo.currencyCode = row.currencyCode;
        freeinfo.currencyName = row.currencyName;
        freeinfo.loanClientId = row.loanClientId;
        freeinfo.loanClientCode = row.loanClientCode;
        freeinfo.loanClientName = row.loanClientName;
        freeinfo.productId = row.productId;
        freeinfo.productCode = row.productCode;
        freeinfo.productName = row.productName;
      } else {
        freeinfo.contractCode = "";
        freeinfo.contractName = "";
        freeinfo.officeId = null;
        freeinfo.officeCode = "";
        freeinfo.officeName = "";
        freeinfo.currencyId = null;
        freeinfo.currencyCode = "";
        freeinfo.currencyName = "";
        freeinfo.loanClientId = null;
        freeinfo.loanClientCode = "";
        freeinfo.loanClientName = "";
        freeinfo.productId = null;
        freeinfo.productCode = "";
        freeinfo.productName = "";
      }

      freeinfo.receiptId = null;
      freeinfo.receiptCode = "";
    },
    handleReceiptChange(row) {
      if (row && row.receiptCode) {
        methods.setBaseInfo(row);
      } else {
        freeinfo.receiptId = null;
        freeinfo.receiptCode = "";
      }
    },
    handleClearBaseChange() {
      freeinfo.receiptId = null;
      freeinfo.receiptCode = null;
      freeinfo.contractId = null;
      freeinfo.contractCode = null;
      freeinfo.productId = null;
      freeinfo.productCode = null;
      freeinfo.productName = null;
      freeinfo.loanClientId = null;
      freeinfo.loanClientCode = null;
      freeinfo.loanClientName = null;
      freeinfo.consignClientId = null;
      freeinfo.consignClientCode = null;
      freeinfo.consignClientName = null;
      //放款通知单金额
      freeinfo.receiptAmount = null;
      //放款通知单余额
      freeinfo.receiptBalance = null;
      freeinfo.payDate = null;
      //执行利率
      freeinfo.executeRate = null;
      freeinfo.receiptStatus = null;
      freeinfo.contractStartDate = null;
      freeinfo.contractEndDate = null;
      freeinfo.contractTerm = null;
      //合同金额
      freeinfo.contractAmount = null;
      //合同余额
      freeinfo.contractBalance = null;
      //已还款金额
      freeinfo.repayAmount = null;
      //贷款账户
      freeinfo.loanAcctId = null;
      freeinfo.loanAcctNo = null;
      freeinfo.loanAcctName = null;
      //委存账户
      freeinfo.consignAcctId = null;
      freeinfo.consignAcctNo = null;
      freeinfo.consignAcctName = null;

      //委存账户
      freeinfo.freePrincipalAmount = 0;
      freeinfo.freeNormalInterest = 0;
      freeinfo.freeOweInterest = 0;
      freeinfo.freeOverdueInterest = 0;
      freeinfo.freeFeeAmount = 0;
      freeinfo.freeAmountTotal = 0;
      freeinfo.businessCode = "";
      freeinfo.freeFeeFlag = "NO";
      freeinfo.feeAmount = null;
      freeinfo.repayPlanId = "";
      freeinfo.freePrincipalAmount = null;
      freeinfo.freeNormalInterestFlag = "NO";
      freeinfo.freeReason = "";
      freeinfo.freeRemark = "";
      freeinfo.businessStatus = "";
      freeinfo.businessType = "";
      freeinfo.loanType = "";
      methods.clearInterest();
    },
    handleBaseChange(row) {
      handler.handleReceiptChange(row);
      methods
        .renderReceiptInfo({
          receiptId: row.receiptId,
          receiptCode: row.receiptCode,
          businessCode: state.businessCode
        })
        .then(receiptInfo => {
          freeinfo.notePayCode = receiptInfo.receiptCode;
          freeinfo.executeRate = receiptInfo.executeRate;
          freeinfo.payAmount = receiptInfo.receiptAmount;
        });
      freeinfo.businessCode = "";
      freeinfo.freeFeeFlag = "NO";
      freeinfo.feeAmount = null;
      freeinfo.repayPlanId = "";
      freeinfo.freePrincipalAmount = null;
      freeinfo.freeNormalInterestFlag = "NO";
      freeinfo.freeReason = "";
      freeinfo.freeRemark = "";
      freeinfo.businessStatus = "";
      freeinfo.businessType = "";
      freeinfo.loanType = "";
      methods.clearInterest();
    },
    handleCrossDirectionChange(val) {
      if (val !== freeinfo.crossDirection) {
        freeinfo.contractCode = "";
      }
    }
  };

  const methods = {
    setBaseInfo(data) {
      freeinfo.crossDirection = data.crossDirection;
      freeinfo.officeId = data.officeId;
      freeinfo.officeCode = data.officeCode;
      freeinfo.officeName = data.officeName;
      freeinfo.currencyId = data.currencyId;
      freeinfo.currencyCode = data.currencyCode;
      freeinfo.currencyName = data.currencyName;
      freeinfo.loanClientId = data.loanClientId;
      freeinfo.loanClientCode = data.loanClientCode;
      freeinfo.loanClientName = data.loanClientName;
      freeinfo.contractId = data.contractId;
      freeinfo.contractCode = data.contractCode;
      freeinfo.contractName = data.contractName;
      freeinfo.receiptId = data.receiptId; // 借据ID
      freeinfo.receiptCode = data.notePayCode || data.receiptCode; // 借据号
      freeinfo.crossDirection = data.crossDirection; //融通方向
      freeinfo.toUsdRate = data.toUsdRate; //折美元汇率
      freeinfo.toUsdAmount = data.toUsdAmount; //折美元金额
    },
    renderReceiptInfo(param) {
      return new Promise(resolve => {
        if (!param) {
          param = {
            receiptId: freeinfo.receiptId, // 借据ID
            receiptCode: freeinfo.receiptCode // 借据编号
          };
        }
        resetReceiptInfo(freeinfo);
        if (param.receiptId || param.receiptCode) {
          httpTool.post(getReceiptInfoUrl, param).then(res => {
            if (res.success && res.data) {
              // Object.assign(freeinfo, res.data);
              freeinfo.receiptId = res.data.receiptId;
              freeinfo.receiptCode = res.data.receiptCode;
              freeinfo.contractId = res.data.contractId;
              freeinfo.contractCode = res.data.contractCode;
              freeinfo.productId = res.data.productId;
              freeinfo.productCode = res.data.productCode;
              freeinfo.productName = res.data.productName;
              freeinfo.loanClientId = res.data.loanClientId;
              freeinfo.loanClientCode = res.data.loanClientCode;
              freeinfo.loanClientName = res.data.loanClientName;
              freeinfo.consignClientId = res.data.consignClientId;
              freeinfo.consignClientCode = res.data.consignClientCode;
              freeinfo.consignClientName = res.data.consignClientName;
              //放款通知单金额
              freeinfo.receiptAmount = res.data.receiptAmount;
              //放款通知单余额
              freeinfo.receiptBalance = res.data.receiptBalance;
              freeinfo.payDate = res.data.payDate;
              //执行利率
              freeinfo.executeRate = res.data.executeRate;
              freeinfo.receiptStatus = res.data.receiptStatus;
              freeinfo.contractStartDate = res.data.contractStartDate;
              freeinfo.contractEndDate = res.data.contractEndDate;
              freeinfo.contractTerm = res.data.contractTerm;
              //合同金额
              freeinfo.contractAmount = res.data.contractAmount;
              //合同余额
              freeinfo.contractBalance = res.data.contractBalance;
              //已还款金额
              freeinfo.repayAmount = res.data.repayAmount;
              //贷款账户
              freeinfo.loanAcctId = res.data.loanAcctId;
              freeinfo.loanAcctNo = res.data.loanAcctNo;
              freeinfo.loanAcctName = res.data.loanAcctName;
              //委存账户
              freeinfo.consignAcctId = res.data.consignAcctId;
              freeinfo.consignAcctId = res.data.consignAcctId;
              freeinfo.consignAcctId = res.data.consignAcctId;

              //委存账户
              freeinfo.freePrincipalAmount = 0;
              freeinfo.freeNormalInterest = 0;
              freeinfo.freeOweInterest = 0;
              freeinfo.freeOverdueInterest = 0;
              freeinfo.freeFeeAmount = 0;
              freeinfo.freeAmountTotal = 0;
              //获取开机日
              httpTool.post(openDateUrl).then((res: any) => {
                freeinfo.freeDate = res.data.onlineDate; //免还日期，默认系统开机日
              });
              resolve(freeinfo);
            } else {
              resolve(freeinfo);
            }
          });
        } else {
          resolve(freeinfo);
        }
      });
    },
    renderFreeInfo(param) {
      return new Promise(resolve => {
        if (!param) {
          param = {
            id: freeinfo.id, // 借据ID
            businessCode: freeinfo.businessCode // 借据编号
          };
        }
        resetFreeinfo(freeinfo);
        freeinfo.freeFeeFlag = YesOrNoEnum.NO;
        freeinfo.freeNormalInterestFlag = YesOrNoEnum.NO;
        if (param.id || param.businessCode) {
          httpTool.post(getFreeinfoUrl, param).then(res => {
            if (res.success && res.data) {
              Object.assign(freeinfo, res.data);
              upload.value.init(res.data.fileIdList);
              resolve(freeinfo);
            } else {
              resolve(freeinfo);
            }
          });
        } else {
          resolve(freeinfo);
        }
      });
    },
    clearInterest() {
      freeinfo.normalInterest = null;
      freeinfo.overdueInterest = null;
      freeinfo.oweInterest = null;
      freeinfo.freeOverdueInterest = null;
    },
    renderAppInfo(row) {
      state.businessCode = row.businessCode;
      methods.setBaseInfo(row);
      methods.renderReceiptInfo({
        receiptId: null,
        receiptCode: row.notePayCode,
        businessCode: row.businessCode
      });
      methods.renderFreeInfo({ id: row.id, businessCode: row.businessCode });
    },
    getFormData() {
      const a = { ...freeinfo, loanType: loanTypeEnum.CROSS_BORDER };
      return a;
    },
    updateFreeVersion(version) {
      nextTick(() => {
        freeinfo.version = version;
      });
    },
    updateFreeInfo(info) {
      nextTick(() => {
        freeinfo.version = info.version;
        freeinfo.id = info.id;
        freeinfo.businessCode = info.businessCode;
        freeinfo.globalSerial = info.globalSerial;
      });
    },
    async validateForm(cb = () => {}) {
      const fileInfos = ref<fileIdList[]>([]);
      fileInfos.value.splice(0);
      fileInfos.value.push(...upload.value.fileData);
      if (fileInfos.value.length > 0) {
        freeinfo.fileIdList = fileInfos.value.map((item: fileIdList) => item.id);
      }
      return await form.value.form.validate(cb);
    }
  };

  onBeforeMount(async () => {
    if (props.id) {
      const freeinfo = await methods.renderFreeInfo({ id: props.id });
      emits("on-loaded", freeinfo);
      state.businessCode = freeinfo.businessCode;
      // methods.setBaseInfo(freeinfo);
      // methods.renderReceiptInfo({
      //     receiptId: null,
      //     receiptCode: freeinfo.notePayCode,
      //     businessCode: state.businessCode
      // });
    } else {
      const { pageParams } = usePage();
      if (pageParams?.type === "modify") {
        if (pageParams.data) {
          methods.renderAppInfo(pageParams.data);
        }
      }
    }
  });

  return { state, freeinfo, baseInfoQuery, handler, methods };
}
