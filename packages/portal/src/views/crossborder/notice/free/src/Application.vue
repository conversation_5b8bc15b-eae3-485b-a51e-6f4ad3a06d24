<template>
  <f-multi-form-panel ref="form" :model="freeinfo" :rules="necessaryRules" :column="3" :disabled="readonly">
    <!-- 基础信息 -->
    <f-panel :title="t('crossborder.notice.free.baseInfo')">
      <!-- 免还编号 -->
      <f-form-item v-if="freeinfo.id" prop="businessCode" :label="t('crossborder.notice.free.businessCode')">
        <f-input v-model="freeinfo.businessCode" disabled />
      </f-form-item>
      <!-- 机构 -->
      <f-form-item prop="officeId" :label="t('crossborder.notice.free.officeName')" required>
        <f-select
          v-model="freeinfo.officeId"
          :url="listOfficeUrl"
          :disabled="baseInfoReadonly"
          value-key="officeId"
          label="officeName"
          @change="handler.handleOfficeChange"
        />
      </f-form-item>
      <!-- 币种 -->
      <f-form-item prop="currencyId" :label="t('crossborder.notice.free.currencyName')" required>
        <f-select
          ref="currency"
          v-model="freeinfo.currencyId"
          :url="listCurrencyUrl"
          :extra-data="baseInfoQuery.currency"
          :disabled="baseInfoReadonly"
          value-key="currencyId"
          label="currencyName"
          @change="handler.handleCurrencyChange"
        />
      </f-form-item>
      <!-- 融通方向 -->
      <f-form-item prop="crossDirection" :label="t('crossborder.notice.free.crossDirection')" required>
        <f-select
          v-model="freeinfo.crossDirection"
          :data="LoanCrossBorderDirectionEnum"
          @change="handler.handleCrossDirectionChange"
        />
      </f-form-item>
      <!-- 借款单位名称 -->
      <f-form-item prop="loanClientId" :label="t('crossborder.notice.free.loanClientName')" required>
        <f-magnifier-single
          v-model="freeinfo.loanClientId"
          :url="listLoanClientCodeUrl"
          :title="t('crossborder.notice.free.loanClientInfo')"
          :params="baseInfoQuery.loanClient"
          :disabled="baseInfoReadonly"
          auto-init
          method="post"
          row-key="clientId"
          row-label="clientName"
          input-key="clientCodeOrName"
          @change="handler.handleLoanClientChange"
        >
          <f-magnifier-column prop="clientCode" :label="t('crossborder.notice.free.clientCode')" />
          <f-magnifier-column prop="clientName" :label="t('crossborder.notice.free.clientName')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 合同编号 -->
      <f-form-item prop="contractId" :label="t('crossborder.notice.free.contractCode')" required>
        <f-magnifier-single
          v-model="freeinfo.contractId"
          :url="listContractCodeUrl"
          :title="t('crossborder.notice.free.loanInfo')"
          :params="baseInfoQuery.contract"
          :disabled="baseInfoReadonly"
          auto-init
          method="post"
          row-key="contractId"
          row-label="contractCode"
          input-key="contractCode"
          @change="handler.handleContractChange"
        >
          <f-magnifier-column prop="contractCode" :label="t('crossborder.notice.free.contractCode')" />
          <f-magnifier-column
            prop="crossDirection"
            :filter-input="false"
            :label="t('crossborder.notice.free.crossDirection')"
            :formatter="{ name: 'const', const: 'common.LoanCrossBorderDirection' }"
          />
          <f-magnifier-column prop="loanClientName" :label="t('crossborder.notice.free.loanClientName')" />
          <f-magnifier-column prop="consignClientName" :label="t('crossborder.notice.free.consignClientName')" />
          <f-magnifier-column
            prop="contractAmount"
            formatter="amount"
            :label="t('crossborder.notice.free.contractAmount')"
          />
          <f-magnifier-column prop="productName" :label="t('crossborder.notice.free.productName')" />
          <f-magnifier-column prop="contractEndDate" :label="t('crossborder.notice.free.contractEndDate')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 放款通知单编号 -->
      <f-form-item prop="receiptCode" :label="t('crossborder.notice.free.receiptId')" required>
        <f-magnifier-single
          v-model="freeinfo.receiptCode"
          :url="listReceiptCodeUrl"
          :title="t('crossborder.notice.free.notePayInfo')"
          :params="baseInfoQuery.receipt"
          :disabled="baseInfoReadonly"
          auto-init
          method="post"
          row-key="receiptCode"
          row-label="receiptCode"
          input-key="receiptCode"
          @change="handler.handleBaseChange"
          @clear="handler.handleClearBaseChange"
        >
          <f-magnifier-column prop="contractCode" :label="t('crossborder.notice.free.contractCode')" />
          <f-magnifier-column prop="receiptCode" :label="t('crossborder.notice.free.notePayCode')" />
          <f-magnifier-column prop="loanClientName" :label="t('crossborder.notice.free.loanClientName')" />
          <f-magnifier-column prop="receiptAmount" formatter="amount" :label="t('crossborder.notice.free.payAmount')" />
          <f-magnifier-column prop="payDate" :label="t('crossborder.notice.free.payDate')" />
        </f-magnifier-single>
      </f-form-item>
      <!-- 委托单位名称 -->
      <f-form-item prop="consignClientName" :label="t('crossborder.notice.free.consignClientName')">
        <f-input v-model="freeinfo.consignClientName" disabled />
      </f-form-item>
    </f-panel>
    <!-- 合同信息 -->
    <f-panel :title="t('crossborder.notice.free.loanInfo')">
      <!-- 合同金额 -->
      <f-form-item prop="contractAmount" :label="t('crossborder.notice.free.contractAmount')">
        <f-amount v-model="freeinfo.contractAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 合同余额 -->
      <f-form-item prop="contractBalance" :label="t('crossborder.notice.free.contractBalance')">
        <f-amount v-model="freeinfo.contractBalance" maxlength="15" disabled />
      </f-form-item>
      <!-- 折美元汇率 -->
      <f-form-item prop="toUsdRate" :label="t('crossborder.notice.free.toUsdRate')">
        <f-number v-model="freeinfo.toUsdRate" :precision="4" max="99.9999" min="0.0000" is-rate disabled />
      </f-form-item>
      <!-- 折美元金额 -->
      <f-form-item prop="toUsdAmount" :label="t('crossborder.notice.free.toUsdAmount')">
        <f-amount v-model="freeinfo.toUsdAmount" disabled />
      </f-form-item>
      <!-- 合同开始日 -->
      <f-form-item prop="loanEndDate" :label="t('crossborder.notice.free.contractStartDate')">
        <f-input v-model="freeinfo.contractStartDate" disabled />
      </f-form-item>
      <!-- 合同期限 -->
      <f-form-item prop="contractTerm" :label="t('crossborder.notice.free.contractTerm')">
        <f-number v-model="freeinfo.contractTerm" disabled>
          <template #suffix>
            <span>{{ t("crossborder.notice.free.month") }}</span>
          </template>
        </f-number>
      </f-form-item>
      <!-- 合同结束日 -->
      <f-form-item prop="loanEndDate" :label="t('crossborder.notice.free.contractEndDate')">
        <f-input v-model="freeinfo.contractEndDate" disabled />
      </f-form-item>
      <!-- 放款日期 -->
      <f-form-item prop="payDate" :label="t('crossborder.notice.free.payDate')">
        <f-date-picker v-model="freeinfo.payDate" type="date" disabled />
      </f-form-item>
      <!-- 放款通知单金额 -->
      <f-form-item prop="receiptAmount" :label="t('crossborder.notice.free.receiptAmount')">
        <f-amount v-model="freeinfo.receiptAmount" maxlength="15" disabled />
      </f-form-item>
      <!-- 已还款金额 -->
      <!-- <f-form-item prop="repayAmount" :label="t('crossborder.notice.free.repayAmount')">
        <f-amount v-model="freeinfo.repayAmount" maxlength="15" disabled />
      </f-form-item> -->
      <!-- 放款通知单余额 -->
      <f-form-item prop="receiptBalance" :label="t('crossborder.notice.free.receiptBalance')">
        <f-amount v-model="freeinfo.receiptBalance" maxlength="15" disabled />
      </f-form-item>
      <!-- 执行利率 -->
      <f-form-item prop="executeRate" :label="t('crossborder.notice.free.executeRate')">
        <f-number v-model="freeinfo.executeRate" :precision="4" max="99.9999" min="0.0000" is-rate disabled />
      </f-form-item>
    </f-panel>

    <!-- 免还信息 -->
    <f-panel :title="t('crossborder.notice.free.freeInfo')">
      <!-- 免还日期 -->
      <f-form-item prop="freeDate" :label="t('crossborder.notice.free.freeDate')" required>
        <f-date-picker v-model="freeinfo.freeDate" type="date" />
      </f-form-item>
      <!-- 免还本金开关 -->
      <f-form-item prop="freePrincipalFlg" :label="t('crossborder.notice.free.freePrincipalFlg')" required>
        <f-switch
          v-model="freeinfo.freePrincipalFlg"
          :active-value="YesOrNoEnum.YES"
          :inactive-value="YesOrNoEnum.NO"
          @change="cleanFreePrincipalAmount"
        />
      </f-form-item>
      <!-- 免还本金金额 -->
      <f-form-item
        prop="freePrincipalAmount"
        :label="t('crossborder.notice.free.freePrincipalAmount')"
        :required="freeinfo.freePrincipalFlg === YesOrNoEnum.YES"
      >
        <f-amount
          v-model="freeinfo.freePrincipalAmount"
          :max="freeinfo.receiptBalance || 0"
          :disabled="freeinfo.freePrincipalFlg !== YesOrNoEnum.YES"
          @change="handleCalculateFreeAmountTotal"
        />
      </f-form-item>
      <!-- 利息计算方式 -->
      <f-form-item prop="interestCalculateType" :label="t('crossborder.notice.free.interestCalculateType')" required>
        <f-select v-model="freeinfo.interestCalculateType" :data="LoanRepayInterestTypeEnum" />
      </f-form-item>
      <!-- 计算利息和费用 -->
      <f-form-item label-width="0">
        <f-button type="primary" @click="handleCalculateInterest">
          {{ t("crossborder.notice.free.calculateInterest") }}
        </f-button>
      </f-form-item>
    </f-panel>

    <!-- 以下内容在归还利息的时候生效 -->
    <f-panel :title="t('crossborder.notice.free.shouldPay')">
      <f-row :gutter="16" style="width: 100%">
        <f-col :span="8">
          <f-card style="height: 230px">
            <f-row :gutter="16" style="width: 100%">
              <f-col :span="24">
                <!-- 应计利息 -->
                <f-form-item prop="normalInterest" :label="t('crossborder.notice.free.normalInterest')" :employ="3">
                  <f-amount v-model="freeinfo.normalInterest" maxlength="15" disabled />
                </f-form-item>
                <!-- 未结欠息 -->
                <f-form-item prop="oweInterest" :label="t('crossborder.notice.free.oweInterest')" :employ="3">
                  <f-amount v-model="freeinfo.oweInterest" maxlength="15" disabled />
                </f-form-item>
                <!-- 未结复利 -->
                <!-- <f-form-item prop="compInterest" :label="t('crossborder.notice.free.compInterest')" :employ="3">
                  <f-amount v-model="freeinfo.compInterest" maxlength="15" disabled />
                </f-form-item> -->
                <!-- 逾期罚息 -->
                <f-form-item prop="overdueInterest" :label="t('crossborder.notice.free.overdueInterest')" :employ="3">
                  <f-amount v-model="freeinfo.overdueInterest" maxlength="15" disabled />
                </f-form-item>
                <!-- 应付手续费 -->
                <f-form-item prop="feeAmount" :label="t('crossborder.notice.free.feeAmount')" :employ="3">
                  <f-amount v-model="freeinfo.feeAmount" maxlength="15" disabled />
                </f-form-item>
                <f-form-item />
              </f-col>
            </f-row>
          </f-card>
        </f-col>
        <f-col :span="16">
          <f-card style="height: 280px">
            <f-row :gutter="16" style="width: 100%">
              <f-col :span="24">
                <!-- 免还利息开关 -->
                <f-form-item
                  prop="freeNormalInterestFlg"
                  :label="t('crossborder.notice.free.freeNormalInterestFlg')"
                  required
                >
                  <f-switch
                    v-model="freeinfo.freeNormalInterestFlg"
                    :active-value="YesOrNoEnum.YES"
                    :inactive-value="YesOrNoEnum.NO"
                    @change="cleanNormalInterest"
                  />
                </f-form-item>
                <!-- 免还利息金额 -->
                <f-form-item
                  prop="freeNormalInterest"
                  :label="t('crossborder.notice.free.freeNormalInterest')"
                  :employ="2"
                >
                  <f-amount
                    v-model="freeinfo.freeNormalInterest"
                    :max="freeinfo.normalInterest || 0"
                    :disabled="freeinfo.freeNormalInterestFlg !== YesOrNoEnum.YES"
                    @change="handleCalculateFreeAmountTotal"
                  />
                </f-form-item>

                <!-- 免还欠息开关 -->
                <f-form-item
                  prop="freeOweInterestFlg"
                  :label="t('crossborder.notice.free.freeOweInterestFlg')"
                  required
                >
                  <f-switch
                    v-model="freeinfo.freeOweInterestFlg"
                    :active-value="YesOrNoEnum.YES"
                    :inactive-value="YesOrNoEnum.NO"
                    @change="cleanFreeOweInterest"
                  />
                </f-form-item>
                <!-- 免还未结欠息金额 -->
                <f-form-item prop="freeOweInterest" :label="t('crossborder.notice.free.freeOweInterest')" :employ="2">
                  <f-amount
                    v-model="freeinfo.freeOweInterest"
                    :max="freeinfo.oweInterest || 0"
                    :disabled="freeinfo.freeOweInterestFlg !== YesOrNoEnum.YES"
                    @change="handleCalculateFreeAmountTotal"
                  />
                </f-form-item>

                <!-- 免还复利 -->
                <!-- <f-form-item
                  prop="freeCompInterestFlg"
                  :label="t('crossborder.notice.free.freeCompInterestFlg')"
                  required
                >
                  <f-switch
                    v-model="freeinfo.freeCompInterestFlg"
                    :active-value="YesOrNoEnum.YES"
                    :inactive-value="YesOrNoEnum.NO"
                    @change="cleanCompInterest"
                  />
                </f-form-item> -->
                <!-- 免还未结复利金额 -->
                <!-- <f-form-item prop="freeCompInterest" :label="t('crossborder.notice.free.freeCompInterest')" :employ="2">
                  <f-amount
                    v-model="freeinfo.freeCompInterest"
                    :max="freeinfo.compInterest || 0"
                    :disabled="freeinfo.freeCompInterestFlg !== YesOrNoEnum.YES"
                    @change="handleCalculateFreeAmountTotal"
                  />
                </f-form-item> -->

                <!-- 免还罚息开关 -->
                <f-form-item
                  prop="freeOverdueInterestFlg"
                  :label="t('crossborder.notice.free.freeOverdueInterestFlg')"
                  required
                >
                  <f-switch
                    v-model="freeinfo.freeOverdueInterestFlg"
                    :active-value="YesOrNoEnum.YES"
                    :inactive-value="YesOrNoEnum.NO"
                    @change="cleanFreeOverdueInterest"
                  />
                </f-form-item>
                <!-- 免还逾期罚息 -->
                <f-form-item
                  prop="freeOverdueInterest"
                  :label="t('crossborder.notice.free.freeOverdueInterest')"
                  :employ="2"
                >
                  <f-amount
                    v-model="freeinfo.freeOverdueInterest"
                    :max="freeinfo.overdueInterest || 0"
                    :disabled="freeinfo.freeOverdueInterestFlg !== YesOrNoEnum.YES"
                    @change="handleCalculateFreeAmountTotal"
                  />
                </f-form-item>

                <!-- 免还手续费开关 -->
                <f-form-item prop="freeFeeAmountFlg" :label="t('crossborder.notice.free.freeFeeAmountFlg')" required>
                  <f-switch
                    v-model="freeinfo.freeFeeAmountFlg"
                    :active-value="YesOrNoEnum.YES"
                    :inactive-value="YesOrNoEnum.NO"
                    @change="cleanFreeFeeAmount"
                  />
                </f-form-item>
                <!-- 免还手续费金额 -->
                <f-form-item prop="freeFeeAmount" :label="t('crossborder.notice.free.freeFeeAmount')" :employ="2">
                  <f-amount
                    v-model="freeinfo.freeFeeAmount"
                    :max="freeinfo.feeAmount || 0"
                    :disabled="freeinfo.freeFeeAmountFlg !== YesOrNoEnum.YES"
                    @change="handleCalculateFreeAmountTotal"
                  />
                </f-form-item>
                <!-- 免还利息合计 -->
                <f-form-item />
                <f-form-item prop="freeAmountTotal" :label="t('crossborder.notice.free.freeAmountTotal')" :employ="2">
                  <f-amount v-model="freeinfo.freeAmountTotal" maxlength="15" disabled />
                </f-form-item>
              </f-col>
            </f-row>
          </f-card>
        </f-col>
      </f-row>
    </f-panel>

    <f-panel>
      <f-form-item prop="freeReason" :label="t('crossborder.notice.free.freeReason')" :employ="3">
        <f-textarea v-model="freeinfo.freeReason" maxlength="300" :max-rows="6" :min-rows="4" />
      </f-form-item>
    </f-panel>
    <f-panel :title="t('crossborder.notice.free.fileInfo')">
      <!-- 附件 -->
      <f-form-item :label="t('crossborder.notice.free.file')" :employ="3">
        <f-attm-upload
          ref="upload"
          v-model="freeinfo.fileIdList"
          :show-upload="!props.readonly"
          :is-show-batch-delete="!props.readonly"
          :is-remove-delete-link="props.readonly"
          drag
          multiple
        />
      </f-form-item>
    </f-panel>
  </f-multi-form-panel>
</template>
<script setup lang="ts">
import { shallowRef, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import usePageOperate from "../hooks/add/usePageOperate";
import useFreeInfoRules from "../hooks/add/useFreeInfoRules";
import {
  listOfficeUrl,
  listCurrencyUrl,
  listLoanClientCodeUrl,
  listContractCodeUrl,
  listReceiptCodeUrl,
  computeChargeUrl,
  calculateInterestUrl
} from "../url";

const { t } = useI18n();

const YesOrNoEnum = useConst("common.YesOrNo");
const loanTypeEnum = useConst("loancounter.LoanType");
const LoanContractStatusEnum = useConst("common.LoanContractStatus");
const LoanRepayInterestTypeEnum = useConst("loancounter.LoanRepayInterestType");
const LoanCrossBorderDirectionEnum = useConst("common.LoanCrossBorderDirection");

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: null
  }
});

const emits = defineEmits(["on-loaded"]);

const form = shallowRef();
const currency = shallowRef();
/** 附件 */
const upload = shallowRef();

const { freeinfo, baseInfoQuery, handler, methods } = usePageOperate(
  props,
  emits,
  form,
  YesOrNoEnum,
  loanTypeEnum,
  LoanContractStatusEnum,
  upload
);

const rules = useFreeInfoRules(t, freeinfo);

// 处理利息匡算部分
const handleCalculateInterest = () => {
  const params = {
    receiptId: freeinfo.receiptId,
    receiptCode: freeinfo.receiptCode,
    calculateDate: freeinfo.freeDate,
    principalAmount: freeinfo.freePrincipalAmount,
    calculateType: freeinfo.interestCalculateType
  };
  httpTool.post(calculateInterestUrl, params).then(res => {
    if (res.success) {
      freeinfo.normalInterest = res.data.normalInterest;
      freeinfo.overdueInterest = res.data.overdueInterest;
      freeinfo.oweInterest = res.data.oweInterest;
      freeinfo.feeAmount = 0;
      // 复利 todo
      freeinfo.compInterest = 0;
      // 计息开始日期
      freeinfo.calculateStartDate = res.data.calculateStartDate;
      // 计息结束日期
      freeinfo.calculateEndDate = res.data.calculateEndDate;

      freeinfo.freeNormalInterest = 0;
      freeinfo.freeOweInterest = 0;
      freeinfo.freeOverdueInterest = 0;
      freeinfo.feeAmount = 0;
      // 复利 todo
      freeinfo.freeCompInterest = 0;
    }
  });
  if (freeinfo.freePrincipalAmount && freeinfo.freeDate && freeinfo.freePrincipalAmount < freeinfo.receiptBalance) {
    httpTool.post(computeChargeUrl, { payNoticeNo: freeinfo.receiptCode, endDate: freeinfo.freeDate }).then(res => {
      if (res.success && res.data && res.data.length > 0) {
        freeinfo.feeAmount = res.data[0].actualChargeAmount;
        freeinfo.freeFeeAmount = 0;
      }
    });
  }
  handleCalculateFreeAmountTotal();
};

const handleCalculateFreeAmountTotal = () => {
  freeinfo.freeAmountTotal =
    (freeinfo.freePrincipalAmount || 0) +
    (freeinfo.freeNormalInterest || 0) +
    (freeinfo.freeOweInterest || 0) +
    (freeinfo.freeOweInterest || 0) +
    (freeinfo.freeCompInterest || 0) +
    (freeinfo.freeFeeAmount || 0);
};

const cleanFreePrincipalAmount = () => {
  if (freeinfo.freePrincipalFlg === YesOrNoEnum.NO) {
    freeinfo.freePrincipalAmount = 0;
  }
  handleCalculateFreeAmountTotal();
};

const cleanNormalInterest = () => {
  if (freeinfo.freeNormalInterestFlag === YesOrNoEnum.NO) {
    freeinfo.freeNormalInterest = 0;
  }
  handleCalculateFreeAmountTotal();
};

const cleanFreeOweInterest = () => {
  if (freeinfo.freeOweInterestFlg === YesOrNoEnum.NO) {
    freeinfo.freeOweInterest = 0;
  }
  handleCalculateFreeAmountTotal();
};

const cleanFreeOverdueInterest = () => {
  if (freeinfo.freeOverdueInterestFlg === YesOrNoEnum.NO) {
    freeinfo.freeOverdueInterest = 0;
  }
  handleCalculateFreeAmountTotal();
};

const cleanFreeFeeAmount = () => {
  if (freeinfo.freeFeeAmountFlg === YesOrNoEnum.NO) {
    freeinfo.freeFeeAmount = 0;
  }
  handleCalculateFreeAmountTotal();
};

const baseInfoReadonly = computed(() => props.readonly || freeinfo.id);
const necessaryRules = computed(() => (props.readonly ? [] : rules));

defineExpose(methods);
</script>
