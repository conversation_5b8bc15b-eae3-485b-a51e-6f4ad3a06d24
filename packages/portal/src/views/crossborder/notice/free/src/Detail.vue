<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('crossborder.notice.free.viewTitle')"
    :isPoint="false"
    destroy-on-close
    @close="closeDrawer"
  >
    <Application ref="appInfo" readonly :id="id" @on-loaded="handleAppLoaded" />
    <f-panel :title="t('crossborder.notice.free.wfHistoryTitle')">
      <f-wf-history :params="wfParams" :is-through="false" />
    </f-panel>
    <template #footer>
      <f-button type="info" plain @click="closeDrawer">{{ t("crossborder.notice.free.close") }}</f-button>
    </template>
  </f-drawer-scene>
</template>

<script setup lang="ts">
import { shallowRef, reactive } from "vue";
import { useBizDetail } from "@/hooks/biz";
import { useI18n } from "vue-i18n";
import Application from "./Application.vue";

const { t } = useI18n();

const props = defineProps({
  id: {
    type: String
  }
});

const appInfo = shallowRef();

const wfParams = reactive({
  systemCode: "Z67",
  transType: "Z670007",
  agencyId: null,
  currencyId: null,
  recordId: null
});

const handleAppLoaded = info => {
  wfParams.agencyId = info.officeId; // 组织ID(机构ID或成员单位ID)
  wfParams.currencyId = info.currencyId; // 币种ID
  wfParams.recordId = info.id; // 业务单据id
};

const initData = () => {};

const { drawerRef, setVisible, closeDrawer } = useBizDetail(props, initData);
defineExpose({ setVisible });
</script>
