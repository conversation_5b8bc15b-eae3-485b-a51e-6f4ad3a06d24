<template>
  <f-query-scene :title="t('crossborder.notice.free.listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="queryForm"
        :url="listPageUrl"
        :export-url="exportPageUrl"
        :table-columns="tableColumns"
        :allow-sort="columnSort"
        :count-label="t('views.record')"
        :count-label-unit="t('views.recordUnit')"
        :summation-biz-label="t('views.record')"
        :summation-biz-unit="t('views.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        :is-single="false"
        table-type="Record"
        :default-sort="defaultSort"
        :post-params="postParams"
        :show-collapse="false"
        :export-exclude="['operate']"
        query-comp-id="crossborder-notice-free-query"
        table-comp-id="crossborder-notice-free-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :status-enum="BusinessStatusEnum"
            :submit-batch-url="submitBatchUrl"
            :delete-batch-url="deleteBatchUrl"
            :revoke-batch-url="revokeBatchUrl"
            @on-add="operateHandler.handleAdd"
          />
        </template>
        <!-- 查询面本表单项 -->
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item prop="officeList" :label="t('crossborder.notice.free.officeName')">
            <f-select
              v-model="queryForm.officeList"
              :url="listOfficeUrl"
              value-key="officeId"
              label="officeName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item prop="currencyList" :label="t('crossborder.notice.free.currencyName')">
            <f-select
              v-model="queryForm.currencyList"
              :url="listCurrencyUrl"
              value-key="currencyId"
              label="currencyName"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 单据状态 -->
          <f-form-item prop="businessStatusList" :label="t('crossborder.notice.free.businessStatus')">
            <f-select
              v-model="queryForm.businessStatusList"
              :data="BusinessStatusEnum"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 免还申请单号 -->
          <f-form-item prop="businessCode" :label="t('crossborder.notice.free.businessCode')">
            <f-input v-model="queryForm.businessCode" />
          </f-form-item>
          <!-- 录入日期 -->
          <f-form-item prop="inputDateArray" :label="t('crossborder.notice.free.inputDateArray')">
            <f-date-picker v-model="queryForm.inputDateArray" type="daterange" />
          </f-form-item>
          <!-- 免还金额 -->
          <f-form-item prop="freeAmount" :label="t('crossborder.notice.free.freeAmountTotalArray')">
            <f-amount-range v-model="queryForm.freeAmount" max="9999999999999.99" tooltip :precision="2" />
          </f-form-item>
          <!-- 借款单位 -->
          <f-form-item prop="loanClientName" :label="t('crossborder.notice.free.loanClientName')">
            <f-magnifier-multi
              :title="t('crossborder.notice.free.loanClientInfo')"
              :url="listLoanClientCodeUrl"
              :params="loanClientQueryParams"
              method="post"
              v-model="queryForm.loanClientIdList"
              row-key="clientId"
              row-label="clientName"
              input-key="clientCodeOrName"
              auto-init
              selected-key="clientId"
              selected-label="clientName"
            >
              <f-magnifier-column prop="clientCode" :label="t('crossborder.notice.free.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('crossborder.notice.free.clientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 委托单位名称 -->
          <f-form-item prop="consignClientName" :label="t('crossborder.notice.free.consignClientName')">
            <f-magnifier-multi
              v-model="queryForm.consignClientIdList"
              :url="listLoanClientCodeUrl"
              :title="t('crossborder.notice.free.loanClientInfo')"
              :params="loanClientQueryParams"
              method="post"
              row-key="clientId"
              row-label="clientName"
              input-key="clientCodeOrName"
              auto-init
              selected-key="clientId"
              selected-label="clientName"
            >
              <f-magnifier-column prop="clientCode" :label="t('crossborder.notice.free.loanClientCode')" />
              <f-magnifier-column prop="clientName" :label="t('crossborder.notice.free.loanClientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 融通方向 -->
          <f-form-item prop="crossDirection" :label="t('crossborder.notice.free.crossDirection')">
            <f-select
              v-model="queryForm.crossDirectionList"
              :data="LoanCrossBorderDirectionEnum"
              value-key="value"
              label="label"
              multiple
              filterable
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 免还类型 -->
          <f-form-item prop="freeTypeList" :label="t('crossborder.notice.free.freeTypeList')">
            <f-select
              v-model="queryForm.freeTypeList"
              :data="freeTypeEnum"
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 合同编号 -->
          <f-form-item prop="contractCode" :label="t('crossborder.notice.free.contractCode')">
            <f-magnifier-multi
              v-model="queryForm.contractCodeList"
              :url="listContractCodeUrl"
              :title="t('crossborder.notice.free.contractCode')"
              :placeholder="t('crossborder.notice.free.contractCodeInput')"
              :params="contractParams"
              method="post"
              row-key="contractCode"
              row-label="contractCode"
              input-key="contractCode"
            >
              <f-magnifier-column prop="contractCode" :label="t('crossborder.notice.free.contractCode')" />
              <f-magnifier-column
                prop="crossDirection"
                :label="t('crossborder.notice.free.crossDirection')"
                :formatter="{ name: 'const', const: 'common.LoanCrossBorderDirection' }"
              />
              <f-magnifier-column prop="loanClientName" :label="t('crossborder.notice.free.loanClientName')" />
              <f-magnifier-column prop="consignClientName" :label="t('crossborder.notice.free.consignClientName')" />
              <f-magnifier-column
                prop="contractAmount"
                formatter="amount"
                :label="t('crossborder.notice.free.contractAmount')"
              />
              <f-magnifier-column prop="productName" :label="t('crossborder.notice.free.productName')" />
              <f-magnifier-column prop="contractEndDate" :label="t('crossborder.notice.free.contractEndDate')" />
            </f-magnifier-multi>
          </f-form-item>
          <!-- 放款通知单号 -->
          <f-form-item prop="receiptCode" :label="t('crossborder.notice.free.receiptId')">
            <f-magnifier-multi
              v-model="queryForm.receiptCodeList"
              :url="listReceiptCodeUrl"
              :title="t('crossborder.notice.free.notePayInfo')"
              :params="receiptCodeQueryParams"
              auto-init
              method="post"
              row-key="receiptCode"
              row-label="receiptCode"
              input-key="receiptCode"
            >
              <f-magnifier-column
                prop="loanType"
                :filter-select="loanTypeEnum.pickConst([loanTypeEnum.CROSS_BORDER])"
                :formatter="row => loanTypeEnum.valueToLabel(row.loanType)"
                :label="t('crossborder.notice.free.loanType')"
              />
              <f-magnifier-column prop="productName" :label="t('crossborder.notice.free.productName')" />
              <f-magnifier-column prop="contractCode" :label="t('crossborder.notice.free.contractCode')" />
              <f-magnifier-column prop="receiptCode" :label="t('crossborder.notice.free.notePayCode')" />
              <f-magnifier-column
                prop="receiptAmount"
                formatter="amount"
                :label="t('crossborder.notice.free.payAmount')"
              />
              <f-magnifier-column prop="payDate" :label="t('crossborder.notice.free.payDate')" />
            </f-magnifier-multi>
          </f-form-item>
        </template>

        <template #link="{ row }">
          <f-button @click="openDetail(row)" link type="primary">
            {{ row.businessCode }}
          </f-button>
        </template>
        <template #businessStatus="{ row }">
          {{ CommonBusinessStatusEnum.find(x => x.code === row.businessStatus)?.label }}
        </template>
        <template #buttons="{ row }">
          <OperateGroup
            :row="row"
            :submit-url="submitUrl"
            :delete-url="deleteUrl"
            :revoke-url="revokeUrl"
            @on-modify="operateHandler.handleModify"
            @operate-success="operateHandler.handleOperateSuccess"
          />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import {
  listPageUrl,
  exportPageUrl,
  submitUrl,
  deleteUrl,
  revokeUrl,
  submitBatchUrl,
  deleteBatchUrl,
  revokeBatchUrl,
  listOfficeUrl,
  listCurrencyUrl,
  listLoanClientCodeUrl,
  listContractCodeUrl,
  listReceiptCodeUrl
} from "../url";
import useEnum from "../hooks/useEnum";
import useQueryForm from "../hooks/list/useQueryForm";
import useTableColumns from "../hooks/list/useTableColumns";
import useListPageOperate from "../hooks/list/useListPageOperate";
import OperateGroup from "../../../components/OperateGroup.vue";
import OperateBatchGroup from "../../../components/OperateBatchGroup.vue";
import Detail from "./Detail.vue";
import { useListDetail } from "@/hooks/biz";

const { t } = useI18n();

const queryTable = shallowRef();
const operateGroup = shallowRef();

const CommonBusinessStatusEnum = useConst("common.BusinessStatus");
const LoanCrossBorderDirectionEnum = useConst("common.LoanCrossBorderDirection");
const { loanTypeList, freeTypeEnum, BusinessStatusEnum, loanTypeEnum } = useEnum(useConst);
const { queryForm, loanClientQueryParams, postParams } = useQueryForm();
const { tableColumns, columnSort, defaultSort } = useTableColumns(t);
const { operateHandler } = useListPageOperate(queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};

const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const contractParams = {
  loanTypeList
};

const receiptCodeQueryParams = {
  loanType: loanTypeEnum.CROSS_BORDER // 只查委托贷款的借据
};

const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};
</script>
