<template>
  <f-query-scene :title="t('forex.report.monthsell.title')">
    <template #query-table>
      <f-query-table
        ref="queryTable"
        row-key="projectCode"
        query-comp-id="forex.report.monthsell-query-001"
        table-comp-id="forex.report.monthsell-table-001"
        :url="listUrl"
        border
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :show-export="true"
        :summation-biz-label="t('forex.report.monthsell.record')"
        :summation-biz-unit="t('forex.report.monthsell.recordUnit')"
        :tile-panel="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :pagination="false"
        :show-layout="false"
        @on-failed="requestErr"
        @print="printData"
        :beforeDownload="beforeDownloadCheck"
      >
        <f-table-column prop="projectCode" :label="t('forex.report.monthsell.projectCodeView')" />
        <f-table-column
          prop="projectName"
          :label="t('forex.report.monthsell.projectNameView')"
          :formatter="{ name: 'indent', key: 'indentLevel' }"
        />
        <f-table-column :label="t('forex.report.monthsell.bankSelfView')">
          <f-table-column prop="bankSelf" label="01" headerAlign="right" formatter="amount" />
        </f-table-column>

        <f-table-column :label="t('forex.report.monthsell.bankCusView')">
          <f-table-column :label="t('forex.report.monthsell.financeOrganView')">
            <f-table-column prop="financeOrgan" label="02" headerAlign="right" formatter="amount" />
          </f-table-column>
          <f-table-column :label="t('forex.report.monthsell.chineseOrganView')">
            <f-table-column prop="chineseOrgan" label="03" headerAlign="right" formatter="amount" />
          </f-table-column>
          <f-table-column :label="t('forex.report.monthsell.foreignOrganView')">
            <f-table-column prop="foreignOrgan" label="04" headerAlign="right" formatter="amount" />
          </f-table-column>
          <f-table-column :label="t('forex.report.monthsell.residentPersonView')">
            <f-table-column prop="residentPerson" label="05" headerAlign="right" formatter="amount" />
          </f-table-column>
          <f-table-column :label="t('forex.report.monthsell.noResidentPersonView')">
            <f-table-column prop="noResidentPerson" label="06" headerAlign="right" formatter="amount" />
          </f-table-column>
        </f-table-column>

        <template #query-panel>
          <f-form-item :label="t('forex.report.monthsell.officeView')" prop="officeId" :required="true">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              @change="officeChange"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.monthsell.reportYearMonthView')" prop="reportYearMonth" :required="true">
            <f-date-picker
              v-model="queryFrom.reportYearMonth"
              type="month"
              :widgetInit="getOpenDate"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.monthsell.benchmarkUnitView')" prop="benchmarkUnit" :required="true">
            <f-select v-model="queryFrom.benchmarkUnit" :data="benchmarkUnit" />
          </f-form-item>
        </template>
      </f-query-table>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { listUrl, officeListUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
const { t } = useI18n();

const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

const { queryFrom, queryTable, getOpenDate, officeChange, requestErr, printData, beforeDownloadCheck } = useList();
</script>
