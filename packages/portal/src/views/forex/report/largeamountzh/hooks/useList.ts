import { useI18n } from "vue-i18n";
import { reactive, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { printUrl } from "../url";
import { FMessageBox } from "@dtg/frontend-plus";
import { formatDate } from "@/utils/date";
import { addOrSubtractDays } from "@/utils/date";
import printJS from "print-js";
import { useOpenDate } from "@/hooks";

export const useList = () => {
  const { t } = useI18n();

  const { openDate } = useOpenDate();

  const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

  // 表格查询对象
  const queryFrom = reactive({
    officeId: null,
    officeName: null,
    reportDate: formatDate(addOrSubtractDays(openDate.value, -1)),
    benchmarkUnit: benchmarkUnit.TEN_THOUSAND_DOLLAR,
    openDate: openDate.value
  });

  // 表格模板
  const queryTable = shallowRef();

  const officeChange = (value: any, info: any) => {
    queryFrom.officeName = info.officeName;
  };

  const requestErr = data => {
    FMessageBox.alert(data.message.description, {
      confirmButtonText: t("forex.report.largeamountzh.close")
    });
    queryTable.value.getTableData().splice(0);
  };

  const printData = () => {
    if (queryTable.value.getTableData().length > 0) {
      httpTool
        .getDownloadData(printUrl, queryFrom, { noLoading: false })
        .then((res: any) => {
          if (res.data.type === "application/json") {
            blobToString(res.data).then(result => {
              const resData = JSON.parse(result);
              if (resData.success) {
                FMessageBox.report({ type: "success", message: resData.data });
              } else {
                FMessageBox.report({ type: "error", message: resData.message.description });
              }
            });
          } else {
            printJS(window.URL.createObjectURL(res.data));
          }
        })
        .catch(res => {
          if (res.data && res.data.description) {
            FMessageBox.report({ type: "error", message: res.data.description });
          } else {
            FMessageBox.report({ type: "error", message: t("common.print.printError") });
          }
        });
    }
  };

  const blobToString = (blob: Blob) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsText(blob);
    });
  };

  const beforeDownloadCheck = () => {
    if (queryFrom.officeId === null || queryFrom.officeId === "" || queryFrom.officeId === undefined) {
      FMessageBox.report(t("forex.report.largeamountzh.officeViewPlaceholder"));
      return false;
    }
    if (queryFrom.reportDate === null || queryFrom.reportDate === "" || queryFrom.reportDate === undefined) {
      FMessageBox.report(t("forex.report.largeamountzh.reportDateViewPlaceholder"));
      return false;
    }
    return true;
  };

  return {
    queryFrom,
    queryTable,
    officeChange,
    requestErr,
    printData,
    beforeDownloadCheck
  };
};
export default useList;
