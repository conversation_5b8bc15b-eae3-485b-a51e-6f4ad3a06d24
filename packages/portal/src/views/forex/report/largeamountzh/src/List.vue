<template>
  <f-query-scene :title="t('forex.report.largeamountzh.title')">
    <template #query-table>
      <f-query-table
        ref="queryTable"
        row-key="projectCode"
        query-comp-id="forex.report.largeamountzh-query-001"
        table-comp-id="forex.report.largeamountzh-table-001"
        :url="listUrl"
        border
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :show-export="true"
        :summation-biz-label="t('forex.report.largeamountzh.record')"
        :summation-biz-unit="t('forex.report.largeamountzh.recordUnit')"
        :tile-panel="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :pagination="false"
        :show-layout="false"
        @print="printData"
        :beforeDownload="beforeDownloadCheck"
      >
        <f-table-column headerAlign="center">
          <f-table-column prop="bankName" :label="t('forex.report.largeamountzh.bankName')" />
        </f-table-column>
        <f-table-column :label="t('forex.report.largeamountzh.sellTitle')" headerAlign="center">
          <f-table-column prop="sellTransType" :label="t('forex.report.largeamountzh.transTypeView')" />
          <f-table-column prop="sellClientName" :label="t('forex.report.largeamountzh.clientNameView')" />
          <f-table-column
            prop="sellTransNature"
            :label="t('forex.report.largeamountzh.transNatureView')"
            :formatter="{ name: 'const', const: 'forex.TransNature' }"
          />
          <f-table-column prop="sellCurrencyName" :label="t('forex.report.largeamountzh.currencyNameView')" />
          <f-table-column
            prop="sellAmount"
            :label="t('forex.report.largeamountzh.amountView')"
            align="right"
            width="120"
          >
            <template #default="{ row }">
              <div :style="{ color: '#FF9E00' }">
                {{
                  row.sellAmount === null || row.sellAmount === undefined
                    ? null
                    : format(row.sellAmount, { precision: 2, symbol: "" })
                }}
              </div>
            </template>
          </f-table-column>
          <f-table-column prop="sellTransVariety" :label="t('forex.report.largeamountzh.transVarietyView')" />
          <f-table-column prop="sellRemark" :label="t('forex.report.largeamountzh.remarkView')" />
        </f-table-column>
        <f-table-column :label="t('forex.report.largeamountzh.buyTitle')" headerAlign="center">
          <f-table-column prop="buyTransType" :label="t('forex.report.largeamountzh.transTypeView')" />
          <f-table-column prop="buyClientName" :label="t('forex.report.largeamountzh.clientNameView')" />
          <f-table-column
            prop="buyTransNature"
            :label="t('forex.report.largeamountzh.transNatureView')"
            :formatter="{ name: 'const', const: 'forex.TransNature' }"
          />
          <f-table-column prop="buyCurrencyName" :label="t('forex.report.largeamountzh.currencyNameView')" />
          <f-table-column
            prop="buyAmount"
            :label="t('forex.report.largeamountzh.amountView')"
            align="right"
            width="120"
          >
            <template #default="{ row }">
              <div :style="{ color: '#FF9E00' }">
                {{
                  row.buyAmount === null || row.buyAmount === undefined
                    ? null
                    : format(row.buyAmount, { precision: 2, symbol: "" })
                }}
              </div>
            </template>
          </f-table-column>
          <f-table-column prop="buyTransVariety" :label="t('forex.report.largeamountzh.transVarietyView')" />
          <f-table-column prop="butRemark" :label="t('forex.report.largeamountzh.remarkView')" />
        </f-table-column>

        <template #query-panel>
          <f-form-item :label="t('forex.report.largeamountzh.officeView')" prop="officeId" :required="true">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              @change="officeChange"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.largeamountzh.reportDateView')" prop="reportDate" :required="true">
            <f-date-picker
              v-model="queryFrom.reportDate"
              type="date"
              :widgetInit="getOpenDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.largeamountzh.benchmarkUnitView')" prop="benchmarkUnit" :required="true">
            <f-select v-model="queryFrom.benchmarkUnit" :data="benchmarkUnit" />
          </f-form-item>
        </template>
      </f-query-table>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { listUrl, officeListUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { format } from "@/utils/currency";

const { t } = useI18n();

const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

const { queryFrom, queryTable, getOpenDate, officeChange, printData, beforeDownloadCheck } = useList();
</script>
