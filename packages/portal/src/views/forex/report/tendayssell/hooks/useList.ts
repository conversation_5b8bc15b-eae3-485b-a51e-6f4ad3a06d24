import { useI18n } from "vue-i18n";
import { reactive, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { openDateUrl, printUrl } from "../url";
import { FMessageBox } from "@dtg/frontend-plus";
import printJS from "print-js";

export const useList = () => {
  const { t } = useI18n();
  const tenDays = useConst("forex.TenDays");
  const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

  // 表格配置
  const tableColumns = [
    {
      prop: "projectCode",
      label: t("forex.report.tendayssell.projectCodeView")
    },
    {
      prop: "projectName",
      label: t("forex.report.tendayssell.projectNameView"),
      formatter: { name: "indent", key: "indentLevel" }
    },
    {
      prop: "amount",
      label: t("forex.report.tendayssell.amountView"),
      headerAlign: "right",
      formatter: "amount"
    }
  ];

  // 表格查询对象
  const queryFrom = reactive({
    officeId: null,
    officeName: null,
    reportYearMonth: null,
    tenDays: null,
    benchmarkUnit: benchmarkUnit.TEN_THOUSAND_DOLLAR,
    openDate: null
  });
  // 表格模板
  const queryTable = shallowRef();

  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(openDateUrl).then((res: any) => {
        resolve(res.data.onlineDate);
        queryFrom.reportYearMonth = res.data.onlineDate.substring(0, 7);
        queryFrom.openDate = res.data.onlineDate.substring(0, 10);
        //计算当前日期所在旬
        tenDaysComputer(queryFrom.openDate);
      });
    });
  };

  const tenDaysComputer = (value: any) => {
    const day = parseInt(value.substring(8, 10), 10);
    switch (true) {
      case day <= 10:
        queryFrom.tenDays = tenDays.FIRST_TEN_DAYS;
        break;
      case day <= 20:
        queryFrom.tenDays = tenDays.MIDDLE_TEN_DAYS;
        break;
      default:
        queryFrom.tenDays = tenDays.LAST_TEN_DAYS;
        break;
    }
  };

  const officeChange = (value: any, info: any) => {
    queryFrom.officeName = info.officeName;
  };

  const requestErr = data => {
    FMessageBox.alert(data.message.description, {
      confirmButtonText: t("forex.report.tendayssell.close")
    });
    queryTable.value.getTableData().splice(0);
  };

  const printData = () => {
    if (queryTable.value.getTableData().length > 0) {
      httpTool
        .getDownloadData(printUrl, queryFrom, { noLoading: false })
        .then((res: any) => {
          if (res.data.type === "application/json") {
            blobToString(res.data).then(result => {
              const resData = JSON.parse(result);
              if (resData.success) {
                FMessageBox.report({ type: "success", message: resData.data });
              } else {
                FMessageBox.report({ type: "error", message: resData.message.description });
              }
            });
          } else {
            printJS(window.URL.createObjectURL(res.data));
          }
        })
        .catch(res => {
          if (res.data && res.data.description) {
            FMessageBox.report({ type: "error", message: res.data.description });
          } else {
            FMessageBox.report({ type: "error", message: t("common.print.printError") });
          }
        });
    }
  };

  const blobToString = (blob: Blob) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsText(blob);
    });
  };

  const beforeDownloadCheck = () => {
    if (queryFrom.officeId === null || queryFrom.officeId === "" || queryFrom.officeId === undefined) {
      FMessageBox.report(t("forex.report.tendayssell.officeViewPlaceholder"));
      return false;
    }
    if (
      queryFrom.reportYearMonth === null ||
      queryFrom.reportYearMonth === "" ||
      queryFrom.reportYearMonth === undefined
    ) {
      FMessageBox.report(t("forex.report.tendayssell.reportYearMonthView"));
      return false;
    }
    return true;
  };

  return {
    tableColumns,
    queryFrom,
    queryTable,
    getOpenDate,
    officeChange,
    requestErr,
    printData,
    beforeDownloadCheck
  };
};
export default useList;
