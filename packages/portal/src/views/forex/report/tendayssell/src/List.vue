<template>
  <f-query-scene :title="t('forex.report.tendayssell.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="projectCode"
        query-comp-id="forex.report.tendayssell-query-001"
        table-comp-id="forex.report.tendayssell-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :show-export="true"
        :summation-biz-label="t('forex.report.tendayssell.record')"
        :summation-biz-unit="t('forex.report.tendayssell.recordUnit')"
        :tile-panel="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :pagination="false"
        :show-layout="false"
        @on-failed="requestErr"
        @print="printData"
        :beforeDownload="beforeDownloadCheck"
      >
        <template #query-panel>
          <f-form-item :label="t('forex.report.tendayssell.officeView')" prop="officeId" :required="true">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              @change="officeChange"
            />
          </f-form-item>

          <f-form-item
            :label="t('forex.report.tendayssell.reportYearMonthView')"
            prop="reportYearMonth"
            :required="true"
          >
            <f-date-picker
              v-model="queryFrom.reportYearMonth"
              type="month"
              :widgetInit="getOpenDate"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.tendayssell.tenDaysView')" prop="tenDays" :required="true">
            <f-select v-model="queryFrom.tenDays" :data="tenDays" />
          </f-form-item>

          <f-form-item :label="t('forex.report.tendayssell.benchmarkUnitView')" prop="benchmarkUnit" :required="true">
            <f-select v-model="queryFrom.benchmarkUnit" :data="benchmarkUnit" />
          </f-form-item>
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { listUrl, officeListUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
const { t } = useI18n();

const tenDays = useConst("forex.TenDays");
const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

const { tableColumns, queryFrom, queryTable, getOpenDate, officeChange, requestErr, printData, beforeDownloadCheck } =
  useList();
</script>
