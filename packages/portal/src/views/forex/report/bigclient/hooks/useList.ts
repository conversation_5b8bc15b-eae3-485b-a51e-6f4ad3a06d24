import { useI18n } from "vue-i18n";
import { reactive, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import httpTool from "@/utils/http";
import { openDateUrl, printUrl, listUrl } from "../url";
import { FMessageBox } from "@dtg/frontend-plus";
import printJS from "print-js";
import { addMonths } from "@/utils/date";

export const useList = () => {
  const { t } = useI18n();
  const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

  // 表格查询对象
  const queryFrom = reactive({
    officeId: null,
    officeName: null,
    reportYearMonth: null,
    benchmarkUnit: benchmarkUnit.TEN_THOUSAND_DOLLAR,
    showCount: 10,
    openDate: null,
    tabName: "goodSell"
  });
  // 表格模板
  const queryTable = shallowRef();

  const getOpenDate = () => {
    return new Promise(resolve => {
      httpTool.post(openDateUrl).then((res: any) => {
        resolve(res.data.onlineDate);
        const date = addMonths(res.data.onlineDate, -1);
        queryFrom.reportYearMonth = date.substring(0, 7);
        queryFrom.openDate = date.substring(0, 10);
      });
    });
  };

  const officeChange = (value: any, info: any) => {
    queryFrom.officeName = info.officeName;
  };

  const printData = () => {
    if (queryTable.value.getTableData().length > 0) {
      httpTool
        .getDownloadData(printUrl, queryFrom, { noLoading: false })
        .then((res: any) => {
          if (res.data.type === "application/json") {
            blobToString(res.data).then(result => {
              const resData = JSON.parse(result);
              if (resData.success) {
                FMessageBox.report({ type: "success", message: resData.data });
              } else {
                FMessageBox.report({ type: "error", message: resData.message.description });
              }
            });
          } else {
            printJS(window.URL.createObjectURL(res.data));
          }
        })
        .catch(res => {
          if (res.data && res.data.description) {
            FMessageBox.report({ type: "error", message: res.data.description });
          } else {
            FMessageBox.report({ type: "error", message: t("common.print.printError") });
          }
        });
    }
  };

  const blobToString = (blob: Blob) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsText(blob);
    });
  };

  const tableData = reactive({ data: [] });

  const reportResultData = reactive({
    officeName: "",
    goodsSellList: [],
    goodsBuyList: [],
    serviceTradeSellList: [],
    serviceTradeBuyList: [],
    financeSellList: [],
    financeBuyList: []
  });

  const queryTableMethod = () => {
    //请求前将原数据对象置空
    clearReportResultData();
    httpTool.post(listUrl, queryFrom).then((res: any) => {
      if (res.success && res.data !== null) {
        Object.assign(reportResultData, res.data);
        handleClick(queryFrom.tabName);
      }
    });
  };

  const clearReportResultData = () => {
    reportResultData.officeName = "";
    reportResultData.goodsSellList = [];
    reportResultData.goodsBuyList = [];
    reportResultData.serviceTradeSellList = [];
    reportResultData.serviceTradeBuyList = [];
    reportResultData.financeSellList = [];
    reportResultData.financeBuyList = [];

    tableData.data = [];
  };

  const handleClick = (value: any) => {
    switch (value) {
      case "goodSell":
        tableData.data = reportResultData.goodsSellList;
        break;
      case "goodBuy":
        tableData.data = reportResultData.goodsBuyList;
        break;
      case "serviceSell":
        tableData.data = reportResultData.serviceTradeSellList;
        break;
      case "serviceBuy":
        tableData.data = reportResultData.serviceTradeBuyList;
        break;
      case "financeSell":
        tableData.data = reportResultData.financeSellList;
        break;
      case "financeBuy":
        tableData.data = reportResultData.financeBuyList;
        break;
    }
  };

  const beforeDownloadCheck = () => {
    if (queryFrom.officeId === null || queryFrom.officeId === "" || queryFrom.officeId === undefined) {
      FMessageBox.report(t("forex.report.bigclientreport.officeViewPlaceholder"));
      return false;
    }
    if (
      queryFrom.reportYearMonth === null ||
      queryFrom.reportYearMonth === "" ||
      queryFrom.reportYearMonth === undefined
    ) {
      FMessageBox.report(t("forex.report.bigclientreport.reportDateViewPlaceholder"));
      return false;
    }
    if (queryFrom.showCount === null || queryFrom.showCount === "" || queryFrom.showCount === undefined) {
      FMessageBox.report(t("forex.report.bigclientreport.showCountViewPlaceholder"));
      return false;
    }
    return true;
  };

  return {
    queryFrom,
    queryTable,
    getOpenDate,
    officeChange,
    printData,
    tableData,
    handleClick,
    queryTableMethod,
    reportResultData,
    beforeDownloadCheck
  };
};
export default useList;
