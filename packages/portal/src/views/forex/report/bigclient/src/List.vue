<template>
  <f-query-scene :title="t('forex.report.bigclientreport.title')">
    <template #query-table>
      <f-query-table
        ref="queryTable"
        row-key="projectCode"
        query-comp-id="forex-report-bigclientreport-query-001"
        table-comp-id="forex-report-bigclientreport-table-001"
        border
        :table-data="{ data: tableData.data }"
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :show-export="true"
        :summation-biz-label="t('forex.report.bigclientreport.record')"
        :summation-biz-unit="t('forex.report.bigclientreport.recordUnit')"
        :tile-panel="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :pagination="false"
        :show-layout="false"
        @print="printData"
        @query-table="queryTableMethod"
        :beforeDownload="beforeDownloadCheck"
      >
        <template #operate>
          <f-tabs v-model="queryFrom.tabName" @tab-change="handleClick">
            <f-tab-pane :label="t('forex.report.bigclientreport.goodSell')" name="goodSell" />
            <f-tab-pane :label="t('forex.report.bigclientreport.goodBuy')" name="goodBuy" />
            <f-tab-pane :label="t('forex.report.bigclientreport.serviceSell')" name="serviceSell" />
            <f-tab-pane :label="t('forex.report.bigclientreport.serviceBuy')" name="serviceBuy" />
            <f-tab-pane :label="t('forex.report.bigclientreport.financeSell')" name="financeSell" />
            <f-tab-pane :label="t('forex.report.bigclientreport.financeBuy')" name="financeBuy" />
          </f-tabs>
        </template>

        <f-table-column
          :label="t('forex.report.bigclientreport.goodSellTitle', [reportResultData.officeName])"
          headerAlign="center"
          v-if="queryFrom.tabName === 'goodSell'"
        >
          <f-table-column prop="index" :label="t('forex.report.bigclientreport.index')" />
          <f-table-column prop="clientName" :label="t('forex.report.bigclientreport.sellClientName')" />
          <f-table-column prop="organizationCode" :label="t('forex.report.bigclientreport.organizationCode')" />
          <f-table-column
            prop="amount"
            :label="t('forex.report.bigclientreport.amount')"
            formatter="amount"
            headerAlign="right"
          />
          <f-table-column prop="useName" :label="t('forex.report.bigclientreport.sellUseName')" />
          <f-table-column prop="useCode" :label="t('forex.report.bigclientreport.sellUseCode')" />
          <f-table-column prop="useDetail" :label="t('forex.report.bigclientreport.sellUseDetail')" />
        </f-table-column>

        <f-table-column
          :label="t('forex.report.bigclientreport.goodBuyTitle', [reportResultData.officeName])"
          headerAlign="center"
          v-if="queryFrom.tabName === 'goodBuy'"
        >
          <f-table-column prop="index" :label="t('forex.report.bigclientreport.index')" />
          <f-table-column prop="clientName" :label="t('forex.report.bigclientreport.buyClientName')" />
          <f-table-column prop="organizationCode" :label="t('forex.report.bigclientreport.organizationCode')" />
          <f-table-column
            prop="amount"
            :label="t('forex.report.bigclientreport.amount')"
            formatter="amount"
            headerAlign="right"
          />
          <f-table-column prop="useName" :label="t('forex.report.bigclientreport.buyUseName')" />
          <f-table-column prop="useCode" :label="t('forex.report.bigclientreport.buyUseCode')" />
          <f-table-column prop="useDetail" :label="t('forex.report.bigclientreport.buyUseDetail')" />
        </f-table-column>

        <f-table-column
          :label="t('forex.report.bigclientreport.serviceSellTitle', [reportResultData.officeName])"
          headerAlign="center"
          v-if="queryFrom.tabName === 'serviceSell'"
        >
          <f-table-column prop="index" :label="t('forex.report.bigclientreport.index')" />
          <f-table-column prop="clientName" :label="t('forex.report.bigclientreport.sellClientName')" />
          <f-table-column prop="organizationCode" :label="t('forex.report.bigclientreport.organizationCode')" />
          <f-table-column
            prop="amount"
            :label="t('forex.report.bigclientreport.amount')"
            formatter="amount"
            headerAlign="right"
          />
          <f-table-column prop="useName" :label="t('forex.report.bigclientreport.sellUseName')" />
          <f-table-column prop="useCode" :label="t('forex.report.bigclientreport.sellUseCode')" />
          <f-table-column prop="useDetail" :label="t('forex.report.bigclientreport.sellUseDetail')" />
        </f-table-column>

        <f-table-column
          :label="t('forex.report.bigclientreport.serviceBuyTitle', [reportResultData.officeName])"
          headerAlign="center"
          v-if="queryFrom.tabName === 'serviceBuy'"
        >
          <f-table-column prop="index" :label="t('forex.report.bigclientreport.index')" />
          <f-table-column prop="clientName" :label="t('forex.report.bigclientreport.buyClientName')" />
          <f-table-column prop="organizationCode" :label="t('forex.report.bigclientreport.organizationCode')" />
          <f-table-column
            prop="amount"
            :label="t('forex.report.bigclientreport.amount')"
            formatter="amount"
            headerAlign="right"
          />
          <f-table-column prop="useName" :label="t('forex.report.bigclientreport.buyUseName')" />
          <f-table-column prop="useCode" :label="t('forex.report.bigclientreport.buyUseCode')" />
          <f-table-column prop="useDetail" :label="t('forex.report.bigclientreport.buyUseDetail')" />
        </f-table-column>

        <f-table-column
          :label="t('forex.report.bigclientreport.financeSellTitle', [reportResultData.officeName])"
          headerAlign="center"
          v-if="queryFrom.tabName === 'financeSell'"
        >
          <f-table-column prop="index" :label="t('forex.report.bigclientreport.index')" />
          <f-table-column prop="clientName" :label="t('forex.report.bigclientreport.sellClientName')" />
          <f-table-column prop="organizationCode" :label="t('forex.report.bigclientreport.organizationCode')" />
          <f-table-column
            prop="amount"
            :label="t('forex.report.bigclientreport.amount')"
            formatter="amount"
            headerAlign="right"
          />
          <f-table-column prop="useName" :label="t('forex.report.bigclientreport.sellUseName')" />
          <f-table-column prop="useCode" :label="t('forex.report.bigclientreport.sellUseCode')" />
          <f-table-column prop="useDetail" :label="t('forex.report.bigclientreport.sellUseDetail')" />
        </f-table-column>

        <f-table-column
          :label="t('forex.report.bigclientreport.financeBuyTitle', [reportResultData.officeName])"
          headerAlign="center"
          v-if="queryFrom.tabName === 'financeBuy'"
        >
          <f-table-column prop="index" :label="t('forex.report.bigclientreport.index')" />
          <f-table-column prop="clientName" :label="t('forex.report.bigclientreport.buyClientName')" />
          <f-table-column prop="organizationCode" :label="t('forex.report.bigclientreport.organizationCode')" />
          <f-table-column
            prop="amount"
            :label="t('forex.report.bigclientreport.amount')"
            formatter="amount"
            headerAlign="right"
          />
          <f-table-column prop="useName" :label="t('forex.report.bigclientreport.buyUseName')" />
          <f-table-column prop="useCode" :label="t('forex.report.bigclientreport.buyUseCode')" />
          <f-table-column prop="useDetail" :label="t('forex.report.bigclientreport.buyUseDetail')" />
        </f-table-column>

        <template #query-panel>
          <f-form-item :label="t('forex.report.bigclientreport.officeView')" prop="officeId" :required="true">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              @change="officeChange"
            />
          </f-form-item>

          <f-form-item
            :label="t('forex.report.bigclientreport.reportYearMonthView')"
            prop="reportYearMonth"
            :required="true"
          >
            <f-date-picker
              v-model="queryFrom.reportYearMonth"
              type="month"
              :widgetInit="getOpenDate"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </f-form-item>

          <f-form-item
            :label="t('forex.report.bigclientreport.benchmarkUnitView')"
            prop="benchmarkUnit"
            :required="true"
          >
            <f-select v-model="queryFrom.benchmarkUnit" :data="benchmarkUnit" />
          </f-form-item>

          <f-form-item :label="t('forex.report.bigclientreport.showCount')" prop="showCount" :required="true">
            <f-number v-model="queryFrom.showCount" :precision="0" />
          </f-form-item>
        </template>
      </f-query-table>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { officeListUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";

const { t } = useI18n();

const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

const {
  queryFrom,
  queryTable,
  getOpenDate,
  officeChange,
  printData,
  tableData,
  handleClick,
  queryTableMethod,
  reportResultData,
  beforeDownloadCheck
} = useList();
</script>
