<template>
  <f-query-scene :title="t('forex.report.position.title')">
    <template #query-table>
      <f-query-table
        ref="queryTable"
        row-key="projectCode"
        query-comp-id="forex.report.position-query-001"
        table-comp-id="forex.report.position-table-001"
        :url="listUrl"
        border
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :export-exclude="['number', 'operate']"
        :export-url="exportUrl"
        :show-export="true"
        :summation-biz-label="t('forex.report.position.record')"
        :summation-biz-unit="t('forex.report.position.recordUnit')"
        :tile-panel="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :pagination="false"
        :show-layout="false"
        @on-failed="requestErr"
        :header-cell-style="headerMethod"
        @reset-form="resetQuery"
        @print="printData"
        :beforeDownload="beforeDownloadCheck"
      >
        <f-table-column prop="projectName" :label="t('forex.report.position.projectView')" />
        <f-table-column :label="t('forex.report.position.balanceView')" headerAlign="center">
          <f-table-column prop="showSerialNo" />
          <f-table-column prop="balance" headerAlign="right" formatter="amount" />
        </f-table-column>
        <f-table-column
          prop="sell"
          :label="t('forex.report.position.sellView')"
          headerAlign="right"
          formatter="amount"
        />
        <f-table-column prop="buy" :label="t('forex.report.position.buyView')" headerAlign="right" formatter="amount" />

        <template #query-panel>
          <f-form-item :label="t('forex.report.position.officeView')" prop="officeId" :required="true">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              @change="officeChange"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.position.reportDateView')" prop="reportDate" :required="true">
            <f-date-picker
              v-model="queryFrom.reportDate"
              type="date"
              :widgetInit="getOpenDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="changeReportDate"
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.position.previousDayView')" prop="previousDay" :required="true">
            <f-date-picker
              v-model="queryFrom.previousDay"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              disabled
            />
          </f-form-item>

          <f-form-item :label="t('forex.report.position.benchmarkUnitView')" prop="benchmarkUnit" :required="true">
            <f-select v-model="queryFrom.benchmarkUnit" :data="benchmarkUnit" />
          </f-form-item>
        </template>
      </f-query-table>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { listUrl, officeListUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
const { t } = useI18n();

const benchmarkUnit = useConst("forex.DollarBenchmarkUnit");

const {
  queryFrom,
  queryTable,
  getOpenDate,
  officeChange,
  requestErr,
  changeReportDate,
  headerMethod,
  resetQuery,
  printData,
  beforeDownloadCheck
} = useList();
</script>
