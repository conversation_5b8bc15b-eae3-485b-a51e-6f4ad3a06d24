import { useI18n } from "vue-i18n";
import { reactive, shallowRef, ref, nextTick } from "vue";
import { useModelRange } from "@/hooks/conversion";
import { useConst } from "@ifs/support";
import { useOpenDate } from "@/hooks";

export const useList = () => {
  const { t } = useI18n();
  const { openDate } = useOpenDate();

  const forexBusinessType = useConst("forex.ForexBusinessType");

  const rowId = ref<number>();
  const settlementSelfApplyDetail = shallowRef();
  const sellSelfApplyDetail = shallowRef();
  const transConfirmApplyDetail = shallowRef();
  const forexClearDetail = shallowRef();

  // 表格配置
  const tableColumns = [
    {
      width: "200px",
      prop: "applyNo",
      label: t("forex.query.exchangeselfquery.applyNo"),
      slots: { default: "applyNo" }
    },
    {
      width: "200px",
      prop: "officeName",
      label: t("forex.query.exchangeselfquery.office")
    },
    {
      width: "200px",
      prop: "currencyName",
      label: t("forex.query.exchangeselfquery.currency")
    },
    {
      width: "200px",
      prop: "forexBusinessType",
      label: t("forex.query.exchangeselfquery.businessType"),
      formatter: { name: "const", const: "forex.ForexBusinessType" }
    },
    {
      width: "200px",
      prop: "foreignAccountNo",
      label: t("forex.query.exchangeselfquery.foreignAccountNo")
    },
    {
      width: "200px",
      prop: "foreignAmount",
      label: t("forex.query.exchangeselfquery.foreignAmount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "cnyAccountNo",
      label: t("forex.query.exchangeselfquery.cnyAccountNo")
    },
    {
      width: "200px",
      prop: "cnyAmount",
      label: t("forex.query.exchangeselfquery.cnyAmount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "convertedUsdAmount",
      label: t("forex.query.exchangeselfquery.convertedUsdAmount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "actualExchangeRate",
      label: t("forex.query.exchangeselfquery.actualExchangeRate"),
      formatter: "exchangeRate",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "transContractCode",
      label: t("forex.query.exchangeselfquery.transContractCode")
    },
    {
      width: "200px",
      prop: "actualForeignAmount",
      label: t("forex.query.exchangeselfquery.actualForeignAmount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "actualCnyAmount",
      label: t("forex.query.exchangeselfquery.actualCnyAmount"),
      formatter: "amount",
      headerAlign: "right"
    },
    {
      width: "200px",
      prop: "transDate",
      label: t("forex.query.exchangeselfquery.transDate")
    },
    {
      width: "200px",
      prop: "checkStatus",
      label: t("forex.query.exchangeselfquery.checkStatus"),
      formatter: { name: "const", const: "forex.CheckStatus" }
    },
    {
      width: "200px",
      prop: "confirmStatus",
      label: t("forex.query.exchangeselfquery.confirmStatus"),
      formatter: { name: "const", const: "forex.ConfirmStatus" }
    },
    {
      width: "200px",
      prop: "confirmNo",
      label: t("forex.query.exchangeselfquery.confirmNo"),
      slots: { default: "confirmNo" }
    },
    {
      width: "200px",
      prop: "clearStatus",
      label: t("forex.query.exchangeselfquery.clearStatus"),
      formatter: { name: "const", const: "forex.ClearStatus" }
    },
    {
      width: "200px",
      prop: "clearNo",
      label: t("forex.query.exchangeselfquery.clearNo"),
      slots: { default: "clearNo" }
    },
    {
      width: "200px",
      prop: "addCompleteFlag",
      label: t("forex.query.exchangeselfquery.addCompleteFlag"),
      formatter: { name: "const", const: "forex.YesOrNo" }
    }
  ];

  const allowSort = [
    "applyNo",
    "officeName",
    "currencyName",
    "forexBusinessType",
    "foreignAccountNo",
    "foreignAmount",
    "cnyAccountNo",
    "cnyAmount",
    "convertedUsdAmount",
    "actualExchangeRate",
    "transContractCode",
    "actualCnyAmount",
    "actualForeignAmount",
    "transDate",
    "confirmNo",
    "clearNo"
  ];
  // 表格查询对象
  const queryFrom = reactive({
    officeId: [],
    currencyId: [],
    cnyAccountId: [],
    foreignAccountId: [],
    foreignAmount: [],
    applyNo: "",
    confirmNo: "",
    clearNo: "",
    actualExchangeRate: 0,
    addCompleteFlag: [],
    checkStatus: [],
    clearStatus: [],
    transDate: [openDate.value, openDate.value],
    confirmStatus: []
  });
  // 表格模板
  const queryTable = shallowRef();
  // 已选列表
  const checkedList = ref<any[]>([]);
  // 控制全选checkbox
  const selectableAll = (rows: any) => {
    return !rows;
  };
  // 勾选checkbox
  const handleSelect = (row: any[]) => {
    checkedList.value = row;
  };
  const clearSelection = () => {
    checkedList.value.splice(0);
  };
  // 关闭抽屉
  const handleClose = () => {
    settlementSelfApplyDetail.value.setFalseToVisible();
    transConfirmApplyDetail.value.setFalseToVisible();
    forexClearDetail.value.setFalseToVisible();
  };
  // 打开抽屉
  const applyNoHandleOpen = (row: any) => {
    rowId.value = row.applyId as number;
    handleClose();
    nextTick(() => {
      if (row.forexBusinessType === forexBusinessType.FOREX_SETTLEMENT_SELF) {
        settlementSelfApplyDetail.value.setTrueToVisible();
      } else {
        sellSelfApplyDetail.value.setTrueToVisible();
      }
    });
  };
  const confirmNoHandleOpen = (row: any) => {
    rowId.value = row.confirmId as number;
    handleClose();
    nextTick(() => {
      transConfirmApplyDetail.value.setTrueToVisible();
    });
  };
  const clearNoHandleOpen = (row: any) => {
    rowId.value = row.clearId as number;
    handleClose();
    nextTick(() => {
      forexClearDetail.value.setTrueToVisible();
    });
  };
  const { postParams } = useModelRange(["foreignAmount", "transDate"]);

  return {
    tableColumns,
    queryFrom,
    queryTable,
    allowSort,
    rowId,
    applyNoHandleOpen,
    settlementSelfApplyDetail,
    sellSelfApplyDetail,
    confirmNoHandleOpen,
    transConfirmApplyDetail,
    clearNoHandleOpen,
    forexClearDetail,
    handleSelect,
    selectableAll,
    clearSelection,
    postParams
  };
};
export default useList;
