<template>
  <f-query-scene :title="t('forex.query.exchangeselfquery.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="forex.query.exchangeselfquery-query-001"
        table-comp-id="forex.query.exchangeselfquery-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :post-params="postParams"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
        :export-url="exportUrl"
        :summation-biz-label="t('forex.query.exchangeselfquery.record')"
        :summation-biz-unit="t('forex.query.exchangeselfquery.recordUnit')"
        :tile-panel="false"
        :count-label="t('forex.query.exchangeselfquery.record')"
        :count-label-unit="t('forex.query.exchangeselfquery.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :allowSort="allowSort"
      >
        <template #query-panel>
          <!--机构-->
          <f-form-item :label="t('forex.query.exchangeselfquery.office')" prop="officeId">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              onReady="onReady1"
            />
          </f-form-item>
          <!--外币币种-->
          <f-form-item :label="t('forex.query.exchangeselfquery.currency')" prop="currencyId">
            <f-select
              v-model="queryFrom.currencyId"
              value-key="currencyId"
              label="currencyName"
              :url="currencyListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :extra-data="{
                excludingCurrencyIds: [1]
              }"
              onReady="onReady1"
            />
          </f-form-item>
          <!--申请状态-->
          <f-form-item :label="t('forex.query.exchangeselfquery.checkStatus')" prop="checkStatus">
            <f-select
              v-model="queryFrom.checkStatus"
              :data="checkStatus.omitConst(['DELETE', 'COMPLETED', 'DRAFT', 'DATA_STATUS_INVALID'])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--实际成交汇率-->
          <f-form-item :label="t('forex.query.exchangeselfquery.actualExchangeRate')" prop="actualExchangeRate">
            <f-amount v-model="queryFrom.actualExchangeRate" :precision="4" symbol=" " />
          </f-form-item>
          <!--交易日期-->
          <f-form-item :label="t('forex.query.exchangeselfquery.transDate')" prop="transDate">
            <f-lax-range-date-picker
              v-model="queryFrom.transDate"
              :startDisabledDate="excuteDateStartControl(queryFrom, 'transDate')"
              :endDisabledDate="excuteDateEndControl(queryFrom, 'transDate')"
            />
          </f-form-item>
          <!--外币金额-->
          <f-form-item :label="t('forex.query.exchangeselfquery.foreignAmount')" prop="foreignAmount">
            <f-amount-range v-model="queryFrom.foreignAmount" value-of-string symbol=" " />
          </f-form-item>
          <!--人民币银行账户-->
          <f-form-item :label="t('forex.query.exchangeselfquery.cnyAccountNo')" prop="cnyAccountId">
            <f-magnifier-multi
              :title="t('forex.query.exchangeselfquery.cnyAccountNoMagnifier')"
              :url="bankAccountInfo"
              method="post"
              v-model="queryFrom.cnyAccountId"
              row-key="id"
              query-key="ids"
              row-label="bankAccountNo"
              selected-key="bankAccountNo"
              selected-label="bankAccountName"
              input-key="bankAccNoOrName"
              auto-init
              :params="{
                ownerType: 1,
                currencyId: 1
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column prop="bankAccountNo" :label="t('forex.query.exchangeselfquery.accountCode')" />
              <f-magnifier-column prop="bankAccountName" :label="t('forex.query.exchangeselfquery.accountName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--外币银行账户-->
          <f-form-item :label="t('forex.query.exchangeselfquery.foreignAccountNo')" prop="foreignAccountId">
            <f-magnifier-multi
              v-model="queryFrom.foreignAccountId"
              :title="t('forex.query.exchangeselfquery.foreignAccountNoMagnifier')"
              :url="bankAccountInfo"
              method="post"
              row-key="id"
              query-key="ids"
              row-label="bankAccountNo"
              selected-key="bankAccountNo"
              selected-label="bankAccountName"
              input-key="bankAccNoOrName"
              auto-init
              :params="{
                ownerType: 1,
                excludeCurrencys: [1]
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column prop="bankAccountNo" :label="t('forex.query.exchangeselfquery.accountCode')" />
              <f-magnifier-column prop="bankAccountName" :label="t('forex.query.exchangeselfquery.accountName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--是否已补录-->
          <f-form-item :label="t('forex.query.exchangeselfquery.addCompleteFlag')" prop="addCompleteFlag">
            <f-select
              v-model="queryFrom.addCompleteFlag"
              :data="yesOrNo"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--申请编号-->
          <f-form-item :label="t('forex.query.exchangeselfquery.applyNo')" prop="applyNo">
            <f-input v-model="queryFrom.applyNo" />
          </f-form-item>
          <!--交易确认编号-->
          <f-form-item :label="t('forex.query.exchangeselfquery.confirmNo')" prop="confirmNo">
            <f-input v-model="queryFrom.confirmNo" />
          </f-form-item>
          <!--清算编号-->
          <f-form-item :label="t('forex.query.exchangeselfquery.clearNo')" prop="clearNo">
            <f-input v-model="queryFrom.clearNo" />
          </f-form-item>
          <!--交易确认状态-->
          <f-form-item :label="t('forex.query.exchangeselfquery.confirmStatus')" prop="confirmStatus">
            <f-select
              v-model="queryFrom.confirmStatus"
              :data="confirmStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--清算状态-->
          <f-form-item :label="t('forex.query.exchangeselfquery.clearStatus')" prop="clearStatus">
            <f-select
              v-model="queryFrom.clearStatus"
              :data="clearStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
        </template>
        <template #applyNo="{ row }">
          <f-button @click="applyNoHandleOpen(row)" link type="primary">{{ row.applyNo }}</f-button>
        </template>
        <template #confirmNo="{ row }">
          <f-button @click="confirmNoHandleOpen(row)" link type="primary">{{ row.confirmNo }}</f-button>
        </template>
        <template #clearNo="{ row }">
          <f-button @click="clearNoHandleOpen(row)" link type="primary">{{ row.clearNo }}</f-button>
        </template>
      </f-query-grid>
    </template>
    <SettlementSelfApplyDetail ref="settlementSelfApplyDetail" :id="rowId" />
    <SellSelfApplyDetail ref="sellSelfApplyDetail" :id="rowId" />
    <TransConfirmApplyDetail ref="transConfirmApplyDetail" :id="rowId" />
    <ForexClearDetail ref="forexClearDetail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import SettlementSelfApplyDetail from "../../../counter/settlementselfapply/src/components/Detail.vue";
import SellSelfApplyDetail from "../../../counter/sellselfapply/src/components/Detail.vue";
import TransConfirmApplyDetail from "../../../counter/transconfirmapply/src/components/Detail.vue";
import ForexClearDetail from "../../../counter/forexclear/src/components/Detail.vue";
import { officeListUrl, currencyListUrl, bankAccountInfo, listUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useCommon } from "@/hooks/useCommon";
import { nextTick, provide } from "vue";

const { excuteDateStartControl, excuteDateEndControl } = useCommon();

const { t } = useI18n();

const yesOrNo = useConst("forex.YesOrNo");
const checkStatus = useConst("forex.CheckStatus");
const confirmStatus = useConst("forex.ConfirmStatus");
const clearStatus = useConst("forex.ClearStatus");

const {
  tableColumns,
  queryFrom,
  queryTable,
  allowSort,
  rowId,
  applyNoHandleOpen,
  settlementSelfApplyDetail,
  sellSelfApplyDetail,
  confirmNoHandleOpen,
  transConfirmApplyDetail,
  clearNoHandleOpen,
  forexClearDetail,
  handleSelect,
  selectableAll,
  clearSelection,
  postParams
} = useList();

provide("onReady1", {
  count: 2,
  handler() {
    nextTick(() => {
      queryTable.value.renderTableData();
    });
  }
});
</script>
