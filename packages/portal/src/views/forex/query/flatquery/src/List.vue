<template>
  <f-query-scene :title="t('forex.query.flatquery.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="forex.query.flatquery-query-001"
        table-comp-id="forex.query.flatquery-table-001"
        :table-columns="tableColumns"
        :url="listUrl"
        border
        :selectable-all="selectableAll"
        :formData="queryFrom"
        :show-header="true"
        auto-reset
        :auto-init="false"
        :post-params="postParams"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="clearSelection"
        :export-url="exportUrl"
        :summation-biz-label="t('forex.query.flatquery.record')"
        :summation-biz-unit="t('forex.query.flatquery.recordUnit')"
        :tile-panel="false"
        :count-label="t('forex.query.flatquery.record')"
        :count-label-unit="t('forex.query.flatquery.recordUnit')"
        :show-count-value="false"
        :show-summation-sum="false"
        :show-print="false"
        tableType="Record"
        :allowSort="allowSort"
      >
        <template #query-panel>
          <!--机构-->
          <f-form-item :label="t('forex.query.flatquery.office')" prop="officeId">
            <f-select
              v-model="queryFrom.officeId"
              value-key="officeId"
              label="officeName"
              :url="officeListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--外币币种-->
          <f-form-item :label="t('forex.query.flatquery.currency')" prop="currencyId">
            <f-select
              v-model="queryFrom.currencyId"
              value-key="currencyId"
              label="currencyName"
              :url="currencyListUrl"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
              :extra-data="{
                excludingCurrencyIds: [1]
              }"
            />
          </f-form-item>
          <!--申请状态-->
          <f-form-item :label="t('forex.query.flatquery.checkStatus')" prop="checkStatus">
            <f-select
              v-model="queryFrom.checkStatus"
              :data="checkStatus.omitConst(['DELETE', 'COMPLETED', 'DRAFT', 'DATA_STATUS_INVALID'])"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--买卖方向-->
          <f-form-item :label="t('forex.query.flatquery.forexBusinessType')" prop="forexBusinessType">
            <f-select
              v-model="queryFrom.forexBusinessType"
              :data="
                forexBusinessType.pickConst([forexBusinessType.FORE_LONG_SELLING, forexBusinessType.FORE_SHORT_BUYING])
              "
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--实际成交汇率-->
          <f-form-item :label="t('forex.query.flatquery.actualExchangeRate')" prop="actualExchangeRate">
            <f-amount v-model="queryFrom.actualExchangeRate" :precision="4" symbol=" " />
          </f-form-item>
          <!--交易日期-->
          <f-form-item :label="t('forex.query.flatquery.transDate')" prop="transDate">
            <f-lax-range-date-picker
              v-model="queryFrom.transDate"
              :startDisabledDate="excuteDateStartControl(queryFrom, 'transDate')"
              :endDisabledDate="excuteDateEndControl(queryFrom, 'transDate')"
              :widgetInit="getOpenDate"
            />
          </f-form-item>
          <!--外币金额-->
          <f-form-item :label="t('forex.query.flatquery.foreignAmount')" prop="foreignAmount">
            <f-amount-range v-model="queryFrom.foreignAmount" value-of-string symbol=" " />
          </f-form-item>
          <!--人民币银行账户-->
          <f-form-item :label="t('forex.query.flatquery.cnyAccountNo')" prop="cnyAccountId">
            <f-magnifier-multi
              v-model="queryFrom.cnyAccountId"
              :title="t('forex.query.flatquery.cnyAccountNoMagnifier')"
              :url="bankAccountInfo"
              method="post"
              row-key="id"
              query-key="ids"
              row-label="bankAccountNo"
              selected-key="bankAccountNo"
              selected-label="bankAccountName"
              input-key="bankAccNoOrName"
              auto-init
              :params="{
                ownerType: 1,
                currencyId: 1
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column prop="bankAccountNo" :label="t('forex.query.flatquery.accountCode')" />
              <f-magnifier-column prop="bankAccountName" :label="t('forex.query.flatquery.accountName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--外币银行账户-->
          <f-form-item :label="t('forex.query.flatquery.foreignAccountNo')" prop="foreignAccountId">
            <f-magnifier-multi
              v-model="queryFrom.foreignAccountId"
              :title="t('forex.query.flatquery.foreignAccountNoMagnifier')"
              :url="bankAccountInfo"
              method="post"
              row-key="id"
              query-key="ids"
              row-label="bankAccountNo"
              selected-key="bankAccountNo"
              selected-label="bankAccountName"
              input-key="bankAccNoOrName"
              auto-init
              :params="{
                ownerType: 1,
                excludeCurrencys: [1]
              }"
              :collapse-tags-tooltip="true"
            >
              <f-magnifier-column prop="bankAccountNo" :label="t('forex.query.flatquery.accountCode')" />
              <f-magnifier-column prop="bankAccountName" :label="t('forex.query.flatquery.accountName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--申请编号-->
          <f-form-item :label="t('forex.query.flatquery.applyNo')" prop="applyNo">
            <f-input v-model="queryFrom.applyNo" />
          </f-form-item>
          <!--交易确认编号-->
          <f-form-item :label="t('forex.query.flatquery.confirmNo')" prop="confirmNo">
            <f-input v-model="queryFrom.confirmNo" />
          </f-form-item>
          <!--清算编号-->
          <f-form-item :label="t('forex.query.flatquery.clearNo')" prop="clearNo">
            <f-input v-model="queryFrom.clearNo" />
          </f-form-item>
          <!--交易确认状态-->
          <f-form-item :label="t('forex.query.flatquery.confirmStatus')" prop="confirmStatus">
            <f-select
              v-model="queryFrom.confirmStatus"
              :data="confirmStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--清算状态-->
          <f-form-item :label="t('forex.query.flatquery.clearStatus')" prop="clearStatus">
            <f-select
              v-model="queryFrom.clearStatus"
              :data="clearStatus"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
        </template>
        <template #applyNo="{ row }">
          <f-button @click="applyNoHandleOpen(row)" link type="primary">{{ row.applyNo }}</f-button>
        </template>
        <template #confirmNo="{ row }">
          <f-button @click="confirmNoHandleOpen(row)" link type="primary">{{ row.confirmNo }}</f-button>
        </template>
        <template #clearNo="{ row }">
          <f-button @click="clearNoHandleOpen(row)" link type="primary">{{ row.clearNo }}</f-button>
        </template>
        <template #convertedUsdAmount="{ row }">
          <div :style="{ color: '#FF9E00' }">
            {{
              row.confirmStatus === confirmStatus.CONFIRMED
                ? format(row.convertedUsdAmount)
                : format(row.applyConverterUseAmount)
            }}
          </div>
        </template>
      </f-query-grid>
    </template>
    <FlatPlateApplyDetail ref="flatPlateApplyDetail" :id="rowId" />
    <TransConfirmApplyDetail ref="transConfirmApplyDetail" :id="rowId" />
    <ForexClearDetail ref="forexClearDetail" :id="rowId" />
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import FlatPlateApplyDetail from "../../../counter/flatplateapply/src/components/Detail.vue";
import ForexClearDetail from "../../../counter/forexclear/src/components/Detail.vue";
import TransConfirmApplyDetail from "../../../counter/transconfirmapply/src/components/Detail.vue";
import { officeListUrl, currencyListUrl, bankAccountInfo, listUrl, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { useCommon } from "@/hooks/useCommon";
import { format } from "@/utils/currency";

const { excuteDateStartControl, excuteDateEndControl } = useCommon();

const { t } = useI18n();

const checkStatus = useConst("forex.CheckStatus");
const clearStatus = useConst("forex.ClearStatus");
const confirmStatus = useConst("forex.ConfirmStatus");
const forexBusinessType = useConst("forex.ForexBusinessType");

const {
  tableColumns,
  queryFrom,
  queryTable,
  allowSort,
  rowId,
  applyNoHandleOpen,
  flatPlateApplyDetail,
  clearNoHandleOpen,
  forexClearDetail,
  handleSelect,
  selectableAll,
  clearSelection,
  postParams,
  transConfirmApplyDetail,
  confirmNoHandleOpen,
  getOpenDate
} = useList();
</script>
