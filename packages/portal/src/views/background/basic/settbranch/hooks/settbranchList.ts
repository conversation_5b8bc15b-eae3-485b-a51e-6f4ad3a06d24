import type { SettBranchReturnDto, SettBranchDto } from "../types";
import { reactive, shallowRef, ref, computed, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { goPage } from "./usePage";
import { useCurrency } from "@/hooks";

type BatchDelete = {
  ids: string;
  versions: string;
};

export const settbranchList = (statusEnum: any) => {
  const { t } = useI18n();
  // 表格配置
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true,
      selectable(row: SettBranchDto) {
        // 已保存的可选
        return [statusEnum.VALID].includes(row?.dataStatus);
      }
    },
    {
      prop: "bankAccountCode",
      slots: { default: "bankAccountCode" },
      label: t("background.basic.settbranch.bankAccountCode"),
      headerAlign: "left"
    },
    {
      prop: "openBankName",
      label: t("background.basic.settbranch.openBankName"),
      headerAlign: "left"
    },
    {
      prop: "subjectCode",
      label: t("background.basic.settbranch.subjectCode"),
      headerAlign: "left"
    },
    {
      prop: "bankAccountName",
      label: t("background.basic.settbranch.bankAccountName"),
      headerAlign: "left"
    },
    {
      prop: "operate",
      label: t("background.basic.settbranch.operate"),
      slots: { default: "buttons" },
      fixed: "right",
      headerAlign: "left"
    }
  ];

  // 币种列表
  const { globalCurrencyList } = useCurrency();
  // 表格查询对象
  const queryForm = reactive({
    //机构
    officeId: "",
    //币种
    currencyId: "",
    //业务类型
    transActionType: "",
    //账户模式
    accountPattern: "",
    ownerType: 1,
    officeIdList: [],
    currencyIdList: [],
    currencyIdDefaultList: globalCurrencyList.map(item => item.currencyId)
  });
  // 表格模板
  const queryTable = shallowRef();
  // 已选列表
  const checkedList = ref<SettBranchReturnDto[]>([]);
  // 控制全选checkbox
  const selectableAll = (rows: SettBranchDto[]) => {
    return !rows.some(row => [statusEnum.VALID].includes(row.dataStatus));
  };
  // 列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };
  // 勾选checkbox
  const handleSelect = (row: SettBranchReturnDto[]) => {
    checkedList.value = row;
  };
  // 是否选中checkbox
  const isChecked = computed(
    () => checkedList.value.length === 0 && checkedList.value.every(row => [statusEnum.VALID].includes(row.dataStatus))
  );
  // 批量删除的参数
  const gatherBatchDeleteParams = () => {
    const params: BatchDelete = {
      ids: checkedList.value.map(row => row.id).join(","),
      versions: checkedList.value.map(row => row.version).join(",")
    };
    return params;
  };
  //操作列按钮
  const generalButtonOption = () => {
    return [
      {
        type: "modify"
      }
    ];
  };

  // 新增跳转
  const add = () => {
    goPage("add");
  };
  // 点击修改
  const modify = (row: SettBranchDto) => {
    goPage("modify", { id: row.id, status: row.dataStatus, bankAccountId: row.bankAccountId });
  };

  const rowId = ref<number>();
  const rowBankAccountId = ref<number>();
  const detail = shallowRef();
  // 打开抽屉
  const handleOpen = (row: SettBranchDto) => {
    rowId.value = row.id as number;
    rowBankAccountId.value = row.bankAccountId as number;
    nextTick(() => {
      detail.value.setTrueToVisible();
    });
  };
  const clearSelection = () => {
    checkedList.value.splice(0);
  };

  const allowSort = ["bankAccountCode", "openBankName", "bankAccountName"];

  return {
    tableColumns,
    queryForm,
    queryTable,
    selectableAll,
    handleSearch,
    clearSelection,
    handleSelect,
    add,
    modify,
    generalButtonOption,
    gatherBatchDeleteParams,
    handleOpen,
    isChecked,
    rowId,
    detail,
    allowSort,
    rowBankAccountId
  };
};
export default settbranchList;
