<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('background.basic.settbranch.detailTitle')"
    destroy-on-close
    :isPoint="false"
    @close="setFalseToVisible"
  >
    <template #default>
      <f-multi-form-panel ref="form1" :model="settBranchDto" :column="3">
        <f-panel>
          <f-form-item :label="t('background.basic.settbranch.officeName')" prop="officeName">
            <f-input v-model="settBranchDto.officeName" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.settbranch.currency')" prop="currencyName">
            <f-input v-model="settBranchDto.currencyName" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.settbranch.openBankName')">
            <f-input v-model="settBranchDto.openBankName" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.settbranch.bankAccountName')">
            <f-input v-model="settBranchDto.bankAccountName" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.settbranch.bankAccountCode')">
            <f-input v-model="settBranchDto.bankAccountCode" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.settbranch.subjectCode')">
            <f-input v-model="settBranchDto.subjectCode" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.settbranch.printName')">
            <f-input v-model="settBranchDto.printName" disabled />
          </f-form-item>
        </f-panel>
      </f-multi-form-panel>
    </template>
    <template #footer>
      <f-button type="info" @click="setFalseToVisible">{{ t("background.basic.settbranch.close") }}</f-button>
    </template>
  </f-drawer-scene>
</template>
<script setup lang="ts">
import { settBranch } from "../../hooks/useSettBranch";
import useDetail from "../../hooks/useDetail";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const props = defineProps({
  id: Number,
  bankAccountId: Number
});
const { settBranchDto, getSettBranchDetailInfo } = settBranch();
const { setTrueToVisible, setFalseToVisible, drawerRef } = useDetail(settBranchDto, getSettBranchDetailInfo, props);
defineExpose({ setTrueToVisible, setFalseToVisible });
</script>
