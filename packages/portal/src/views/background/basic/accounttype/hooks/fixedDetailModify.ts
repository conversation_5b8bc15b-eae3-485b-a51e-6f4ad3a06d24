import type { SettSubaccounttypeFixedDto, SettAccountTypeDto, IsShowFixed } from "../types";
import { goPage } from "./usePage";
import httpTool from "@/utils/http";
import { getfixedDetailUrl } from "../url";
import { ref, reactive } from "vue";

export const useModify = (
  settSubaccounttypeFixedDto: SettSubaccounttypeFixedDto,
  accountGroupEnum: any,
  row: SettAccountTypeDto,
  getsettaccounttypeDetailInfo: Function
) => {
  // 返回列表页
  const goBack = () => {
    goPage("fixedDetailList", { row });
  };
  const form = ref();
  // 保存
  const updateInfo = () => {
    return settSubaccounttypeFixedDto;
  };
  // 删除
  const removeInfo = () => {
    type BatchDelete = {
      ids: string;
      versions: string;
    };
    const params: BatchDelete = {
      ids: settSubaccounttypeFixedDto.id.toString(),
      versions: (settSubaccounttypeFixedDto.version as number).toString()
    };
    return params;
  };
  //客户放大镜修改事件
  const onClientCodeSelected = (row: any) => {
    settSubaccounttypeFixedDto.clientName = row.clientName;
    settSubaccounttypeFixedDto.clientCode = row.clientCode;
    settSubaccounttypeFixedDto.clientId = row.clientId;
    settSubaccounttypeFixedDto.depositNo = "";
  };
  const rollBackClientCodeSelected = () => {
    settSubaccounttypeFixedDto.clientId = null;
    settSubaccounttypeFixedDto.depositNo = "";
    settSubaccounttypeFixedDto.accountName = "";
    settSubaccounttypeFixedDto.accountId = null;
  };
  //账户放大镜修改事件
  const onAccountCodeSelected = (row: any) => {
    settSubaccounttypeFixedDto.accountName = row.accountName;
    settSubaccounttypeFixedDto.accountCode = row.accountCode;
    settSubaccounttypeFixedDto.accountId = row.accountId;
    settSubaccounttypeFixedDto.clientId = row.clientId;
    settSubaccounttypeFixedDto.clientCode = row.clientCode;
    settSubaccounttypeFixedDto.clientName = row.clientName;
  };
  const isShowInfo = reactive<IsShowFixed>({
    clientClass: false,
    clientCode: false,
    accountCode: false,
    depositMonth: false,
    depositForm: false,
    interestType: false, //计价类型
    marginType: false, //保证金类型
    amountSubject: false,
    interestSubject: false,
    bookedinterestSubject: false
  });

  if (
    row.accountGroup === accountGroupEnum.FIXED ||
    row.accountGroup === accountGroupEnum.NOTICE ||
    row.accountGroup === accountGroupEnum.MARGINGROUP
  ) {
    //定期通知保证金账户组
    isShowInfo.clientClass = row.isClientClass === 1;
    isShowInfo.clientCode = row.client === 1;
    isShowInfo.accountCode = row.account === 1;
    isShowInfo.depositMonth = row.depositMonth === 1;
    isShowInfo.depositForm = row.depositForm === 1;
    isShowInfo.interestType = row.interestType === 1;
    isShowInfo.marginType = row.marginType === 1;
  }

  // 获取明细信息
  const getDetailInfo = (id: number | string) => {
    return httpTool.post(getfixedDetailUrl, { id: id }).then((res: any) => {
      getsettaccounttypeDetailInfo(res);
    });
  };
  // 保证金类型下拉框
  const marginTypeChange = (value: any, info: any) => {
    settSubaccounttypeFixedDto.marginTypeId = info.id;
    settSubaccounttypeFixedDto.marginTypeName = info.name;
    settSubaccounttypeFixedDto.marginTypeCode = info.code;
  };
  const depositTermChange = (value: any, info: any) => {
    settSubaccounttypeFixedDto.depositTerm = info.term;
    settSubaccounttypeFixedDto.termType = info.termType;
  };
  const handSuccess = (row: any) => {
    if (row.success) {
      goBack();
    }
  };
  // 点击提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await form.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  return {
    isShowInfo,
    settSubaccounttypeFixedDto,
    onClientCodeSelected,
    onAccountCodeSelected,
    depositTermChange,
    rollBackClientCodeSelected,
    marginTypeChange,
    updateInfo,
    removeInfo,
    goBack,
    form,
    getDetailInfo,
    handSuccess,
    formValidator
  };
};
export default useModify;
