import type { SettSubaccounttypeeLoanDto, SettAccountTypeDto, IsShowLoan } from "../types";
import { goPage } from "./usePage";
import { ref, reactive, onMounted } from "vue";

export const loanDetailAdd = (
  settSubaccounttypeLoanDto: SettSubaccounttypeeLoanDto,
  accountGroupEnum: any,
  row: SettAccountTypeDto
) => {
  onMounted(() => {
    settSubaccounttypeLoanDto.officeId = row.officeId;
    settSubaccounttypeLoanDto.currencyId = row.currencyId;
    settSubaccounttypeLoanDto.accountGroup = row.accountGroup;
  });
  // 返回列表页
  const goBack = () => {
    goPage("loanDetailList", { row });
  };
  const form = ref();
  // 保存
  const saveInfo = () => {
    //主表id
    settSubaccounttypeLoanDto.accountTypeId = row.id;
    //业务类型编码
    settSubaccounttypeLoanDto.accountTypeCode = row.accountTypeCode;
    //账户组
    settSubaccounttypeLoanDto.accountGroup = row.accountGroup;
    //机构相关信息
    settSubaccounttypeLoanDto.officeId = row.officeId;
    settSubaccounttypeLoanDto.officeCode = row.officeCode;
    settSubaccounttypeLoanDto.officeName = row.officeName;
    //币种相关信息
    settSubaccounttypeLoanDto.currencyId = row.currencyId;
    settSubaccounttypeLoanDto.currencyCode = row.currencyCode;
    settSubaccounttypeLoanDto.currencyName = row.currencyName;
    return settSubaccounttypeLoanDto;
  };
  //客户放大镜修改事件
  const onClientCodeSelected = (row: any) => {
    settSubaccounttypeLoanDto.clientName = row.clientName;
    settSubaccounttypeLoanDto.clientId = row.clientId;
    settSubaccounttypeLoanDto.depositNo = "";
  };
  const rollBackClientCodeSelected = () => {
    settSubaccounttypeLoanDto.clientId = null;
    settSubaccounttypeLoanDto.depositNo = "";
    settSubaccounttypeLoanDto.accountName = "";
    settSubaccounttypeLoanDto.accountId = null;
  };
  //委托方放大镜修改事件
  const onConsignClientCodeSelected = (row: any) => {
    settSubaccounttypeLoanDto.consignclientName = row.clientName;
    settSubaccounttypeLoanDto.consignclientCode = row.clientCode;
    settSubaccounttypeLoanDto.consignclientId = row.clientId;
  };
  const rollBackConsignClientCodeSelected = () => {
    settSubaccounttypeLoanDto.consignclientCode = "";
    settSubaccounttypeLoanDto.consignclientName = "";
    settSubaccounttypeLoanDto.consignclientId = null;
  };
  //合同放大镜修改事件
  const onContractCodeSelected = (row: any) => {
    settSubaccounttypeLoanDto.contractId = row.contractId;
    settSubaccounttypeLoanDto.contractCode = row.contractCode;
  };
  const rollBackContractCodeSelected = () => {
    settSubaccounttypeLoanDto.contractId = null;
    settSubaccounttypeLoanDto.contractCode = "";
  };
  //放款单放大镜修改事件
  const onPayFormCodeSelected = (row: any) => {
    settSubaccounttypeLoanDto.payformId = row.receiptId;
    settSubaccounttypeLoanDto.payformCode = row.receiptCode;
  };
  const rollBackPayFormCodeSelected = () => {
    settSubaccounttypeLoanDto.payformId = null;
    settSubaccounttypeLoanDto.payformCode = "";
  };
  //账户放大镜修改事件
  const onAccountCodeSelected = (row: any) => {
    settSubaccounttypeLoanDto.accountName = row.accountName;
    settSubaccounttypeLoanDto.accountId = row.accountId;
  };
  const isShowInfo = reactive<IsShowLoan>({
    draftType: false,
    clientClass: false,
    clientCode: false,
    loanType: false,
    loanMonth: false,
    consign: false,
    contract: false,
    loanForm: false,
    recourseFlag: false,
    commissionSubject: false
  });
  if (
    row.accountGroup === accountGroupEnum.SELFLOAN ||
    row.accountGroup === accountGroupEnum.DISCOUNT ||
    row.accountGroup === accountGroupEnum.CONSIGNLOAN ||
    row.accountGroup === accountGroupEnum.BANKGROUP ||
    row.accountGroup === accountGroupEnum.FACTORING
  ) {
    //贷款类账户组
    isShowInfo.loanType = row.loanType === 1;
    isShowInfo.loanMonth = row.loanMonth === 1;
    isShowInfo.consign = row.consign === 1;
    isShowInfo.clientCode = row.client === 1;
    isShowInfo.draftType = row.draftType === 1;
    isShowInfo.contract = row.contract === 1;
    isShowInfo.loanForm = row.loanForm === 1;
    isShowInfo.clientClass = row.isClientClass === 1;
    isShowInfo.isRecourse = row.isRecourse === 1;
    isShowInfo.commissionSubject = [accountGroupEnum.CONSIGNLOAN, accountGroupEnum.FACTORING].includes(
      settSubaccounttypeLoanDto.accountGroup
    );
  }
  // 点击提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await form.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  const handSuccess = (row: any) => {
    if (row.success) {
      goBack();
    }
  };
  return {
    settSubaccounttypeLoanDto,
    isShowInfo,
    saveInfo,
    goBack,
    formValidator,
    onClientCodeSelected,
    rollBackContractCodeSelected,
    onPayFormCodeSelected,
    rollBackPayFormCodeSelected,
    onContractCodeSelected,
    onAccountCodeSelected,
    onConsignClientCodeSelected,
    rollBackConsignClientCodeSelected,
    rollBackClientCodeSelected,
    form,
    handSuccess
  };
};
export default loanDetailAdd;
