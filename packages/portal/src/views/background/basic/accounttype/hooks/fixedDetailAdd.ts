import type { SettSubaccounttypeFixedDto, SettAccountTypeDto, IsShowFixed } from "../types";
import { goPage } from "./usePage";
import { ref, reactive, onMounted } from "vue";

export const fixedDetailAdd = (
  settSubaccounttypeFixedDto: SettSubaccounttypeFixedDto,
  accountGroupEnum: any,
  row: SettAccountTypeDto
) => {
  onMounted(() => {
    settSubaccounttypeFixedDto.officeId = row.officeId;
    settSubaccounttypeFixedDto.currencyId = row.currencyId;
    settSubaccounttypeFixedDto.accountGroup = row.accountGroup;
  });
  // 返回列表页
  const goBack = () => {
    goPage("fixedDetailList", { row });
  };
  const form = ref();
  // 保存
  const saveInfo = () => {
    //主表id
    settSubaccounttypeFixedDto.accountTypeId = row.id;
    //业务类型编码
    settSubaccounttypeFixedDto.accountTypeCode = row.accountTypeCode;
    //账户组
    settSubaccounttypeFixedDto.accountGroup = row.accountGroup;
    //机构相关信息
    settSubaccounttypeFixedDto.officeId = row.officeId as string;
    settSubaccounttypeFixedDto.officeCode = row.officeCode;
    settSubaccounttypeFixedDto.officeName = row.officeName;
    //币种相关信息
    settSubaccounttypeFixedDto.currencyId = row.currencyId as string;
    settSubaccounttypeFixedDto.currencyCode = row.currencyCode;
    settSubaccounttypeFixedDto.currencyName = row.currencyName;
    return settSubaccounttypeFixedDto;
  };
  //客户放大镜修改事件
  const onClientCodeSelected = (row: any) => {
    settSubaccounttypeFixedDto.clientName = row.clientName;
    settSubaccounttypeFixedDto.clientId = row.clientId;
    settSubaccounttypeFixedDto.depositNo = "";
  };
  const rollBackClientCodeSelected = () => {
    settSubaccounttypeFixedDto.clientId = null;
    settSubaccounttypeFixedDto.depositNo = "";
    settSubaccounttypeFixedDto.accountName = "";
    settSubaccounttypeFixedDto.accountId = null;
  };
  //账户放大镜修改事件
  const onAccountCodeSelected = (row: any) => {
    settSubaccounttypeFixedDto.accountName = row.accountName;
    settSubaccounttypeFixedDto.accountCode = row.accountCode;
    settSubaccounttypeFixedDto.accountId = row.accountId;
    settSubaccounttypeFixedDto.clientId = row.clientId;
    settSubaccounttypeFixedDto.clientCode = row.clientCode;
    settSubaccounttypeFixedDto.clientName = row.clientName;
  };
  const depositTermChange = (value: any, info: any) => {
    settSubaccounttypeFixedDto.depositTerm = info.term;
    settSubaccounttypeFixedDto.termType = info.termType;
  };
  const isShowInfo = reactive<IsShowFixed>({
    clientClass: false,
    clientCode: false,
    accountCode: false,
    depositMonth: false,
    depositForm: false,
    interestType: false, //计价类型
    marginType: false, //保证金类型
    amountSubject: false,
    interestSubject: false,
    bookedinterestSubject: false
  });
  if (
    row.accountGroup === accountGroupEnum.FIXED ||
    row.accountGroup === accountGroupEnum.NOTICE ||
    row.accountGroup === accountGroupEnum.MARGINGROUP
  ) {
    //定期通知保证金账户组
    isShowInfo.clientClass = row.isClientClass === 1;
    isShowInfo.clientCode = row.client === 1;
    isShowInfo.accountCode = row.account === 1;
    isShowInfo.depositMonth = row.depositMonth === 1;
    isShowInfo.depositForm = row.depositForm === 1;
    isShowInfo.interestType = row.interestType === 1;
    isShowInfo.marginType = row.marginType === 1;
  }
  // 点击提交 弹窗弹出之前校验整体表单项
  const formValidator = async () => {
    let result = true;
    await form.value.form.validate((valid: any) => {
      if (!valid) {
        result = false;
      }
    });
    return result;
  };
  // 保证金类型下拉框
  const marginTypeChange = (value: any, info: any) => {
    settSubaccounttypeFixedDto.marginTypeId = info.id;
    settSubaccounttypeFixedDto.marginTypeName = info.name;
    settSubaccounttypeFixedDto.marginTypeCode = info.code;
  };
  const handSuccess = (row: any) => {
    if (row.success) {
      goBack();
    }
  };
  return {
    settSubaccounttypeFixedDto,
    isShowInfo,
    saveInfo,
    marginTypeChange,
    goBack,
    formValidator,
    onClientCodeSelected,
    onAccountCodeSelected,
    depositTermChange,
    rollBackClientCodeSelected,
    form,
    handSuccess
  };
};
export default fixedDetailAdd;
