<template>
  <f-blank-scene :title="t('background.basic.accounttype.subModifyTitle')">
    <f-multi-form-panel ref="form" :model="settSubaccounttypeLoanDto" :rules="rules" :column="3">
      <f-panel>
        <!--贷款种类-->
        <f-form-item :label="t('background.basic.accounttype.loanType')" prop="loanType" v-if="isShowInfo.loanType">
          <f-select
            v-model="settSubaccounttypeLoanDto.loanType"
            auto-select
            value-key="value"
            label="label"
            :data="loanBizCategory"
          />
        </f-form-item>
        <!--贷款期限由-->
        <f-form-item
          :label="t('background.basic.accounttype.loanmonthStart')"
          prop="loanmonthStart"
          v-if="isShowInfo.loanMonth"
        >
          <f-number v-model="settSubaccounttypeLoanDto.loanmonthStart" :precision="0" :min="1" :max="100" />
        </f-form-item>
        <!--贷款期限至-->
        <f-form-item
          :label="t('background.basic.accounttype.loanmonthEnd')"
          prop="loanmonthEnd"
          v-if="isShowInfo.loanMonth"
        >
          <f-number v-model="settSubaccounttypeLoanDto.loanmonthEnd" :precision="0" :min="1" :max="100" />
        </f-form-item>
        <!-- 合同 -->
        <f-form-item :label="t('background.basic.accounttype.contract')" prop="contractId" v-if="isShowInfo.contract">
          <f-magnifier-single
            :title="t('background.basic.accounttype.contractMagnifier')"
            :url="contractInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.contractId"
            row-key="contractId"
            row-label="contractCode"
            input-key="contractCode"
            @change="onContractCodeSelected"
            @clear="rollBackContractCodeSelected"
            auto-init
          >
            <f-magnifier-column prop="contractCode" :label="t('background.basic.accounttype.contractCode')" />
          </f-magnifier-single>
        </f-form-item>
        <!-- 放款单 -->
        <f-form-item :label="t('background.basic.accounttype.loanForm')" prop="receiptId" v-if="isShowInfo.loanForm">
          <f-magnifier-single
            :title="t('background.basic.accounttype.loanFormMagnifier')"
            :url="payFormInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.payformCode"
            row-key="receiptCode"
            row-label="receiptCode"
            input-key="receiptCode"
            @change="onPayFormCodeSelected"
            @clear="rollBackPayFormCodeSelected"
            auto-init
          >
            <f-magnifier-column prop="receiptCode" :label="t('background.basic.accounttype.loanFormCode')" />
          </f-magnifier-single>
        </f-form-item>
        <!-- 委托方 -->
        <f-form-item
          :label="t('background.basic.accounttype.consign')"
          prop="consignclientCode"
          v-if="isShowInfo.consign"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.clientCode')"
            :url="clientInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.consignclientCode"
            row-key="clientCode"
            row-label="clientCode"
            @change="onConsignClientCodeSelected"
            @clear="rollBackConsignClientCodeSelected"
            auto-init
          >
            <f-magnifier-column prop="clientCode" :label="t('background.basic.accounttype.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('background.basic.accounttype.clientName')" />
          </f-magnifier-single>
        </f-form-item>
        <!-- 客户号 -->
        <f-form-item
          :label="
            [accountGroupEnum.CONSIGNLOAN].includes(settSubaccounttypeLoanDto.accountGroup)
              ? t('background.basic.accounttype.jkclient')
              : t('background.basic.accounttype.clientCode')
          "
          prop="clientCode"
          v-if="isShowInfo.clientCode"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.clientCode')"
            :url="clientInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.clientCode"
            row-key="clientCode"
            row-label="clientCode"
            @change="onClientCodeSelected"
            @clear="rollBackClientCodeSelected"
            auto-init
          >
            <f-magnifier-column prop="clientCode" :label="t('background.basic.accounttype.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('background.basic.accounttype.clientName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.draftType')"
          prop="drafttypeId"
          v-if="isShowInfo.draftType"
        >
          <f-select v-model="settSubaccounttypeLoanDto.drafttypeId" :data="billMedium" />
        </f-form-item>
        <!-- 客户类型 -->
        <f-form-item
          :label="t('background.basic.accounttype.clientClass')"
          prop="clientClass"
          v-if="isShowInfo.clientClass"
        >
          <f-select
            v-model="settSubaccounttypeLoanDto.clientClass"
            filterable
            :data="clientClass"
            :placeholder="t('background.basic.accounttype.clientClassPlaceHolder')"
          />
        </f-form-item>
        <!-- 有无追索权 -->
        <f-form-item
          :label="t('background.basic.accounttype.isRecourse')"
          prop="clientClass"
          v-if="isShowInfo.isRecourse"
        >
          <f-select v-model="settSubaccounttypeLoanDto.recourseFlag" filterable :data="haveRecourseType" />
        </f-form-item>
        <f-form-item
          :label="
            [accountGroupEnum.DISCOUNT].includes(settSubaccounttypeLoanDto.accountGroup)
              ? t('background.basic.accounttype.billSubject')
              : t('background.basic.accounttype.subject')
          "
          prop="amountsubject"
        >
          <f-magnifier-single
            :title="
              [accountGroupEnum.DISCOUNT].includes(settSubaccounttypeLoanDto.accountGroup)
                ? t('background.basic.accounttype.billSubject')
                : t('background.basic.accounttype.subject')
            "
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.amountsubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeLoanDto.officeId,
              currencyId: settSubaccounttypeLoanDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('background.basic.accounttype.interestSubject')" prop="interestsubject">
          <f-magnifier-single
            :title="t('background.basic.accounttype.interestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.interestsubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeLoanDto.officeId,
              currencyId: settSubaccounttypeLoanDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('background.basic.accounttype.bookedinterestSubject')" prop="bookedinterestsubject">
          <f-magnifier-single
            :title="t('background.basic.accounttype.bookedinterestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.bookedinterestsubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeLoanDto.officeId,
              currencyId: settSubaccounttypeLoanDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.commissionSubject')"
          prop="commissionSubject"
          v-if="isShowInfo.commissionSubject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.commissionSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeLoanDto.commissionSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeLoanDto.officeId,
              currencyId: settSubaccounttypeLoanDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="updateInfo"
        :url="updateloanUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="handSuccess"
      />
      <f-submit-state
        :gather-params="removeInfo"
        type="danger"
        :url="loanbatchDelete"
        operate="remove"
        @close="handSuccess"
      />
      <f-button type="info" plain @click.prevent="goBack">{{ t("background.basic.accounttype.queryList") }}</f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { accountLoan, accountLoanRules } from "../hooks/useLoan";
import useModify from "../hooks/loanDetailModify";
import { updateloanUrl, loanbatchDelete, clientInfo, subjectInfo, contractInfo, payFormInfo } from "../url";
import { onMounted } from "vue";
import { usePage } from "../hooks/usePage";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const { pageParams } = usePage();
//定义修改条件的下拉框静态值
//业务类型
//必填校验
const rules = accountLoanRules();
const { settSubaccounttypeLoanDto, getaccountloanDetailInfo } = accountLoan();

//客户类型
const clientClass = useConst("clientmanage.ClientNMClassEnum");
const haveRecourseType = useConst("basic.HaveRecourseType");
//状态
const accountGroupEnum = useConst("basic.AccountGroup");
//票据介质
const billMedium = useConst("accounting.BillType");
//贷款业务类型
const loanBizCategory = useConst("loancounter.LoanBusinessCategory");
const {
  isShowInfo,
  onClientCodeSelected,
  updateInfo,
  removeInfo,
  goBack,
  getDetailInfo,
  onContractCodeSelected,
  rollBackContractCodeSelected,
  onPayFormCodeSelected,
  rollBackPayFormCodeSelected,
  rollBackClientCodeSelected,
  onConsignClientCodeSelected,
  rollBackConsignClientCodeSelected,
  handSuccess,
  formValidator,
  form
} = useModify(settSubaccounttypeLoanDto, accountGroupEnum, pageParams?.row, getaccountloanDetailInfo);

// 页面初始化
onMounted(() => {
  getDetailInfo(pageParams?.id);
});
</script>
