<template>
  <f-drawer-scene
    ref="drawerRef"
    :title="t('background.basic.accounttype.detailTitle')"
    destroy-on-close
    @close="setFalseToVisible"
  >
    <template #default>
      <f-multi-form-panel ref="form1" :model="settaccounttypeDto" :column="3">
        <f-panel>
          <f-form-item :label="t('background.basic.accounttype.officeName')" prop="officeName">
            <f-input v-model="settaccounttypeDto.officeName" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.currency')" prop="currencyName">
            <f-input v-model="settaccounttypeDto.currencyName" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.accountGroup')" prop="accountGroup">
            <f-scene-view :search="settaccounttypeDto.accountGroup" :data="accountGroup" params="value" />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.accountTypeCode')">
            <f-input v-model="settaccounttypeDto.accountTypeCode" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.accountType')">
            <f-input v-model="settaccounttypeDto.accountType" disabled />
          </f-form-item>
          <f-form-item />
          <f-form-item :label="t('background.basic.accounttype.existSubclassValue')">
            <f-switch
              v-model="settaccounttypeDto.existSubclass"
              :active-value="yesOrNo.YES"
              :inactive-value="yesOrNo.NO"
              disabled
            />
          </f-form-item>
          <!--缺省-->
          <f-form-item />
          <!--缺省-->
          <f-form-item />
          <f-form-item :employ="3" :label="t('background.basic.accounttype.existSubclassMes')">
            <f-checkbox
              v-model="settaccounttypeDto.consign"
              v-if="isShowInfo.consign"
              :label="t('background.basic.accounttype.consign')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.client"
              v-if="isShowInfo.client"
              :label="
                [accountGroup.CONSIGNLOAN].includes(settaccounttypeDto.accountGroup)
                  ? t('background.basic.accounttype.jkclient')
                  : t('background.basic.accounttype.client')
              "
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.account"
              v-if="isShowInfo.account"
              :label="t('background.basic.accounttype.account')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.interestType"
              v-if="isShowInfo.interestType"
              :label="t('background.basic.accounttype.interestType')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.marginType"
              v-if="isShowInfo.marginType"
              :label="t('background.basic.accounttype.marginType')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.loanType"
              v-if="isShowInfo.loanType"
              :label="t('background.basic.accounttype.loanType')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.loanMonth"
              v-if="isShowInfo.loanMonth"
              :label="t('background.basic.accounttype.loanMonth')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.contract"
              v-if="isShowInfo.contract"
              :label="t('background.basic.accounttype.contract')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.loanForm"
              v-if="isShowInfo.loanForm"
              :label="t('background.basic.accounttype.loanForm')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.draftType"
              v-if="isShowInfo.draftType"
              :label="t('background.basic.accounttype.draftType')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.isClientClass"
              v-if="isShowInfo.isClientClass"
              :label="t('background.basic.accounttype.isClientClass')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.depositMonth"
              v-if="isShowInfo.depositMonth"
              :label="t('background.basic.accounttype.depositMonth')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <f-checkbox
              v-model="settaccounttypeDto.depositForm"
              v-if="isShowInfo.depositForm"
              :label="t('background.basic.accounttype.depositForm')"
              :true-label="1"
              :false-label="2"
              disabled
            />
            <!-- 有无追索权 -->
            <f-checkbox
              v-model="settaccounttypeDto.isRecourse"
              v-if="isShowInfo.isRecourse"
              :label="t('background.basic.accounttype.isRecourse')"
              :true-label="1"
              :false-label="2"
              disabled
            />
          </f-form-item>
          <f-form-item
            :label="
              accountGroup.DISCOUNT === settaccounttypeDto.accountGroup
                ? t('background.basic.accounttype.billSubject')
                : t('background.basic.accounttype.subject')
            "
            v-if="isShowInfo.subject"
          >
            <f-input v-model="settaccounttypeDto.subject" disabled />
          </f-form-item>
          <f-form-item
            :label="
              [
                accountGroup.SELFLOAN,
                accountGroup.DISCOUNT,
                accountGroup.CONSIGNLOAN,
                accountGroup.BANKGROUP,
                accountGroup.FACTORING
              ].includes(settaccounttypeDto.accountGroup)
                ? t('background.basic.accounttype.srinterestSubject')
                : t('background.basic.accounttype.interestSubject')
            "
            v-if="isShowInfo.interestSubject"
          >
            <f-input v-model="settaccounttypeDto.interestSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.bookedinterestSubject')"
            v-if="isShowInfo.bookedinterestSubject"
          >
            <f-input v-model="settaccounttypeDto.bookedinterestSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.negotiateinterestSubject')"
            v-if="isShowInfo.negotiateinterestSubject"
          >
            <f-input v-model="settaccounttypeDto.negotiateinterestSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.negotiateBookinterestSubject')"
            v-if="isShowInfo.negotiateBookinterestSubject"
          >
            <f-input v-model="settaccounttypeDto.negotiateBookinterestSubject" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.commissionSubject')" v-if="isShowInfo.commissionSubject">
            <f-input v-model="settaccounttypeDto.commissionSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.leaseassertSubject')"
            v-if="isShowInfo.leaseassertSubject"
          >
            <f-input v-model="settaccounttypeDto.leaseassertSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.longTermableReceiveSubject')"
            v-if="isShowInfo.longTermableReceiveSubject"
          >
            <f-input v-model="settaccounttypeDto.longTermableReceiveSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.unrealizedIncomeSubject')"
            v-if="isShowInfo.unrealizedIncomeSubject"
          >
            <f-input v-model="settaccounttypeDto.unrealizedIncomeSubject" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.incometaxSubject')" v-if="isShowInfo.incometaxSubject">
            <f-input v-model="settaccounttypeDto.incometaxSubject" disabled />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.outputtaxSubject')" v-if="isShowInfo.outputtaxSubject">
            <f-input v-model="settaccounttypeDto.outputtaxSubject" disabled />
          </f-form-item>
          <f-form-item
            :label="t('background.basic.accounttype.accountPatternValue')"
            prop="accountPattern"
            v-if="isShowInfo.accountPattern"
          >
            <f-scene-view :search="settaccounttypeDto.accountPattern" :data="accountPattern" params="value" />
          </f-form-item>
          <f-form-item :label="t('background.basic.accounttype.payTypeValue')" prop="payType" v-if="isShowInfo.payType">
            <f-scene-view :search="settaccounttypeDto.payType" :data="payType" params="value" />
          </f-form-item>
        </f-panel>
      </f-multi-form-panel>
    </template>
    <template #footer>
      <f-button type="info" @click="setFalseToVisible">{{ t("background.basic.accounttype.close") }}</f-button>
    </template>
  </f-drawer-scene>
</template>
<script setup lang="ts">
import { detailProps } from "./Detail";
import { accountType } from "../../hooks/useAccountType";
import useDetail from "../../hooks/useDetail";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
//账户组
const accountGroup = useConst("basic.AccountGroup");
//账户模式
const accountPattern = useConst("basic.AccountPattern");
//付款方式
const payType = useConst("basic.PayType");
//是否
const yesOrNo = useConst("basic.YesOrNo");
//必填校验payType
defineOptions({
  name: "Detail"
});
const props = defineProps(detailProps);
const { settaccounttypeDto, getsettaccounttypeDetailInfo } = accountType();
const { setTrueToVisible, setFalseToVisible, drawerRef, isShowInfo } = useDetail(
  settaccounttypeDto,
  getsettaccounttypeDetailInfo,
  accountGroup,
  props
);
defineExpose({ setTrueToVisible, setFalseToVisible });
</script>
