<template>
  <f-blank-scene :title="t('background.basic.accounttype.modifyTitle')">
    <f-multi-form-panel ref="form1" :model="settaccounttypeDto" :rules="rules" :column="3">
      <f-panel>
        <!--机构-->
        <f-form-item :label="t('background.basic.accounttype.office')" prop="officeId">
          <f-input v-model="settaccounttypeDto.officeName" disabled />
        </f-form-item>
        <!--币种-->
        <f-form-item :label="t('background.basic.accounttype.currency')" prop="currencyId">
          <f-input v-model="settaccounttypeDto.currencyName" disabled />
        </f-form-item>
        <!-- 账户组 -->
        <f-form-item :label="t('background.basic.accounttype.accountGroup')" prop="accountGroup">
          <f-select v-model="settaccounttypeDto.accountGroup" filterable :data="accountGroup" disabled />
        </f-form-item>
        <!--账户业务类型编码-->
        <f-form-item :label="t('background.basic.accounttype.accountTypeCode')" prop="accountTypeCode">
          <f-input v-model="settaccounttypeDto.accountTypeCode" disabled />
        </f-form-item>
        <!--账户类型-->
        <f-form-item :label="t('background.basic.accounttype.accountType')" prop="accountType">
          <f-input v-model="settaccounttypeDto.accountType" disabled />
        </f-form-item>
        <!--缺省-->
        <f-form-item />

        <f-form-item :label="t('background.basic.accounttype.existSubclassValue')" v-if="isShowInfo.existSubclass">
          <f-switch
            v-model="settaccounttypeDto.existSubclass"
            :active-value="yesOrNo.YES"
            :inactive-value="yesOrNo.NO"
            @change="existSubclassChange"
          />
        </f-form-item>

        <f-form-item
          :employ="3"
          :label="t('background.basic.accounttype.existSubclassMes')"
          v-if="settaccounttypeDto.existSubclass === yesOrNo.YES"
        >
          <div>
            <f-checkbox
              v-model="settaccounttypeDto.consign"
              v-if="isShowInfo.consign"
              :label="t('background.basic.accounttype.consign')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.client"
              v-if="isShowInfo.client"
              :label="
                [accountGroup.CONSIGNLOAN].includes(settaccounttypeDto.accountGroup)
                  ? t('background.basic.accounttype.jkclient')
                  : t('background.basic.accounttype.client')
              "
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.account"
              v-if="isShowInfo.account"
              :label="t('background.basic.accounttype.account')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.interestType"
              v-if="isShowInfo.interestType"
              :label="t('background.basic.accounttype.interestType')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.marginType"
              v-if="isShowInfo.marginType"
              :label="t('background.basic.accounttype.marginType')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.loanType"
              v-if="isShowInfo.loanType"
              :label="t('background.basic.accounttype.loanType')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.loanMonth"
              v-if="isShowInfo.loanMonth"
              :label="t('background.basic.accounttype.loanMonth')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.contract"
              v-if="isShowInfo.contract"
              :label="t('background.basic.accounttype.contract')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.loanForm"
              v-if="isShowInfo.loanForm"
              :label="t('background.basic.accounttype.loanForm')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.draftType"
              v-if="isShowInfo.draftType"
              :label="t('background.basic.accounttype.draftType')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.isClientClass"
              v-if="isShowInfo.isClientClass"
              :label="t('background.basic.accounttype.isClientClass')"
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.depositMonth"
              v-if="isShowInfo.depositMonth"
              :label="
                accountGroup.FIXED === settaccounttypeDto.accountGroup
                  ? t('background.basic.accounttype.depositMonth')
                  : t('background.basic.accounttype.noticeDepositMonth')
              "
              :true-label="1"
              :false-label="2"
            />
            <f-checkbox
              v-model="settaccounttypeDto.depositForm"
              v-if="isShowInfo.depositForm"
              :label="t('background.basic.accounttype.depositForm')"
              :true-label="1"
              :false-label="2"
            />
            <!-- 有无追索权 -->
            <f-checkbox
              v-model="settaccounttypeDto.isRecourse"
              v-if="isShowInfo.isRecourse"
              :label="t('background.basic.accounttype.isRecourse')"
              :true-label="1"
              :false-label="2"
            />
          </div>
        </f-form-item>
        <f-form-item
          :label="
            accountGroup.DISCOUNT === settaccounttypeDto.accountGroup
              ? t('background.basic.accounttype.billSubject')
              : t('background.basic.accounttype.subject')
          "
          prop="subject"
          v-if="isShowInfo.subject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.subject')"
            :url="subjectInfo"
            method="post"
            v-model="settaccounttypeDto.subject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settaccounttypeDto.officeId,
              currencyId: settaccounttypeDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="
            [
              accountGroup.SELFLOAN,
              accountGroup.DISCOUNT,
              accountGroup.CONSIGNLOAN,
              accountGroup.BANKGROUP,
              accountGroup.FACTORING
            ].includes(settaccounttypeDto.accountGroup)
              ? t('background.basic.accounttype.srinterestSubject')
              : t('background.basic.accounttype.interestSubject')
          "
          prop="interestSubject"
          v-if="isShowInfo.interestSubject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.interestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settaccounttypeDto.interestSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settaccounttypeDto.officeId,
              currencyId: settaccounttypeDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.negotiateinterestSubject')"
          prop="negotiateinterestSubject"
          v-if="isShowInfo.negotiateinterestSubject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.negotiateinterestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settaccounttypeDto.negotiateinterestSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settaccounttypeDto.officeId,
              currencyId: settaccounttypeDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.bookedinterestSubject')"
          prop="bookedinterestSubject"
          v-if="isShowInfo.bookedinterestSubject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.bookedinterestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settaccounttypeDto.bookedinterestSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settaccounttypeDto.officeId,
              currencyId: settaccounttypeDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.negotiateBookinterestSubject')"
          prop="negotiateBookinterestSubject"
          v-if="isShowInfo.negotiateBookinterestSubject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.negotiateBookinterestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settaccounttypeDto.negotiateBookinterestSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settaccounttypeDto.officeId,
              currencyId: settaccounttypeDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.commissionSubject')"
          prop="commissionSubject"
          v-if="isShowInfo.commissionSubject"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.negotiateBookinterestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settaccounttypeDto.commissionSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settaccounttypeDto.officeId,
              currencyId: settaccounttypeDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <!-- 账户模式 -->
        <f-form-item
          :label="t('background.basic.accounttype.accountPatternValue')"
          prop="accountPattern"
          v-if="isShowInfo.accountPattern"
        >
          <f-select
            v-model="settaccounttypeDto.accountPattern"
            :data="accountPattern"
            :placeholder="t('background.basic.accounttype.accountPatternPlaceHolder')"
          />
        </f-form-item>
        <!-- 付款方式 -->
        <f-form-item :label="t('background.basic.accounttype.payTypeValue')" prop="payType" v-if="isShowInfo.payType">
          <f-select
            v-model="settaccounttypeDto.payType"
            :data="payType"
            :placeholder="t('background.basic.accounttype.payTypePlaceHolder')"
          />
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state :gather-params="updateInfo" :url="updateUrl" operate="save" @close="handSuccess" />
      <f-submit-state
        :gather-params="removeInfo"
        type="danger"
        :url="batchDelete"
        operate="remove"
        @close="handSuccess"
      />
      <f-button type="info" plain @click.prevent="goBack">{{ t("background.basic.accounttype.queryList") }}</f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { accountType, accounttypeSettRules } from "../hooks/useAccountType";
import useModify from "../hooks/useModify";
import { updateUrl, subjectInfo, batchDelete } from "../url";
import { onMounted } from "vue";
import { usePage } from "../hooks/usePage";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const { pageParams } = usePage();
//定义修改条件的下拉框静态值
//业务类型
//账户组
const accountGroupEnum = useConst("basic.AccountGroup");
//活期类账户、定期类账户、通知类账户、保证金类账户、自营贷款类账户、贴现类账户、委托贷款类账户、银团贷款类账户、保理类账户
const accountGroup = accountGroupEnum.pickConst([
  accountGroupEnum.CURRENT,
  accountGroupEnum.FIXED,
  accountGroupEnum.NOTICE,
  accountGroupEnum.MARGINGROUP,
  accountGroupEnum.SELFLOAN,
  accountGroupEnum.DISCOUNT,
  accountGroupEnum.CONSIGNLOAN,
  accountGroupEnum.BANKGROUP,
  accountGroupEnum.FACTORING,
  accountGroupEnum.CONSIGNDEPOSIT
]);
//账户模式
const accountPattern = useConst("basic.AccountPattern");
//付款方式
const payType = useConst("basic.PayType");
//是否
const yesOrNo = useConst("basic.YesOrNo");
//必填校验
const rules = accounttypeSettRules();
const { settaccounttypeDto, getsettaccounttypeDetailInfo } = accountType();

const { isShowInfo, updateInfo, removeInfo, goBack, getDetailInfo, existSubclassChange, handSuccess } = useModify(
  settaccounttypeDto,
  accountGroup,
  getsettaccounttypeDetailInfo
);

// 页面初始化
onMounted(() => {
  getDetailInfo(pageParams?.id);
});
</script>
