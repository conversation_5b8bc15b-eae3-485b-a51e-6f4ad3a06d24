<template>
  <f-blank-scene :title="t('background.basic.accounttype.subAddTitle')">
    <f-multi-form-panel ref="form" :model="settSubaccounttypeFixedDto" :rules="rules" :column="3">
      <f-panel>
        <!-- 客户类型 -->
        <f-form-item
          :label="t('background.basic.accounttype.clientClass')"
          prop="clientClass"
          v-if="isShowInfo.clientClass"
        >
          <f-select
            v-model="settSubaccounttypeFixedDto.clientClass"
            filterable
            :data="clientClass"
            :placeholder="t('background.basic.accounttype.clientClassPlaceHolder')"
          />
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.clientCode')"
          prop="clientCode"
          v-if="isShowInfo.clientCode"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.clientCode')"
            :url="clientInfo"
            method="post"
            v-model="settSubaccounttypeFixedDto.clientCode"
            row-key="clientCode"
            row-label="clientCode"
            :params="{
              clientId: settSubaccounttypeFixedDto.clientId
            }"
            @change="onClientCodeSelected"
            @clear="rollBackClientCodeSelected"
            auto-init
          >
            <f-magnifier-column prop="clientCode" :label="t('background.basic.accounttype.clientCode')" />
            <f-magnifier-column prop="clientName" :label="t('background.basic.accounttype.clientName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.accountCode')"
          prop="accountCode"
          v-if="isShowInfo.accountCode"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.accountCode')"
            :url="accountInfo"
            method="post"
            v-model="settSubaccounttypeFixedDto.accountCode"
            row-key="accountCode"
            row-label="accountCode"
            :params="{
              clientId: settSubaccounttypeFixedDto.clientId,
              accountGroup: settSubaccounttypeFixedDto.accountGroup
            }"
            row.accountGroup
            @change="onAccountCodeSelected"
            auto-init
          >
            <f-magnifier-column prop="accountCode" :label="t('background.basic.accounttype.accountCode')" />
            <f-magnifier-column prop="accountName" :label="t('background.basic.accounttype.accountName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.depositMonth')"
          prop="termShow"
          v-if="isShowInfo.depositMonth"
        >
          <f-select
            v-model="settSubaccounttypeFixedDto.termShow"
            ref="termRef"
            value-key="termShow"
            label="termShow"
            :url="termListUrl"
            method="post"
            :extra-data="{
              currencyId: pageParams?.row.currencyId,
              depositVarieties: pageParams?.row.accountGroup
            }"
            auto-select
            @change="depositTermChange"
          />
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.depositNo')"
          prop="depositNo"
          v-if="isShowInfo.depositForm"
        >
          <f-magnifier-single
            :title="t('background.basic.accounttype.depositNoMagnifier')"
            :url="getDepositNo"
            method="post"
            v-model="settSubaccounttypeFixedDto.depositNo"
            row-key="depositNo"
            row-label="depositNo"
            auto-init
            :params="{
              officeId: pageParams?.row.officeId,
              currencyId: pageParams?.row.currencyId,
              clientId: settSubaccounttypeFixedDto.clientId,
              accountGroup: pageParams?.row.accountGroup
            }"
          >
            <f-magnifier-column prop="depositNo" :label="t('background.basic.accounttype.depositNo')" />
            <f-magnifier-column
              formatter="amount"
              prop="amount"
              :label="t('background.basic.accounttype.accountBalance')"
            />
            <f-magnifier-column
              formatter="amount"
              prop="balance"
              :label="t('background.basic.accounttype.accountCanUsedBalance')"
            />
            <f-magnifier-column prop="endDate" :label="t('background.basic.accounttype.endDate')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.interestType')"
          prop="interestType"
          v-if="isShowInfo.interestType"
        >
          <f-select v-model="settSubaccounttypeFixedDto.interestType" :data="pricieType" />
        </f-form-item>
        <f-form-item
          :label="t('background.basic.accounttype.marginType')"
          prop="marginTypeId"
          v-if="isShowInfo.marginType"
        >
          <f-select
            ref="marginTypeRef"
            v-model="settSubaccounttypeFixedDto.marginTypeId"
            value-key="id"
            label="name"
            :url="getMarginTypeInfo"
            @change="marginTypeChange"
            method="post"
            auto-select
          />
        </f-form-item>
        <f-form-item :label="t('background.basic.accounttype.subject')" prop="amountSubject">
          <f-magnifier-single
            :title="t('background.basic.accounttype.subject')"
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeFixedDto.amountSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeFixedDto.officeId,
              currencyId: settSubaccounttypeFixedDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('background.basic.accounttype.interestSubject')" prop="interestSubject">
          <f-magnifier-single
            :title="t('background.basic.accounttype.interestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeFixedDto.interestSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeFixedDto.officeId,
              currencyId: settSubaccounttypeFixedDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
        <f-form-item :label="t('background.basic.accounttype.bookedinterestSubject')" prop="bookedinterestSubject">
          <f-magnifier-single
            :title="t('background.basic.accounttype.bookedinterestSubject')"
            :url="subjectInfo"
            method="post"
            v-model="settSubaccounttypeFixedDto.bookedinterestSubject"
            row-key="subjectCode"
            row-label="subjectCode"
            input-key="codeAndName"
            :params="{
              officeId: settSubaccounttypeFixedDto.officeId,
              currencyId: settSubaccounttypeFixedDto.currencyId
            }"
            auto-init
          >
            <f-magnifier-column prop="subjectCode" :label="t('background.basic.accounttype.subjectCode')" />
            <f-magnifier-column prop="subjectName" :label="t('background.basic.accounttype.subjectName')" />
          </f-magnifier-single>
        </f-form-item>
      </f-panel>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="saveInfo"
        :url="savefixedUrl"
        operate="save"
        :before-trigger="formValidator"
        @close="handSuccess"
      />
      <f-button type="info" plain @click.prevent="goBack">{{ t("background.basic.accounttype.queryList") }}</f-button>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { accountFixed, accountFixedRules } from "../hooks/useFixed";
import { fixedDetailAdd } from "../hooks/fixedDetailAdd";
import {
  savefixedUrl,
  subjectInfo,
  clientInfo,
  accountInfo,
  getDepositNo,
  termListUrl,
  getMarginTypeInfo
} from "../url";
import { usePage } from "../hooks/usePage";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
const { pageParams } = usePage();
const { t } = useI18n();

//定义修改条件的下拉框静态值
//客户类型
const clientClass = useConst("clientmanage.ClientNMClassEnum");
//计划类型
const pricieType = useConst("basic.PricieType");
//必填校验
const rules = accountFixedRules();
const { settSubaccounttypeFixedDto } = accountFixed();
//状态
const accountGroupEnum = useConst("basic.AccountGroup");

const {
  isShowInfo,
  saveInfo,
  goBack,
  formValidator,
  marginTypeChange,
  form,
  onClientCodeSelected,
  onAccountCodeSelected,
  depositTermChange,
  rollBackClientCodeSelected,
  handSuccess
} = fixedDetailAdd(settSubaccounttypeFixedDto, accountGroupEnum, pageParams?.row);
</script>
