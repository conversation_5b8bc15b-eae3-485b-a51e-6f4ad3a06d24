<template>
  <f-query-scene :title="t('antimoneylaundering.messageManage.add.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        border
        :table-columns="tableColumns"
        :url="list"
        :form-data="state.form"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-messageManage-add-query-001"
        table-comp-id="antimoneylaundering-messageManage-add-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        :post-params="postParams"
        :show-summation-sum="false"
        :show-print="false"
        :export-url="exportUrl"
        @select="handleCheckedList"
        @select-all="handleCheckedList"
      >
        <template #operate>
          <f-button @click="goPage('selection')" type="primary">
            {{ t("antimoneylaundering.messageManage.add.add") }}
          </f-button>
          <f-submit-state
            operate="remove"
            type="danger"
            :gather-params="getRemoveParams"
            :url="batchRemove"
            :disabled="removeDisabled"
            is-batch
            compatible
            @close="handleSearch"
            :batchConfirmMap="{
              success: t('common.operate.deleteSuccess'),
              fail: t('common.operate.deleteFail')
            }"
          />
          <f-submit-state
            operate="submit"
            type="primary"
            :gather-params="getRemoveParams"
            :url="batchSubmit"
            :disabled="removeDisabled"
            is-batch
            compatible
            @close="handleSearch"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('antimoneylaundering.messageManage.add.status')" prop="statusList">
            <f-select
              v-model="state.form.statusList"
              :data="messgaeStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('antimoneylaundering.messageManage.add.sendStatus')" prop="sendStatusList">
            <f-select
              v-model="state.form.sendStatusList"
              :data="messageSendStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
            />
          </f-form-item>
          <f-form-item :label="t('antimoneylaundering.messageManage.add.messageDate')" prop="messageDate">
            <f-lax-range-date-picker v-model="state.form.messageDate" />
          </f-form-item>
        </template>
        <template #file-name="{ row }">
          <f-button type="primary" @click="openDetail(row)" link>
            {{ row.filename }}
          </f-button>
        </template>
        <template #receipt="{ row }">
          <f-button type="primary" @click="goReceipt(row)" link>
            {{ row.receipttypestr }}
          </f-button>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="goModify(row)">
            <template #suffix v-if="row.statusId === messgaeStatus.APPROVED">
              <f-submit-state
                :gather-params="
                  () => {
                    return {
                      ...row,
                      ...cancelModel
                    };
                  }
                "
                :url="revoke"
                operate="cancel"
                :operate-name="t('common.operate.cancel')"
                confirm-text=" "
                :icon="DtgCopy"
                link
                :is-show-result-btn-group="false"
                :result-confirm="t('common.operate.cancelSuccess')"
                @close="handleSearch"
              >
                <template #confirmEdit>
                  <f-multi-form-panel :model="cancelModel">
                    <f-form-item :label="t('common.operate.cancelReason')" prop="revokeReason">
                      <f-input v-model="cancelModel.revokeReason" size="large" maxlength="50" />
                    </f-form-item>
                  </f-multi-form-panel>
                </template>
              </f-submit-state>
            </template>
          </OperateButton>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>
<script setup lang="ts">
import Detail from "./Detail.vue";
import { reactive } from "vue";
import { useEnum, goPage } from "../hooks";
import { useModelRange } from "@/hooks/conversion";
import { useBizList, useListDetail } from "@/hooks/biz";
import { list, operateSubmit, remove, revoke, batchRemove, batchSubmit, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { DtgCopy } from "@dtg/frontend-plus-icons";
const { t } = useI18n();
const cancelModel = reactive({
  revokeReason: ""
});
const { messageSendStatus, messgaeStatus, messageType } = useEnum();

const { queryTable, handleCheckedList, controlOperateDisabled, handleSearch, gatherCheckedList } = useBizList();
const removeDisabled = controlOperateDisabled(
  messgaeStatus.omitConst([messgaeStatus.SAVE, messgaeStatus.REFUSED]),
  "statusId",
  true
);
const { id, detail, open } = useListDetail();

const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};

const state = reactive({
  form: {
    statusList: [],
    sendStatusList: [],
    messageDate: [],
    messageType: String(messageType.NSMESSAGE) // 只查新增报文
  }
});
const { postParams } = useModelRange(["messageDate"]);
const generalButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: [messgaeStatus.SAVE, messgaeStatus.REFUSED, messgaeStatus.TMPSAVE].includes(row.statusid)
    },
    {
      type: "remove",
      isShow: [messgaeStatus.SAVE, messgaeStatus.REFUSED, messgaeStatus.TMPSAVE].includes(row.statusid),
      submitComOpt: {
        url: remove,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleSearch();
          }
        }
      }
    },
    {
      type: "submit",
      isShow: [messgaeStatus.SAVE, messgaeStatus.REFUSED].includes(row.statusid),
      submitComOpt: {
        url: operateSubmit,
        gatherParams: () => {
          return row;
        },
        close: (response: any) => {
          if (response.success) {
            handleSearch();
          }
        }
      }
    },
    {
      isShow: true,
      type: "process",
      trackingParams: {
        recordId: row.id, // 单据唯一id
        systemCode: "T91", // 当前功能systemCode
        transType: "T910003", // 当前业务类型枚举
        agencyId: 11, // 机构
        currencyId: 0, // 币种
        globalSerialNo: row.globalSerialNo // 全局流水号 非必填
      }
    }
  ];
};
const goModify = (row: Record<string, any>) => {
  goPage("modify", row);
};
const goReceipt = (row: Record<string, any>) => {
  goPage("receipt", row);
};
const tableColumns = [
  {
    prop: "selection",
    type: "selection",
    fixed: "left",
    reserveSelection: true
  },
  {
    prop: "messagedate",
    label: t("antimoneylaundering.messageManage.add.messageDate")
  },
  {
    prop: "messagetype",
    label: t("antimoneylaundering.messageManage.add.messageType")
  },
  {
    prop: "filename",
    label: t("antimoneylaundering.messageManage.add.fileName"),
    width: "250px",
    slots: {
      default: "file-name"
    }
  },
  {
    prop: "statusid",
    label: t("antimoneylaundering.messageManage.add.status"),
    formatter: { name: "const", const: "antimoneylaundering.MessageStatus" }
  },
  {
    prop: "sendstatus",
    label: t("antimoneylaundering.messageManage.add.sendStatus")
  },
  {
    prop: "receipttypestr",
    label: t("antimoneylaundering.messageManage.add.receiptTypeStr"),
    slots: {
      default: "receipt"
    }
  },
  {
    prop: "inputUserName",
    label: t("antimoneylaundering.messageManage.add.inputUserName")
  },
  {
    prop: "inputtime",
    label: t("antimoneylaundering.messageManage.add.inputTime")
  },
  {
    prop: "revokereason",
    label: t("antimoneylaundering.messageManage.add.revokeReason")
  },
  {
    prop: "operate",
    label: t("antimoneylaundering.messageManage.add.operate"),
    fixed: "right",
    width: "200px",
    slots: {
      default: "buttons"
    }
  }
];
const getRemoveParams = () => {
  return {
    list: gatherCheckedList()
  };
};
</script>
