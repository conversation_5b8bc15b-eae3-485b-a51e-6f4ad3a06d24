<template>
  <f-query-scene :title="t('antimoneylaundering.messageManage.report.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="list"
        :form-data="state.form"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-messageManage-report-query-001"
        table-comp-id="antimoneylaundering-messageManage-report-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        :post-params="postParams"
        :show-summation-sum="false"
        :show-print="false"
        :export-url="exportUrl"
        @select="handleCheckedList"
        @select-all="handleCheckedList"
      >
        <template #operate>
          <f-submit-state
            operate="save"
            :gather-params="getRemoveParams"
            :url="batchUpdateFile"
            :operate-name="t('antimoneylaundering.messageManage.report.updateFile')"
            :confirm-text="t('antimoneylaundering.messageManage.report.isUpdateFile')"
            :result-confirm="t('antimoneylaundering.messageManage.report.updateFileSuccess')"
            :result-title="t('antimoneylaundering.messageManage.report.updateFile')"
            :batchConfirmMap="{
              success: t('antimoneylaundering.messageManage.report.updateFileSuccess'),
              fail: t('antimoneylaundering.messageManage.report.updateFileFail')
            }"
            is-batch
            :disabled="!checkedListHasValue"
            @close="handleSearch"
          />
          <f-submit-state
            operate="save"
            :gather-params="getRemoveParams"
            :url="batchCreateFile"
            :operate-name="t('antimoneylaundering.messageManage.report.createFile')"
            :confirm-text="t('antimoneylaundering.messageManage.report.isCreateFile')"
            :result-confirm="t('antimoneylaundering.messageManage.report.createFileSuccess')"
            :result-title="t('antimoneylaundering.messageManage.report.createFile')"
            :batchConfirmMap="{
              success: t('antimoneylaundering.messageManage.report.createFileSuccess'),
              fail: t('antimoneylaundering.messageManage.report.createFileFail')
            }"
            is-batch
            :disabled="batchCreateFileDisabled"
            @close="handleSearch"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('antimoneylaundering.messageManage.report.sendstatus')" prop="sendStatusList">
            <f-select
              v-model="state.form.sendStatusList"
              :data="messageSendStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
            />
          </f-form-item>
        </template>
        <template #send-status="{ row }">
          <f-select v-model="row.sendstatus" :data="messageSendStatus" />
        </template>
        <template #buttons="{ row }">
          <f-submit-state
            operate="save"
            :gather-params="() => row"
            link
            :url="updateFile"
            :operate-name="t('antimoneylaundering.messageManage.report.updateFile')"
            :confirm-text="t('antimoneylaundering.messageManage.report.isUpdateFile')"
            :result-confirm="t('antimoneylaundering.messageManage.report.updateFileSuccess')"
            :result-title="t('antimoneylaundering.messageManage.report.updateFile')"
            @close="handleSearch"
          />
          <template v-if="!row.rpfilename">
            <f-submit-state
              operate="save"
              :gather-params="() => row"
              link
              :url="createFile"
              :operate-name="t('antimoneylaundering.messageManage.report.createFile')"
              :confirm-text="t('antimoneylaundering.messageManage.report.isCreateFile')"
              :result-confirm="t('antimoneylaundering.messageManage.report.createFileSuccess')"
              :result-title="t('antimoneylaundering.messageManage.report.createFile')"
              @close="handleSearch"
            />
          </template>
          <template v-if="row.rpfilename">
            <f-button type="primary" link @click="doDownload(row)">
              {{ t("antimoneylaundering.messageManage.report.download") }}
            </f-button>
          </template>
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import httpTool from "@/utils/http";
import { reactive, computed } from "vue";
import { useEnum } from "../hooks";
import { useModelRange } from "@/hooks/conversion";
import { useBizList } from "@/hooks/biz";
import { list, updateFile, batchUpdateFile, createFile, batchCreateFile, downloadZip, exportUrl } from "../url";
import { useI18n } from "vue-i18n";
import { FMessageBox } from "@dtg/frontend-plus";
const { t } = useI18n();
const { messageSendStatus } = useEnum();

const { queryTable, handleCheckedList, checkedListHasValue, handleSearch, gatherCheckedList, checkedList } =
  useBizList();
// 判断生成报送文件按钮disabled状态
const batchCreateFileDisabled = computed(() => {
  if (!checkedListHasValue.value) {
    return true;
  }
  return !!checkedList.value.every(item => item.rpFileName);
});
// 下载
const doDownload = (row: any) => {
  try {
    httpTool.download(downloadZip, row);
  } catch (e) {
    console.warn(e);
    FMessageBox.report(t("antimoneylaundering.messageManage.report.downloadFail"));
  }
};
const state = reactive({
  form: {
    sendStatusList: []
  }
});
const { postParams } = useModelRange(["messageDate"]);
const tableColumns = [
  {
    prop: "selection",
    type: "selection",
    fixed: "left",
    reserveSelection: true
  },
  {
    prop: "messagedate",
    label: t("antimoneylaundering.messageManage.report.messageDate")
  },
  {
    prop: "messagetypestr",
    label: t("antimoneylaundering.messageManage.report.messageType")
  },
  {
    prop: "filename",
    label: t("antimoneylaundering.messageManage.report.fileName")
  },
  {
    prop: "rpfilename",
    label: t("antimoneylaundering.messageManage.report.rpFileName"),
    width: "300px"
  },
  {
    prop: "sendstatusstr",
    label: t("antimoneylaundering.messageManage.report.sendstatus"),
    width: "300px",
    slots: {
      default: "send-status"
    }
  },
  {
    prop: "operate",
    label: t("antimoneylaundering.messageManage.report.operate"),
    fixed: "right",
    width: "400px",
    slots: {
      default: "buttons"
    }
  }
];
const getRemoveParams = () => {
  return {
    list: gatherCheckedList()
  };
};
</script>
