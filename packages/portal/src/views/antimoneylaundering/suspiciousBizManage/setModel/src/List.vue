<template>
  <f-query-scene :title="t('antimoneylaundering.suspiciousBizManage.setModel.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="list"
        :form-data="queryForm"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-suspiciousBizManage-setModel-query-001"
        table-comp-id="antimoneylaundering-suspiciousBizManage-setModel-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        :export-url="exportUrl"
        border
        :show-print="false"
        @select="handleCheckedList"
        @select-all="handleCheckedList"
      >
        <template #operate>
          <f-submit-state
            operate="remove"
            type="danger"
            :confirm-text="batchRemovetip"
            :gather-params="getRemoveParams"
            :url="batchRemove"
            :disabled="removeDisabled"
            is-batch
            compatible
            @close="handleSearch"
          />
          <f-submit-state
            operate="save"
            :gather-params="getRemoveParams"
            :url="batchCopy"
            :operate-name="t('antimoneylaundering.suspiciousBizManage.setModel.batchCopy')"
            :confirm-text="t('antimoneylaundering.suspiciousBizManage.setModel.batchCopyValid')"
            :result-confirm="t('antimoneylaundering.suspiciousBizManage.setModel.batchCopySuccess')"
            :result-title="t('antimoneylaundering.suspiciousBizManage.setModel.batchCopy')"
            :disabled="copyDisabled"
            is-batch
            compatible
            @close="handleSearch"
          />
        </template>
        <template #query-panel>
          <f-form-item :label="t('antimoneylaundering.suspiciousBizManage.setModel.officeId')" prop="officeIdList">
            <f-select
              v-model="queryForm.officeIdList"
              :url="officeName"
              blankOption
              multiple
              select-all
              collapse-tags
              value-key="officeId"
              label="officeName"
            />
          </f-form-item>
          <f-form-item :label="t('antimoneylaundering.suspiciousBizManage.setModel.currencyId')" prop="currencyIdList">
            <f-select
              v-model="queryForm.currencyIdList"
              :url="currency"
              value-key="currencyId"
              label="currencyName"
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
        </template>
        <template #code="{ row }">
          <template v-if="row.bulitinflag === bulitInFlag.YES">
            {{ row.modeltype }}
          </template>
          <template v-else>
            <f-button type="primary" @click="openDetail(row)" link>
              {{ row.modeltype }}
            </f-button>
          </template>
        </template>
        <template #operater="{ row }">
          <OperateButton :options="generalButtonOption(row)" @on-modify="goModify(row)">
            <template #suffix>
              <template v-if="row.startstopstatus === startAndStop.OPEN && row.bulitinflag === bulitInFlag.NO">
                <f-submit-state
                  operate="save"
                  :gather-params="
                    () => {
                      return {
                        id: row.id,
                        startstopstatus: startAndStop.CLOSE
                      };
                    }
                  "
                  link
                  :url="doBatchActiveOrStop"
                  :operate-name="t('antimoneylaundering.suspiciousBizManage.setModel.deactivate')"
                  :confirm-text="t('antimoneylaundering.suspiciousBizManage.setModel.deactivateValid')"
                  :result-confirm="t('antimoneylaundering.suspiciousBizManage.setModel.deactivateSuccess')"
                  :result-title="t('antimoneylaundering.suspiciousBizManage.setModel.deactivate')"
                  @close="handleSuccess"
                />
              </template>
              <template v-if="row.startstopstatus === startAndStop.CLOSE && row.bulitinflag === bulitInFlag.NO">
                <f-submit-state
                  operate="save"
                  :gather-params="
                    () => {
                      return {
                        id: row.id,
                        startstopstatus: startAndStop.OPEN
                      };
                    }
                  "
                  :url="doBatchActiveOrStop"
                  :operate-name="t('antimoneylaundering.suspiciousBizManage.setModel.enable')"
                  :confirm-text="t('antimoneylaundering.suspiciousBizManage.setModel.enableValid')"
                  :result-confirm="t('antimoneylaundering.suspiciousBizManage.setModel.enableSuccess')"
                  :result-title="t('antimoneylaundering.suspiciousBizManage.setModel.enable')"
                  @close="handleSuccess"
                  link
                />
              </template>
              <template v-if="row.bulitinflag === bulitInFlag.YES">
                <f-submit-state
                  operate="save"
                  :gather-params="() => row"
                  :url="copy"
                  :operate-name="t('antimoneylaundering.suspiciousBizManage.setModel.copy')"
                  :confirm-text="t('antimoneylaundering.suspiciousBizManage.setModel.isCopy')"
                  :result-confirm="t('antimoneylaundering.suspiciousBizManage.setModel.copySuccess')"
                  :result-title="t('antimoneylaundering.suspiciousBizManage.setModel.copy')"
                  @close="handleSuccess"
                  link
                />
              </template>
            </template>
          </OperateButton>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" :extra-params="rowData" />
  </f-query-scene>
</template>
<script setup lang="ts">
import {
  list,
  officeName,
  currency,
  doBatchActiveOrStop,
  remove,
  batchRemove,
  copy,
  batchCopy,
  exportUrl
} from "../url";
import { reactive, ref } from "vue";
import { useBizList, useListDetail } from "@/hooks/biz";
import { useI18n } from "vue-i18n";
import { goPage, useEnum } from "../hooks";
import Detail from "./Detail.vue";
const { t } = useI18n();
const { queryTable, handleCheckedList, controlOperateDisabled, handleSearch, gatherCheckedList, batchRemovetip } =
  useBizList();
const { bulitInFlag, startAndStop } = useEnum();
const removeDisabled = controlOperateDisabled(bulitInFlag.omitConst([bulitInFlag.NO]), "bulitInFlag", true);
const copyDisabled = controlOperateDisabled(bulitInFlag.omitConst([bulitInFlag.YES]), "bulitInFlag", true);
const rowData = ref();
const queryForm = reactive({
  officeIdList: [],
  currencyIdList: [],
  bondTypeId: "",
  amount: [],
  applyDate: [],
  businessStatusList: []
});
const { id, detail, open } = useListDetail();
const handleSuccess = res => {
  if (res.success) {
    handleSearch();
  }
};
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  rowData.value = row;
  open();
};
const goModify = (row: Record<string, any>) => {
  goPage("modify", row);
};
const generalButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: row.bulitinflag !== bulitInFlag.YES
    },
    {
      type: "remove",
      isShow: row.bulitinflag === bulitInFlag.NO,
      submitComOpt: {
        url: remove,
        gatherParams: () => {
          return {
            ...row,
            officeId: row.officeid,
            currencyId: row.currencyid
          };
        },
        close: (response: any) => {
          if (response.success) {
            handleSearch();
          }
        }
      }
    }
  ];
};
const tableColumns = [
  {
    prop: "selection",
    type: "selection",
    reserveSelection: true
  },
  {
    prop: "ordinal",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.ordinal")
  },
  {
    prop: "modeltype",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.modelType"),
    slots: { default: "code" }
  },
  {
    prop: "officename",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.officeId")
  },
  {
    prop: "currencyname",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.currencyId")
  },
  {
    prop: "inputUserName",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.inputUserName")
  },
  {
    prop: "inputdate",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.modifyDate")
  },
  {
    prop: "bulitinflagname",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.isbuiltin")
  },
  {
    prop: "startstopstatusname",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.startStopStatus")
  },
  {
    prop: "operate",
    label: t("antimoneylaundering.suspiciousBizManage.setModel.operate"),
    width: "200px",
    slots: { default: "operater" }
  }
];
const getRemoveParams = () => {
  return {
    list: gatherCheckedList().map(item => {
      return {
        ...item,
        officeId: item.officeId,
        currencyId: item.currencyId
      };
    })
  };
};
</script>
