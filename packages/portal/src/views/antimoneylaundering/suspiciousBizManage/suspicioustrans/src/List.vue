<template>
  <f-query-scene :title="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        border
        :table-columns="tableColumns"
        :url="list"
        :form-data="queryForm"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-suspiciousBizManage-suspicioustrans-query-001"
        table-comp-id="antimoneylaundering-suspiciousBizManage-suspicioustrans-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        :export-url="exportUrl"
        :post-params="postParams"
        :show-print="false"
        :show-summation-detail="false"
        :show-summation-sum="false"
      >
        <template #query-panel>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.officeId')"
            prop="officeIdList"
          >
            <f-select
              v-model="queryForm.officeIdList"
              :url="officeName"
              blank-option
              value-key="officeId"
              label="officeName"
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.currencyId')"
            prop="currencyIdList"
          >
            <f-select
              v-model="queryForm.currencyIdList"
              :url="currency"
              value-key="currencyId"
              label="currencyName"
              blank-option
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.signatureNo')"
            prop="signatureNoList"
          >
            <f-select
              v-model="queryForm.signatureNoList"
              :data="modelType"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
              label="value"
          /></f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.excuteDate')"
            prop="excuteDate"
          >
            <f-lax-range-date-picker v-model="queryForm.excuteDate" :widgetInit="getOpenDate" />
          </f-form-item>
        </template>
        <template #buttons="{ row }">
          <f-submit-state
            v-if="
              row.rexecinstance === t('antimoneylaundering.suspiciousBizManage.suspicioustrans.unExec') ||
              row.dealstatussum <= 0 ||
              (row.rexecinstance === t('antimoneylaundering.suspiciousBizManage.suspicioustrans.execd') &&
                row.nconfirmsuspicioustransnum === 0)
            "
            operate="save"
            link
            :gather-params="() => row"
            :url="exec"
            :operate-name="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.exec')"
            :confirm-text="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.isExec')"
            :result-confirm="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.execSuccess')"
            :result-title="t('antimoneylaundering.suspiciousBizManage.suspicioustrans.exec')"
            @close="handleSearch"
          />
        </template>
        <template #signature-no="{ row }">
          <template v-if="row.nsuspicioustransnum > 0">
            <f-button
              type="primary"
              link
              @click="
                goPage('search', {
                  ...row,
                  signatureNo: row.signatureno,
                  officeID: row.officeid,
                  currencyId: row.currencyid,
                  rexecTime: row.rexectime
                })
              "
            >
              {{ row.signatureno }}
            </f-button>
          </template>
          <template v-else>
            {{ row.signatureno }}
          </template>
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import { list, exportUrl, officeName, currency, exec } from "../url";
import { useModelRange } from "@/hooks/conversion";
import { useBizList } from "@/hooks/biz";
import { useEnum, goPage } from "../hooks";
import { reactive } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { modelType } = useEnum();
const queryForm = reactive({
  officeIdList: [],
  currencyIdList: [],
  signatureNoList: [],
  excuteDate: []
});
const { postParams } = useModelRange(["excuteDate"]);
const { queryTable, getOpenDate, handleSearch } = useBizList({ isRange: true });
const tableColumns = [
  {
    prop: "office",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.officeId")
  },
  {
    prop: "currency",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.currencyId")
  },
  {
    prop: "rexectime",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.excuteDate")
  },
  {
    prop: "signatureno",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.signatureNo"),
    slots: {
      default: "signature-no"
    }
  },
  {
    prop: "rstartdate",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.rstartDate")
  },
  {
    prop: "renddate",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.rendDate")
  },
  {
    prop: "rexecinstance",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.execstatusName")
  },
  {
    prop: "nsuspicioustransnum",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.nSuspiciousTransNum")
  },
  {
    prop: "nconfirmsuspicioustransnum",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.nConfirmSuspiciousTransNum")
  },
  {
    prop: "operate",
    label: t("antimoneylaundering.suspiciousBizManage.suspicioustrans.operate"),
    width: "200px",
    slots: { default: "buttons" }
  }
];
</script>
