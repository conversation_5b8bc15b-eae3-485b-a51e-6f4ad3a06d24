<template>
  <f-query-scene :title="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="list"
        :form-data="state.form"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-suspiciousBizManage-suspiciousquery-query-001"
        table-comp-id="antimoneylaundering-suspiciousBizManage-suspiciousquery-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        :post-params="postParams"
        :export-url="exportUrl"
        :show-summation-sum="false"
        :show-print="false"
      >
        <template #query-panel>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.officeId')"
            prop="officeIdList"
          >
            <f-select
              v-model="state.form.officeIdList"
              :url="officeName"
              blank-option
              value-key="officeId"
              label="officeName"
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.currencyId')"
            prop="currencyIdList"
          >
            <f-select
              v-model="state.form.currencyIdList"
              :url="currency"
              value-key="currencyId"
              label="currencyName"
              blank-option
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.signatureNo')"
            prop="signatureNoList"
          >
            <f-select
              v-model="state.form.signatureNoList"
              :data="modelType"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
              label="value"
          /></f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.excuteDate')"
            prop="excuteDate"
          >
            <f-lax-range-date-picker v-model="state.form.excuteDate" />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.confirmStatus')"
            prop="dealStatusList"
          >
            <f-select
              v-model="state.form.dealStatusList"
              :data="dealStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
          /></f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.suspiciousquery.dealStatusList')"
            prop="isConfirmList"
          >
            <f-select
              v-model="state.form.isConfirmList"
              :data="isSuspociousTransDealStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
          /></f-form-item>
        </template>
        <template #buttons="{ row }">
          <f-button
            type="primary"
            @click="handleConfirm(row)"
            link
            v-if="[dealStatus.SAVE, dealStatus.REFUSED, INIT_DEAL_STATUS].includes(row.dealstatus)"
          >
            {{ t("antimoneylaundering.suspiciousBizManage.suspiciousquery.confirm") }}
          </f-button>
        </template>
        <template #batch-no="{ row }">
          <f-button type="primary" @click="goDetail(row)" link>
            {{ row.batchno }}
          </f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" :extra-params="extraParams" />
  </f-query-scene>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { useListDetail } from "@/hooks/biz";
import { useEnum, goPage } from "../hooks";
import { list, officeName, currency, exportUrl } from "../url";
import { useModelRange } from "@/hooks/conversion";
import { useI18n } from "vue-i18n";
import Detail from "./Detail.vue";
const { t } = useI18n();
const INIT_DEAL_STATUS = -1;
const { modelType, dealStatus, isSuspociousTransDealStatus } = useEnum();
const extraParams = ref({});
const { id, detail, open } = useListDetail();
const goDetail = (row: Record<string, any>) => {
  id.value = row.suspicioustransid;
  extraParams.value = {
    ...row,
    suspicioustransid: row.suspicioustransid
  };
  open();
};
const state = reactive({
  form: {
    officeIdList: [],
    currencyIdList: [],
    excuteDate: [],
    signatureNoList: [],
    status: "", // 是否可疑
    dealStatusList: [],
    isConfirmList: []
  }
});
const { postParams } = useModelRange(["excuteDate"]);
const tableColumns = [
  {
    prop: "officename",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.officeId"),
    width: "150px"
  },
  {
    prop: "currencyname",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.currencyId"),
    width: "150px"
  },
  {
    prop: "statusname", // 是否可疑
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.status"),
    width: "150px"
  },
  {
    prop: "executedate",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.sExcuteDate"),
    width: "150px"
  },
  {
    prop: "signatureno",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.signatureNo"),
    width: "150px"
  },
  {
    prop: "startdate",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.startDate"),
    width: "150px"
  },
  {
    prop: "enddate",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.endDate"),
    width: "150px"
  },
  {
    prop: "batchno",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.batchNo"),
    width: "150px",
    slots: { default: "batch-no" }
  },
  {
    prop: "clientcode",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.clientCode"),
    width: "150px"
  },
  {
    prop: "clientname",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.clientName"),
    width: "150px"
  },
  {
    prop: "transcount",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.transNum"),
    width: "150px"
  },
  {
    prop: "dealstatusname",
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.businessStatus"),
    width: "150px"
  },
  {
    prop: "dealdate", // 确认状态
    label: t("antimoneylaundering.suspiciousBizManage.suspiciousquery.dealDate"),
    width: "150px"
  }
];
const handleConfirm = row => {
  goPage("confirm", row);
};
</script>
