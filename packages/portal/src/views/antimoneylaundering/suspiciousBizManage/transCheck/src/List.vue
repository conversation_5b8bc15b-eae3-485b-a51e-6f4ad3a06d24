<template>
  <f-query-scene :title="t('antimoneylaundering.suspiciousBizManage.transCheck.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        border
        :table-columns="tableColumns"
        :url="list"
        :form-data="state.form"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-suspiciousBizManage-transCheck-query-001"
        table-comp-id="antimoneylaundering-suspiciousBizManage-transCheck-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        :post-params="postParams"
        :show-summation-sum="false"
        :show-print="false"
      >
        <template #query-panel>
          <f-form-item :label="t('antimoneylaundering.suspiciousBizManage.transCheck.officeId')" prop="officeIdList">
            <f-select
              v-model="state.form.officeIdList"
              :url="officeName"
              blank-option
              value-key="officeId"
              label="officeName"
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.transCheck.currencyId')"
            prop="currencyIdList"
          >
            <f-select
              v-model="state.form.currencyIdList"
              :url="currency"
              value-key="currencyId"
              label="currencyName"
              blank-option
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.transCheck.signatureNo')"
            prop="signatureNoList"
          >
            <f-select
              v-model="state.form.signatureNoList"
              :data="modelType"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
              label="value"
          /></f-form-item>
          <f-form-item :label="t('antimoneylaundering.suspiciousBizManage.transCheck.excuteDate')" prop="excuteDate">
            <f-lax-range-date-picker v-model="state.form.excuteDate" />
          </f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.transCheck.confirmStatus')"
            prop="dealStatusList"
          >
            <f-select
              v-model="state.form.dealStatusList"
              :data="dealStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
          /></f-form-item>
          <f-form-item :label="t('antimoneylaundering.suspiciousBizManage.transCheck.status')" prop="status">
            <f-select
              v-model="state.form.status"
              :data="suspociousTransStatus"
              init-if-blank
              blank-option
              blank-option-value="-2"
          /></f-form-item>
          <f-form-item
            :label="t('antimoneylaundering.suspiciousBizManage.transCheck.dealStatusList')"
            prop="isConfirmList"
          >
            <f-select
              v-model="state.form.isConfirmList"
              :data="isSuspociousTransDealStatus"
              multiple
              select-all
              collapse-tags
              init-if-blank
              value-key="value"
          /></f-form-item>
        </template>
        <template #buttons="{ row }">
          <f-button
            type="primary"
            @click="handleConfirm(row)"
            link
            v-if="[dealStatus.SAVE, dealStatus.REFUSED, INIT_DEAL_STATUS, dealStatus.REVOKED].includes(row.dealstatus)"
          >
            {{ t("antimoneylaundering.suspiciousBizManage.transCheck.confirm") }}
          </f-button>
          <template v-if="row.dealstatus === dealStatus.APPROVED">
            <f-submit-state
              :gather-params="
                () => {
                  return {
                    ...row,
                    ...cancelModel
                  };
                }
              "
              :url="revoke"
              operate="cancel"
              :operate-name="t('common.operate.cancel')"
              confirm-text=" "
              :icon="DtgCopy"
              link
              :is-show-result-btn-group="false"
              :result-confirm="t('common.operate.cancelSuccess')"
              @close="handleSuceess"
            >
              <template #confirmEdit>
                <f-multi-form-panel :model="cancelModel">
                  <f-form-item :label="t('common.operate.cancelReason')" prop="revokeReason">
                    <f-input v-model="cancelModel.revokeReason" size="large" maxlength="50" />
                  </f-form-item>
                </f-multi-form-panel>
              </template>
            </f-submit-state>
          </template>
          <f-process-tracking-dialog
            :params="{
              recordId: row.id,
              systemCode: 'T91',
              transType: 'T910028',
              currencyId: row.currencyid,
              agencyId: row.officeid
            }"
          />
        </template>
        <template #batch-no="{ row }">
          <template v-if="row.statusStr">
            <f-button type="primary" @click="goDetail(row)" link>
              {{ row.batchno }}
            </f-button>
          </template>
          <template v-else>
            {{ row.batchno }}
          </template>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" :extra-params="extraParams" />
  </f-query-scene>
</template>
<script setup lang="ts">
import { reactive, ref, shallowRef } from "vue";
import { useListDetail } from "@/hooks/biz";
import { useEnum, goPage } from "../hooks";
import { list, officeName, currency, revoke } from "../url";
import { useModelRange } from "@/hooks/conversion";
import { useI18n } from "vue-i18n";
import Detail from "./Detail.vue";
import { DtgCopy } from "@dtg/frontend-plus-icons";
const { t } = useI18n();
const INIT_DEAL_STATUS = -1;
const { modelType, dealStatus, isSuspociousTransDealStatus, suspociousTransStatus } = useEnum();
const extraParams = ref({});
const { id, detail, open } = useListDetail();
const goDetail = (row: Record<string, any>) => {
  id.value = row.id;
  extraParams.value = row;
  open();
};
const state = reactive({
  form: {
    officeIdList: [],
    currencyIdList: [],
    excuteDate: [],
    signatureNoList: [],
    status: "", // 是否可疑
    dealStatusList: [],
    isConfirmList: []
  }
});
const cancelModel = reactive({
  revokeReason: ""
});
const { postParams } = useModelRange(["excuteDate"]);
const tableColumns = [
  {
    prop: "office",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.officeId"),
    width: "150px"
  },
  {
    prop: "currency",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.currencyId"),
    width: "150px"
  },
  {
    prop: "excutedate",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.sExcuteDate"),
    width: "150px"
  },
  {
    prop: "signatureno",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.signatureNo"),
    width: "150px"
  },
  {
    prop: "batchno",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.batchNo"),
    width: "150px",
    slots: { default: "batch-no" }
  },
  {
    prop: "startdate",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.startDate"),
    width: "150px"
  },
  {
    prop: "enddate",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.endDate"),
    width: "150px"
  },
  {
    prop: "clientcode",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.clientCode"),
    width: "150px"
  },
  {
    prop: "clientname",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.clientName"),
    width: "150px"
  },
  {
    prop: "transnum", // 交易笔数
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.transNum"),
    width: "150px"
  },
  {
    prop: "status", // 是否可疑
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.status"),
    width: "150px",
    formatter: { name: "const", const: "antimoneylaundering.SuspociousTransStatus" }
  },
  {
    prop: "dealstatusstr", // 确认状态
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.confirmStatus"),
    width: "150px"
  },
  {
    prop: "dealusername", // 确认人
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.dealUserName"),
    width: "150px"
  },
  {
    prop: "dealdate", // 确认时间
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.dealDate"),
    width: "150px"
  },
  {
    prop: "revokereason", // 撤销原因
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.revokeReason"),
    width: "150px"
  },
  {
    prop: "operate",
    label: t("antimoneylaundering.suspiciousBizManage.transCheck.operate"),
    slots: { default: "buttons" },
    fixed: "right",
    width: "150px"
  }
];
const handleConfirm = row => {
  goPage("confirm", row);
};
const queryTable = shallowRef();
const handleSuceess = res => {
  if (res.success) {
    queryTable.value.renderTableData();
  }
};
</script>
