export default function useTableColumns(t) {
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      selectable: row => !row.crertref && !row.maintainref
    },
    {
      width: "185px",
      prop: "creditratingno",
      label: t("creditRatingNo"), // 风险评级编号
      showOverflowTooltip: true,
      slots: { default: "link" }
    },
    {
      prop: "clienttype",
      label: t("clientType"), // 客户类型
      showOverflowTooltip: true,
      formatter: { name: "const", const: "antimoneylaundering.ClientType" }
    },
    {
      prop: "clientno",
      label: t("clientCode"), // 客户编号
      showOverflowTooltip: true
    },
    {
      prop: "clientname",
      label: t("clientName"), // 客户名称
      showOverflowTooltip: true
    },
    {
      prop: "clientcreditgrade",
      label: t("clientCreditGrade"), // 客户风险等级
      showOverflowTooltip: true
    },
    {
      prop: "ratingeffectivedate",
      label: t("ratingEffectiveDate"), // 评级生效日期
      showOverflowTooltip: true
    },
    {
      prop: "invalidtime",
      label: t("invalidTime"), // 评级生效日期
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "businessstatus",
      label: t("businessStatus"), // 状态
      showOverflowTooltip: true,
      formatter: { name: "const", const: "common.BusinessStatus" }
    },
    {
      width: "180px",
      prop: "inputUserName",
      label: t("inputUserName"), // 录入人
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "inputtime",
      label: t("inputTime"), // 录入时间
      showOverflowTooltip: true
    },
    {
      width: "190px",
      prop: "id",
      fixed: "right",
      label: t("operate"), // 操作栏
      showOverflowTooltip: true,
      slots: { default: "buttons" }
    }
  ];

  const sortColumnMap = {
    inputUserName: "inputUserName"
  };

  const columnSort = [
    "creditratingno",
    "clienttype",
    "clientcode",
    "clientname",
    "clientcreditgrade",
    "ratingeffectivedate",
    "invalidtime",
    "businessstatus",
    "inputUserName",
    "inputtime"
  ];

  const defaultSort = {
    inputtime: "desc"
  };

  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort
  };
}
