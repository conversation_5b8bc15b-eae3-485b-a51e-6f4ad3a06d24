<template>
  <OperateButton :options="operateButtonOption(row)" @on-modify="handleModify(row)">
    <template #suffix v-if="row.businessStatus === 'APPROVED'">
      <f-submit-state
        :gather-params="() => row"
        :url="revokeUrl"
        operate="cancel"
        :is-batch="false"
        :operate-name="t('common.operate.cancel')"
        confirm-text=" "
        :icon="DtgCopy"
        link
        :is-show-result-btn-group="false"
        :result-confirm="t('common.operate.cancelSuccess')"
        @close="handleOperateSuccess"
      >
        <template #confirmEdit>
          <f-multi-form-panel :model="cancelModel">
            <f-form-item :label="t('common.operate.cancelReason')" prop="revokeReason">
              <f-input v-model="cancelModel.revokeReason" size="large" maxlength="50" />
            </f-form-item>
          </f-multi-form-panel>
        </template>
      </f-submit-state>
    </template>
  </OperateButton>
</template>
<script setup lang="ts">
import { reactive } from "vue";
import { useI18n } from "vue-i18n";
import { DtgCopy } from "@dtg/frontend-plus-icons";

const { t } = useI18n();

const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  deleteUrl: {
    type: String,
    required: true
  },
  submitUrl: {
    type: String,
    required: true
  },
  revokeUrl: {
    type: String,
    required: true
  }
});

const emits = defineEmits(["on-modify", "operate-success"]);

const operateButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: ["SAVE", "REFUSE"].includes(row.businessstatus)
    },
    {
      type: "remove",
      isShow: ["SAVE", "REFUSE"].includes(row.businessstatus),
      submitComOpt: {
        url: props.deleteUrl,
        gatherParams: () => {
          return {
            id: row.id,
            colUuid: row.coluuid,
            firstReviewId: row.firstreviewid,
            creditRatingNo: row.creditratingno,
            inputUserId: row.inputUserId
          };
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    },
    {
      type: "submit",
      isShow: ["SAVE", "REFUSE"].includes(row.businessstatus),
      submitComOpt: {
        url: props.submitUrl,
        gatherParams: () => {
          return {
            id: row.id,
            colUuid: row.coluuid,
            firstReviewId: row.firstreviewid,
            creditRatingNo: row.creditratingno,
            inputUserId: row.inputUserId
          };
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    },
    {
      isShow: true,
      type: "process",
      trackingParams: {
        recordId: row.id, // 单据唯一id
        systemCode: "T91", // 当前功能systemCode
        transType: "T910022", // 当前业务类型枚举
        agencyId: 11, // 机构
        currencyId: 0, // 币种
        globalSerialNo: row.globalSerialNo // 全局流水号 非必填
      }
    }
  ];
};

const cancelModel = reactive({
  revokeReason: ""
});

const handleModify = (row: Record<string, any>) => {
  emits("on-modify", row);
};

const handleOperateSuccess = res => {
  emits("operate-success", res.data);
};
</script>
