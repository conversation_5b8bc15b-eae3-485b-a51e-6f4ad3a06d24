<template>
  <OperateButton :options="operateButtonOption(row)" @on-modify="handleModify(row)" />
</template>
<script setup lang="ts">
const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  deleteUrl: {
    type: String,
    required: true
  }
});

const emits = defineEmits(["on-modify", "operate-success"]);

const operateButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: !props.row.crertref && !props.row.maintainref
    },
    {
      type: "remove",
      isShow: !props.row.crertref && !props.row.maintainref,
      submitComOpt: {
        url: props.deleteUrl,
        gatherParams: () => {
          return {
            id: row.id,
            colUuid: row.colUuid,
            gradeName: row.gradename,
            inputUserId: row.inputUserId
          };
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    }
  ];
};

const handleModify = (row: Record<string, any>) => {
  emits("on-modify", row);
};

const handleOperateSuccess = res => {
  emits("operate-success", res.data);
};
</script>
