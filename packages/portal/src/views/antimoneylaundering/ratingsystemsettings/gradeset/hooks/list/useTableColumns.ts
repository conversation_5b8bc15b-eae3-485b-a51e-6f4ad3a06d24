export default function useTableColumns(t) {
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      selectable: row => !row.crertRef && !row.maintainRef
    },
    {
      prop: "gradename",
      label: t("gradeName"), // 风险等级体系名称
      showOverflowTooltip: true,
      slots: { default: "link" }
    },
    {
      width: "180px",
      prop: "modifyUserName",
      label: t("modifyUserName"), // 操作人
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "modifyTime",
      label: t("modifyTime"), // 操作时间
      showOverflowTooltip: true
    },
    {
      width: "130px",
      prop: "id",
      fixed: "right",
      label: t("operate"), // 操作栏
      showOverflowTooltip: true,
      slots: { default: "buttons" }
    }
  ];

  const sortColumnMap = {
    modifyUserName: "modifyUserName",
    modifyTime: "modifyTime"
  };

  const columnSort = ["gradename", "modifyUserName", "modifyTime"];

  const defaultSort = { field: "modifyTime", order: "desc" };

  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort
  };
}
