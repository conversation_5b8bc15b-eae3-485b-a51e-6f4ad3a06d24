<template>
  <f-button type="primary" @click="handleAdd">
    {{ t("views.add") }}
  </f-button>
  <f-submit-state
    operate="remove"
    type="danger"
    :gather-params="getBatchParams"
    :url="deleteBatchUrl"
    :disabled="removeDisabled"
    :need-extend-columns="state.columns"
    is-batch
    compatible
    @close="doFreshTable"
  />
</template>
<script setup lang="ts">
import { computed, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { usePageI18n } from "../hooks/usePage";

const { t } = useI18n();
const it = usePageI18n(t);

const props = defineProps({
  table: {
    type: Object,
    required: true
  },
  deleteBatchUrl: {
    type: String,
    required: true
  }
});

const state = reactive({
  checkedList: [],
  columns: [
    {
      field: "criteriaName",
      title: it("criteriaName")
    },
    {
      field: "failReason",
      title: it("failReason")
    }
  ]
});

const doFreshTable = () => {
  props.table.renderTableData();
  state.checkedList.length = 0;
  props.table.clearSelection();
};

const getBatchParams = () => {
  const arr = [];
  for (const item of state.checkedList) {
    arr.push({
      id: item.id,
      criteriaCode: item.criteriacode,
      criteriaName: item.criterianame,
      colUuid: item.coluuid,
      inputUserId: item.inputUserId
    });
  }
  return arr;
};

const controlOperateDisabled = (excludeStatus: unknown[], attr: string) => {
  return computed(() => {
    let result = false;
    const enumCodeList = excludeStatus.map(es => {
      return es.code;
    });
    if (state.checkedList.length === 0) {
      result = true;
    }
    if (!result) {
      for (const item of state.checkedList) {
        if (enumCodeList.includes(item[attr])) {
          result = true;
          break;
        }
      }
    }
    return result;
  });
};

const removeDisabled = controlOperateDisabled([], "");

const emits = defineEmits(["on-add"]);

const handleAdd = () => {
  emits("on-add");
};

const methods = {
  changeCheckedList(list) {
    state.checkedList = list;
  }
};
defineExpose(methods);
</script>
