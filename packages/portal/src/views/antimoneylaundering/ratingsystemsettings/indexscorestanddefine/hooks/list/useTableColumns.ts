export default function useTableColumns(t) {
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true
    },
    {
      prop: "criteriacode",
      label: t("criteriaCode"), // 评分标准编号
      showOverflowTooltip: true,
      slots: { default: "link" }
    },
    {
      prop: "criterianame",
      label: t("criteriaName"), // 评分标准名称
      showOverflowTooltip: true
    },
    {
      prop: "indexbodysetname",
      label: t("indexBodySetName"), // 指标体系名称
      showOverflowTooltip: true
    },
    {
      prop: "modifyUserName",
      label: t("modifyUserName"), // 操作人
      showOverflowTooltip: true
    },
    {
      prop: "modifyTime",
      label: t("modifyTime"), // 操作时间
      showOverflowTooltip: true
    },
    {
      prop: "id",
      fixed: "right",
      label: t("operate"), // 操作栏
      showOverflowTooltip: true,
      slots: { default: "buttons" }
    }
  ];

  const sortColumnMap = {
    modifyUserName: "modifyUserName",
    modifyTime: "modifyTime"
  };

  const columnSort = ["criteriacode", "criterianame", "indexbodysetname", "modifyUserName", "modifyTime"];

  const defaultSort = { field: "modifyTime", order: "desc" };

  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort
  };
}
