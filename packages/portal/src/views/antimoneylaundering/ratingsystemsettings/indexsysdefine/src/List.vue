<template>
  <f-query-scene :title="it('listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="queryForm"
        :url="listPageUrl"
        :table-columns="tableColumns"
        :sort-column-map="sortColumnMap"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :show-collapse="false"
        :show-export="false"
        :show-print="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-ratingsystemsettings-indexsysdefine-query"
        table-comp-id="antimoneylaundering-ratingsystemsettings-indexsysdefine-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :delete-batch-url="deleteBatchUrl"
            @on-add="operateHandler.handleAdd"
          />
        </template>
        <!-- 查询面本表单项 -->
        <template #query-panel>
          <f-form-item prop="bodyCode" :label="it('bodyCode')">
            <f-input v-model="queryForm.bodyCode" clearable />
          </f-form-item>
          <f-form-item prop="bodyName" :label="it('bodyName')">
            <f-input v-model="queryForm.bodyName" clearable />
          </f-form-item>
          <f-form-item prop="scoreType" :label="it('scoreType')">
            <f-select v-model="queryForm.scoreType" :data="IndexScoringSystemEnum" disabled clearable />
          </f-form-item>
        </template>

        <template #link="{ row }">
          <f-button @click="openDetail(row)" link type="primary">
            {{ row.bodycode }}
          </f-button>
        </template>
        <template #buttons="{ $index, row }">
          <OperateGroup
            :index="$index"
            :row="row"
            :delete-url="deleteUrl"
            :copy-url="copyUrl"
            @on-modify="operateHandler.handleModify"
            @operate-success="operateHandler.handleOperateSuccess"
          />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { usePageI18n } from "../hooks/usePage";
import { useConst } from "@ifs/support";
import { listPageUrl, deleteUrl, deleteBatchUrl, copyUrl } from "../url";
import useQueryForm from "../hooks/list/useQueryForm";
import useTableColumns from "../hooks/list/useTableColumns";
import usePageOperate from "../hooks/list/usePageOperate";
import OperateGroup from "./OperateGroup.vue";
import OperateBatchGroup from "./OperateBatchGroup.vue";
import Detail from "./Detail.vue";
import { useListDetail } from "@/hooks/biz";

const { t } = useI18n();
const it = usePageI18n(t);

const queryTable = shallowRef();
const operateGroup = shallowRef();

const IndexScoringSystemEnum = useConst("antimoneylaundering.IndexScoringSystem");

const { queryForm } = useQueryForm(IndexScoringSystemEnum);
const { tableColumns, sortColumnMap, columnSort, defaultSort } = useTableColumns(it);
const { operateHandler } = usePageOperate(queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};
const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};
</script>
