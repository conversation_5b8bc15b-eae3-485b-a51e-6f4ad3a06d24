<template>
  <div style="display: flex">
    <f-submit-state
      style="margin-right: 10px"
      operate="submit"
      :operate-name="it('copy')"
      :confirm-text="it('confirmCopy')"
      link
      :icon="DtgCopy"
      :gather-params="getCopyParams"
      :url="copyUrl"
      @close="handleCopyClose"
    />
    <OperateButton :options="operateButtonOption(row)" @on-modify="handleModify(row)" />
  </div>
</template>
<script setup lang="ts">
import { DtgCopy } from "@dtg/frontend-plus-icons";
import { useI18n } from "vue-i18n";
import { usePageI18n } from "../hooks/usePage";
import { useUserStoreHook } from "@/stores/modules/user";
import { FMessageBox } from "@dtg/frontend-plus";

const userStore = useUserStoreHook();
const { t } = useI18n();
const it = usePageI18n(t);

const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  deleteUrl: {
    type: String,
    required: true
  },
  copyUrl: {
    type: String,
    required: true
  }
});

const emits = defineEmits(["on-modify", "operate-success"]);

const operateButtonOption = (row: Record<string, any>) => {
  return [
    {
      type: "modify",
      isShow: true
    },
    {
      type: "remove",
      isShow: !props.row.criteriaref,
      submitComOpt: {
        url: props.deleteUrl,
        gatherParams: () => {
          return {
            id: row.id,
            bodyCode: row.bodycode,
            bodyName: row.bodyname,
            colUuid: row.coluuid,
            inputUserId: row.inputUserId
          };
        },
        close: (response: any) => {
          if (response.success) {
            handleOperateSuccess(response);
          }
        }
      }
    }
  ];
};

const handleModify = (row: Record<string, any>) => {
  if (row.modifyUserName !== userStore.loginUserName) {
    FMessageBox.report(it("notsamePerson"));
    return;
  }
  emits("on-modify", row);
};

const handleOperateSuccess = res => {
  emits("operate-success", res.data);
};

const getCopyParams = () => ({ id: props.row.id });

const handleCopyClose = res => {
  if (res.success) {
    handleOperateSuccess(res);
  }
};
</script>
