export default function useTableColumns(t) {
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      selectable: row => !row.ruleRef && !row.criteriaRef
    },
    {
      prop: "bodycode",
      label: t("bodyCode"), // 体系编号
      showOverflowTooltip: true,
      slots: { default: "link" }
    },
    {
      prop: "bodyname",
      label: t("bodyName"), // 体系名称
      showOverflowTooltip: true
    },
    {
      prop: "scoretype",
      label: t("scoreType"), // 指标计分制
      showOverflowTooltip: true,
      formatter: { name: "const", const: "antimoneylaundering.IndexScoringSystem" }
    },
    {
      prop: "modifyUserName",
      label: t("modifyUserName"), // 操作人
      showOverflowTooltip: true
    },
    {
      prop: "modifyTime",
      label: t("modifyTime"), // 操作时间
      showOverflowTooltip: true
    },
    {
      prop: "id",
      fixed: "right",
      label: t("operate"), // 操作栏
      showOverflowTooltip: true,
      slots: { default: "buttons" }
    }
  ];

  const sortColumnMap = {
    modifyUserName: "modifyUserName",
    modifyTime: "modifyTime"
  };

  const columnSort = ["bodycode", "bodyname", "modifyUserName", "modifyTime"];

  const defaultSort = { field: "modifyTime", order: "desc" };

  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort
  };
}
