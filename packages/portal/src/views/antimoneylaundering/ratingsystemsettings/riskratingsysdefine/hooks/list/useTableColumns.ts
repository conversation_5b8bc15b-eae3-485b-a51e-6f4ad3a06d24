export default function useTableColumns(t) {
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      selectable: row => !row.crertRef && !row.maintainRef
    },
    {
      prop: "crertbodycode",
      label: t("crertBodyCode"), // 风险评级体系编号
      showOverflowTooltip: true,
      slots: { default: "link" }
    },
    {
      prop: "crertbodyname",
      label: t("crertBodyName"), // 风险评级体系名称
      showOverflowTooltip: true
    },
    {
      prop: "effectivedate",
      label: t("effectiveDate"), // 生效日期
      showOverflowTooltip: true
    },
    {
      prop: "gradename",
      label: t("gradeName"), // 等级标准
      showOverflowTooltip: true
    },
    {
      prop: "rulename",
      label: t("ruleName"), // 计算规则
      showOverflowTooltip: true
    },
    {
      prop: "criterianame",
      label: t("criteriaName"), // 评分标准
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "modifyUserName",
      label: t("modifyUserName"), // 操作人
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "modifyTime",
      label: t("modifyTime"), // 操作时间
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "id",
      fixed: "right",
      label: t("operate"), // 操作栏
      showOverflowTooltip: true,
      slots: { default: "buttons" }
    }
  ];

  const sortColumnMap = {};

  const columnSort = [
    "crertbodycode",
    "crertbodyname",
    "effectivedate",
    "gradename",
    "rulename",
    "criterianame",
    "modifyUserName",
    "modifyTime"
  ];

  const defaultSort = {};

  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort
  };
}
