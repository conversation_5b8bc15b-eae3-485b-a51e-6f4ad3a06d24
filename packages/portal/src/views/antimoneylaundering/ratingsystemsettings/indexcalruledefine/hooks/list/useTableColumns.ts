export default function useTableColumns(t) {
  const tableColumns = [
    {
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      selectable: row => !row.criteriaRef
    },
    {
      prop: "rulecode",
      label: t("ruleCode"), // 规则编号
      showOverflowTooltip: true,
      slots: { default: "link" }
    },
    {
      prop: "rulename",
      label: t("ruleName"), // 规则名称
      showOverflowTooltip: true
    },
    {
      prop: "indexbodysetname",
      label: t("bodyName"), // 体系名称
      showOverflowTooltip: true
    },
    {
      prop: "modifyUserName",
      label: t("modifyUserName"), // 操作人
      showOverflowTooltip: true
    },
    {
      prop: "modifyTime",
      label: t("modifyTime"), // 操作时间
      showOverflowTooltip: true
    },
    {
      prop: "id",
      fixed: "right",
      label: t("operate"), // 操作栏
      showOverflowTooltip: true,
      slots: { default: "buttons" }
    }
  ];

  const sortColumnMap = {
    modifyUserName: "modifyUserName",
    modifyTime: "modifyTime"
  };

  const columnSort = ["rulecode", "rulename", "indexbodysetname", "modifyUserName", "modifyTime"];

  const defaultSort = { field: "modifyTime", order: "desc" };

  return {
    tableColumns,
    sortColumnMap,
    columnSort,
    defaultSort
  };
}
