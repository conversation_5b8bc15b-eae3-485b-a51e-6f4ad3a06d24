<template>
  <f-query-scene :title="it('listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="queryForm"
        :url="listPageUrl"
        :table-columns="tableColumns"
        :sort-column-map="sortColumnMap"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :show-collapse="false"
        :show-export="false"
        :show-print="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-ratingsystemsettings-indexcalruledefine-query"
        table-comp-id="antimoneylaundering-ratingsystemsettings-indexcalruledefine-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :delete-batch-url="deleteBatchUrl"
            @on-add="operateHandler.handleAdd"
          />
        </template>
        <!-- 查询面本表单项 -->
        <template #query-panel>
          <f-form-item prop="ruleCode" :label="it('ruleCode')">
            <f-input v-model="queryForm.ruleCode" clearable />
          </f-form-item>
          <f-form-item prop="ruleName" :label="it('ruleName')">
            <f-input v-model="queryForm.ruleName" clearable />
          </f-form-item>
          <f-form-item prop="indexBodySetId" :label="it('bodyCode')">
            <f-magnifier-single
              v-model="queryForm.indexBodySetId"
              :url="listIndexBody"
              :title="it('bodyCode')"
              :placeholder="it('bodyCodeInput')"
              auto-init
              method="post"
              row-key="ID"
              row-label="BODYCODE"
              input-key="BODYNAME"
            >
              <f-magnifier-column prop="BODYCODE" :label="it('bodyCode')" />
              <f-magnifier-column prop="BODYNAME" :label="it('bodyName')" />
            </f-magnifier-single>
          </f-form-item>
        </template>

        <template #link="{ row }">
          <f-button @click="openDetail(row)" link type="primary">
            {{ row.rulecode }}
          </f-button>
        </template>
        <template #buttons="{ $index, row }">
          <OperateGroup
            :index="$index"
            :row="row"
            :delete-url="deleteUrl"
            @on-modify="operateHandler.handleModify"
            @operate-success="operateHandler.handleOperateSuccess"
          />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { usePageI18n } from "../hooks/usePage";
import { listIndexBody, listPageUrl, deleteUrl, deleteBatchUrl } from "../url";
import useQueryForm from "../hooks/list/useQueryForm";
import useTableColumns from "../hooks/list/useTableColumns";
import usePageOperate from "../hooks/list/usePageOperate";
import OperateGroup from "./OperateGroup.vue";
import OperateBatchGroup from "./OperateBatchGroup.vue";
import Detail from "./Detail.vue";
import { useListDetail } from "@/hooks/biz";

const { t } = useI18n();
const it = usePageI18n(t);

const queryTable = shallowRef();
const operateGroup = shallowRef();

const { queryForm } = useQueryForm();
const { tableColumns, sortColumnMap, columnSort, defaultSort } = useTableColumns(it);
const { operateHandler } = usePageOperate(queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};
const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};

const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};
</script>
