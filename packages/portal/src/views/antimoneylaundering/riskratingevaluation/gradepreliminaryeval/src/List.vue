<template>
  <f-query-scene :title="it('listTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        label-width="90px"
        :form-data="queryForm"
        :url="listPageUrl"
        :table-columns="tableColumns"
        :sort-column-map="sortColumnMap"
        :allow-sort="columnSort"
        :default-sort="defaultSort"
        :post-params="postParams"
        :show-collapse="false"
        :show-export="false"
        :show-print="false"
        :show-count-value="false"
        :show-summation-sum="false"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-riskratingevaluation-gradepreliminaryeval-query"
        table-comp-id="antimoneylaundering-riskratingevaluation-gradepreliminaryeval-table"
        border
        show-header
        auto-reset
        auto-init
        @select="handleCheckedList"
        @select-all="handleCheckedList"
        @clear-selection="handleClearSelection"
      >
        <!-- 表格左上角操作按钮 -->
        <template #operate>
          <OperateBatchGroup
            ref="operateGroup"
            :table="queryTable"
            :status-enum="statusList"
            :delete-batch-url="deleteBatchUrl"
            :submit-batch-url="submitBatchUrl"
            :revoke-batch-url="revokeBatchUrl"
            @on-add="operateHandler.handleAdd"
          />
        </template>
        <!-- 查询面本表单项 -->
        <template #query-panel>
          <!--  风险评级编号   -->
          <f-form-item prop="firstReviewCode" :label="it('firstReviewCode')">
            <f-input v-model="queryForm.firstReviewCode" clearable />
          </f-form-item>
          <!--  客户风险等级   -->
          <f-form-item prop="clientCreditGrade" :label="it('clientCreditGrade')">
            <f-select
              v-model="queryForm.clientCreditGrades"
              :url="queryLevel"
              clearable
              multiple
              select-all
              collapse-tags
              label="gradename"
              value-key="id"
            />
          </f-form-item>
          <!--  客户类型   -->
          <f-form-item prop="clientType" :label="it('clientType')">
            <f-select v-model="queryForm.clientType" :data="ClientTypeEnum" clearable default-first init-if-blank />
          </f-form-item>
          <!--  操作类型   -->
          <f-form-item prop="operateTypeList" :label="it('openedClient')">
            <f-select
              v-model="queryForm.operateTypeList"
              :data="operateType"
              clearable
              init-if-blank
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--  状态   -->
          <f-form-item prop="businessStatusList" :label="it('businessStatus')">
            <f-select
              v-model="queryForm.businessStatusList"
              :data="statusList"
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--  生效日期   -->
          <f-form-item prop="ratingEffectiveDate" :label="it('ratingEffectiveDate')">
            <f-lax-range-date-picker v-model="queryForm.effectiveDateList" clearable />
          </f-form-item>
          <!--  客户名称   -->
          <f-form-item :label="it('clientName')" prop="clientId">
            <f-magnifier-single
              ref="clientCodeMagnifier"
              :title="it('clientCode')"
              :url="listClientUrl"
              method="post"
              v-model="queryForm.clientId"
              row-key="id"
              row-label="clientName"
              input-key="clientCodeOrName"
              @confirm="handleClientConfirm"
              auto-init
            >
              <f-magnifier-column prop="clientCode" :label="it('clientCode')" />
              <f-magnifier-column prop="clientName" :label="it('clientName')" />
            </f-magnifier-single>
          </f-form-item>
        </template>

        <template #link="{ row }">
          <f-button @click="openDetail(row)" link type="primary">
            {{ row.firstreviewcode }}
          </f-button>
        </template>
        <template #buttons="{ $index, row }">
          <OperateGroup
            :index="$index"
            :row="row"
            :delete-url="deleteUrl"
            :submit-url="submitUrl"
            :revoke-url="revokeUrl"
            @on-modify="operateHandler.handleModify"
            @operate-success="operateHandler.handleOperateSuccess"
          />
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
  </f-query-scene>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { usePageI18n } from "../hooks/usePage";
import {
  listPageUrl,
  deleteUrl,
  submitUrl,
  revokeUrl,
  deleteBatchUrl,
  submitBatchUrl,
  revokeBatchUrl,
  queryLevel,
  listClientUrl
} from "../url";
import { useConst } from "@ifs/support";
import useQueryForm from "../hooks/list/useQueryForm";
import useTableColumns from "../hooks/list/useTableColumns";
import usePageOperate from "../hooks/list/usePageOperate";
import OperateGroup from "./OperateGroup.vue";
import OperateBatchGroup from "./OperateBatchGroup.vue";
import Detail from "./Detail.vue";
import { useListDetail } from "@/hooks/biz";

const { t } = useI18n();
const it = usePageI18n(t);

const BusinessStatusEnum = useConst("common.BusinessStatus");
const ClientTypeEnum = useConst("antimoneylaundering.ClientType");
const operateType = useConst("antimoneylaundering.OperateType");

const statusList = BusinessStatusEnum.pickConst([
  BusinessStatusEnum.SAVE,
  BusinessStatusEnum.USED,
  BusinessStatusEnum.APPROVING,
  BusinessStatusEnum.APPROVED,
  BusinessStatusEnum.REFUSE,
  BusinessStatusEnum.CANCELLING,
  BusinessStatusEnum.CANCEL
]);

const queryTable = shallowRef();
const operateGroup = shallowRef();

const { queryForm, postParams } = useQueryForm();
const { tableColumns, sortColumnMap, columnSort, defaultSort } = useTableColumns(it);
const { operateHandler } = usePageOperate(queryTable);

const handleCheckedList = list => {
  operateGroup.value.changeCheckedList(list);
};
const handleClearSelection = () => {
  operateGroup.value.changeCheckedList([]);
};
const handleClientConfirm = (row: Record<string, any>) => {
  if (row) {
    queryForm.socialCreditCode = row.socialCreditCode;
  } else {
    queryForm.socialCreditCode = "";
  }
};
const { id, detail, open } = useListDetail();
const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};
</script>
