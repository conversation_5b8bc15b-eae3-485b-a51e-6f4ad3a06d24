<template>
  <f-blank-scene :title="t('antimoneylaundering.query.blocktrading.title')">
    <template #default>
      <div>
        <f-query-grid
          ref="queryTable"
          row-key="id"
          class="block-trading"
          :table-columns="tableColumns"
          :url="list"
          :form-data="queryForm"
          :export-exclude="['operate']"
          query-comp-id="antimoneylaundering-query-blocktrading-query-002"
          table-comp-id="antimoneylaundering-query-blocktrading-table-002"
          :count-label="t('common.summary.record')"
          :count-label-unit="t('common.summary.recordUnit')"
          :summation-biz-label="t('common.summary.record')"
          :summation-biz-unit="t('common.summary.recordUnit')"
          border
          :show-print="false"
          :show-export="false"
          :post-params="postParams"
          @query-table="clearSelection"
          @select="handleCheckedList"
          @select-all="handleCheckedList"
          @clear-selection="handClearSelection"
        >
          <template #operate>
            <f-button type="primary" @click="downLoadReport">{{
              t("antimoneylaundering.query.blocktrading.exportExcel")
            }}</f-button>
          </template>
          <template #query-panel>
            <!--   机构       -->
            <f-form-item :label="t('antimoneylaundering.query.blocktrading.officeId')" prop="officeIds">
              <f-select
                v-model="queryForm.officeIds"
                :url="officeName"
                blankOption
                value-key="officeId"
                label="officeName"
                init-if-blank
                multiple
                select-all
                collapse-tags
              />
            </f-form-item>
            <!--   币种       -->
            <f-form-item :label="t('antimoneylaundering.query.blocktrading.currencyId')" prop="currencyIds">
              <f-select
                v-model="queryForm.currencyIds"
                :url="currency"
                value-key="currencyId"
                label="currencyName"
                blankOption
                init-if-blank
                multiple
                select-all
                collapse-tags
              />
            </f-form-item>
            <!--   执行日期   -->
            <f-form-item :label="t('antimoneylaundering.query.blocktrading.executeDate')" prop="executeDate">
              <f-lax-range-date-picker v-model="queryForm.executeDate" :widgetInit="getOpenDate" />
            </f-form-item>
            <!--   单位       -->
            <f-form-item :label="t('antimoneylaundering.query.blocktrading.unit')" prop="unit">
              <f-select v-model="queryForm.unit" :data="Unit" />
            </f-form-item>
            <!--   业务类型     -->
            <f-form-item
              :label="t('antimoneylaundering.query.blocktrading.transactionType')"
              prop="transactionTypeIdList"
            >
              <f-select
                v-model="queryForm.transactionTypeIdList"
                :data="transactionTypeTwo"
                init-if-blank
                blankOption
                multiple
                select-all
                collapse-tags
              />
            </f-form-item>
            <!--    客户名称      -->
            <f-form-item :label="t('antimoneylaundering.query.blocktrading.clientName')" prop="clientIdList">
              <f-magnifier-multi
                v-model="queryForm.clientIdList"
                :title="t('antimoneylaundering.query.blocktrading.clientName')"
                :url="queryClientInfo"
                row-key="id"
                row-label="clientName"
                collapse-tags-tooltip
                auto-init
                :params="{
                  clientClass: 1
                }"
              >
                <f-magnifier-column prop="clientCode" :label="t('antimoneylaundering.query.blocktrading.clientCode')" />
                <f-magnifier-column prop="clientName" :label="t('antimoneylaundering.query.blocktrading.clientName')" />
              </f-magnifier-multi>
            </f-form-item>
            <!--  是否包含下级客户  -->
            <f-form-item
              :label="t('antimoneylaundering.query.blocktrading.inclusionLowerFlag')"
              prop="inclusionLowerFlag"
            >
              <f-select v-model="queryForm.inclusionLowerFlag" :data="yesOrNo" />
            </f-form-item>
            <!--   查询口径   -->
            <f-form-item
              v-if="queryForm.clientIdList.length > 0"
              :label="t('antimoneylaundering.query.blocktrading.queryAperture')"
              prop="queryAperture"
            >
              <f-select
                v-model="queryForm.queryAperture"
                :url="queryAperture"
                label="orgStructureName"
                value-key="orgStructureCode"
              />
            </f-form-item>
            <f-form-item prop="empty1" />
            <f-form-item prop="empty2" />
            <!--   单笔交易金额       -->
            <f-form-item
              prop="singleCheckBox"
              style="width: 15%"
              :label="t('antimoneylaundering.query.blocktrading.blank')"
            >
              <f-checkbox v-model="queryForm.singleCheckBox" @change="queryForm.amount1 = null">{{
                t("antimoneylaundering.query.blocktrading.singleTransAmount")
              }}</f-checkbox>
            </f-form-item>
            <!--   大于等于       -->
            <f-form-item
              style="width: 35%"
              :label="t('antimoneylaundering.query.blocktrading.greaterThanOrEqual')"
              prop="amount1"
            >
              <f-amount v-model="queryForm.amount1" :disabled="!queryForm.singleCheckBox" />
            </f-form-item>
            <!--   单日多笔累计交易金额       -->
            <f-form-item
              style="width: 15%"
              prop="multiCheckBox"
              :label="t('antimoneylaundering.query.blocktrading.blank')"
            >
              <f-checkbox v-model="queryForm.multiCheckBox" @change="queryForm.amount2 = null">{{
                t("antimoneylaundering.query.blocktrading.mutlSumAmount")
              }}</f-checkbox>
            </f-form-item>
            <!--   大于等于       -->
            <f-form-item
              style="width: 35%"
              :label="t('antimoneylaundering.query.blocktrading.greaterThanOrEqual')"
              prop="amount2"
            >
              <f-amount v-model="queryForm.amount2" :disabled="!queryForm.multiCheckBox" />
            </f-form-item>
          </template>
        </f-query-grid>
        <br />
        <f-panel v-if="checkedListHasValue" :title="t('antimoneylaundering.query.blocktrading.detailTitle')">
          <f-query-grid
            v-if="checkedListHasValue"
            ref="queryTableDetail"
            row-key="id"
            :table-columns="tableColumnsDetail"
            :url="detailList"
            :form-data="queryForm"
            :export-exclude="['operate']"
            query-comp-id="antimoneylaundering-query-blocktrading-querydetail-001"
            table-comp-id="antimoneylaundering-query-blocktrading-querydetail-001"
            :count-label="t('common.summary.record')"
            :count-label-unit="t('common.summary.recordUnit')"
            :summation-biz-label="t('common.summary.record')"
            :summation-biz-unit="t('common.summary.recordUnit')"
            border
            :show-print="false"
            :show-export="false"
            :post-params="postParams"
            :show-query-panel="false"
          />
        </f-panel>
      </div>
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import { list, queryClientInfo, officeName, currency, detailList, queryAperture } from "../url";
import { useI18n } from "vue-i18n";
import { useModelRange } from "@/hooks/conversion";
import { useBizList } from "../hooks/useBizList";
import { useEnum } from "../hooks/useEnum";
const { postParams } = useModelRange(["executeDate"]);
const { t } = useI18n();
const { transactionTypeTwo, Unit, yesOrNo } = useEnum();
const {
  handleCheckedList,
  queryForm,
  queryTable,
  getOpenDate,
  queryTableDetail,
  checkedListHasValue,
  downLoadReport,
  handClearSelection,
  clearSelection
} = useBizList();

//大额交易查询列表
const tableColumns = [
  {
    prop: "selection",
    type: "selection",
    fixed: "left"
  },
  /*客户名称*/
  {
    prop: "clientname",
    label: t("antimoneylaundering.query.blocktrading.clientName"),
    sortable: true
  },
  /*执行日*/
  {
    prop: "executedate",
    label: t("antimoneylaundering.query.blocktrading.executeDates"),
    sortable: true
  },
  /*累计金额*/
  {
    prop: "amount",
    label: t("antimoneylaundering.query.blocktrading.sumAmount"),
    sortable: false,
    formatter: { name: "amount" }
  },
  /*累计笔数*/
  {
    prop: "countrows",
    label: t("antimoneylaundering.query.blocktrading.sumCount"),
    sortable: false
  }
];
//大额交易详情
const tableColumnsDetail = [
  /*机构*/
  {
    prop: "officename",
    label: t("antimoneylaundering.query.blocktrading.officeId"),
    sortable: true,
    width: "150px"
  },
  /*币种*/
  {
    prop: "currencyname",
    label: t("antimoneylaundering.query.blocktrading.currencyId"),
    sortable: true,
    width: "150px"
  },
  /*执行日*/
  {
    prop: "executedate",
    label: t("antimoneylaundering.query.blocktrading.executeDates"),
    sortable: false,
    width: "150px"
  },
  /*交易号*/
  {
    prop: "transno",
    label: t("antimoneylaundering.query.blocktrading.transNo"),
    sortable: false,
    width: "150px"
  },
  /*业务类型*/
  {
    prop: "transactiontype",
    label: t("antimoneylaundering.query.blocktrading.transactionType"),
    sortable: false,
    width: "150px",
    formatter: { name: "const", const: "antimoneylaundering.TransactionType" }
  },
  /*付款人名称*/
  {
    prop: "payclientname",
    label: t("antimoneylaundering.query.blocktrading.payClientName"),
    sortable: true,
    width: "150px"
  },
  /*付款账户号*/
  {
    prop: "payaccountno",
    label: t("antimoneylaundering.query.blocktrading.payAccountNo"),
    sortable: true,
    width: "150px"
  },
  /*收款人名称*/
  {
    prop: "receiveclientname",
    label: t("antimoneylaundering.query.blocktrading.receiveClientName"),
    sortable: true,
    width: "150px"
  },
  /*收款人账户号*/
  {
    prop: "receiveaccountno",
    label: t("antimoneylaundering.query.blocktrading.receiveAccountNo"),
    sortable: true,
    width: "150px"
  },
  /*资金用途*/
  {
    prop: "remarks",
    label: t("antimoneylaundering.query.blocktrading.remarks"),
    sortable: true
  },
  /*金额*/
  {
    prop: "amount",
    label: t("antimoneylaundering.query.blocktrading.amount"),
    sortable: true,
    formatter: { name: "amount" },
    width: "150px"
  }
];
</script>
<style lang="scss">
.block-trading {
  padding: 0px !important;
}
</style>
