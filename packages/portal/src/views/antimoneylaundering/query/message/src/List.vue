<template>
  <f-query-scene :title="t('antimoneylaundering.query.message.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="list"
        :form-data="queryForm"
        :post-params="postParams"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-query-message-query-001"
        table-comp-id="antimoneylaundering-query-message-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        border
        :show-print="false"
        :export-url="exportUrl"
      >
        <template #query-panel>
          <!--   报文日期   -->
          <f-form-item :label="t('antimoneylaundering.query.message.messageDate')" prop="messageDate">
            <f-lax-range-date-picker v-model="queryForm.messageDate" />
          </f-form-item>
          <!--   报文状态   -->
          <f-form-item :label="t('antimoneylaundering.query.message.messageStatus')" prop="statusIdList">
            <f-select
              v-model="queryForm.statusIdList"
              :data="messageStatus"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--   报送状态   -->
          <f-form-item :label="t('antimoneylaundering.query.message.sendStatus')" prop="sendStatusList">
            <f-select
              v-model="queryForm.sendStatusList"
              :data="messagesSendStatus"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--   报文类型   -->
          <f-form-item :label="t('antimoneylaundering.query.message.messageType')" prop="messageTypes">
            <f-select
              v-model="queryForm.messageTypes"
              :data="messageType"
              init-if-blank
              blank-option
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--   回执类型   -->
          <f-form-item :label="t('antimoneylaundering.query.message.receiptType')" prop="receiptTypeList">
            <f-select
              v-model="queryForm.receiptTypeList"
              :data="messageReceiptNoticeType"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
        </template>

        <template #fileName="{ row }">
          <f-button link type="primary" @click="openDetail(row)">{{ row.filename }}</f-button>
        </template>
        <!--   回执类型 超链接  -->
        <template #receiptTypeStr="{ row }">
          <f-button type="primary" @click="openRDetail(row)" link>
            {{ row.receipttypestr }}
          </f-button>
        </template>
      </f-query-grid>
    </template>
    <Detail ref="detail" :id="id" />
    <RDetail ref="rDetail" :id="rid" :params="rParams" />
  </f-query-scene>
</template>
<script setup lang="ts">
import RDetail from "./Receipt.vue";
import Detail from "./Detail.vue";
import { list, exportUrl } from "../url";
import { reactive, ref } from "vue";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { useModelRange } from "@/hooks/conversion";
import { useListDetail } from "@/hooks/biz";

const { t } = useI18n();
//报文回执类型
const messageReceiptNoticeType = useConst("antimoneylaundering.MessageReceiptNoticeType");
//报文类型
const messageType = useConst("antimoneylaundering.MessagesType");
//报文状态
const messageStatus = useConst("antimoneylaundering.MessageStatus");
//报送状态
const messagesSendStatus = useConst("antimoneylaundering.MessagesSendStatus");
const queryForm = reactive({
  statusIdList: [],
  sendStatusList: [],
  messageTypes: [],
  receiptTypeList: [],
  messageDate: []
});
const { id, detail, open } = useListDetail();
const rParams = ref();
const { id: rid, detail: rDetail, open: rOpen } = useListDetail();
const { postParams } = useModelRange(["messageDate"]);

const openDetail = (row: Record<string, any>) => {
  id.value = row.id;
  open();
};

const openRDetail = (row: Record<string, any>) => {
  rid.value = row.id;
  rParams.value = row;
  rOpen();
};

const tableColumns = [
  /*报文日期*/
  {
    prop: "messagedate",
    label: t("antimoneylaundering.query.message.messageDate"),
    sortable: true
  },
  /*报文类型*/
  {
    prop: "messagetypestr",
    label: t("antimoneylaundering.query.message.messageType"),
    sortable: false
  },
  /*报文名称*/
  {
    prop: "filename",
    label: t("antimoneylaundering.query.message.messageName"),
    sortable: true,
    slots: { default: "fileName" }
  },
  /*报送文件名*/
  {
    prop: "rpfilename",
    label: t("antimoneylaundering.query.message.rpFileName"),
    sortable: true
  },
  /*报文状态*/
  {
    prop: "statuestr",
    label: t("antimoneylaundering.query.message.messageStatus"),
    sortable: false
  },
  /*报送状态*/
  {
    prop: "sendstatusstr",
    label: t("antimoneylaundering.query.message.sendStatus"),
    sortable: false
  },
  /*回执类型*/
  {
    prop: "receipttypestr",
    slots: { default: "receiptTypeStr" },
    label: t("antimoneylaundering.query.message.receiptType"),
    sortable: false
  },
  /*录入人*/
  {
    prop: "inputUserName",
    label: t("antimoneylaundering.query.message.inputUserId"),
    sortable: true
  },
  /*录入时间*/
  {
    prop: "inputdate",
    label: t("antimoneylaundering.query.message.inputTime"),
    sortable: true
  }
];
</script>
