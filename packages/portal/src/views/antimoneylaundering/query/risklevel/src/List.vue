<template>
  <f-query-scene :title="t('antimoneylaundering.query.risklevel.title')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        :table-columns="tableColumns"
        :url="list"
        :form-data="queryForm"
        :export-exclude="['operate']"
        query-comp-id="antimoneylaundering-query-risklevel-query-001"
        table-comp-id="antimoneylaundering-query-risklevel-table-001"
        :count-label="t('common.summary.record')"
        :count-label-unit="t('common.summary.recordUnit')"
        :summation-biz-label="t('common.summary.record')"
        :summation-biz-unit="t('common.summary.recordUnit')"
        border
        :show-print="false"
        :export-url="exportUrl"
        :post-params="_postParams"
      >
        <template #query-panel>
          <!--   客户类型   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.clientType')" prop="clientTypeList">
            <f-select
              v-model="queryForm.clientTypeList"
              :data="clientType"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--   风险等级   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.riskLevel')" prop="riskLevelList">
            <f-select
              v-model="queryForm.riskLevelList"
              :url="riskLevelUrl"
              value-key="key"
              label="value"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--   客户名称      -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.clientName')" prop="clientCodeList">
            <f-magnifier-multi
              v-model="queryForm.clientCodeList"
              :title="t('antimoneylaundering.query.risklevel.clientName')"
              :url="queryClientInfo"
              row-key="clientCode"
              row-label="clientName"
              collapse-tags-tooltip
              auto-init
              :params="{
                clientClass: 1
              }"
            >
              <f-magnifier-column prop="clientCode" :label="t('antimoneylaundering.query.risklevel.clientCode')" />
              <f-magnifier-column prop="clientName" :label="t('antimoneylaundering.query.risklevel.clientName')" />
            </f-magnifier-multi>
          </f-form-item>
          <!--   操作类型   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.openClientFlag')" prop="operateTypeList">
            <f-select
              v-model="queryForm.operateTypeList"
              :data="operateType"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
          <!--   是否包含下级客户   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.inclusionLowerFlag')" prop="inclusionLowerFlag">
            <f-select v-model="queryForm.inclusionLowerFlag" :data="YesOrNoEnum" />
          </f-form-item>
          <!--   查询口径   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.queryAperture')" prop="queryAperture">
            <f-select
              v-model="queryForm.queryAperture"
              :url="queryAperture"
              label="orgStructureName"
              value-key="orgStructureCode"
            />
          </f-form-item>
          <!--   生效日期   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.effectDate')" prop="queryDate">
            <f-lax-range-date-picker v-model="queryForm.queryDate" />
          </f-form-item>
          <!--    评级体系编号      -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.crertBodyCode')" prop="crertBodyIdList">
            <f-magnifier-multi
              v-model="queryForm.crertBodyIdList"
              :title="t('antimoneylaundering.query.risklevel.crertBodyCode')"
              :url="queryCrertBodyCodeUrl"
              row-key="crertBodyId"
              row-label="crertBodyCode"
              collapse-tags-tooltip
              auto-init
            >
              <f-magnifier-column
                prop="crertBodyCode"
                :label="t('antimoneylaundering.query.risklevel.crertBodyCode')"
              />
              <f-magnifier-column
                prop="crertBodyName"
                :label="t('antimoneylaundering.query.risklevel.crertBodyName')"
              />
            </f-magnifier-multi>
          </f-form-item>
          <!--   评级状态   -->
          <f-form-item :label="t('antimoneylaundering.query.risklevel.statusId')" prop="statusList">
            <f-select
              v-model="queryForm.statusList"
              :data="RecordStatus.pickConst([RecordStatus.EFFECTED, RecordStatus.ASIDE])"
              init-if-blank
              blankOption
              multiple
              select-all
              collapse-tags
            />
          </f-form-item>
        </template>

        <template #code="{ row }">
          <f-button link type="primary" @click="openDetail(row)">{{ row.firstreviewcode }}</f-button>
        </template>
        <template #statusId="{ row }">
          {{ RecordStatus.find(x => x.code === row.recordstatus)?.label }}
        </template>
        <template #clientType="{ row }">
          {{ clientType.find(x => x.value === row.clienttype)?.label }}
        </template>
        <template #openedClient="{ row }">
          {{ operateType.find(x => x.value === row.openedclient)?.label }}
        </template>
      </f-query-grid>
    </template>
    <RiskMainDetail ref="riskMainDetailRef" :id="id" />
    <GradepreDetail ref="gradepreDetailRef" :id="id" />
  </f-query-scene>
</template>
<script setup lang="ts">
import { list, queryClientInfo, riskLevelUrl, queryCrertBodyCodeUrl, exportUrl, queryAperture } from "../url";
import { ref, reactive, shallowRef } from "vue";
import { useConst } from "@ifs/support";
import { useI18n } from "vue-i18n";
import { useModelRange } from "@/hooks/conversion";

import { Detail as RiskMainDetail } from "@/views/antimoneylaundering/riskmaintain";
import { Detail as GradepreDetail } from "@/views/antimoneylaundering/riskratingevaluation/gradepreliminaryeval";

const { t } = useI18n();
const clientType = useConst("antimoneylaundering.ClientType");
const RecordStatus = useConst("antimoneylaundering.RecordStatus");
const { postParams } = useModelRange(["queryDate"]);
const operateType = useConst("antimoneylaundering.OperateType");
const YesOrNoEnum = useConst("common.YesOrNo");

const queryForm = reactive({
  clientTypeList: [],
  riskLevelList: [],
  clientCodeList: [],
  operateTypeList: [],
  queryDate: [],
  crertBodyIdList: [],
  queryAperture: "",
  inclusionLowerFlag: YesOrNoEnum.NO,
  statusList: []
});
const riskMainDetailRef = shallowRef();
const gradepreDetailRef = shallowRef();
const id = ref("");
const openDetail = (row: any) => {
  id.value = row.id;
  if (row.type === 1) {
    gradepreDetailRef.value?.setVisible(true);
  } else if (row.type === 2) {
    riskMainDetailRef.value?.setVisible(true);
  } else {
    alert(`${row.id} clicked! no satisfy detail page`);
  }
};

const _postParams = (formData: Record<string, any>) => {
  const queryForm = postParams(formData);
  if (!queryForm.clientCodeList?.length) {
    delete queryForm.inclusionLowerFlag;
    delete queryForm.queryAperture;
  }
  return queryForm;
};

const tableColumns = [
  /*风险评级编号*/
  {
    prop: "firstreviewcode",
    label: t("antimoneylaundering.query.risklevel.firstReviewCode"),
    slots: { default: "code" },
    sortable: true,
    width: "150px"
  },
  /*客户类型*/
  {
    prop: "clienttype",
    label: t("antimoneylaundering.query.risklevel.clientType"),
    sortable: false,
    slots: { default: "clientType" },
    width: "150px"
  },
  /*操作类型*/
  {
    prop: "operatetype",
    label: t("antimoneylaundering.query.risklevel.openClientFlag"),
    sortable: false,
    slots: { default: "openedClient" },
    width: "150px"
  },
  /*客户编号*/
  {
    prop: "clientcode",
    label: t("antimoneylaundering.query.risklevel.clientCode"),
    sortable: true,
    width: "150px"
  },
  /*客户名称*/
  {
    prop: "clientname",
    label: t("antimoneylaundering.query.risklevel.clientName"),
    sortable: true,
    width: "150px"
  },
  /*备注*/
  {
    prop: "remark",
    label: t("antimoneylaundering.query.risklevel.remark"),
    sortable: true,
    width: "150px"
  },
  /*风险等级评估分值*/
  {
    prop: "score",
    label: t("antimoneylaundering.query.risklevel.score"),
    sortable: true,
    width: "150px"
  },
  /*风险等级*/
  {
    prop: "autogradecode",
    label: t("antimoneylaundering.query.risklevel.riskLevel"),
    sortable: true,
    width: "150px"
  },
  /*生效日期*/
  {
    prop: "effectivedate",
    label: t("antimoneylaundering.query.risklevel.effectDate"),
    sortable: true,
    width: "150px"
  },
  /*作废生效日期*/
  {
    prop: "enddate",
    label: t("antimoneylaundering.query.risklevel.cancelEffectDate"),
    sortable: true,
    width: "150px"
  },
  /*状态*/
  {
    prop: "recordstatus",
    label: t("antimoneylaundering.query.risklevel.statusId"),
    sortable: false,
    slots: { default: "statusId" },
    width: "150px"
  }
];
</script>
