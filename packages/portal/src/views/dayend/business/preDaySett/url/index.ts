//预日结
export const preDaySettUrl = "{dayend}/api/v1/finance/day-end/handle/day-sett-handle/advance-day-sett";
//获取联机日期
export const openDateUrl = "{dayend}/api/v1/finance/dayend/remote/server/open-date";
//获取机构信息
export const getOfficeInfoUrl = "{dayend}/api/v1/finance/daysett/remote/server/query-office-info";
//获取币种信息
export const getCurrencyInfoUrl = "{dayend}/api/v1/finance/daysett/remote/server/query-currency-info";
//查询日结校验信息
export const daySettCheckUrl = "{dayend}/api/v1/finance/day-end/set/day-sett-check-set/list";

//查询审批在途信息
export const queryApprovalInTransitUrl = "{workflow}/api/v1/finance/workflow/queryApprovalInTransitSum";
//查询交易中心账务未处理信息
export const transCenterAccountingSearch =
  "{transaction-query}/api/v1/finance/trans-center/trans-instruction/get-accounting-fail-trans";
//查询交易中心外部指令待受理信息
export const instructionRecSearch =
  "{transaction-query}/api/v1/finance/trans-center/instruction-rec/remote/server/query-instruction-rec-sum";
//查询交易中心交易未处理信息
export const transCenterTransSearch =
  "{transaction-query}/api/v1/finance/trans-center/trans-instruction/get-fail-trans-sum";
//查询结售汇在途
export const forexInTransitUrl = "{forex-manage-query}/api/v1/finance/query/forex/core/forex-info/day-sett-list";
//查询跨境资金池待结购汇在途
export const crossBorderInTransitUrl =
  "{forex-manage-query}/api/v1/finance/forex/crossborder/forex-manage/remote/server/query-day-end-sett";
//查询交易中心银企指令未处理信息
export const transCenterInstructSearch =
  "{transaction-query}/api/v1/finance/trans-center/query/bank-instruction/remote/server/query-bank-instruction-unfinish";
//查询未审批的手动上划下拨数据
export const bankPlatUnApprovedSearch = "{bankportal}/api/v1/finance/bank-plat/manual-gather/query-unApproved-list";
//查询银企待入账信息
export const bankPlatTransSearch = "{bankportal}/api/v1/finance/bank-plat/acc-trans/find-trans-list";
//查询存放同业在途
export const queryInterBankDepositValidatekUrl =
  "{interbank-deposit-query}/api/v1/finance/query/interbank-deposit/business/fixed-open-register/query-day-sett-validate-list";
//查询质押式回购在途
export const queryNCDCheckUrl = "{ncdmanage-operate}/api/v1/finance/ncd/confirm/list-page";
//查询上一日日终跑批异常信息
export const dayEndErrorSearch = "{dayend}/api/v1/finance/day-end/handle/day-sett-handle/day-end-error-search-sum";
