<template>
  <f-blank-scene :title="t('dayend.business.daySett.title')" :isPoint="false">
    <f-multi-form-panel ref="daySettStatusform" :model="daySettHandleDto" :rules="rules" :column="3">
      <f-panel>
        <f-form-item :label="t('dayend.business.daySett.daySettDate')">
          <f-input v-model="daySettHandleDto.onlineDate" disabled />
        </f-form-item>
        <f-form-item />
        <f-form-item />
        <f-form-item :label="t('dayend.business.daySett.office')" prop="officeIds">
          <f-select
            v-model="daySettHandleDto.officeIds"
            :url="getOfficeInfoUrl"
            value-key="officeId"
            label="officeName"
            multiple
            select-all
            collapse-tags
            collapse-tags-tooltip
            @change="officeChange"
          />
        </f-form-item>
        <f-form-item />
        <f-form-item />
        <f-form-item :label="t('dayend.business.daySett.currency')" prop="currencyIds">
          <f-select
            ref="currencyRef"
            v-model="daySettHandleDto.currencyIds"
            :url="getCurrencyInfoUrl"
            value-key="currencyId"
            label="currencyName"
            :extra-data="currencyParams"
            multiple
            select-all
            collapse-tags
            collapse-tags-tooltip
            @change="currencyChange"
          />
        </f-form-item>
        <f-form-item />
        <f-form-item />
        <f-form-item :label="t('dayend.business.daySett.daySettStatus')">
          <f-select v-model="daySettHandleDto.daySettStatus" :data="daySettStatusEnum" disabled />
        </f-form-item>
        <f-form-item>
          <f-button text type="primary" @click="daySettStatusDialog">{{
            t("dayend.business.daySett.btnDaySettStatus")
          }}</f-button>
        </f-form-item>
        <f-form-item>
          <f-button text type="primary" @click="daySettValidateDialog">{{
            t("dayend.business.daySett.btnValidateDetailInfo")
          }}</f-button>
        </f-form-item>
      </f-panel>
      <!--  日结状态列表 -->
      <f-dialog
        ref="daySettStatusDialogRef"
        v-model="daySettStatusDialogVisible"
        :title="t('dayend.business.daySett.daySettStatus')"
        destroy-on-close
      >
        <f-query-table
          row-key="id"
          :url="daySettStatusSearch"
          border
          :params="daySettHandleDto"
          show-header
          auto-reset
          auto-init
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column
            prop="daySettStatus"
            :label="t('dayend.business.daySett.daySettStatus')"
            :formatter="{ name: 'const', const: 'dayend.DaySettStatus' }"
          />
        </f-query-table>
      </f-dialog>
      <!-- 日结校验明细 -->
      <f-dialog
        ref="daySettDialogRef"
        v-model="daySettValidateDialogVisible"
        :title="t('dayend.business.daySett.title')"
        width="80%"
        destroy-on-close
      >
        <f-form-item :label="t('dayend.business.daySett.currency')" prop="currencyId">
          <f-select
            v-model="queryForm.currencyId"
            :url="getCurrencyInfoUrl"
            value-key="currencyId"
            label="currencyName"
          />
        </f-form-item>
        <f-form-item :label="t('dayend.business.daySett.controlMode')" prop="controlMode">
          <f-select
            v-model="queryForm.controlMode"
            :data="controlType.omitConst([controlType.NOT_CONTROL])"
            blank-option
            filterable
            multiple
            collapse-tags
            select-all
            init-if-blank
          />
        </f-form-item>
        <f-button type="info" plain @click.prevent="daysettDetailChange">{{
          t("dayend.business.daySett.query")
        }}</f-button>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE10]">
          {{ showMapEnumObj.CODE10.label }}
          {{
            controlTppe[showMapEnum.CODE10] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE10QueryTable"
          v-if="controlHidden[showMapEnum.CODE10]"
          row-key="id"
          :url="queryApprovalInTransitUrl"
          border
          :params="{
            moduleCode: 'Z05',
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" width="200px" />
          <f-table-column prop="transTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column
            prop="transAmount"
            :label="t('dayend.business.daySett.amount')"
            :formatter="{ name: 'amount' }"
          />
          <f-table-column prop="workflowStatusName" :label="t('dayend.business.daySett.businessStatus')" />
          <f-table-column prop="createUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" width="220px" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE20]">
          {{ showMapEnumObj.CODE20.label }}
          {{
            controlTppe[showMapEnum.CODE20] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE20QueryTable"
          v-if="controlHidden[showMapEnum.CODE20]"
          row-key="id"
          :url="queryApprovalInTransitUrl"
          border
          :params="{
            moduleCode: 'Z04',
            consValues: ['Z040002', 'Z040006', 'Z040009', 'Z040010', 'Z040011'],
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" width="200px" />
          <f-table-column prop="transTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column prop="workflowStatusName" :label="t('dayend.business.daySett.businessStatus')" />
          <f-table-column prop="createUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" width="220px" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE30]">
          {{ showMapEnumObj.CODE30.label }}
          {{
            controlTppe[showMapEnum.CODE30] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE30QueryTable"
          v-if="controlHidden[showMapEnum.CODE30]"
          row-key="id"
          :url="queryApprovalInTransitUrl"
          border
          :params="CODE30QueryParams"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" width="200px" />
          <f-table-column prop="transTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column
            prop="transAmount"
            :label="t('dayend.business.daySett.amount')"
            :formatter="{ name: 'amount' }"
          />
          <f-table-column prop="workflowStatusName" :label="t('dayend.business.daySett.businessStatus')" />
          <f-table-column prop="createUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" width="220px" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE40]">
          {{ showMapEnumObj.CODE40.label }}
          {{
            controlTppe[showMapEnum.CODE40] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE40QueryTable"
          v-if="controlHidden[showMapEnum.CODE40]"
          row-key="id"
          :url="queryApprovalInTransitUrl"
          border
          :params="{
            moduleCode: 'Z10',
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" width="200px" />
          <f-table-column prop="transTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column
            prop="transAmount"
            :label="t('dayend.business.daySett.amount')"
            :formatter="{ name: 'amount' }"
          />
          <f-table-column prop="workflowStatusName" :label="t('dayend.business.daySett.businessStatus')" />
          <f-table-column prop="createUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" width="220px" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE50]">
          {{ showMapEnumObj.CODE50.label }}
          {{
            controlTppe[showMapEnum.CODE50] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE50QueryTable"
          v-if="controlHidden[showMapEnum.CODE50]"
          row-key="id"
          :url="transCenterInstructSearch"
          border
          :params="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE60]">
          {{ showMapEnumObj.CODE60.label }}
          {{
            controlTppe[showMapEnum.CODE60] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE60QueryTable"
          v-if="controlHidden[showMapEnum.CODE60]"
          row-key="id"
          :table-data="CODE60TableData"
          border
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="accountNo" :label="t('dayend.business.daySett.bankAcctNo')" />
          <f-table-column prop="bankTypeName" :label="t('dayend.business.daySett.bankType')" />
          <f-table-column
            prop="direction"
            :label="t('dayend.business.daySett.direction')"
            :formatter="{ name: 'const', const: 'bankplat.Direction' }"
          />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE70]">
          {{ showMapEnumObj.CODE70.label }}
          {{
            controlTppe[showMapEnum.CODE70] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE70QueryTable"
          v-if="controlHidden[showMapEnum.CODE70]"
          row-key="id"
          :url="bankPlatUnApprovedSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE71]">
          {{ showMapEnumObj.CODE71.label }}
          {{
            controlTppe[showMapEnum.CODE71] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE71QueryTable"
          v-if="controlHidden[showMapEnum.CODE71]"
          row-key="id"
          :url="forexInTransitUrl"
          border
          :params="{
            currencyIds: [queryForm.currencyId],
            runBatchDate: daySettHandleDto.onlineDate
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="applyNo" :label="t('dayend.business.daySett.businessCode')" />
          <f-table-column prop="forexBusinessTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column prop="businessStatusName" :label="t('dayend.business.daySett.status')" />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
          <f-table-column prop="inputUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE72]">
          {{ showMapEnumObj.CODE72.label }}
          {{
            controlTppe[showMapEnum.CODE72] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE72QueryTable"
          v-if="controlHidden[showMapEnum.CODE72]"
          row-key="id"
          :url="crossBorderInTransitUrl"
          border
          :params="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="applyNo" :label="t('dayend.business.daySett.businessCode')" />
          <f-table-column prop="forexBusinessTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column prop="businessStatusName" :label="t('dayend.business.daySett.status')" />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
          <f-table-column prop="inputUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE73]">
          {{ showMapEnumObj.CODE73.label }}
          {{
            controlTppe[showMapEnum.CODE73] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE73QueryTable"
          v-if="controlHidden[showMapEnum.CODE73]"
          row-key="id"
          :url="bankPlatUnApprovedSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE74]">
          {{ showMapEnumObj.CODE74.label }}
          {{
            controlTppe[showMapEnum.CODE74] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE74QueryTable"
          v-if="controlHidden[showMapEnum.CODE74]"
          row-key="id"
          :url="bankPlatUnApprovedSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE80]">
          {{ showMapEnumObj.CODE80.label }}
          {{
            controlTppe[showMapEnum.CODE80] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE80QueryTable"
          v-if="controlHidden[showMapEnum.CODE80]"
          row-key="id"
          :url="queryApprovalInTransitUrl"
          border
          :params="{
            consValues: ['Z620005', 'Z620006', 'Z620017', 'Z620013', 'Z500001', 'Z610020', 'Z020071', 'Z500005'],
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" width="200px" />
          <f-table-column prop="transTypeName" :label="t('dayend.business.daySett.businessType')" />
          <f-table-column
            prop="transAmount"
            :label="t('dayend.business.daySett.amount')"
            :formatter="{ name: 'amount' }"
          />
          <f-table-column prop="workflowStatusName" :label="t('dayend.business.daySett.businessStatus')" />
          <f-table-column prop="createUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column prop="nextOperatorNames" :label="t('dayend.business.daySett.nextOperator')" width="220px" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE90]">
          {{ showMapEnumObj.CODE90.label }}
          {{
            controlTppe[showMapEnum.CODE90] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE90QueryTable"
          v-if="controlHidden[showMapEnum.CODE90]"
          row-key="id"
          :url="transCenterInstructSearch"
          border
          :params="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE100]">
          {{ showMapEnumObj.CODE100.label }}
          {{
            controlTppe[showMapEnum.CODE100] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE100QueryTable"
          v-if="controlHidden[showMapEnum.CODE100]"
          row-key="id"
          :url="transCenterAccountingSearch"
          border
          :params="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="transNo" :label="t('dayend.business.daySett.transCode')" />
          <f-table-column
            prop="transactionType"
            :label="t('dayend.business.daySett.TransactionType')"
            :formatter="{ name: 'const', const: 'common.TransactionType' }"
          />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
          <f-table-column
            prop="accountingStatus"
            :label="t('dayend.business.daySett.status')"
            :formatter="{ name: 'const', const: 'counter.accountingProcStatus' }"
          />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE110]">
          {{ showMapEnumObj.CODE110.label }}
          {{
            controlTppe[showMapEnum.CODE110] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE110QueryTable"
          v-if="controlHidden[showMapEnum.CODE110]"
          row-key="id"
          :url="transCenterAccountingSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId],
            eqExecuteDate: daySettHandleDto.onlineDate
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE120]">
          {{ showMapEnumObj.CODE120.label }}
          {{
            controlTppe[showMapEnum.CODE120] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE120QueryTable"
          v-if="controlHidden[showMapEnum.CODE120]"
          row-key="id"
          :url="instructionRecSearch"
          border
          :params="CODE120QueryParams"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="instructionNo" :label="t('dayend.business.daySett.instructionCode')" />
          <f-table-column
            prop="instructionType"
            :label="t('dayend.business.daySett.businessType')"
            :formatter="{ name: 'const', const: 'common.InstructionType' }"
          />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
          <f-table-column prop="inputUserName" :label="t('dayend.business.daySett.inputUserName')" />
          <f-table-column
            prop="instStatus"
            :label="t('dayend.business.daySett.status')"
            :formatter="{ name: 'const', const: 'counter.instructionStatus' }"
          />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE130]">
          {{ showMapEnumObj.CODE130.label }}
          {{
            controlTppe[showMapEnum.CODE130] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE130QueryTable"
          v-if="controlHidden[showMapEnum.CODE130]"
          row-key="id"
          :url="transCenterAccountingSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId],
            eqExecuteDate: daySettHandleDto.onlineDate
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE140]">
          {{ showMapEnumObj.CODE140.label }}
          {{
            controlTppe[showMapEnum.CODE140] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE140QueryTable"
          v-if="controlHidden[showMapEnum.CODE140]"
          row-key="id"
          :url="transCenterAccountingSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId],
            eqExecuteDate: daySettHandleDto.onlineDate
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE150]">
          {{ showMapEnumObj.CODE150.label }}
          {{
            controlTppe[showMapEnum.CODE150] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE150QueryTable"
          v-if="controlHidden[showMapEnum.CODE150]"
          row-key="id"
          :url="queryInterBankDepositValidatekUrl"
          border
          :params="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" />
          <f-table-column
            prop="businessType"
            :label="t('dayend.business.daySett.businessType')"
            :formatter="{ name: 'const', const: 'interbankdeposit.InterBankDepositBusinessType' }"
          />
          <f-table-column
            prop="businessStatus"
            :label="t('dayend.business.daySett.businessStatus')"
            :formatter="{ name: 'const', const: 'interbankdeposit.BusinessStatus' }"
          />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
          <f-table-column prop="inputUserName" :label="t('dayend.business.daySett.inputUserName')" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE160]">
          {{ showMapEnumObj.CODE160.label }}
          {{
            controlTppe[showMapEnum.CODE160] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE160QueryTable"
          v-if="controlHidden[showMapEnum.CODE160]"
          row-key="id"
          :url="queryNCDCheckUrl"
          border
          :params="{
            currencyId: queryForm.currencyId,
            businessStatusList: [businessStatus.PENDING_CONFIRM],
            execBusinessTypeList: [
              ncdBusinessType.NCD_BUY_EXECUTE,
              ncdBusinessType.NCD_SELL_EXECUTE,
              ncdBusinessType.NCD_DUE_PAYMENT
            ],
            businessTypeList: [
              ncdBusinessType.NCD_BUY_CONFIRM,
              ncdBusinessType.NCD_SELL_CONFIRM,
              ncdBusinessType.NCD_DUE_PAYMENT
            ]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" />
          <f-table-column
            prop="businessType"
            :label="t('dayend.business.daySett.businessType')"
            :formatter="{ name: 'const', const: 'ncdmanage.NcdBusinessType' }"
          />
          <f-table-column
            prop="businessStatus"
            :label="t('dayend.business.daySett.businessStatus')"
            :formatter="{ name: 'const', const: 'common.BusinessStatus' }"
          />
          <f-table-column
            prop="nominalTransAmount"
            :label="t('dayend.business.daySett.amount')"
            :formatter="{ name: 'amount' }"
          />
          <f-table-column prop="inputUserName" :label="t('dayend.business.daySett.inputUserName')" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE170]">
          {{ showMapEnumObj.CODE170.label }}
          {{
            controlTppe[showMapEnum.CODE170] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE170QueryTable"
          v-if="controlHidden[showMapEnum.CODE170]"
          row-key="id"
          :url="queryNCDCheckUrl"
          border
          :params="{
            currencyId: queryForm.currencyId,
            businessStatusList: [businessStatus.PENDING_CONFIRM],
            execBusinessTypeList: [
              ncdBusinessType.NCD_PLEDGE_REPO_EXECUTE,
              ncdBusinessType.NCD_PLEDGE_REVERSE_REPO_EXECUTE,
              ncdBusinessType.NCD_PLEDGE_REPO_DUE_REDEEM_EXECUTE,
              ncdBusinessType.NCD_PLEDGE_REVERSE_REPO_DUE_RESALE
            ],
            businessTypeList: [
              ncdBusinessType.NCD_PLEDGE_REPO_CONFIRM,
              ncdBusinessType.NCD_PLEDGE_REVERSE_REPO_CONFIRM,
              ncdBusinessType.NCD_PLEDGE_REPO_DUE_REDEEM_CONFIRM,
              ncdBusinessType.NCD_PLEDGE_REVERSE_REPO_DUE_RESALE
            ]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column prop="businessCode" :label="t('dayend.business.daySett.businessCode')" />
          <f-table-column
            prop="businessType"
            :label="t('dayend.business.daySett.businessType')"
            :formatter="{ name: 'const', const: 'ncdmanage.NcdBusinessType' }"
          />
          <f-table-column
            prop="businessStatus"
            :label="t('dayend.business.daySett.businessStatus')"
            :formatter="{ name: 'const', const: 'common.BusinessStatus' }"
          />
          <f-table-column
            prop="nominalTransAmount"
            :label="t('dayend.business.daySett.amount')"
            :formatter="{ name: 'amount' }"
          />
          <f-table-column prop="inputUserName" :label="t('dayend.business.daySett.inputUserName')" />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE180]">
          {{ showMapEnumObj.CODE180.label }}
          {{
            controlTppe[showMapEnum.CODE180] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE180QueryTable"
          v-if="controlHidden[showMapEnum.CODE180]"
          row-key="id"
          :url="transCenterAccountingSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId],
            eqExecuteDate: daySettHandleDto.onlineDate
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        />
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE190]">
          {{ showMapEnumObj.CODE190.label }}
          {{
            controlTppe[showMapEnum.CODE190] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE190QueryTable"
          v-if="controlHidden[showMapEnum.CODE190]"
          row-key="id"
          :url="transCenterTransSearch"
          border
          :form-data="{
            currencyIds: [queryForm.currencyId]
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-count-value="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column prop="officeName" :label="t('dayend.business.daySett.office')" />
          <f-table-column prop="currencyName" :label="t('dayend.business.daySett.currency')" />
          <f-table-column
            prop="srcChannel"
            :label="t('dayend.business.daySett.channel')"
            :formatter="{ name: 'const', const: 'common.Channel' }"
          />
          <f-table-column prop="instructionNo" :label="t('dayend.business.daySett.instructionCode')" />
          <f-table-column
            prop="transactionType"
            :label="t('dayend.business.daySett.TransactionType')"
            :formatter="{ name: 'const', const: 'common.TransactionType' }"
          />
          <f-table-column prop="amount" :label="t('dayend.business.daySett.amount')" :formatter="{ name: 'amount' }" />
          <f-table-column
            prop="procStatus"
            :label="t('dayend.business.daySett.status')"
            :formatter="{ name: 'const', const: 'counter.instructionStatus' }"
          />
        </f-query-table>
        <f-divider content-position="left" v-if="controlHidden[showMapEnum.CODE999]">
          {{ showMapEnumObj.CODE999.label }}
          {{
            controlTppe[showMapEnum.CODE999] === controlType.STRONG
              ? t("dayend.business.daySett.strong")
              : t("dayend.business.daySett.soft")
          }}
        </f-divider>
        <f-query-table
          ref="CODE999QueryTable"
          v-if="controlHidden[showMapEnum.CODE999]"
          row-key="id"
          :url="dayEndErrorSearch"
          border
          :params="{
            onlineDate: daySettHandleDto.onlineDate
          }"
          show-header
          auto-reset
          :auto-init="false"
          :show-collapse="false"
          :show-print="false"
          :show-export="false"
          :show-layout="false"
          :show-query-panel="false"
          :show-summation-sum="false"
          :summation-biz-label="t('dayend.business.daySett.record')"
          :summation-biz-unit="t('dayend.business.daySett.slip')"
        >
          <f-table-column
            prop="handleType"
            :label="t('dayend.business.daySett.handleType')"
            :formatter="{ name: 'const', const: 'dayend.DayEndType' }"
          />
          <f-table-column prop="handleInfoName" :label="t('dayend.business.daySett.handleInfoName')" />
          <f-table-column
            prop="handleStatus"
            :label="t('dayend.business.daySett.handleStatus')"
            :formatter="{ name: 'const', const: 'dayend.DayEndDetailRecordStatus' }"
          />
        </f-query-table>
      </f-dialog>
    </f-multi-form-panel>
    <template #footer>
      <f-submit-state
        :gather-params="daySettInfo"
        :url="daySettUrl"
        :result-title="t('dayend.business.daySett.daySett')"
        :operate-name="t('dayend.business.daySett.daySett')"
        :confirm-text="t('dayend.business.daySett.daySettInfo')"
        :is-custom-button="isCustomButton"
        :is-custom-result="isCustomResult"
        :before-trigger="formValidator"
        @submit-success="onSuccess"
      >
        <template #custom-result="{ res }">
          <div :style="{ 'white-space': 'pre-wrap' }">
            {{ res.data.message }}
          </div>
        </template>
        <template #custom-button="{ close }">
          <f-submit-state
            :gather-params="daySettInfoSoft"
            :url="daySettUrl"
            :result-title="t('dayend.business.daySett.daySett')"
            :operate-name="t('dayend.business.daySett.daySettSoft')"
            :confirm-text="t('dayend.business.daySett.daySettInfo')"
            :result-confirm="resultInfo"
            @submit-success="
              res => {
                onSuccess(res);
              }
            "
            @close="close"
          />
        </template>
      </f-submit-state>
      <f-submit-state
        :result-title="t('dayend.business.daySett.cancelDaySett')"
        :operate-name="t('dayend.business.daySett.cancelDaySett')"
        :confirm-text="t('dayend.business.daySett.cancelDaySettInfo')"
        :result-confirm="t('dayend.business.daySett.cancelDaySettSuccess')"
        :gather-params="daySettInfo"
        :url="cancelDaySettUrl"
        @submit-success="getReturnInfo"
      />
    </template>
  </f-blank-scene>
</template>
<script setup lang="ts">
import {
  queryApprovalInTransitUrl,
  transCenterAccountingSearch,
  instructionRecSearch,
  forexInTransitUrl,
  crossBorderInTransitUrl,
  transCenterTransSearch,
  getOfficeInfoUrl,
  getCurrencyInfoUrl,
  daySettStatusSearch,
  cancelDaySettUrl,
  dayEndErrorSearch,
  daySettUrl,
  bankPlatUnApprovedSearch,
  queryInterBankDepositValidatekUrl,
  queryNCDCheckUrl
} from "../url";
import { useDaySett, useDaySettStatusRules } from "../hooks/useDaySett";
import { useI18n } from "vue-i18n";
import { useConst } from "@ifs/support";
import { onMounted } from "vue";
const { t } = useI18n();
const daySettStatusEnum = useConst("dayend.DaySettStatus");
const controlType = useConst("dayend.ControlType");
const showMapEnum = useConst("dayend.ShowMap");
const businessStatus = useConst("common.BusinessStatus");
const ncdBusinessType = useConst("ncdmanage.NcdBusinessType");
const showMapEnumObj = showMapEnum.toObject();
//必填校验
const rules = useDaySettStatusRules();
const {
  daySettHandleDto,
  getOnlineInfo,
  officeChange,
  currencyChange,
  currencyRef,
  currencyParams,
  daySettStatusDialog,
  daySettStatusDialogVisible,
  daySettValidateDialog,
  daySettValidateDialogVisible,
  daySettStatusform,
  formValidator,
  daySettInfo,
  getReturnInfo,
  queryForm,
  daysettDetailChange,
  controlHidden,
  controlTppe,
  CODE10QueryTable,
  CODE20QueryTable,
  CODE30QueryTable,
  CODE30QueryParams,
  CODE40QueryTable,
  CODE50QueryTable,
  CODE60QueryTable,
  CODE60TableData,
  CODE70QueryTable,
  CODE71QueryTable,
  CODE72QueryTable,
  CODE73QueryTable,
  CODE74QueryTable,
  CODE80QueryTable,
  CODE90QueryTable,
  CODE100QueryTable,
  CODE110QueryTable,
  CODE120QueryTable,
  CODE120QueryParams,
  CODE130QueryTable,
  CODE140QueryTable,
  CODE150QueryTable,
  CODE160QueryTable,
  CODE170QueryTable,
  CODE180QueryTable,
  CODE190QueryTable,
  CODE999QueryTable,
  daySettInfoSoft,
  onSuccess,
  isCustomResult,
  isCustomButton,
  resultInfo
} = useDaySett();

//获取开机日
onMounted(() => {
  getOnlineInfo();
});
</script>
