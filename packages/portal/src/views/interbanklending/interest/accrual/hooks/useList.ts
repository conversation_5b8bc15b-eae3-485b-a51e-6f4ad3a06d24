import useTableColumn from "./useTableColumn";
import { useConst } from "@ifs/support";
import { computed, reactive, ref, shallowRef, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import {
  predrawSearch,
  predrawExport,
  writeOffSearch,
  writeOffExport,
  interestSettAlrTransSearch,
  interestSettAlrTransSearchExport,
  predrawDeleteUrl
} from "../url";
import { FMessageBox } from "@dtg/frontend-plus";
import { exportXlsx } from "@/utils/xlsx";
import { useOpenDate } from "@/hooks";
import { useUserStoreHook } from "@/stores/modules/user";
import { storeToRefs } from "pinia";

export const useList = () => {
  const { defaultOfficeId, defaultCurrencyId } = storeToRefs(useUserStoreHook());
  const { computerColumn, queryColumn } = useTableColumn();
  const { openDate } = useOpenDate();
  const { t } = useI18n();
  //状态
  const interestOptSource = useConst("interbanklending.InterestOperateType");
  const yesOrNo = useConst("counter.YesOrNo");

  const state = reactive({
    column: [],
    queryUrl: interestSettAlrTransSearch,
    exportUrl: interestSettAlrTransSearchExport
  });

  const allowSort = [
    "applyCode",
    "dealCode",
    "counterpartyName",
    "financeBankAcctNo",
    "interbankType",
    "amount",
    "lendingCategory",
    "transNo",
    "inputUserName"
  ];

  //表格模板
  const queryTable = shallowRef();

  //列表查询对象
  const queryFrom = reactive({
    officeIds: [defaultOfficeId.value ?? undefined],
    currencyIds: [defaultCurrencyId.value ?? undefined],
    lendingTypes: [],
    operateType: "",
    accrualDate: openDate.value,
    executeDate: openDate.value,
    dealCode: "",
    counterpartyId: null
  });

  //列表查询
  const handleSearch = () => {
    queryTable.value.renderTableData();
    queryTable.value.clearSelection();
  };

  //已选列表数据
  const checkedList = ref<any[]>([]);

  //控制全选checkbox
  const selectableAll = (row: any[]) => {
    return row;
  };

  //勾选checkbox
  const handleSelect = (row: any[]) => {
    checkedList.value = row;
  };

  //是否选中checkbox，控制批量删除按钮是否可以操作
  const isChecked = computed(() => checkedList.value.length > 0);

  //是否选中checkbox，控制批量提交按钮是否可以操作
  const isSubmitChecked = computed(() => checkedList.value.length > 0);

  //是否选中checkbox，控制批量提交按钮是否可以操作
  const isDeleteChecked = computed(
    () => checkedList.value.length > 0 && checkedList.value.every((row: any) => openDate.value === row?.executeDate)
  );

  const handClearSelection = () => {
    checkedList.value.splice(0);
  };

  //操作列按钮
  const generalButtonOption = (row: any) => {
    return reactive([
      {
        type: "remove",
        isShow: openDate.value === row?.executeDate,
        submitComOpt: {
          url: predrawDeleteUrl,
          gatherParams: () => {
            return row;
          },
          close: () => {
            handleSearch();
          }
        }
      }
    ]);
  };

  const disabledDate = (date: any) => {
    return date.getTime() > new Date(openDate.value).getTime();
  };

  const predrawDateDisabledDate = (date: any) => {
    const afterDate = new Date(openDate.value);
    afterDate.setDate(afterDate.getDate() + 1);
    return date.getTime() > afterDate.getTime();
  };

  const submitMessage = ref("");
  const beforeWriteOffSettleTrigger = () => {
    submitMessage.value = t("interbanklending.interest.accruleinterest.settleWriteOffTip", [checkedList.value.length]);
    return true;
  };

  const beforeSettlTrigger = () => {
    submitMessage.value = t("interbanklending.interest.accruleinterest.batchSettleTip", [checkedList.value.length]);
    return true;
  };

  const beforeDeleteTrigger = () => {
    submitMessage.value = t("interbanklending.interest.accruleinterest.batchDeleteTip", [checkedList.value.length]);
    return true;
  };

  const writeOffSettleRusultConfirm = {
    success: t("interbanklending.interest.accruleinterest.wirteOffSuccess"),
    fail: t("interbanklending.interest.accruleinterest.wirteOffFail")
  };

  const settleRusultConfirm = {
    success: t("interbanklending.interest.accruleinterest.settleSuccess"),
    fail: t("interbanklending.interest.accruleinterest.settleFail")
  };

  const deleteRusultConfirm = {
    success: t("interbanklending.interest.accruleinterest.deleteSuccess"),
    fail: t("interbanklending.interest.accruleinterest.deleteFail")
  };

  //表单校验项
  const operateTypeRef = shallowRef();
  const interestDateRef = shallowRef();
  const executeDateRef = shallowRef();

  const buttonShow = ref(true);
  const allButtonShow = ref(true);
  const batchDeleteShow = ref(false);
  const computerOpeate = async () => {
    await operateTypeRef.value.validate();
    await interestDateRef.value.validate();
    await executeDateRef.value.validate();
    allButtonShow.value = true;
    //更新请求路径
    if (queryFrom.operateType === interestOptSource.ACCRUAL) {
      state.queryUrl = predrawSearch;
      state.exportUrl = predrawExport;
    } else {
      // if (daysBetween(new Date(queryFrom.executeDate), new Date(openDate.value)) <= 0) {
      //   FMessageBox.report(t("interbanklending.interest.accruleinterest.tip1"));
      //   return false;
      // }
      state.queryUrl = writeOffSearch;
      state.exportUrl = writeOffExport;
    }
    if (queryFrom.executeDate !== openDate.value) {
      FMessageBox.report(t("interbanklending.interest.accruleinterest.errorAlert1"));
      return false;
    }
    nextTick(() => {
      queryTable.value.renderTableData();
      queryTable.value.clearSelection();
      state.column.length = 0;
      operateType = "computer";
      //按钮显示判断
      buttonShow.value = queryFrom.operateType === interestOptSource.ACCRUAL;
      batchDeleteShow.value = false;
    });
  };

  let operateType = "";

  const queryOpeate = () => {
    state.queryUrl = interestSettAlrTransSearch;
    state.exportUrl = interestSettAlrTransSearchExport;
    state.column.length = 0;
    operateType = "query";
    allButtonShow.value = false;
    batchDeleteShow.value = true;
    queryTable.value.clearSelection();
  };

  const handleOnLoaded = () => {
    nextTick(() => {
      if (operateType === "query") {
        state.column.push(...queryColumn);
      } else if (operateType === "computer") {
        state.column.push(...computerColumn);
      }
    });
  };

  //批量操作的参数
  const gatherBatchParams = () => {
    //结息操作入参
    const interestSettleList = [];
    checkedList.value.forEach((element: any) => {
      if (queryFrom.operateType !== interestOptSource.ACCRUAL) {
        element.writeOffFlag = yesOrNo.YES;
      } else {
        element.accrualDate = queryFrom.accrualDate;
      }
      element.executeDate = queryFrom.executeDate;
      interestSettleList.push(element);
    });
    return { list: interestSettleList };
  };

  const gatherWritOffBatchParams = () => {
    return { list: checkedList.value };
  };

  const batchOperateColumn = [
    {
      field: "applyCode",
      title: t("interbanklending.interest.accruleinterest.dealCode")
    },
    {
      field: "counterpartyNo",
      title: t("interbanklending.interest.accruleinterest.counterpartyCode")
    },
    {
      field: "counterpartyName",
      title: t("interbanklending.interest.accruleinterest.counterpartyName")
    },
    {
      field: "interest",
      title: t("interbanklending.interest.accruleinterest.interest")
    },
    {
      field: "failReason",
      title: t("interbanklending.interest.accruleinterest.failReason")
    }
  ];

  const batchOperateExportColumn = [
    {
      prop: "applyCode",
      label: t("interbanklending.interest.accruleinterest.dealCode")
    },
    {
      prop: "counterpartyNo",
      label: t("interbanklending.interest.accruleinterest.counterpartyCode")
    },
    {
      prop: "counterpartyName",
      label: t("interbanklending.interest.accruleinterest.counterpartyName")
    },
    {
      prop: "interest",
      label: t("interbanklending.interest.accruleinterest.interest")
    },
    {
      prop: "failReason",
      label: t("interbanklending.interest.accruleinterest.failReason")
    }
  ];

  const operateResult = reactive({ failData: [] });

  //导出失败列表是否可点击
  const expoerFailButton = computed(() => operateResult.failData.length > 0);

  const operateAfter = (data: any) => {
    operateResult.failData.length = 0;
    operateResult.failData.push(...data.failData);
  };

  const errorExport = () => {
    exportXlsx(batchOperateExportColumn, operateResult.failData, "失败原因.xlsx");
  };

  return {
    queryTable,
    state,
    selectableAll,
    queryFrom,
    handleSelect,
    isChecked,
    isSubmitChecked,
    gatherBatchParams,
    gatherWritOffBatchParams,
    handleSearch,
    generalButtonOption,
    openDate,
    handClearSelection,
    submitMessage,
    beforeWriteOffSettleTrigger,
    beforeSettlTrigger,
    writeOffSettleRusultConfirm,
    settleRusultConfirm,
    allowSort,
    disabledDate,
    computerOpeate,
    buttonShow,
    queryOpeate,
    allButtonShow,
    interestDateRef,
    executeDateRef,
    operateTypeRef,
    batchDeleteShow,
    beforeDeleteTrigger,
    deleteRusultConfirm,
    batchOperateColumn,
    predrawDateDisabledDate,
    isDeleteChecked,
    handleOnLoaded,
    expoerFailButton,
    operateAfter,
    errorExport
  };
};

export default useList;
