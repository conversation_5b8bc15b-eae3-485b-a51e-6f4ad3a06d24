import { useI18n } from "vue-i18n";

export const useTableColumn = () => {
  const { t } = useI18n();

  //列表显示列配置
  const computerColumn = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "150px",
      prop: "applyCode",
      label: t("interbanklending.interest.accruleinterest.dealCode"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "interbankType",
      label: t("interbanklending.interest.accruleinterest.businessType"),
      formatter: { name: "const", const: "interbanklending.InterBankType" }
    },
    {
      width: "150px",
      prop: "counterpartyName",
      label: t("interbanklending.interest.accruleinterest.counterpartyName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "financeBankAcctNo",
      label: t("interbanklending.interest.accruleinterest.financeBankAcctNo"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "lendingCategory",
      label: t("interbanklending.interest.accruleinterest.lendingCategory"),
      formatter: { name: "const", const: "interbanklending.LendingCategory" }
    },
    {
      width: "150px",
      prop: "amount",
      label: t("interbanklending.interest.accruleinterest.amount"),
      formatter: "amount",
      showOverflowTooltip: true,
      align: "right"
    },
    {
      width: "150px",
      prop: "startDate",
      label: t("interbanklending.interest.accruleinterest.startDate"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("interbanklending.interest.accruleinterest.endDate"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "days",
      label: t("interbanklending.interest.accruleinterest.days"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "interestBalance",
      label: t("interbanklending.interest.accruleinterest.interestBalance"),
      formatter: "amount",
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "interestRate",
      label: t("interbanklending.interest.accruleinterest.interestRate"),
      formatter: "rate",
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "hisAccrualInterest",
      label: t("interbanklending.interest.accruleinterest.hisAccrualInterestColumn"),
      formatter: "amount",
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "interest",
      slots: { default: "totalAccruedInterest" },
      label: t("interbanklending.interest.accruleinterest.accruedInterest"),
      formatter: "amount",
      showOverflowTooltip: true,
      align: "right"
    },
    {
      width: "150px",
      prop: "executeDate",
      label: t("interbanklending.interest.accruleinterest.executeDate"),
      showOverflowTooltip: true
    }
  ];

  const queryColumn = [
    {
      prop: "selection",
      type: "selection",
      reserveSelection: true
    },
    {
      width: "150px",
      prop: "applyCode",
      label: t("interbanklending.interest.accruleinterest.dealCode"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "interbankType",
      label: t("interbanklending.interest.accruleinterest.interbankType"),
      formatter: { name: "const", const: "interbanklending.InterBankType" }
    },
    {
      width: "150px",
      prop: "counterpartyName",
      label: t("interbanklending.interest.accruleinterest.counterpartyName"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "financeBankAcctNo",
      label: t("interbanklending.interest.accruleinterest.financeBankAcctNo"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "lendingCategory",
      label: t("interbanklending.interest.accruleinterest.lendingCategory"),
      formatter: { name: "const", const: "interbanklending.LendingCategory" }
    },
    {
      width: "150px",
      prop: "amount",
      label: t("interbanklending.interest.accruleinterest.amount"),
      formatter: "amount",
      showOverflowTooltip: true,
      align: "right"
    },
    {
      width: "150px",
      prop: "startDate",
      label: t("interbanklending.interest.accruleinterest.startDate"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "endDate",
      label: t("interbanklending.interest.accruleinterest.endDate"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "days",
      label: t("interbanklending.interest.accruleinterest.days"),
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "interestBalance",
      label: t("interbanklending.interest.accruleinterest.interestBalance"),
      formatter: "amount",
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "interestRate",
      label: t("interbanklending.interest.accruleinterest.interestRate"),
      formatter: "rate",
      showOverflowTooltip: true
    },
    {
      width: "150px",
      prop: "hisAccrualInterest",
      label: t("interbanklending.interest.accruleinterest.hisAccrualInterest"),
      formatter: "amount",
      showOverflowTooltip: true,
      align: "right"
    },
    {
      width: "180px",
      prop: "accruedInterest",
      slots: { default: "accruedInterest" },
      label: t("interbanklending.interest.accruleinterest.accruedInterest"),
      showOverflowTooltip: true,
      align: "right"
    },
    {
      width: "180px",
      prop: "writeOffInterest",
      slots: { default: "writeOffInterest" },
      label: t("interbanklending.interest.accruleinterest.writeOffInterest"),
      showOverflowTooltip: true,
      align: "right"
    },
    {
      width: "180px",
      prop: "inputUserName",
      label: t("interbanklending.interest.accruleinterest.inputUserName"),
      showOverflowTooltip: true
    },
    {
      width: "180px",
      prop: "executeDate",
      label: t("interbanklending.interest.accruleinterest.executeDate"),
      showOverflowTooltip: true
    }
  ];

  return { computerColumn, queryColumn };
};

export default useTableColumn;
