<template>
  <f-query-scene :title="t('interbanklending.interest.accruleinterest.predrawInterestTitle')">
    <template #query-table>
      <f-query-grid
        ref="queryTable"
        row-key="id"
        query-comp-id="interbanklending-business-accruleinterest-query-001"
        table-comp-id="interbanklending-business-accruleinterest-table-001"
        :table-columns="state.column"
        :url="state.queryUrl"
        border
        :select-all="selectableAll"
        :form-data="queryFrom"
        :show-header="true"
        :show-print="false"
        auto-reset
        :auto-init="false"
        :export-url="state.exportUrl"
        :export-exclude="['operate']"
        @select="handleSelect"
        @select-all="handleSelect"
        @clear-selection="handClearSelection"
        :countLabel="t('interbanklending.interest.accruleinterest.record')"
        :countLabelUnit="t('interbanklending.interest.accruleinterest.recordUnit')"
        :summation-biz-label="t('interbanklending.interest.accruleinterest.record')"
        :summation-biz-unit="t('interbanklending.interest.accruleinterest.recordUnit')"
        :allowSort="allowSort"
        :show-count-value="false"
        :show-summation-sum="false"
        tableType="Record"
        @query-table="queryOpeate"
        @on-loaded="handleOnLoaded"
      >
        <template #extend-btns>
          <div>
            <f-button type="primary" @click="computerOpeate()">{{
              t("interbanklending.interest.accruleinterest.computer")
            }}</f-button>
          </div>
        </template>
        <template #operate>
          <!--全额结息-->
          <f-submit-state
            v-if="buttonShow && allButtonShow"
            :is-batch="true"
            :disabled="!isSubmitChecked"
            :gather-params="gatherBatchParams"
            :url="predrawSaveUrl"
            @close="handleSearch"
            :operate-name="t('interbanklending.interest.accruleinterest.batchSettle')"
            :confirm-text="submitMessage"
            :before-trigger="beforeSettlTrigger"
            :batch-confirm-map="settleRusultConfirm"
            :need-extend-columns="batchOperateColumn"
            @submit-success="operateAfter"
            :is-show-result-btn-group="true"
            :is-custom-button="true"
          >
            <template #custom-button>
              <f-button type="primary" @click="errorExport()" :disabled="!expoerFailButton">
                {{ t("interbanklending.interest.accruleinterest.exportFailReason") }}
              </f-button>
            </template>
          </f-submit-state>
          <!--冲销结息-->
          <f-submit-state
            v-if="!buttonShow && allButtonShow"
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isChecked"
            :gather-params="gatherBatchParams"
            :url="predrawSaveUrl"
            @close="handleSearch"
            :operate-name="t('interbanklending.interest.accruleinterest.writeOffSettle')"
            :confirm-text="submitMessage"
            :before-trigger="beforeWriteOffSettleTrigger"
            :batch-confirm-map="writeOffSettleRusultConfirm"
            :need-extend-columns="batchOperateColumn"
            @submit-success="operateAfter"
            :is-show-result-btn-group="true"
            :is-custom-button="true"
          >
            <template #custom-button>
              <f-button type="primary" @click="errorExport()" :disabled="!expoerFailButton">
                {{ t("interbanklending.interest.accruleinterest.exportFailReason") }}
              </f-button>
            </template>
          </f-submit-state>
          <!--批量删除-->
          <f-submit-state
            v-if="batchDeleteShow"
            :is-batch="true"
            operate="remove"
            type="danger"
            :disabled="!isDeleteChecked"
            :gather-params="gatherWritOffBatchParams"
            :url="batchPredrawDeleteUrl"
            @close="handleSearch"
            :operate-name="t('interbanklending.interest.accruleinterest.batchDelete')"
            :confirm-text="submitMessage"
            :before-trigger="beforeDeleteTrigger"
            :batch-confirm-map="deleteRusultConfirm"
            :need-extend-columns="batchOperateColumn"
            @submit-success="operateAfter"
            :is-show-result-btn-group="true"
            :is-custom-button="true"
          >
            <template #custom-button>
              <f-button type="primary" @click="errorExport()" :disabled="!expoerFailButton">
                {{ t("interbanklending.interest.accruleinterest.exportFailReason") }}
              </f-button>
            </template>
          </f-submit-state>
        </template>
        <template #query-panel>
          <!-- 机构 -->
          <f-form-item :label="t('interbanklending.interest.accruleinterest.officeId')" prop="officeIds">
            <f-select
              v-model="queryFrom.officeIds"
              :url="getOfficeInfo"
              value-key="officeId"
              label="officeName"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!-- 币种 -->
          <f-form-item :label="t('interbanklending.interest.accruleinterest.currencyId')" prop="currencyIds">
            <f-select
              v-model="queryFrom.currencyIds"
              :url="getCurrencyInfo"
              value-key="currencyId"
              label="currencyName"
              method="post"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--拆借类型-->
          <f-form-item :label="t('interbanklending.interest.accruleinterest.lendingType')" prop="lendingTypes">
            <f-select
              v-model="queryFrom.lendingTypes"
              :data="interBankType"
              blank-option
              filterable
              multiple
              collapse-tags
              select-all
              init-if-blank
            />
          </f-form-item>
          <!--操作类型-->
          <f-form-item
            :label="t('interbanklending.interest.accruleinterest.operateType')"
            prop="operateType"
            :required="true"
            ref="operateTypeRef"
          >
            <f-select v-model="queryFrom.operateType" :data="interestOptSource" />
          </f-form-item>
          <!--计提日-->
          <f-form-item
            :label="t('interbanklending.interest.accruleinterest.accrualDate')"
            prop="accrualDate"
            :required="true"
            ref="interestDateRef"
          >
            <f-date-picker v-model="queryFrom.accrualDate" type="date" :disabled-date="predrawDateDisabledDate" />
          </f-form-item>
          <!--执行日-->
          <f-form-item
            :label="t('interbanklending.interest.accruleinterest.executeDate')"
            prop="executeDate"
            :required="true"
            ref="executeDateRef"
          >
            <f-date-picker v-model="queryFrom.executeDate" type="date" />
          </f-form-item>
          <!--交易对手编号-->
          <f-form-item :label="t('interbanklending.interest.accruleinterest.counterpartyCode')" prop="counterpartyId">
            <f-magnifier-single
              :title="t('interbanklending.interest.accruleinterest.counterpartyCodeMarginifer')"
              :url="counterPartyMagnifierUrl"
              method="post"
              v-model="queryFrom.counterpartyId"
              row-key="clientId"
              row-label="clientName"
              input-key="codeOrName"
              :params="{
                clientClass: 3,
                counterpartyClass: 1
              }"
              auto-init
            >
              <f-magnifier-column
                prop="clientCode"
                :label="t('interbanklending.interest.accruleinterest.counterpartyCode')"
              />
              <f-magnifier-column
                prop="clientName"
                :label="t('interbanklending.interest.accruleinterest.counterpartyName')"
              />
            </f-magnifier-single>
          </f-form-item>
          <!--交易成交编号-->
          <f-form-item :label="t('interbanklending.interest.accruleinterest.dealCode')" prop="dealCode">
            <f-input v-model="queryFrom.dealCode" />
          </f-form-item>
        </template>
        <template #accruedInterest="{ row }">
          <div :style="{ color: '#FF9E00' }">
            {{ row.writeOffFlag === yesOrNo.YES ? "" : format(row.interest) }}
          </div>
        </template>
        <template #writeOffInterest="{ row }">
          <div :style="{ color: '#FF9E00' }">
            {{ row.writeOffFlag === yesOrNo.YES ? format(row.interest) : "" }}
          </div>
        </template>
        <template #buttons="{ row }">
          <OperateButton :options="generalButtonOption(row)" />
        </template>
      </f-query-grid>
    </template>
  </f-query-scene>
</template>
<script setup lang="ts">
import useList from "../hooks/useList";
import { useI18n } from "vue-i18n";
import {
  getOfficeInfo,
  counterPartyMagnifierUrl,
  getCurrencyInfo,
  predrawSaveUrl,
  batchPredrawDeleteUrl
} from "../url";
import { useConst } from "@ifs/support";
import OperateButton from "@/components/operate-button/operate-button";
import { format } from "@/utils/currency";

const { t } = useI18n();

//业务类型
const interBankType = useConst("interbanklending.InterBankType");
//操作类型
const interestOptSource = useConst("interbanklending.InterestOperateType");
//是否标识
const yesOrNo = useConst("common.YesOrNo");

const {
  queryTable,
  state,
  selectableAll,
  queryFrom,
  handleSelect,
  isChecked,
  isSubmitChecked,
  gatherBatchParams,
  gatherWritOffBatchParams,
  handleSearch,
  generalButtonOption,
  handClearSelection,
  submitMessage,
  beforeWriteOffSettleTrigger,
  beforeSettlTrigger,
  writeOffSettleRusultConfirm,
  settleRusultConfirm,
  allowSort,
  computerOpeate,
  buttonShow,
  queryOpeate,
  allButtonShow,
  interestDateRef,
  executeDateRef,
  operateTypeRef,
  batchDeleteShow,
  beforeDeleteTrigger,
  deleteRusultConfirm,
  batchOperateColumn,
  operateAfter,
  predrawDateDisabledDate,
  isDeleteChecked,
  handleOnLoaded,
  expoerFailButton,
  errorExport
} = useList();
</script>
