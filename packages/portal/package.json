{"name": "portal", "version": "7.0.3", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "report": "vite build", "preview": "vite preview", "test:unit": "vitest --environment jsdom --root src/", "type-check": "vue-tsc --build", "lint:eslint": "eslint . --fix", "lint:other": "prettier -w ./src/style -w ../common", "lint": "run-s lint:*"}, "dependencies": {"@dtg/frontend-plus": "0.2.78-alpha.62", "@dtg/frontend-plus-icons": "2.0.35", "@dtg/hiprint": "workspace:*", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.8.3", "blueimp-md5": "2.19.0", "currency.js": "2.0.4", "dayjs": "^1.11.13", "default-passive-events": "^2.0.0", "echarts": "5.4.1", "event-source-polyfill": "^1.0.31", "html-to-image": "^1.11.11", "jspdf": "^2.5.1", "lunar-typescript": "^1.7.6", "mitt": "^3.0.0", "nprogress": "0.2.0", "pinia": "^3.0.1", "print-js": "^1.6.0", "pyfl": "1.1.4", "qrcode.vue": "^3.4.1", "qs": "^6.11.0", "sm-crypto": "^0.3.12", "vue": "^3.5.13", "vue-draggable-plus": "^0.5.2", "vue-i18n": "11.1.1", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "6.0.3", "@modyfi/vite-plugin-yaml": "1.1.1", "@types/blueimp-md5": "^2.18.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/test-utils": "2.4.4", "@vue/tsconfig": "0.7.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.1", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.87.0", "vue-macros": "^3.0.0-beta.8", "vite": "^6.2.4", "vite-plugin-banner": "0.8.0", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-yaml2": "1.0.0", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}