{"name": "ifinance-frontend-base", "version": "7.0.3", "description": "财司7.0产品前端工程", "author": "dtg-frontend", "keywords": ["ifs", "frontend", "vue3", "typescript"], "private": true, "type": "module", "engines": {"pnpm": ">=10.0.0", "node": ">=20.0.0"}, "scripts": {"dev": "pnpm -C packages/portal dev", "build": "NODE_OPTIONS=--max-old-space-size=12288 pnpm -C packages/portal build-only", "report": "pnpm -C packages/portal report", "lint:oxlint": "oxlint ./packages/portal --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint ./packages/portal --fix", "lint": "run-s lint:*", "format": "prettier --write packages/common/ --write packages/portal/", "t": "NODE_OPTIONS=--max-old-space-size=12288 pnpm -C packages/portal type-check", "build:support": "pnpm -C packages/support build", "build:hiprint": "pnpm -C packages/hiprint build", "postinstall": "pnpm run build:support", "ci-build": "sh build/build.sh", "clean-install": "sh build/clean.sh", "prepare": "husky"}, "dependencies": {"@ifs/support": "workspace:*", "common": "workspace:*"}, "devDependencies": {"@tsconfig/node20": "^20.1.5", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue-macros/eslint-config": "^3.0.0-beta.8", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^16.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^1.3.0", "prettier": "3.5.3", "typescript": "~5.8.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["oxlint --fix -D correctness", "eslint --fix --max-warnings=0", "prettier --write"], "*.{json,css,scss,md,packages/common/**/*.yaml}": ["prettier --write"]}}